# 消声风洞综合示教系统·用户手册
# 一、风洞展示
1. 总览：点击上方按钮"总览"，进入对应页面，点击对应按钮展示对应的区域，显示相关介绍。
![[总览.png]]
2. 漫游：点击上方按钮"漫游"，自动漫游整个风洞实验室。

# 二、风洞实验
1. 振动噪声：支持选择不同数据类型，查看不同通道下噪声波形和振动波形
![[实验.png]]
2. 激振力：支持选择不同数据类型，查看不同通道下力波形
3. 阻力流场：支持选择不同数据类型，查看如下数据：
	1. 倾角：XY两个角度；
	2. 六分力天平：xyz三个方向力，和三个方向旋转扭矩；
	3. 六分力波形
	4. 倾角波形
	5. 扭矩波形

# 三、交互控制
支持在**总览**和**实验状态**下用**键盘加鼠标**进行模型进行移动拖拽缩放操作
1. 移动控制：
	- WASD键：控制相机前后左右移动
	- W：向前移动
	- S：向后移动
	- A：向左移动
	- D：向右移动
	- QE键：控制相机上下移动
	- Q：向上移动
	- E：向下移动
2. 缩放控制：
- 鼠标滚轮：控制相机缩放（视野大小变化）
3. 视角旋转：
- 鼠标左键拖拽：围绕场景原点(0,0,0)旋转视角
- 鼠标中键拖拽：平移相机视角

# 四、开发平台介绍

## Unity
本系统采用Unity 3D引擎开发。Unity是一款功能强大的跨平台游戏引擎和实时3D开发平台，具有以下特点：

1. **跨平台支持**：可发布至Windows、macOS、Linux、Android、iOS等多个平台
2. **实时渲染**：提供高质量的3D图形渲染能力，支持PBR材质和光照系统
3. **物理引擎**：内置NVIDIA PhysX物理引擎，可模拟真实物理交互
4. **可视化编辑**：所见即所得的编辑器界面，支持拖拽式场景搭建
5. **脚本支持**：使用C#语言进行逻辑开发，支持面向对象编程
6. **资源管理**：完善的资源导入管道，支持FBX、OBJ等主流3D格式

在本项目中，Unity的以下功能被重点应用：
- 场景漫游系统的相机控制
- 实验数据的可视化呈现
- 用户交互系统的实现
- 风洞模型的3D展示
