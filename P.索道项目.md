---
tags:
  - Project
Status: 预研
Goal: 给出方案
Deadline: 2025-04-30
created: 2025-01-06 17:15:36
---

# 花台20250425需求拆解
**来源：胡璟胡经理**
1、巡检各区域的二维码要做限制：只能显示本区域巡检内容，不能同时显示各区域的巡检内容;  ——这个需求简单，隐藏一个按钮，改成「目前该区域没有巡检任务」即可。
2、系统要支持日巡检内容导出表格  
 - 要跟现有日志系统表格保持一致  
 - 要支持人员签名

## 方案一：增加对单条巡检数据的打印，美化打印页面的效果

优点：
- 研发成本相对较低
- 扩展性好：客户想要什么样的表格，就设计什么样的巡检任务

缺点：
- 需要商务说服客户修改流程
- 客户对美化后的结果也不一定满意

## 方案二：完全对照现有的日检系统表格

优点：
- 客户适应性好，没有替换成本

缺点：
- 研发成本高：完全定制好的工作，后续维护成本也高，客户愿不愿意付这么高的费用也是一个问题。

**可以调优的路径 ：**
- 把相关的分析工作交给 AI，让 AI 深度理解数据库的字段含义，自动根据表格去生成相关代码，压缩研发成本。

## 方案三：方案一和方案二的融合方案
- 核心表格：客户无法妥协的，必须一致的，走方案二
- 边缘表格：客户能接受改动的，走方案一。

## 待办列表
- 评估日志系统打印表格各种方案的研发成本
- 调研支持人员签名的技术方案的研发成本
- 汇总后的方案提交给 look、林园，看看怎么跟胡璟汇报
- 正式汇报给胡璟

