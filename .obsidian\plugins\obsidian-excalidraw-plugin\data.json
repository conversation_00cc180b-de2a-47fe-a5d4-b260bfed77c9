{"folder": "Excalidraw", "cropFolder": "", "annotateFolder": "", "embedUseExcalidrawFolder": false, "templateFilePath": "Excalidraw/Template.excalidraw", "scriptFolderPath": "Excalidraw/Scripts", "fontAssetsPath": "Excalidraw/CJK Fonts", "loadChineseFonts": false, "loadJapaneseFonts": false, "loadKoreanFonts": false, "compress": true, "decompressForMDView": false, "onceOffCompressFlagReset": true, "onceOffGPTVersionReset": true, "autosave": true, "autosaveIntervalDesktop": 60000, "autosaveIntervalMobile": 30000, "drawingFilenamePrefix": "Drawing ", "drawingEmbedPrefixWithFilename": true, "drawingFilnameEmbedPostfix": " ", "drawingFilenameDateTime": "YYYY-MM-DD HH.mm.ss", "useExcalidrawExtension": true, "cropPrefix": "cropped_", "annotatePrefix": "annotated_", "annotatePreserveSize": false, "previewImageType": "SVGIMG", "renderingConcurrency": 3, "allowImageCache": true, "allowImageCacheInScene": true, "displayExportedImageIfAvailable": false, "previewMatchObsidianTheme": false, "width": "400", "height": "", "overrideObsidianFontSize": false, "dynamicStyling": "colorful", "isLeftHanded": false, "iframeMatchExcalidrawTheme": true, "matchTheme": false, "matchThemeAlways": false, "matchThemeTrigger": false, "defaultMode": "normal", "defaultPenMode": "never", "penModeDoubleTapEraser": true, "penModeSingleFingerPanning": true, "penModeCrosshairVisible": true, "renderImageInMarkdownReadingMode": false, "renderImageInHoverPreviewForMDNotes": false, "renderImageInMarkdownToPDF": false, "allowPinchZoom": false, "allowWheelZoom": false, "zoomToFitOnOpen": true, "zoomToFitOnResize": true, "zoomToFitMaxLevel": 2, "linkPrefix": "📍", "urlPrefix": "🌐", "parseTODO": false, "todo": "☐", "done": "🗹", "hoverPreviewWithoutCTRL": false, "linkOpacity": 1, "openInAdjacentPane": true, "showSecondOrderLinks": true, "focusOnFileTab": true, "openInMainWorkspace": true, "showLinkBrackets": true, "allowCtrlClick": true, "forceWrap": false, "pageTransclusionCharLimit": 200, "wordWrappingDefault": 0, "removeTransclusionQuoteSigns": true, "iframelyAllowed": true, "pngExportScale": 1, "exportWithTheme": true, "exportWithBackground": true, "exportPaddingSVG": 10, "exportEmbedScene": false, "keepInSync": false, "autoexportSVG": false, "autoexportPNG": false, "autoExportLightAndDark": false, "autoexportExcalidraw": false, "embedType": "excalidraw", "embedMarkdownCommentLinks": true, "embedWikiLink": true, "syncExcalidraw": false, "experimentalFileType": false, "experimentalFileTag": "✏️", "experimentalLivePreview": true, "fadeOutExcalidrawMarkup": false, "loadPropertySuggestions": true, "experimentalEnableFourthFont": false, "experimantalFourthFont": "<PERSON>", "addDummyTextElement": false, "zoteroCompatibility": false, "fieldSuggester": true, "compatibilityMode": false, "drawingOpenCount": 0, "library": "deprecated", "library2": {"type": "excalidrawlib", "version": 2, "source": "https://github.com/zsviczian/obsidian-excalidraw-plugin/releases/tag/2.9.2", "libraryItems": [{"status": "unpublished", "elements": [{"type": "line", "version": 1700, "versionNonce": 29893255, "isDeleted": false, "id": "1OMHrnYMU3LJ3w3IaXU_R", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 117.44214663391949, "y": 338.83587294718825, "strokeColor": "#881fa3", "backgroundColor": "#be4bdb", "width": 116.42036295658869, "height": 103.65107323746608, "seed": 1445523839, "groupIds": [], "strokeSharpness": "sharp", "boundElementIds": [], "startBinding": null, "endBinding": null, "points": [[0, 0], [-62.44191743896485, 19.199290805487387], [-63.17668831316513, 79.43840749607878], [-7.618334228588694, 103.65107323746608], [51.96311717336729, 79.15871076413049], [53.24367464342357, 21.285677238400698], [0, 0]], "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": null, "index": "a0", "frameId": null, "roundness": null, "boundElements": [], "updated": 1744286021735, "link": null, "locked": false}], "id": "oUaCo65OdHgJDaeJzqyqw", "created": 1744286021734}, {"status": "unpublished", "elements": [{"id": "b-rwW8s76ztV_uTu1SHq1", "type": "line", "x": -249.71261089403362, "y": 331.4364480697953, "width": 88.21658171083376, "height": 113.8575037534261, "angle": 0, "strokeColor": "#0a11d3", "backgroundColor": "#228be6", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["N2YAi9nU-wlRb0rDaDZoe"], "strokeSharpness": "round", "seed": 1513238033, "version": 3902, "versionNonce": 2015784297, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [0.29089298333313673, 86.05288422061678], [0.013613108737802165, 95.84963140781468], [4.543349062013738, 100.08268472409586], [20.317928500125443, 103.66521849306073], [46.98143617553956, 104.78076599153316], [72.45665455006592, 102.9996310009587], [85.99182564238487, 98.74007888522631], [87.90077837148979, 95.14923176741362], [88.16888387182134, 87.26194204835767], [87.95845222911922, 7.219356674957439], [87.48407176050935, -0.3431928547433216], [81.81967725989045, -4.569951534960701], [69.89167127292335, -7.017866506201685], [42.70935725136615, -9.076737761892943], [20.91603533578692, -7.849028196182914], [3.775735655469765, -3.684787148572539], [-0.047697839012426885, -0.0517060607782156], [0, 0]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null, "index": "a0", "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1744286021735, "link": null, "locked": false}, {"id": "3CMZYj34FwjhgPB7jUC3f", "type": "line", "x": -249.02524930453623, "y": 396.72657610058286, "width": 88.30808627974527, "height": 9.797916664247975, "angle": 0, "strokeColor": "#0a11d3", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["N2YAi9nU-wlRb0rDaDZoe"], "strokeSharpness": "round", "seed": 683951089, "version": 1636, "versionNonce": 1439709607, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [2.326538897826852, 3.9056133261361587], [12.359939318521995, 7.182387014695761], [25.710950037209347, 9.166781347006062], [46.6269757640547, 9.347610268342288], [71.03526003420632, 8.084235941711592], [85.2899738827162, 3.4881086608341767], [88.30808627974527, -0.45030639590568633]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null, "index": "a1", "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1744286021735, "link": null, "locked": false}, {"id": "DX3fUhBWtlJwYyrBDhebG", "type": "line", "x": -250.11899081659772, "y": 363.6524215385111, "width": 88.30808627974527, "height": 9.797916664247975, "angle": 0, "strokeColor": "#0a11d3", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["N2YAi9nU-wlRb0rDaDZoe"], "strokeSharpness": "round", "seed": 1817746897, "version": 1723, "versionNonce": 1504106569, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [2.326538897826852, 3.9056133261361587], [12.359939318521995, 7.182387014695761], [25.710950037209347, 9.166781347006062], [46.6269757640547, 9.347610268342288], [71.03526003420632, 8.084235941711592], [85.2899738827162, 3.4881086608341767], [88.30808627974527, -0.45030639590568633]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null, "index": "a2", "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1744286021735, "link": null, "locked": false}, {"type": "ellipse", "version": 4739, "versionNonce": 453536967, "isDeleted": false, "id": "a-Snvp2FgqDYqSLylF44S", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -251.23981350275943, "y": 323.4117518426986, "strokeColor": "#0a11d3", "backgroundColor": "#fff", "width": 87.65074610854188, "height": 17.72670397681366, "seed": 1409727409, "groupIds": ["N2YAi9nU-wlRb0rDaDZoe"], "strokeSharpness": "sharp", "boundElementIds": ["bxuMGTzXLn7H-uBCptINx"], "index": "a3", "frameId": null, "roundness": null, "boundElements": [{"type": "arrow", "id": "bxuMGTzXLn7H-uBCptINx"}], "updated": 1744286021735, "link": null, "locked": false}, {"id": "7-6c-JFuB2yGoNQRgb2WM", "type": "ellipse", "x": -179.73008120217884, "y": 347.98755471983213, "width": 12.846057046979809, "height": 13.941904362416096, "angle": 0, "strokeColor": "#0a11d3", "backgroundColor": "#fff", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["N2YAi9nU-wlRb0rDaDZoe"], "strokeSharpness": "sharp", "seed": 1073094033, "version": 110, "versionNonce": 1234626345, "isDeleted": false, "boundElementIds": null, "index": "a4", "frameId": null, "roundness": null, "boundElements": [], "updated": 1744286021735, "link": null, "locked": false}, {"id": "150XitJtlKDhTPRCyzv56", "type": "ellipse", "x": -179.73008120217884, "y": 378.5900085788926, "width": 12.846057046979809, "height": 13.941904362416096, "angle": 0, "strokeColor": "#0a11d3", "backgroundColor": "#fff", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["N2YAi9nU-wlRb0rDaDZoe"], "strokeSharpness": "sharp", "seed": 526271345, "version": 159, "versionNonce": 1829583847, "isDeleted": false, "boundElementIds": null, "index": "a5", "frameId": null, "roundness": null, "boundElements": [], "updated": 1744286021735, "link": null, "locked": false}, {"id": "cmwAR3NBl1VqvSorrQN2W", "type": "ellipse", "x": -179.73008120217884, "y": 411.8508097533892, "width": 12.846057046979809, "height": 13.941904362416096, "angle": 0, "strokeColor": "#0a11d3", "backgroundColor": "#fff", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["N2YAi9nU-wlRb0rDaDZoe"], "strokeSharpness": "sharp", "seed": 243707217, "version": 213, "versionNonce": 576854537, "isDeleted": false, "boundElementIds": null, "index": "a6", "frameId": null, "roundness": null, "boundElements": [], "updated": 1744286021735, "link": null, "locked": false}], "id": "LzHJzg-fAHOJEFH8cTDo7", "created": 1744286021735}, {"status": "unpublished", "elements": [{"id": "aDDArXRjZugwyEawdhCeZ", "type": "diamond", "x": -109.55894395256101, "y": 381.22641397493356, "width": 112.64736525303451, "height": 36.77344700318558, "angle": 0, "strokeColor": "#c92a2a", "backgroundColor": "#fd8888", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["M6ByXuSmtHCr3RtPPKJQh"], "strokeSharpness": "sharp", "seed": 511870335, "version": 660, "versionNonce": 1072375559, "isDeleted": false, "boundElementIds": null, "index": "a0", "frameId": null, "roundness": null, "boundElements": [], "updated": 1744286021735, "link": null, "locked": false}, {"id": "Hzx8zkeyDs3YicO2Tdv6G", "type": "diamond", "x": -109.55894395256101, "y": 372.354634046675, "width": 112.64736525303451, "height": 36.77344700318558, "angle": 0, "strokeColor": "#c92a2a", "backgroundColor": "#fd8888", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["M6ByXuSmtHCr3RtPPKJQh"], "strokeSharpness": "sharp", "seed": 1283079231, "version": 701, "versionNonce": 1694757097, "isDeleted": false, "boundElementIds": null, "index": "a1", "frameId": null, "roundness": null, "boundElements": [], "updated": 1744286021735, "link": null, "locked": false}, {"id": "PNzYhT295VNCT5EXmqvmw", "type": "diamond", "x": -109.55894395256101, "y": 359.72407445196296, "width": 112.64736525303451, "height": 36.77344700318558, "angle": 0, "strokeColor": "#c92a2a", "backgroundColor": "#fd8888", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["M6ByXuSmtHCr3RtPPKJQh"], "strokeSharpness": "sharp", "seed": 996251633, "version": 781, "versionNonce": 1377742375, "isDeleted": false, "boundElementIds": null, "index": "a2", "frameId": null, "roundness": null, "boundElements": [], "updated": 1744286021735, "link": null, "locked": false}, {"id": "jiMMAhQF3__7bF-obgXc0", "type": "diamond", "x": -109.55894395256101, "y": 347.1924021546656, "width": 112.64736525303451, "height": 36.77344700318558, "angle": 0, "strokeColor": "#c92a2a", "backgroundColor": "#fd8888", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["M6ByXuSmtHCr3RtPPKJQh"], "strokeSharpness": "sharp", "seed": 1764842481, "version": 823, "versionNonce": 1728572361, "isDeleted": false, "boundElementIds": null, "index": "a3", "frameId": null, "roundness": null, "boundElements": [], "updated": 1744286021735, "link": null, "locked": false}], "id": "GilqlWrxMJtGva9kuXNGj", "created": 1744286021735}, {"status": "unpublished", "elements": [{"id": "BXfdLRoPYZ9MIumzzoA9-", "type": "line", "x": -471.86835398136793, "y": 480.59859081404704, "width": 52.317507746132115, "height": 154.56722543646003, "angle": 1.5707963267948957, "strokeColor": "#087f5b", "backgroundColor": "#40c057", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["HSrtfEf-CssQTf160Fb6R"], "strokeSharpness": "round", "seed": 1424381745, "version": 4767, "versionNonce": 1586778439, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [0.1725162731731403, 116.82107121890462], [0.008073356596080744, 130.12064288619618], [2.694467356765587, 135.86722334556717], [12.049700419984944, 140.7306911579349], [27.86269432990675, 142.2451023824676], [42.97096432627199, 139.82712302629713], [50.998099415101294, 134.0445691284302], [52.13021819883883, 129.16981553143583], [52.28922018370778, 118.46242736729553], [52.16442211418855, 9.800635828982735], [51.88308720685254, -0.46590137319512337], [48.52377541516831, -6.203936550968926], [41.449781688403746, -9.527102901326515], [25.329105770103325, -12.322123053992442], [12.404412180527922, -10.655446243436785], [2.239228448568725, -5.00228186200372], [-0.028287562424331975, -0.07019354973772352], [0, 0]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null, "index": "a0", "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1744286021735, "link": null, "locked": false}, {"id": "TYsYe2VvJ60T_yKa3kyOw", "type": "line", "x": -496.3957643857249, "y": 539.7035473716122, "width": 50.7174766392476, "height": 12.698053371678215, "angle": 1.5707963267948957, "strokeColor": "#087f5b", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["HSrtfEf-CssQTf160Fb6R"], "strokeSharpness": "round", "seed": 726657713, "version": 2406, "versionNonce": 1082755753, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [1.3361877396713384, 5.061656285093649], [7.098613049589299, 9.308339392337079], [14.766422451441104, 11.880105003906111], [26.779003528407447, 12.114458425450186], [40.79727342221974, 10.477131310135727], [48.98410145879092, 4.5205722256349645], [50.7174766392476, -0.5835949462280285]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null, "index": "a1", "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1744286021735, "link": null, "locked": false}, {"id": "VIuxhGjvYUBniitomEkKm", "type": "line", "x": -450.969983237283, "y": 540.0426324966123, "width": 50.57247907260371, "height": 10.178760037658167, "angle": 1.5707963267948957, "strokeColor": "#087f5b", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["HSrtfEf-CssQTf160Fb6R"], "strokeSharpness": "round", "seed": 1977326481, "version": 2539, "versionNonce": 245928039, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [1.332367676378171, 4.05742385947015], [7.078318632616268, 7.4615651903783], [14.724206326638113, 9.523092596748189], [26.70244431044034, 9.710950307853885], [40.68063699304561, 8.398468833558885], [48.84405948536458, 3.623690858023169], [50.57247907260371, -0.4678097298042818]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null, "index": "a2", "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1744286021735, "link": null, "locked": false}, {"type": "ellipse", "version": 5504, "versionNonce": 2050248073, "isDeleted": false, "id": "1acGiqpJjntE3sr1JVnBP", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 1.5707963267948957, "x": -404.36521010516793, "y": 534.1894365757241, "strokeColor": "#087f5b", "backgroundColor": "#fff", "width": 51.27812853552538, "height": 22.797152568995934, "seed": 1774660383, "groupIds": ["HSrtfEf-CssQTf160Fb6R"], "strokeSharpness": "sharp", "boundElementIds": ["bxuMGTzXLn7H-uBCptINx"], "index": "a3", "frameId": null, "roundness": null, "boundElements": [{"type": "arrow", "id": "bxuMGTzXLn7H-uBCptINx"}], "updated": 1744286021735, "link": null, "locked": false}], "id": "Gh9cVmujJHuUfG9h60V9D", "created": 1744286021735}, {"status": "unpublished", "elements": [{"type": "rectangle", "version": 4271, "versionNonce": 740813703, "isDeleted": false, "id": "SqGRpNqls7OV1QB2Eq-0m", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -393.3000561423187, "y": 338.9742643666818, "strokeColor": "#000000", "backgroundColor": "#fff", "width": 70.67858069123133, "height": 107.25081879410921, "seed": 371096063, "groupIds": ["9ppmKFUbA4iKjt8FaDFox"], "strokeSharpness": "sharp", "boundElementIds": ["CFu0B4Mw_1wC1Hbgx8Fs0", "XIl_NhaFtRO00pX5Pq6VU", "EndiSTFlx1AT7vcBVjgve"], "index": "a0", "frameId": null, "roundness": null, "boundElements": [{"type": "arrow", "id": "CFu0B4Mw_1wC1Hbgx8Fs0"}, {"type": "arrow", "id": "XIl_NhaFtRO00pX5Pq6VU"}, {"type": "arrow", "id": "EndiSTFlx1AT7vcBVjgve"}], "updated": 1744286021736, "link": null, "locked": false}, {"type": "rectangle", "version": 4320, "versionNonce": 2068034665, "isDeleted": false, "id": "fayss6b_GPh6LK1x4iX-q", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -400.8474891780329, "y": 331.95417508096745, "strokeColor": "#000000", "backgroundColor": "#fff", "width": 70.67858069123133, "height": 107.25081879410921, "seed": 685932433, "groupIds": ["0RJwA-yKP5dqk5oMiSeot", "9ppmKFUbA4iKjt8FaDFox"], "strokeSharpness": "sharp", "boundElementIds": ["CFu0B4Mw_1wC1Hbgx8Fs0", "XIl_NhaFtRO00pX5Pq6VU", "EndiSTFlx1AT7vcBVjgve"], "index": "a1", "frameId": null, "roundness": null, "boundElements": [{"type": "arrow", "id": "CFu0B4Mw_1wC1Hbgx8Fs0"}, {"type": "arrow", "id": "XIl_NhaFtRO00pX5Pq6VU"}, {"type": "arrow", "id": "EndiSTFlx1AT7vcBVjgve"}], "updated": 1744286021736, "link": null, "locked": false}, {"type": "rectangle", "version": 4418, "versionNonce": 323936935, "isDeleted": false, "id": "HgAnv2rwYhUpLiJiZAXv-", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -410.24257846374826, "y": 323.7002688309677, "strokeColor": "#000000", "backgroundColor": "#fff", "width": 70.67858069123133, "height": 107.25081879410921, "seed": 58634943, "groupIds": ["9ppmKFUbA4iKjt8FaDFox"], "strokeSharpness": "sharp", "boundElementIds": ["CFu0B4Mw_1wC1Hbgx8Fs0", "XIl_NhaFtRO00pX5Pq6VU", "EndiSTFlx1AT7vcBVjgve"], "index": "a2", "frameId": null, "roundness": null, "boundElements": [{"type": "arrow", "id": "CFu0B4Mw_1wC1Hbgx8Fs0"}, {"type": "arrow", "id": "XIl_NhaFtRO00pX5Pq6VU"}, {"type": "arrow", "id": "EndiSTFlx1AT7vcBVjgve"}], "updated": 1744286021736, "link": null, "locked": false}, {"type": "line", "version": 3542, "versionNonce": 931348297, "isDeleted": false, "id": "12aO-Bs9HdALZN_-tuQTr", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -398.2561518768373, "y": 372.4475058783203, "strokeColor": "#000000", "backgroundColor": "#fff", "width": 46.57983585730082, "height": 3.2499538442902027, "seed": 1673003743, "groupIds": ["9ppmKFUbA4iKjt8FaDFox"], "strokeSharpness": "round", "boundElementIds": [], "points": [[0, 0], [40.42449133807562, 0.1573930526684746], [46.57983585730082, -3.0925607916217284]], "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": null, "index": "a3", "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1744286021736, "link": null, "locked": false, "startBinding": null, "endBinding": null}, {"type": "line", "version": 3568, "versionNonce": 1690957255, "isDeleted": false, "id": "Ck_Y0EVPh_fsY0qoRnGiD", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -396.400899638823, "y": 340.9822185794818, "strokeColor": "#000000", "backgroundColor": "#fff", "width": 45.567415680676426, "height": 2.8032978840147194, "seed": 1821527807, "groupIds": ["9ppmKFUbA4iKjt8FaDFox"], "strokeSharpness": "round", "boundElementIds": [], "points": [[0, 0], [16.832548902953302, -2.8032978840147194], [45.567415680676426, -0.3275477042019195]], "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": null, "index": "a4", "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1744286021736, "link": null, "locked": false, "startBinding": null, "endBinding": null}, {"type": "line", "version": 3593, "versionNonce": 491679273, "isDeleted": false, "id": "a_7IZapEuD918VW1P8Ss_", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -396.4774991551924, "y": 408.37659284983897, "strokeColor": "#000000", "backgroundColor": "#fff", "width": 48.33668263438425, "height": 4.280657518731036, "seed": 1485707039, "groupIds": ["9ppmKFUbA4iKjt8FaDFox"], "strokeSharpness": "round", "boundElementIds": [], "points": [[0, 0], [26.41225578429045, -0.2552319773002338], [37.62000339651456, 2.3153712935189787], [48.33668263438425, -1.9652862252120569]], "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": null, "index": "a5", "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1744286021736, "link": null, "locked": false, "startBinding": null, "endBinding": null}, {"type": "line", "version": 3630, "versionNonce": 2008392935, "isDeleted": false, "id": "8io6FVNdFOLsQ266W8Lni", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -399.6615463367227, "y": 419.61974125811776, "strokeColor": "#000000", "backgroundColor": "#fff", "width": 54.40694982784246, "height": 2.9096445412231735, "seed": 1042012991, "groupIds": ["9ppmKFUbA4iKjt8FaDFox"], "strokeSharpness": "round", "boundElementIds": [], "points": [[0, 0], [10.166093050596771, -1.166642430373031], [16.130660965377448, -0.8422655250909383], [46.26079588567538, 0.6125567455206506], [54.40694982784246, -2.297087795702523]], "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": null, "index": "a6", "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1744286021736, "link": null, "locked": false, "startBinding": null, "endBinding": null}, {"type": "line", "version": 3595, "versionNonce": 1133560073, "isDeleted": false, "id": "LJI5kY6tg7UFAjPV3fKL-", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -399.3767034411569, "y": 356.042820132743, "strokeColor": "#000000", "backgroundColor": "#fff", "width": 46.92865289294453, "height": 2.4757501798128, "seed": 295443295, "groupIds": ["9ppmKFUbA4iKjt8FaDFox"], "strokeSharpness": "round", "boundElementIds": [], "points": [[0, 0], [18.193786115221407, -0.5912874140789839], [46.92865289294453, 1.884462765733816]], "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": null, "index": "a7", "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1744286021736, "link": null, "locked": false, "startBinding": null, "endBinding": null}, {"type": "line", "version": 3610, "versionNonce": 194932743, "isDeleted": false, "id": "zCrZOHW-q8YWKLw6ltKxX", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -399.26921524500654, "y": 390.5261491685826, "strokeColor": "#000000", "backgroundColor": "#fff", "width": 46.92865289294453, "height": 2.4757501798128, "seed": 1734301567, "groupIds": ["9ppmKFUbA4iKjt8FaDFox"], "strokeSharpness": "round", "boundElementIds": [], "points": [[0, 0], [8.093938105125233, 1.4279702913643746], [18.193786115221407, -0.5912874140789839], [46.92865289294453, 1.884462765733816]], "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": null, "index": "a8", "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1744286021736, "link": null, "locked": false, "startBinding": null, "endBinding": null}], "id": "Sjt9mHmgPKOo5t_K-0eGZ", "created": 1744286021735}, {"status": "unpublished", "elements": [{"id": "XOD3vRhtoLWoxC9wF9Sk8", "type": "rectangle", "x": -593.9896997899341, "y": 343.9798351106279, "width": 127.88383573213892, "height": 76.53703389977764, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["TC0RSM64Cxmu17MlE12-o"], "strokeSharpness": "sharp", "seed": 106569279, "version": 677, "versionNonce": 1472575465, "isDeleted": false, "boundElementIds": null, "index": "a0", "frameId": null, "roundness": null, "boundElements": [], "updated": 1744286021736, "link": null, "locked": false}, {"id": "WBkTga1PjKzYK-tcGjnjZ", "type": "line", "x": -595.0652975408293, "y": 354.6963695028721, "width": 128.84193229844433, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["TC0RSM64Cxmu17MlE12-o"], "strokeSharpness": "round", "seed": 73916127, "version": 463, "versionNonce": 981522215, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [128.84193229844433, 0]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null, "index": "a1", "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1744286021736, "link": null, "locked": false}, {"id": "FHX0ZsIzUUfYPJqrZ8Lso", "type": "ellipse", "x": -589.5016643209792, "y": 348.2514049106367, "width": 5.001953125, "height": 5.001953125, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#fa5252", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "groupIds": ["TC0RSM64Cxmu17MlE12-o"], "strokeSharpness": "sharp", "seed": 387857791, "version": 283, "versionNonce": 1274598089, "isDeleted": false, "boundElementIds": null, "index": "a2", "frameId": null, "roundness": null, "boundElements": [], "updated": 1744286021736, "link": null, "locked": false}, {"id": "ugVRR0f_uDOjrllO10yAs", "type": "ellipse", "x": -579.2389690084792, "y": 348.2514049106367, "width": 5.001953125, "height": 5.001953125, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#fab005", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "groupIds": ["TC0RSM64Cxmu17MlE12-o"], "strokeSharpness": "sharp", "seed": 1486370207, "version": 328, "versionNonce": 717838919, "isDeleted": false, "boundElementIds": null, "index": "a3", "frameId": null, "roundness": null, "boundElements": [], "updated": 1744286021736, "link": null, "locked": false}, {"id": "SBzNA0Sn-ou4QGxotj0SB", "type": "ellipse", "x": -568.525552542133, "y": 348.7021260644829, "width": 5.001953125, "height": 5.001953125, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#40c057", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "groupIds": ["TC0RSM64Cxmu17MlE12-o"], "strokeSharpness": "sharp", "seed": 610150847, "version": 386, "versionNonce": 1153798569, "isDeleted": false, "boundElementIds": null, "index": "a4", "frameId": null, "roundness": null, "boundElements": [], "updated": 1744286021736, "link": null, "locked": false}, {"id": "VKcfbELTVlyJ90m0bGsj0", "type": "ellipse", "x": -552.4984915525058, "y": 364.75449494249875, "width": 42.72020253937572, "height": 42.72020253937572, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#04aaf7", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 90, "groupIds": ["TC0RSM64Cxmu17MlE12-o"], "strokeSharpness": "sharp", "seed": 144280593, "version": 665, "versionNonce": 22313319, "isDeleted": false, "boundElementIds": null, "index": "a5", "frameId": null, "roundness": null, "boundElements": [], "updated": 1744286021736, "link": null, "locked": false}, {"id": "zWrJVrKnkF5K8iXNxi9Aa", "type": "line", "x": -525.4203274905302, "y": 380.97884700695636, "width": 28.226201983883442, "height": 24.441122842819972, "angle": 0, "strokeColor": "#087f5b", "backgroundColor": "#40c057", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "groupIds": ["TC0RSM64Cxmu17MlE12-o"], "strokeSharpness": "round", "seed": 29167967, "version": 1282, "versionNonce": 1196913801, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [-1.8305638688608794, -0.4146385831511896], [-7.572250391862635, -6.271625431345418], [-11.35769254157424, -4.347633009945142], [-12.611765400987878, 2.3733287939365098], [-11.26889653301009, 6.740045588043412], [-17.42450906516472, 8.886103861507927], [-17.203202089974113, 13.643170786196503], [-12.380895778721076, 13.349974465892952], [-8.695178377089249, 9.477701170522828], [-3.6201449645383086, 17.867626643824725], [-0.415292101592283, 18.169497411474552], [-3.7772455950748833, 4.800439161419844], [1.3865838260401944, 4.3476330099451115], [3.162503997323137, 5.86739618500973], [9.236150983110862, 5.86739618500973], [10.80169291871872, 0.6349695457461695], [4.221225637895673, 1.1103292603211785], [5.486227236824892, -5.759833037916143], [2.472627315401658, -5.345194454764953], [-0.23770008446403423, -0.9229611976419838], [0, 0]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null, "index": "a6", "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1744286021736, "link": null, "locked": false}, {"id": "LX6kTl9A8K36ld2MEV4tI", "type": "line", "x": -551.4394290784783, "y": 385.71736850567976, "width": 42.095115772272244, "height": 0, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#99bcff", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 90, "groupIds": ["TC0RSM64Cxmu17MlE12-o"], "strokeSharpness": "round", "seed": 1443027377, "version": 702, "versionNonce": 326022279, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [42.095115772272244, 0]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null, "index": "a7", "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1744286021736, "link": null, "locked": false}, {"id": "SHmV_QtcwxIE-peI_QOX1", "type": "line", "x": -546.3441000487039, "y": 370.2828395787136, "width": 29.31860660384862, "height": 5.711199931375845, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#99bcff", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 90, "groupIds": ["TC0RSM64Cxmu17MlE12-o"], "strokeSharpness": "round", "seed": 244310513, "version": 2909, "versionNonce": 661257065, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [0.7724193963150375, 2.2765797384357125], [4.103544916365185, 4.186609221587683], [8.536129150893453, 5.343311508306209], [15.480325949120388, 5.448716592152419], [23.583965316012858, 4.712296432964328], [28.316582284417855, 2.033216518393959], [29.31860660384862, -0.2624833392234258]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null, "index": "a8", "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1744286021736, "link": null, "locked": false}, {"id": "PKRg6SqIetkWIgRqBAnDY", "type": "ellipse", "x": -538.2701841247845, "y": 363.37196531290607, "width": 15.528434353116108, "height": 44.82230388130942, "angle": 0, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 90, "groupIds": ["TC0RSM64Cxmu17MlE12-o"], "strokeSharpness": "sharp", "seed": 683572113, "version": 726, "versionNonce": 1490324391, "isDeleted": false, "boundElementIds": null, "index": "a9", "frameId": null, "roundness": null, "boundElements": [], "updated": 1744286021736, "link": null, "locked": false}, {"id": "HrelUAgvfxi_4v8MyL_iT", "type": "line", "x": -544.828148539078, "y": 402.0199316371545, "width": 29.31860660384862, "height": 5.896061363392446, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#99bcff", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "opacity": 90, "groupIds": ["TC0RSM64Cxmu17MlE12-o"], "strokeSharpness": "round", "seed": 318798801, "version": 3114, "versionNonce": 893953609, "isDeleted": false, "boundElementIds": null, "points": [[0, 0], [4.103544916365185, -4.322122351104391], [8.536129150893453, -5.516265043290966], [15.480325949120388, -5.625081903117008], [23.583965316012858, -4.8648251269605955], [28.316582284417855, -2.0990281379671547], [29.31860660384862, 0.2709794602754383]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null, "index": "aA", "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1744286021736, "link": null, "locked": false}], "id": "N4DFjyIgYC9DraPRK6ccW", "created": 1744286021736}, {"status": "unpublished", "elements": [{"id": "dba8s5bDYEnF20oGn2a8b", "type": "rectangle", "x": -715.1043446306466, "y": 330.4231266309418, "width": 70.81644178885557, "height": 108.30428902193904, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#ced4da", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["GMZ-NW9lG7c1AtfBInZ0n"], "strokeSharpness": "sharp", "seed": 1914896753, "version": 686, "versionNonce": 498237127, "isDeleted": false, "boundElementIds": null, "index": "a0", "frameId": null, "roundness": null, "boundElements": [], "updated": 1744286021736, "link": null, "locked": false}, {"id": "3HxCT4mFZF-jJ6m9pyOCt", "type": "rectangle", "x": -706.996640540555, "y": 338.68030798133873, "width": 55.801163535143246, "height": 82.83278895375764, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#fff", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["GMZ-NW9lG7c1AtfBInZ0n"], "strokeSharpness": "sharp", "seed": 1306468145, "version": 836, "versionNonce": 734983465, "isDeleted": false, "boundElementIds": null, "index": "a1", "frameId": null, "roundness": null, "boundElements": [], "updated": 1744286021736, "link": null, "locked": false}, {"id": "xX9mcMHy_0Bn-D0UAMyCc", "type": "ellipse", "x": -684.8099707762028, "y": 425.0579911039235, "width": 11.427824006438863, "height": 11.427824006438863, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#fff", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["GMZ-NW9lG7c1AtfBInZ0n"], "strokeSharpness": "sharp", "seed": 93422161, "version": 882, "versionNonce": 1956974055, "isDeleted": false, "boundElementIds": null, "index": "a2", "frameId": null, "roundness": null, "boundElements": [], "updated": 1744286021736, "link": null, "locked": false}, {"id": "h60d2h6UPYkopTlW_XEs4", "type": "rectangle", "x": -698.7169501405845, "y": 349.2244646574789, "width": 39.2417827352022, "height": 19.889460471185775, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#fab005", "fillStyle": "cross-hatch", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["GMZ-NW9lG7c1AtfBInZ0n"], "strokeSharpness": "sharp", "seed": 11646495, "version": 529, "versionNonce": 1583900681, "isDeleted": false, "boundElementIds": null, "index": "a3", "frameId": null, "roundness": null, "boundElements": [], "updated": 1744286021736, "link": null, "locked": false}, {"id": "bZbx28BjXM33JV1UezMcH", "type": "rectangle", "x": -698.7169501405845, "y": 384.7822247024333, "width": 39.2417827352022, "height": 19.889460471185775, "angle": 0, "strokeColor": "#000000", "backgroundColor": "#fab005", "fillStyle": "cross-hatch", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": ["GMZ-NW9lG7c1AtfBInZ0n"], "strokeSharpness": "sharp", "seed": 291717649, "version": 571, "versionNonce": 1756936455, "isDeleted": false, "boundElementIds": null, "index": "a4", "frameId": null, "roundness": null, "boundElements": [], "updated": 1744286021736, "link": null, "locked": false}], "id": "ubO1BasqS42Vwjhsoyzf2", "created": 1744286021736}]}, "imageElementNotice": true, "mdSVGwidth": 500, "mdSVGmaxHeight": 800, "mdFont": "<PERSON>", "mdFontColor": "Black", "mdBorderColor": "Black", "mdCSS": "", "scriptEngineSettings": {}, "defaultTrayMode": true, "previousRelease": "2.9.2", "showReleaseNotes": true, "showNewVersionNotification": true, "latexBoilerplate": "\\color{blue}", "latexPreambleLocation": "preamble.sty", "taskboneEnabled": false, "taskboneAPIkey": "", "pinnedScripts": [], "customPens": [{"type": "default", "freedrawOnly": false, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 0, "roughness": 0, "penOptions": {"highlighter": false, "constantPressure": false, "hasOutline": false, "outlineWidth": 1, "options": {"thinning": 0.6, "smoothing": 0.5, "streamline": 0.5, "easing": "easeOutSine", "start": {"cap": true, "taper": 0, "easing": "linear"}, "end": {"cap": true, "taper": 0, "easing": "linear"}}}}, {"type": "highlighter", "freedrawOnly": true, "strokeColor": "#FFC47C", "backgroundColor": "#FFC47C", "fillStyle": "solid", "strokeWidth": 2, "roughness": null, "penOptions": {"highlighter": true, "constantPressure": true, "hasOutline": true, "outlineWidth": 4, "options": {"thinning": 1, "smoothing": 0.5, "streamline": 0.5, "easing": "linear", "start": {"taper": 0, "cap": true, "easing": "linear"}, "end": {"taper": 0, "cap": true, "easing": "linear"}}}}, {"type": "finetip", "freedrawOnly": false, "strokeColor": "#3E6F8D", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 0.5, "roughness": 0, "penOptions": {"highlighter": false, "hasOutline": false, "outlineWidth": 1, "constantPressure": true, "options": {"smoothing": 0.4, "thinning": -0.5, "streamline": 0.4, "easing": "linear", "start": {"taper": 5, "cap": false, "easing": "linear"}, "end": {"taper": 5, "cap": false, "easing": "linear"}}}}, {"type": "fountain", "freedrawOnly": false, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "roughness": 0, "penOptions": {"highlighter": false, "constantPressure": false, "hasOutline": false, "outlineWidth": 1, "options": {"smoothing": 0.2, "thinning": 0.6, "streamline": 0.2, "easing": "easeInOutSine", "start": {"taper": 150, "cap": true, "easing": "linear"}, "end": {"taper": 1, "cap": true, "easing": "linear"}}}}, {"type": "marker", "freedrawOnly": true, "strokeColor": "#B83E3E", "backgroundColor": "#FF7C7C", "fillStyle": "dashed", "strokeWidth": 2, "roughness": 3, "penOptions": {"highlighter": false, "constantPressure": true, "hasOutline": true, "outlineWidth": 4, "options": {"thinning": 1, "smoothing": 0.5, "streamline": 0.5, "easing": "linear", "start": {"taper": 0, "cap": true, "easing": "linear"}, "end": {"taper": 0, "cap": true, "easing": "linear"}}}}, {"type": "thick-thin", "freedrawOnly": true, "strokeColor": "#CECDCC", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 0, "roughness": null, "penOptions": {"highlighter": true, "constantPressure": true, "hasOutline": false, "outlineWidth": 1, "options": {"thinning": 1, "smoothing": 0.5, "streamline": 0.5, "easing": "linear", "start": {"taper": 0, "cap": true, "easing": "linear"}, "end": {"cap": true, "taper": true, "easing": "linear"}}}}, {"type": "thin-thick-thin", "freedrawOnly": true, "strokeColor": "#CECDCC", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 0, "roughness": null, "penOptions": {"highlighter": true, "constantPressure": true, "hasOutline": false, "outlineWidth": 1, "options": {"thinning": 1, "smoothing": 0.5, "streamline": 0.5, "easing": "linear", "start": {"cap": true, "taper": true, "easing": "linear"}, "end": {"cap": true, "taper": true, "easing": "linear"}}}}, {"type": "default", "freedrawOnly": false, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 0, "roughness": 0, "penOptions": {"highlighter": false, "constantPressure": false, "hasOutline": false, "outlineWidth": 1, "options": {"thinning": 0.6, "smoothing": 0.5, "streamline": 0.5, "easing": "easeOutSine", "start": {"cap": true, "taper": 0, "easing": "linear"}, "end": {"cap": true, "taper": 0, "easing": "linear"}}}}, {"type": "default", "freedrawOnly": false, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 0, "roughness": 0, "penOptions": {"highlighter": false, "constantPressure": false, "hasOutline": false, "outlineWidth": 1, "options": {"thinning": 0.6, "smoothing": 0.5, "streamline": 0.5, "easing": "easeOutSine", "start": {"cap": true, "taper": 0, "easing": "linear"}, "end": {"cap": true, "taper": 0, "easing": "linear"}}}}, {"type": "default", "freedrawOnly": false, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 0, "roughness": 0, "penOptions": {"highlighter": false, "constantPressure": false, "hasOutline": false, "outlineWidth": 1, "options": {"thinning": 0.6, "smoothing": 0.5, "streamline": 0.5, "easing": "easeOutSine", "start": {"cap": true, "taper": 0, "easing": "linear"}, "end": {"cap": true, "taper": 0, "easing": "linear"}}}}], "numberOfCustomPens": 0, "pdfScale": 4, "pdfBorderBox": true, "pdfFrame": false, "pdfGapSize": 20, "pdfGroupPages": false, "pdfLockAfterImport": true, "pdfNumColumns": 1, "pdfNumRows": 1, "pdfDirection": "right", "pdfImportScale": 0.3, "gridSettings": {"DYNAMIC_COLOR": true, "COLOR": "#000000", "OPACITY": 50}, "laserSettings": {"DECAY_LENGTH": 50, "DECAY_TIME": 1000, "COLOR": "#ff0000"}, "embeddableMarkdownDefaults": {"useObsidianDefaults": false, "backgroundMatchCanvas": false, "backgroundMatchElement": true, "backgroundColor": "#fff", "backgroundOpacity": 60, "borderMatchElement": true, "borderColor": "#fff", "borderOpacity": 0, "filenameVisible": false}, "markdownNodeOneClickEditing": false, "canvasImmersiveEmbed": true, "startupScriptPath": "", "openAIAPIToken": "", "openAIDefaultTextModel": "gpt-3.5-turbo-1106", "openAIDefaultVisionModel": "gpt-4o", "openAIDefaultImageGenerationModel": "dall-e-3", "openAIURL": "https://api.openai.com/v1/chat/completions", "openAIImageGenerationURL": "https://api.openai.com/v1/images/generations", "openAIImageEditsURL": "https://api.openai.com/v1/images/edits", "openAIImageVariationURL": "https://api.openai.com/v1/images/variations", "modifierKeyConfig": {"Mac": {"LocalFileDragAction": {"defaultAction": "image-import", "rules": [{"shift": false, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image-import"}, {"shift": true, "ctrl_cmd": false, "alt_opt": true, "meta_ctrl": false, "result": "link"}, {"shift": true, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image-url"}, {"shift": false, "ctrl_cmd": false, "alt_opt": true, "meta_ctrl": false, "result": "embeddable"}]}, "WebBrowserDragAction": {"defaultAction": "image-url", "rules": [{"shift": false, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image-url"}, {"shift": true, "ctrl_cmd": false, "alt_opt": true, "meta_ctrl": false, "result": "link"}, {"shift": false, "ctrl_cmd": false, "alt_opt": true, "meta_ctrl": false, "result": "embeddable"}, {"shift": true, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image-import"}]}, "InternalDragAction": {"defaultAction": "link", "rules": [{"shift": false, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "link"}, {"shift": false, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": true, "result": "embeddable"}, {"shift": true, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image"}, {"shift": true, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": true, "result": "image-fullsize"}]}, "LinkClickAction": {"defaultAction": "new-tab", "rules": [{"shift": false, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "active-pane"}, {"shift": false, "ctrl_cmd": true, "alt_opt": false, "meta_ctrl": false, "result": "new-tab"}, {"shift": false, "ctrl_cmd": true, "alt_opt": true, "meta_ctrl": false, "result": "new-pane"}, {"shift": true, "ctrl_cmd": true, "alt_opt": true, "meta_ctrl": false, "result": "popout-window"}, {"shift": false, "ctrl_cmd": true, "alt_opt": false, "meta_ctrl": true, "result": "md-properties"}]}}, "Win": {"LocalFileDragAction": {"defaultAction": "image-import", "rules": [{"shift": false, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image-import"}, {"shift": false, "ctrl_cmd": true, "alt_opt": false, "meta_ctrl": false, "result": "link"}, {"shift": true, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image-url"}, {"shift": true, "ctrl_cmd": true, "alt_opt": false, "meta_ctrl": false, "result": "embeddable"}]}, "WebBrowserDragAction": {"defaultAction": "image-url", "rules": [{"shift": false, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image-url"}, {"shift": false, "ctrl_cmd": true, "alt_opt": false, "meta_ctrl": false, "result": "link"}, {"shift": true, "ctrl_cmd": true, "alt_opt": false, "meta_ctrl": false, "result": "embeddable"}, {"shift": true, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image-import"}]}, "InternalDragAction": {"defaultAction": "link", "rules": [{"shift": false, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "link"}, {"shift": true, "ctrl_cmd": true, "alt_opt": false, "meta_ctrl": false, "result": "embeddable"}, {"shift": true, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image"}, {"shift": false, "ctrl_cmd": true, "alt_opt": true, "meta_ctrl": false, "result": "image-fullsize"}]}, "LinkClickAction": {"defaultAction": "new-tab", "rules": [{"shift": false, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "active-pane"}, {"shift": false, "ctrl_cmd": true, "alt_opt": false, "meta_ctrl": false, "result": "new-tab"}, {"shift": false, "ctrl_cmd": true, "alt_opt": true, "meta_ctrl": false, "result": "new-pane"}, {"shift": true, "ctrl_cmd": true, "alt_opt": true, "meta_ctrl": false, "result": "popout-window"}, {"shift": false, "ctrl_cmd": true, "alt_opt": false, "meta_ctrl": true, "result": "md-properties"}]}}}, "slidingPanesSupport": false, "areaZoomLimit": 1, "longPressDesktop": 500, "longPressMobile": 500, "doubleClickLinkOpenViewMode": true, "isDebugMode": false, "rank": "Bronze", "modifierKeyOverrides": [{"modifiers": ["Mod"], "key": "Enter"}, {"modifiers": ["Mod"], "key": "k"}, {"modifiers": ["Mod"], "key": "G"}], "showSplashscreen": true, "pdfSettings": {"pageSize": "A4", "pageOrientation": "portrait", "fitToPage": 1, "paperColor": "white", "customPaperColor": "#ffffff", "alignment": "center", "margin": "normal"}}