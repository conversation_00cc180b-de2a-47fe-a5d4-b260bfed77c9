{"nodes": [{"type": "text", "text": "## 💫 精神\n> 对一切保持好奇心，随心而动\n```dataview\nLIST\nFROM \"02精神\"\nWHERE status=\"active\" AND !contains(file.name, \"Template\")\nSORT file.name ASC\n```", "id": "45f7f22634f0bb7e", "x": -180, "y": -360, "width": 380, "height": 500, "color": "6"}, {"type": "text", "text": "## 🔥 身体\n> 健康是一切的基石\n```dataview\nLIST\nFROM \"01身体\"\nWHERE status=\"active\" AND !contains(file.name, \"Template\")\nSORT file.name ASC\n```", "id": "b78fa040a3fea1ec", "x": -560, "y": -360, "width": 380, "height": 500, "color": "4"}, {"type": "text", "text": "## 📝财富\n> 经济基础决定上层建筑\n```dataview\nLIST\nFROM \"03财富\"\nWHERE status=\"active\" AND !contains(file.name, \"Template\")\nSORT file.name ASC\n```", "id": "9ec2492e84f151f4", "x": -560, "y": 140, "width": 380, "height": 480}, {"type": "text", "text": "## 📦 社交\n> 爱世人\n```dataview\nLIST\nFROM \"04社交\"\nWHERE status=\"active\" AND !contains(file.name, \"Template\")\nSORT file.name ASC\n```", "id": "528e809ab3b9cd3c", "x": -180, "y": 140, "width": 380, "height": 480, "color": "5"}], "edges": []}