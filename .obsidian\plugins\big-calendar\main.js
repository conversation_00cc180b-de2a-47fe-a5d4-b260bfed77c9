"use strict";var J1=Object.defineProperty;var Z1=(t,r,a)=>r in t?J1(t,r,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[r]=a;var ye=(t,r,a)=>Z1(t,typeof r!="symbol"?r+"":r,a);const ee=require("obsidian");var gn=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Ct(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function Cl(t){if(t.__esModule)return t;var r=t.default;if(typeof r=="function"){var a=function o(){return this instanceof o?Reflect.construct(r,arguments,this.constructor):r.apply(this,arguments)};a.prototype=r.prototype}else a={};return Object.defineProperty(a,"__esModule",{value:!0}),Object.keys(t).forEach(function(o){var s=Object.getOwnPropertyDescriptor(t,o);Object.defineProperty(a,o,s.get?s:{enumerable:!0,get:function(){return t[o]}})}),a}var uw={exports:{}},tl={},ay;function eD(){if(ay)return tl;ay=1;var t=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function a(o,s,u){var c=null;if(u!==void 0&&(c=""+u),s.key!==void 0&&(c=""+s.key),"key"in s){u={};for(var d in s)d!=="key"&&(u[d]=s[d])}else u=s;return s=u.ref,{$$typeof:t,type:o,key:c,ref:s!==void 0?s:null,props:u}}return tl.Fragment=r,tl.jsx=a,tl.jsxs=a,tl}var cw={exports:{}},Se={},oy;function tD(){if(oy)return Se;oy=1;var t=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),u=Symbol.for("react.consumer"),c=Symbol.for("react.context"),d=Symbol.for("react.forward_ref"),v=Symbol.for("react.suspense"),h=Symbol.for("react.memo"),m=Symbol.for("react.lazy"),g=Symbol.iterator;function y(O){return O===null||typeof O!="object"?null:(O=g&&O[g]||O["@@iterator"],typeof O=="function"?O:null)}var w={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},k=Object.assign,S={};function E(O,P,H){this.props=O,this.context=P,this.refs=S,this.updater=H||w}E.prototype.isReactComponent={},E.prototype.setState=function(O,P){if(typeof O!="object"&&typeof O!="function"&&O!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,O,P,"setState")},E.prototype.forceUpdate=function(O){this.updater.enqueueForceUpdate(this,O,"forceUpdate")};function x(){}x.prototype=E.prototype;function _(O,P,H){this.props=O,this.context=P,this.refs=S,this.updater=H||w}var F=_.prototype=new x;F.constructor=_,k(F,E.prototype),F.isPureReactComponent=!0;var C=Array.isArray,A={H:null,A:null,T:null,S:null},B=Object.prototype.hasOwnProperty;function q(O,P,H,W,L,G){return H=G.ref,{$$typeof:t,type:O,key:P,ref:H!==void 0?H:null,props:G}}function Q(O,P){return q(O.type,P,void 0,void 0,void 0,O.props)}function I(O){return typeof O=="object"&&O!==null&&O.$$typeof===t}function V(O){var P={"=":"=0",":":"=2"};return"$"+O.replace(/[=:]/g,function(H){return P[H]})}var J=/\/+/g;function le(O,P){return typeof O=="object"&&O!==null&&O.key!=null?V(""+O.key):P.toString(36)}function ae(){}function fe(O){switch(O.status){case"fulfilled":return O.value;case"rejected":throw O.reason;default:switch(typeof O.status=="string"?O.then(ae,ae):(O.status="pending",O.then(function(P){O.status==="pending"&&(O.status="fulfilled",O.value=P)},function(P){O.status==="pending"&&(O.status="rejected",O.reason=P)})),O.status){case"fulfilled":return O.value;case"rejected":throw O.reason}}throw O}function me(O,P,H,W,L){var G=typeof O;(G==="undefined"||G==="boolean")&&(O=null);var ne=!1;if(O===null)ne=!0;else switch(G){case"bigint":case"string":case"number":ne=!0;break;case"object":switch(O.$$typeof){case t:case r:ne=!0;break;case m:return ne=O._init,me(ne(O._payload),P,H,W,L)}}if(ne)return L=L(O),ne=W===""?"."+le(O,0):W,C(L)?(H="",ne!=null&&(H=ne.replace(J,"$&/")+"/"),me(L,P,H,"",function(Oe){return Oe})):L!=null&&(I(L)&&(L=Q(L,H+(L.key==null||O&&O.key===L.key?"":(""+L.key).replace(J,"$&/")+"/")+ne)),P.push(L)),1;ne=0;var ve=W===""?".":W+":";if(C(O))for(var ce=0;ce<O.length;ce++)W=O[ce],G=ve+le(W,ce),ne+=me(W,P,H,G,L);else if(ce=y(O),typeof ce=="function")for(O=ce.call(O),ce=0;!(W=O.next()).done;)W=W.value,G=ve+le(W,ce++),ne+=me(W,P,H,G,L);else if(G==="object"){if(typeof O.then=="function")return me(fe(O),P,H,W,L);throw P=String(O),Error("Objects are not valid as a React child (found: "+(P==="[object Object]"?"object with keys {"+Object.keys(O).join(", ")+"}":P)+"). If you meant to render a collection of children, use an array instead.")}return ne}function $(O,P,H){if(O==null)return O;var W=[],L=0;return me(O,W,"","",function(G){return P.call(H,G,L++)}),W}function ie(O){if(O._status===-1){var P=O._result;P=P(),P.then(function(H){(O._status===0||O._status===-1)&&(O._status=1,O._result=H)},function(H){(O._status===0||O._status===-1)&&(O._status=2,O._result=H)}),O._status===-1&&(O._status=0,O._result=P)}if(O._status===1)return O._result.default;throw O._result}var se=typeof reportError=="function"?reportError:function(O){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var P=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof O=="object"&&O!==null&&typeof O.message=="string"?String(O.message):String(O),error:O});if(!window.dispatchEvent(P))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",O);return}console.error(O)};function ge(){}return Se.Children={map:$,forEach:function(O,P,H){$(O,function(){P.apply(this,arguments)},H)},count:function(O){var P=0;return $(O,function(){P++}),P},toArray:function(O){return $(O,function(P){return P})||[]},only:function(O){if(!I(O))throw Error("React.Children.only expected to receive a single React element child.");return O}},Se.Component=E,Se.Fragment=a,Se.Profiler=s,Se.PureComponent=_,Se.StrictMode=o,Se.Suspense=v,Se.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=A,Se.act=function(){throw Error("act(...) is not supported in production builds of React.")},Se.cache=function(O){return function(){return O.apply(null,arguments)}},Se.cloneElement=function(O,P,H){if(O==null)throw Error("The argument must be a React element, but you passed "+O+".");var W=k({},O.props),L=O.key,G=void 0;if(P!=null)for(ne in P.ref!==void 0&&(G=void 0),P.key!==void 0&&(L=""+P.key),P)!B.call(P,ne)||ne==="key"||ne==="__self"||ne==="__source"||ne==="ref"&&P.ref===void 0||(W[ne]=P[ne]);var ne=arguments.length-2;if(ne===1)W.children=H;else if(1<ne){for(var ve=Array(ne),ce=0;ce<ne;ce++)ve[ce]=arguments[ce+2];W.children=ve}return q(O.type,L,void 0,void 0,G,W)},Se.createContext=function(O){return O={$$typeof:c,_currentValue:O,_currentValue2:O,_threadCount:0,Provider:null,Consumer:null},O.Provider=O,O.Consumer={$$typeof:u,_context:O},O},Se.createElement=function(O,P,H){var W,L={},G=null;if(P!=null)for(W in P.key!==void 0&&(G=""+P.key),P)B.call(P,W)&&W!=="key"&&W!=="__self"&&W!=="__source"&&(L[W]=P[W]);var ne=arguments.length-2;if(ne===1)L.children=H;else if(1<ne){for(var ve=Array(ne),ce=0;ce<ne;ce++)ve[ce]=arguments[ce+2];L.children=ve}if(O&&O.defaultProps)for(W in ne=O.defaultProps,ne)L[W]===void 0&&(L[W]=ne[W]);return q(O,G,void 0,void 0,null,L)},Se.createRef=function(){return{current:null}},Se.forwardRef=function(O){return{$$typeof:d,render:O}},Se.isValidElement=I,Se.lazy=function(O){return{$$typeof:m,_payload:{_status:-1,_result:O},_init:ie}},Se.memo=function(O,P){return{$$typeof:h,type:O,compare:P===void 0?null:P}},Se.startTransition=function(O){var P=A.T,H={};A.T=H;try{var W=O(),L=A.S;L!==null&&L(H,W),typeof W=="object"&&W!==null&&typeof W.then=="function"&&W.then(ge,se)}catch(G){se(G)}finally{A.T=P}},Se.unstable_useCacheRefresh=function(){return A.H.useCacheRefresh()},Se.use=function(O){return A.H.use(O)},Se.useActionState=function(O,P,H){return A.H.useActionState(O,P,H)},Se.useCallback=function(O,P){return A.H.useCallback(O,P)},Se.useContext=function(O){return A.H.useContext(O)},Se.useDebugValue=function(){},Se.useDeferredValue=function(O,P){return A.H.useDeferredValue(O,P)},Se.useEffect=function(O,P){return A.H.useEffect(O,P)},Se.useId=function(){return A.H.useId()},Se.useImperativeHandle=function(O,P,H){return A.H.useImperativeHandle(O,P,H)},Se.useInsertionEffect=function(O,P){return A.H.useInsertionEffect(O,P)},Se.useLayoutEffect=function(O,P){return A.H.useLayoutEffect(O,P)},Se.useMemo=function(O,P){return A.H.useMemo(O,P)},Se.useOptimistic=function(O,P){return A.H.useOptimistic(O,P)},Se.useReducer=function(O,P,H){return A.H.useReducer(O,P,H)},Se.useRef=function(O){return A.H.useRef(O)},Se.useState=function(O){return A.H.useState(O)},Se.useSyncExternalStore=function(O,P,H){return A.H.useSyncExternalStore(O,P,H)},Se.useTransition=function(){return A.H.useTransition()},Se.version="19.0.0",Se}cw.exports=tD();var te=cw.exports;const R=Ct(te);uw.exports=eD();var ue=uw.exports;const ul="big-calendar";var fw={exports:{}},nl={},jd={exports:{}},Id={},iy;function nD(){return iy||(iy=1,function(t){function r($,ie){var se=$.length;$.push(ie);e:for(;0<se;){var ge=se-1>>>1,O=$[ge];if(0<s(O,ie))$[ge]=ie,$[se]=O,se=ge;else break e}}function a($){return $.length===0?null:$[0]}function o($){if($.length===0)return null;var ie=$[0],se=$.pop();if(se!==ie){$[0]=se;e:for(var ge=0,O=$.length,P=O>>>1;ge<P;){var H=2*(ge+1)-1,W=$[H],L=H+1,G=$[L];if(0>s(W,se))L<O&&0>s(G,W)?($[ge]=G,$[L]=se,ge=L):($[ge]=W,$[H]=se,ge=H);else if(L<O&&0>s(G,se))$[ge]=G,$[L]=se,ge=L;else break e}}return ie}function s($,ie){var se=$.sortIndex-ie.sortIndex;return se!==0?se:$.id-ie.id}if(t.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var u=performance;t.unstable_now=function(){return u.now()}}else{var c=Date,d=c.now();t.unstable_now=function(){return c.now()-d}}var v=[],h=[],m=1,g=null,y=3,w=!1,k=!1,S=!1,E=typeof setTimeout=="function"?setTimeout:null,x=typeof clearTimeout=="function"?clearTimeout:null,_=typeof setImmediate<"u"?setImmediate:null;function F($){for(var ie=a(h);ie!==null;){if(ie.callback===null)o(h);else if(ie.startTime<=$)o(h),ie.sortIndex=ie.expirationTime,r(v,ie);else break;ie=a(h)}}function C($){if(S=!1,F($),!k)if(a(v)!==null)k=!0,fe();else{var ie=a(h);ie!==null&&me(C,ie.startTime-$)}}var A=!1,B=-1,q=5,Q=-1;function I(){return!(t.unstable_now()-Q<q)}function V(){if(A){var $=t.unstable_now();Q=$;var ie=!0;try{e:{k=!1,S&&(S=!1,x(B),B=-1),w=!0;var se=y;try{t:{for(F($),g=a(v);g!==null&&!(g.expirationTime>$&&I());){var ge=g.callback;if(typeof ge=="function"){g.callback=null,y=g.priorityLevel;var O=ge(g.expirationTime<=$);if($=t.unstable_now(),typeof O=="function"){g.callback=O,F($),ie=!0;break t}g===a(v)&&o(v),F($)}else o(v);g=a(v)}if(g!==null)ie=!0;else{var P=a(h);P!==null&&me(C,P.startTime-$),ie=!1}}break e}finally{g=null,y=se,w=!1}ie=void 0}}finally{ie?J():A=!1}}}var J;if(typeof _=="function")J=function(){_(V)};else if(typeof MessageChannel<"u"){var le=new MessageChannel,ae=le.port2;le.port1.onmessage=V,J=function(){ae.postMessage(null)}}else J=function(){E(V,0)};function fe(){A||(A=!0,J())}function me($,ie){B=E(function(){$(t.unstable_now())},ie)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function($){$.callback=null},t.unstable_continueExecution=function(){k||w||(k=!0,fe())},t.unstable_forceFrameRate=function($){0>$||125<$?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):q=0<$?Math.floor(1e3/$):5},t.unstable_getCurrentPriorityLevel=function(){return y},t.unstable_getFirstCallbackNode=function(){return a(v)},t.unstable_next=function($){switch(y){case 1:case 2:case 3:var ie=3;break;default:ie=y}var se=y;y=ie;try{return $()}finally{y=se}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function($,ie){switch($){case 1:case 2:case 3:case 4:case 5:break;default:$=3}var se=y;y=$;try{return ie()}finally{y=se}},t.unstable_scheduleCallback=function($,ie,se){var ge=t.unstable_now();switch(typeof se=="object"&&se!==null?(se=se.delay,se=typeof se=="number"&&0<se?ge+se:ge):se=ge,$){case 1:var O=-1;break;case 2:O=250;break;case 5:O=1073741823;break;case 4:O=1e4;break;default:O=5e3}return O=se+O,$={id:m++,callback:ie,priorityLevel:$,startTime:se,expirationTime:O,sortIndex:-1},se>ge?($.sortIndex=se,r(h,$),a(v)===null&&$===a(h)&&(S?(x(B),B=-1):S=!0,me(C,se-ge))):($.sortIndex=O,r(v,$),k||w||(k=!0,fe())),$},t.unstable_shouldYield=I,t.unstable_wrapCallback=function($){var ie=y;return function(){var se=y;y=ie;try{return $.apply(this,arguments)}finally{y=se}}}}(Id)),Id}var ly;function rD(){return ly||(ly=1,jd.exports=nD()),jd.exports}var Yd={exports:{}},yt={},sy;function aD(){if(sy)return yt;sy=1;var t=te;function r(v){var h="https://react.dev/errors/"+v;if(1<arguments.length){h+="?args[]="+encodeURIComponent(arguments[1]);for(var m=2;m<arguments.length;m++)h+="&args[]="+encodeURIComponent(arguments[m])}return"Minified React error #"+v+"; visit "+h+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function a(){}var o={d:{f:a,r:function(){throw Error(r(522))},D:a,C:a,L:a,m:a,X:a,S:a,M:a},p:0,findDOMNode:null},s=Symbol.for("react.portal");function u(v,h,m){var g=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:s,key:g==null?null:""+g,children:v,containerInfo:h,implementation:m}}var c=t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function d(v,h){if(v==="font")return"";if(typeof h=="string")return h==="use-credentials"?h:""}return yt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,yt.createPortal=function(v,h){var m=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!h||h.nodeType!==1&&h.nodeType!==9&&h.nodeType!==11)throw Error(r(299));return u(v,h,null,m)},yt.flushSync=function(v){var h=c.T,m=o.p;try{if(c.T=null,o.p=2,v)return v()}finally{c.T=h,o.p=m,o.d.f()}},yt.preconnect=function(v,h){typeof v=="string"&&(h?(h=h.crossOrigin,h=typeof h=="string"?h==="use-credentials"?h:"":void 0):h=null,o.d.C(v,h))},yt.prefetchDNS=function(v){typeof v=="string"&&o.d.D(v)},yt.preinit=function(v,h){if(typeof v=="string"&&h&&typeof h.as=="string"){var m=h.as,g=d(m,h.crossOrigin),y=typeof h.integrity=="string"?h.integrity:void 0,w=typeof h.fetchPriority=="string"?h.fetchPriority:void 0;m==="style"?o.d.S(v,typeof h.precedence=="string"?h.precedence:void 0,{crossOrigin:g,integrity:y,fetchPriority:w}):m==="script"&&o.d.X(v,{crossOrigin:g,integrity:y,fetchPriority:w,nonce:typeof h.nonce=="string"?h.nonce:void 0})}},yt.preinitModule=function(v,h){if(typeof v=="string")if(typeof h=="object"&&h!==null){if(h.as==null||h.as==="script"){var m=d(h.as,h.crossOrigin);o.d.M(v,{crossOrigin:m,integrity:typeof h.integrity=="string"?h.integrity:void 0,nonce:typeof h.nonce=="string"?h.nonce:void 0})}}else h==null&&o.d.M(v)},yt.preload=function(v,h){if(typeof v=="string"&&typeof h=="object"&&h!==null&&typeof h.as=="string"){var m=h.as,g=d(m,h.crossOrigin);o.d.L(v,m,{crossOrigin:g,integrity:typeof h.integrity=="string"?h.integrity:void 0,nonce:typeof h.nonce=="string"?h.nonce:void 0,type:typeof h.type=="string"?h.type:void 0,fetchPriority:typeof h.fetchPriority=="string"?h.fetchPriority:void 0,referrerPolicy:typeof h.referrerPolicy=="string"?h.referrerPolicy:void 0,imageSrcSet:typeof h.imageSrcSet=="string"?h.imageSrcSet:void 0,imageSizes:typeof h.imageSizes=="string"?h.imageSizes:void 0,media:typeof h.media=="string"?h.media:void 0})}},yt.preloadModule=function(v,h){if(typeof v=="string")if(h){var m=d(h.as,h.crossOrigin);o.d.m(v,{as:typeof h.as=="string"&&h.as!=="script"?h.as:void 0,crossOrigin:m,integrity:typeof h.integrity=="string"?h.integrity:void 0})}else o.d.m(v)},yt.requestFormReset=function(v){o.d.r(v)},yt.unstable_batchedUpdates=function(v,h){return v(h)},yt.useFormState=function(v,h,m){return c.H.useFormState(v,h,m)},yt.useFormStatus=function(){return c.H.useHostTransitionStatus()},yt.version="19.0.0",yt}var uy;function dw(){if(uy)return Yd.exports;uy=1;function t(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(t)}catch(r){console.error(r)}}return t(),Yd.exports=aD(),Yd.exports}var cy;function oD(){if(cy)return nl;cy=1;var t=rD(),r=te,a=dw();function o(e){var n="https://react.dev/errors/"+e;if(1<arguments.length){n+="?args[]="+encodeURIComponent(arguments[1]);for(var i=2;i<arguments.length;i++)n+="&args[]="+encodeURIComponent(arguments[i])}return"Minified React error #"+e+"; visit "+n+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}var u=Symbol.for("react.element"),c=Symbol.for("react.transitional.element"),d=Symbol.for("react.portal"),v=Symbol.for("react.fragment"),h=Symbol.for("react.strict_mode"),m=Symbol.for("react.profiler"),g=Symbol.for("react.provider"),y=Symbol.for("react.consumer"),w=Symbol.for("react.context"),k=Symbol.for("react.forward_ref"),S=Symbol.for("react.suspense"),E=Symbol.for("react.suspense_list"),x=Symbol.for("react.memo"),_=Symbol.for("react.lazy"),F=Symbol.for("react.offscreen"),C=Symbol.for("react.memo_cache_sentinel"),A=Symbol.iterator;function B(e){return e===null||typeof e!="object"?null:(e=A&&e[A]||e["@@iterator"],typeof e=="function"?e:null)}var q=Symbol.for("react.client.reference");function Q(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===q?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case v:return"Fragment";case d:return"Portal";case m:return"Profiler";case h:return"StrictMode";case S:return"Suspense";case E:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case w:return(e.displayName||"Context")+".Provider";case y:return(e._context.displayName||"Context")+".Consumer";case k:var n=e.render;return e=e.displayName,e||(e=n.displayName||n.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case x:return n=e.displayName||null,n!==null?n:Q(e.type)||"Memo";case _:n=e._payload,e=e._init;try{return Q(e(n))}catch{}}return null}var I=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,V=Object.assign,J,le;function ae(e){if(J===void 0)try{throw Error()}catch(i){var n=i.stack.trim().match(/\n( *(at )?)/);J=n&&n[1]||"",le=-1<i.stack.indexOf(`
    at`)?" (<anonymous>)":-1<i.stack.indexOf("@")?"@unknown:0:0":""}return`
`+J+e+le}var fe=!1;function me(e,n){if(!e||fe)return"";fe=!0;var i=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(n){var Z=function(){throw Error()};if(Object.defineProperty(Z.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(Z,[])}catch(U){var Y=U}Reflect.construct(e,[],Z)}else{try{Z.call()}catch(U){Y=U}e.call(Z.prototype)}}else{try{throw Error()}catch(U){Y=U}(Z=e())&&typeof Z.catch=="function"&&Z.catch(function(){})}}catch(U){if(U&&Y&&typeof U.stack=="string")return[U.stack,Y.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var f=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");f&&f.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var p=l.DetermineComponentFrameRoot(),b=p[0],D=p[1];if(b&&D){var T=b.split(`
`),N=D.split(`
`);for(f=l=0;l<T.length&&!T[l].includes("DetermineComponentFrameRoot");)l++;for(;f<N.length&&!N[f].includes("DetermineComponentFrameRoot");)f++;if(l===T.length||f===N.length)for(l=T.length-1,f=N.length-1;1<=l&&0<=f&&T[l]!==N[f];)f--;for(;1<=l&&0<=f;l--,f--)if(T[l]!==N[f]){if(l!==1||f!==1)do if(l--,f--,0>f||T[l]!==N[f]){var K=`
`+T[l].replace(" at new "," at ");return e.displayName&&K.includes("<anonymous>")&&(K=K.replace("<anonymous>",e.displayName)),K}while(1<=l&&0<=f);break}}}finally{fe=!1,Error.prepareStackTrace=i}return(i=e?e.displayName||e.name:"")?ae(i):""}function $(e){switch(e.tag){case 26:case 27:case 5:return ae(e.type);case 16:return ae("Lazy");case 13:return ae("Suspense");case 19:return ae("SuspenseList");case 0:case 15:return e=me(e.type,!1),e;case 11:return e=me(e.type.render,!1),e;case 1:return e=me(e.type,!0),e;default:return""}}function ie(e){try{var n="";do n+=$(e),e=e.return;while(e);return n}catch(i){return`
Error generating stack: `+i.message+`
`+i.stack}}function se(e){var n=e,i=e;if(e.alternate)for(;n.return;)n=n.return;else{e=n;do n=e,n.flags&4098&&(i=n.return),e=n.return;while(e)}return n.tag===3?i:null}function ge(e){if(e.tag===13){var n=e.memoizedState;if(n===null&&(e=e.alternate,e!==null&&(n=e.memoizedState)),n!==null)return n.dehydrated}return null}function O(e){if(se(e)!==e)throw Error(o(188))}function P(e){var n=e.alternate;if(!n){if(n=se(e),n===null)throw Error(o(188));return n!==e?null:e}for(var i=e,l=n;;){var f=i.return;if(f===null)break;var p=f.alternate;if(p===null){if(l=f.return,l!==null){i=l;continue}break}if(f.child===p.child){for(p=f.child;p;){if(p===i)return O(f),e;if(p===l)return O(f),n;p=p.sibling}throw Error(o(188))}if(i.return!==l.return)i=f,l=p;else{for(var b=!1,D=f.child;D;){if(D===i){b=!0,i=f,l=p;break}if(D===l){b=!0,l=f,i=p;break}D=D.sibling}if(!b){for(D=p.child;D;){if(D===i){b=!0,i=p,l=f;break}if(D===l){b=!0,l=p,i=f;break}D=D.sibling}if(!b)throw Error(o(189))}}if(i.alternate!==l)throw Error(o(190))}if(i.tag!==3)throw Error(o(188));return i.stateNode.current===i?e:n}function H(e){var n=e.tag;if(n===5||n===26||n===27||n===6)return e;for(e=e.child;e!==null;){if(n=H(e),n!==null)return n;e=e.sibling}return null}var W=Array.isArray,L=a.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,G={pending:!1,data:null,method:null,action:null},ne=[],ve=-1;function ce(e){return{current:e}}function Oe(e){0>ve||(e.current=ne[ve],ne[ve]=null,ve--)}function Me(e,n){ve++,ne[ve]=e.current,e.current=n}var ot=ce(null),cn=ce(null),jt=ce(null),dr=ce(null);function Ba(e,n){switch(Me(jt,n),Me(cn,e),Me(ot,null),e=n.nodeType,e){case 9:case 11:n=(n=n.documentElement)&&(n=n.namespaceURI)?Ag(n):0;break;default:if(e=e===8?n.parentNode:n,n=e.tagName,e=e.namespaceURI)e=Ag(e),n=Rg(e,n);else switch(n){case"svg":n=1;break;case"math":n=2;break;default:n=0}}Oe(ot),Me(ot,n)}function jn(){Oe(ot),Oe(cn),Oe(jt)}function Wa(e){e.memoizedState!==null&&Me(dr,e);var n=ot.current,i=Rg(n,e.type);n!==i&&(Me(cn,e),Me(ot,i))}function Va(e){cn.current===e&&(Oe(ot),Oe(cn)),dr.current===e&&(Oe(dr),Xi._currentValue=G)}var ni=Object.prototype.hasOwnProperty,fn=t.unstable_scheduleCallback,vr=t.unstable_cancelCallback,ts=t.unstable_shouldYield,ns=t.unstable_requestPaint,Pt=t.unstable_now,rs=t.unstable_getCurrentPriorityLevel,ri=t.unstable_ImmediatePriority,ai=t.unstable_UserBlockingPriority,ta=t.unstable_NormalPriority,Ak=t.unstable_LowPriority,Dp=t.unstable_IdlePriority,Rk=t.log,Fk=t.unstable_setDisableYieldValue,oi=null,It=null;function zk(e){if(It&&typeof It.onCommitFiberRoot=="function")try{It.onCommitFiberRoot(oi,e,void 0,(e.current.flags&128)===128)}catch{}}function pr(e){if(typeof Rk=="function"&&Fk(e),It&&typeof It.setStrictMode=="function")try{It.setStrictMode(oi,e)}catch{}}var Yt=Math.clz32?Math.clz32:Ik,Lk=Math.log,jk=Math.LN2;function Ik(e){return e>>>=0,e===0?32:31-(Lk(e)/jk|0)|0}var as=128,os=4194304;function na(e){var n=e&42;if(n!==0)return n;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194176;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function is(e,n){var i=e.pendingLanes;if(i===0)return 0;var l=0,f=e.suspendedLanes,p=e.pingedLanes,b=e.warmLanes;e=e.finishedLanes!==0;var D=i&134217727;return D!==0?(i=D&~f,i!==0?l=na(i):(p&=D,p!==0?l=na(p):e||(b=D&~b,b!==0&&(l=na(b))))):(D=i&~f,D!==0?l=na(D):p!==0?l=na(p):e||(b=i&~b,b!==0&&(l=na(b)))),l===0?0:n!==0&&n!==l&&!(n&f)&&(f=l&-l,b=n&-n,f>=b||f===32&&(b&4194176)!==0)?n:l}function ii(e,n){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&n)===0}function Yk(e,n){switch(e){case 1:case 2:case 4:case 8:return n+250;case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return n+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function xp(){var e=as;return as<<=1,!(as&4194176)&&(as=128),e}function Tp(){var e=os;return os<<=1,!(os&62914560)&&(os=4194304),e}function Nc(e){for(var n=[],i=0;31>i;i++)n.push(e);return n}function li(e,n){e.pendingLanes|=n,n!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Hk(e,n,i,l,f,p){var b=e.pendingLanes;e.pendingLanes=i,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=i,e.entangledLanes&=i,e.errorRecoveryDisabledLanes&=i,e.shellSuspendCounter=0;var D=e.entanglements,T=e.expirationTimes,N=e.hiddenUpdates;for(i=b&~i;0<i;){var K=31-Yt(i),Z=1<<K;D[K]=0,T[K]=-1;var Y=N[K];if(Y!==null)for(N[K]=null,K=0;K<Y.length;K++){var U=Y[K];U!==null&&(U.lane&=-536870913)}i&=~Z}l!==0&&_p(e,l,0),p!==0&&f===0&&e.tag!==0&&(e.suspendedLanes|=p&~(b&~n))}function _p(e,n,i){e.pendingLanes|=n,e.suspendedLanes&=~n;var l=31-Yt(n);e.entangledLanes|=n,e.entanglements[l]=e.entanglements[l]|1073741824|i&4194218}function Op(e,n){var i=e.entangledLanes|=n;for(e=e.entanglements;i;){var l=31-Yt(i),f=1<<l;f&n|e[l]&n&&(e[l]|=n),i&=~f}}function Cp(e){return e&=-e,2<e?8<e?e&134217727?32:268435456:8:2}function Pp(){var e=L.p;return e!==0?e:(e=window.event,e===void 0?32:Jg(e.type))}function Bk(e,n){var i=L.p;try{return L.p=e,n()}finally{L.p=i}}var hr=Math.random().toString(36).slice(2),mt="__reactFiber$"+hr,Mt="__reactProps$"+hr,Ua="__reactContainer$"+hr,Ac="__reactEvents$"+hr,Wk="__reactListeners$"+hr,Vk="__reactHandles$"+hr,Mp="__reactResources$"+hr,si="__reactMarker$"+hr;function Rc(e){delete e[mt],delete e[Mt],delete e[Ac],delete e[Wk],delete e[Vk]}function ra(e){var n=e[mt];if(n)return n;for(var i=e.parentNode;i;){if(n=i[Ua]||i[mt]){if(i=n.alternate,n.child!==null||i!==null&&i.child!==null)for(e=Lg(e);e!==null;){if(i=e[mt])return i;e=Lg(e)}return n}e=i,i=e.parentNode}return null}function $a(e){if(e=e[mt]||e[Ua]){var n=e.tag;if(n===5||n===6||n===13||n===26||n===27||n===3)return e}return null}function ui(e){var n=e.tag;if(n===5||n===26||n===27||n===6)return e.stateNode;throw Error(o(33))}function Ka(e){var n=e[Mp];return n||(n=e[Mp]={hoistableStyles:new Map,hoistableScripts:new Map}),n}function it(e){e[si]=!0}var Np=new Set,Ap={};function aa(e,n){qa(e,n),qa(e+"Capture",n)}function qa(e,n){for(Ap[e]=n,e=0;e<n.length;e++)Np.add(n[e])}var In=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Uk=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Rp={},Fp={};function $k(e){return ni.call(Fp,e)?!0:ni.call(Rp,e)?!1:Uk.test(e)?Fp[e]=!0:(Rp[e]=!0,!1)}function ls(e,n,i){if($k(n))if(i===null)e.removeAttribute(n);else{switch(typeof i){case"undefined":case"function":case"symbol":e.removeAttribute(n);return;case"boolean":var l=n.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){e.removeAttribute(n);return}}e.setAttribute(n,""+i)}}function ss(e,n,i){if(i===null)e.removeAttribute(n);else{switch(typeof i){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttribute(n,""+i)}}function Yn(e,n,i,l){if(l===null)e.removeAttribute(i);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(i);return}e.setAttributeNS(n,i,""+l)}}function Kt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function zp(e){var n=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(n==="checkbox"||n==="radio")}function Kk(e){var n=zp(e)?"checked":"value",i=Object.getOwnPropertyDescriptor(e.constructor.prototype,n),l=""+e[n];if(!e.hasOwnProperty(n)&&typeof i<"u"&&typeof i.get=="function"&&typeof i.set=="function"){var f=i.get,p=i.set;return Object.defineProperty(e,n,{configurable:!0,get:function(){return f.call(this)},set:function(b){l=""+b,p.call(this,b)}}),Object.defineProperty(e,n,{enumerable:i.enumerable}),{getValue:function(){return l},setValue:function(b){l=""+b},stopTracking:function(){e._valueTracker=null,delete e[n]}}}}function us(e){e._valueTracker||(e._valueTracker=Kk(e))}function Lp(e){if(!e)return!1;var n=e._valueTracker;if(!n)return!0;var i=n.getValue(),l="";return e&&(l=zp(e)?e.checked?"true":"false":e.value),e=l,e!==i?(n.setValue(e),!0):!1}function cs(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var qk=/[\n"\\]/g;function qt(e){return e.replace(qk,function(n){return"\\"+n.charCodeAt(0).toString(16)+" "})}function Fc(e,n,i,l,f,p,b,D){e.name="",b!=null&&typeof b!="function"&&typeof b!="symbol"&&typeof b!="boolean"?e.type=b:e.removeAttribute("type"),n!=null?b==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+Kt(n)):e.value!==""+Kt(n)&&(e.value=""+Kt(n)):b!=="submit"&&b!=="reset"||e.removeAttribute("value"),n!=null?zc(e,b,Kt(n)):i!=null?zc(e,b,Kt(i)):l!=null&&e.removeAttribute("value"),f==null&&p!=null&&(e.defaultChecked=!!p),f!=null&&(e.checked=f&&typeof f!="function"&&typeof f!="symbol"),D!=null&&typeof D!="function"&&typeof D!="symbol"&&typeof D!="boolean"?e.name=""+Kt(D):e.removeAttribute("name")}function jp(e,n,i,l,f,p,b,D){if(p!=null&&typeof p!="function"&&typeof p!="symbol"&&typeof p!="boolean"&&(e.type=p),n!=null||i!=null){if(!(p!=="submit"&&p!=="reset"||n!=null))return;i=i!=null?""+Kt(i):"",n=n!=null?""+Kt(n):i,D||n===e.value||(e.value=n),e.defaultValue=n}l=l??f,l=typeof l!="function"&&typeof l!="symbol"&&!!l,e.checked=D?e.checked:!!l,e.defaultChecked=!!l,b!=null&&typeof b!="function"&&typeof b!="symbol"&&typeof b!="boolean"&&(e.name=b)}function zc(e,n,i){n==="number"&&cs(e.ownerDocument)===e||e.defaultValue===""+i||(e.defaultValue=""+i)}function Qa(e,n,i,l){if(e=e.options,n){n={};for(var f=0;f<i.length;f++)n["$"+i[f]]=!0;for(i=0;i<e.length;i++)f=n.hasOwnProperty("$"+e[i].value),e[i].selected!==f&&(e[i].selected=f),f&&l&&(e[i].defaultSelected=!0)}else{for(i=""+Kt(i),n=null,f=0;f<e.length;f++){if(e[f].value===i){e[f].selected=!0,l&&(e[f].defaultSelected=!0);return}n!==null||e[f].disabled||(n=e[f])}n!==null&&(n.selected=!0)}}function Ip(e,n,i){if(n!=null&&(n=""+Kt(n),n!==e.value&&(e.value=n),i==null)){e.defaultValue!==n&&(e.defaultValue=n);return}e.defaultValue=i!=null?""+Kt(i):""}function Yp(e,n,i,l){if(n==null){if(l!=null){if(i!=null)throw Error(o(92));if(W(l)){if(1<l.length)throw Error(o(93));l=l[0]}i=l}i==null&&(i=""),n=i}i=Kt(n),e.defaultValue=i,l=e.textContent,l===i&&l!==""&&l!==null&&(e.value=l)}function Xa(e,n){if(n){var i=e.firstChild;if(i&&i===e.lastChild&&i.nodeType===3){i.nodeValue=n;return}}e.textContent=n}var Qk=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Hp(e,n,i){var l=n.indexOf("--")===0;i==null||typeof i=="boolean"||i===""?l?e.setProperty(n,""):n==="float"?e.cssFloat="":e[n]="":l?e.setProperty(n,i):typeof i!="number"||i===0||Qk.has(n)?n==="float"?e.cssFloat=i:e[n]=(""+i).trim():e[n]=i+"px"}function Bp(e,n,i){if(n!=null&&typeof n!="object")throw Error(o(62));if(e=e.style,i!=null){for(var l in i)!i.hasOwnProperty(l)||n!=null&&n.hasOwnProperty(l)||(l.indexOf("--")===0?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="");for(var f in n)l=n[f],n.hasOwnProperty(f)&&i[f]!==l&&Hp(e,f,l)}else for(var p in n)n.hasOwnProperty(p)&&Hp(e,p,n[p])}function Lc(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Xk=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Gk=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function fs(e){return Gk.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var jc=null;function Ic(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ga=null,Ja=null;function Wp(e){var n=$a(e);if(n&&(e=n.stateNode)){var i=e[Mt]||null;e:switch(e=n.stateNode,n.type){case"input":if(Fc(e,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name),n=i.name,i.type==="radio"&&n!=null){for(i=e;i.parentNode;)i=i.parentNode;for(i=i.querySelectorAll('input[name="'+qt(""+n)+'"][type="radio"]'),n=0;n<i.length;n++){var l=i[n];if(l!==e&&l.form===e.form){var f=l[Mt]||null;if(!f)throw Error(o(90));Fc(l,f.value,f.defaultValue,f.defaultValue,f.checked,f.defaultChecked,f.type,f.name)}}for(n=0;n<i.length;n++)l=i[n],l.form===e.form&&Lp(l)}break e;case"textarea":Ip(e,i.value,i.defaultValue);break e;case"select":n=i.value,n!=null&&Qa(e,!!i.multiple,n,!1)}}}var Yc=!1;function Vp(e,n,i){if(Yc)return e(n,i);Yc=!0;try{var l=e(n);return l}finally{if(Yc=!1,(Ga!==null||Ja!==null)&&(qs(),Ga&&(n=Ga,e=Ja,Ja=Ga=null,Wp(n),e)))for(n=0;n<e.length;n++)Wp(e[n])}}function ci(e,n){var i=e.stateNode;if(i===null)return null;var l=i[Mt]||null;if(l===null)return null;i=l[n];e:switch(n){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(i&&typeof i!="function")throw Error(o(231,n,typeof i));return i}var Hc=!1;if(In)try{var fi={};Object.defineProperty(fi,"passive",{get:function(){Hc=!0}}),window.addEventListener("test",fi,fi),window.removeEventListener("test",fi,fi)}catch{Hc=!1}var mr=null,Bc=null,ds=null;function Up(){if(ds)return ds;var e,n=Bc,i=n.length,l,f="value"in mr?mr.value:mr.textContent,p=f.length;for(e=0;e<i&&n[e]===f[e];e++);var b=i-e;for(l=1;l<=b&&n[i-l]===f[p-l];l++);return ds=f.slice(e,1<l?1-l:void 0)}function vs(e){var n=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&n===13&&(e=13)):e=n,e===10&&(e=13),32<=e||e===13?e:0}function ps(){return!0}function $p(){return!1}function Nt(e){function n(i,l,f,p,b){this._reactName=i,this._targetInst=f,this.type=l,this.nativeEvent=p,this.target=b,this.currentTarget=null;for(var D in e)e.hasOwnProperty(D)&&(i=e[D],this[D]=i?i(p):p[D]);return this.isDefaultPrevented=(p.defaultPrevented!=null?p.defaultPrevented:p.returnValue===!1)?ps:$p,this.isPropagationStopped=$p,this}return V(n.prototype,{preventDefault:function(){this.defaultPrevented=!0;var i=this.nativeEvent;i&&(i.preventDefault?i.preventDefault():typeof i.returnValue!="unknown"&&(i.returnValue=!1),this.isDefaultPrevented=ps)},stopPropagation:function(){var i=this.nativeEvent;i&&(i.stopPropagation?i.stopPropagation():typeof i.cancelBubble!="unknown"&&(i.cancelBubble=!0),this.isPropagationStopped=ps)},persist:function(){},isPersistent:ps}),n}var oa={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},hs=Nt(oa),di=V({},oa,{view:0,detail:0}),Jk=Nt(di),Wc,Vc,vi,ms=V({},di,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:$c,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==vi&&(vi&&e.type==="mousemove"?(Wc=e.screenX-vi.screenX,Vc=e.screenY-vi.screenY):Vc=Wc=0,vi=e),Wc)},movementY:function(e){return"movementY"in e?e.movementY:Vc}}),Kp=Nt(ms),Zk=V({},ms,{dataTransfer:0}),eE=Nt(Zk),tE=V({},di,{relatedTarget:0}),Uc=Nt(tE),nE=V({},oa,{animationName:0,elapsedTime:0,pseudoElement:0}),rE=Nt(nE),aE=V({},oa,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),oE=Nt(aE),iE=V({},oa,{data:0}),qp=Nt(iE),lE={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},sE={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},uE={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function cE(e){var n=this.nativeEvent;return n.getModifierState?n.getModifierState(e):(e=uE[e])?!!n[e]:!1}function $c(){return cE}var fE=V({},di,{key:function(e){if(e.key){var n=lE[e.key]||e.key;if(n!=="Unidentified")return n}return e.type==="keypress"?(e=vs(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?sE[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:$c,charCode:function(e){return e.type==="keypress"?vs(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?vs(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),dE=Nt(fE),vE=V({},ms,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Qp=Nt(vE),pE=V({},di,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:$c}),hE=Nt(pE),mE=V({},oa,{propertyName:0,elapsedTime:0,pseudoElement:0}),gE=Nt(mE),yE=V({},ms,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),bE=Nt(yE),wE=V({},oa,{newState:0,oldState:0}),SE=Nt(wE),kE=[9,13,27,32],Kc=In&&"CompositionEvent"in window,pi=null;In&&"documentMode"in document&&(pi=document.documentMode);var EE=In&&"TextEvent"in window&&!pi,Xp=In&&(!Kc||pi&&8<pi&&11>=pi),Gp=" ",Jp=!1;function Zp(e,n){switch(e){case"keyup":return kE.indexOf(n.keyCode)!==-1;case"keydown":return n.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function eh(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Za=!1;function DE(e,n){switch(e){case"compositionend":return eh(n);case"keypress":return n.which!==32?null:(Jp=!0,Gp);case"textInput":return e=n.data,e===Gp&&Jp?null:e;default:return null}}function xE(e,n){if(Za)return e==="compositionend"||!Kc&&Zp(e,n)?(e=Up(),ds=Bc=mr=null,Za=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(n.ctrlKey||n.altKey||n.metaKey)||n.ctrlKey&&n.altKey){if(n.char&&1<n.char.length)return n.char;if(n.which)return String.fromCharCode(n.which)}return null;case"compositionend":return Xp&&n.locale!=="ko"?null:n.data;default:return null}}var TE={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function th(e){var n=e&&e.nodeName&&e.nodeName.toLowerCase();return n==="input"?!!TE[e.type]:n==="textarea"}function nh(e,n,i,l){Ga?Ja?Ja.push(l):Ja=[l]:Ga=l,n=Zs(n,"onChange"),0<n.length&&(i=new hs("onChange","change",null,i,l),e.push({event:i,listeners:n}))}var hi=null,mi=null;function _E(e){Og(e,0)}function gs(e){var n=ui(e);if(Lp(n))return e}function rh(e,n){if(e==="change")return n}var ah=!1;if(In){var qc;if(In){var Qc="oninput"in document;if(!Qc){var oh=document.createElement("div");oh.setAttribute("oninput","return;"),Qc=typeof oh.oninput=="function"}qc=Qc}else qc=!1;ah=qc&&(!document.documentMode||9<document.documentMode)}function ih(){hi&&(hi.detachEvent("onpropertychange",lh),mi=hi=null)}function lh(e){if(e.propertyName==="value"&&gs(mi)){var n=[];nh(n,mi,e,Ic(e)),Vp(_E,n)}}function OE(e,n,i){e==="focusin"?(ih(),hi=n,mi=i,hi.attachEvent("onpropertychange",lh)):e==="focusout"&&ih()}function CE(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return gs(mi)}function PE(e,n){if(e==="click")return gs(n)}function ME(e,n){if(e==="input"||e==="change")return gs(n)}function NE(e,n){return e===n&&(e!==0||1/e===1/n)||e!==e&&n!==n}var Ht=typeof Object.is=="function"?Object.is:NE;function gi(e,n){if(Ht(e,n))return!0;if(typeof e!="object"||e===null||typeof n!="object"||n===null)return!1;var i=Object.keys(e),l=Object.keys(n);if(i.length!==l.length)return!1;for(l=0;l<i.length;l++){var f=i[l];if(!ni.call(n,f)||!Ht(e[f],n[f]))return!1}return!0}function sh(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function uh(e,n){var i=sh(e);e=0;for(var l;i;){if(i.nodeType===3){if(l=e+i.textContent.length,e<=n&&l>=n)return{node:i,offset:n-e};e=l}e:{for(;i;){if(i.nextSibling){i=i.nextSibling;break e}i=i.parentNode}i=void 0}i=sh(i)}}function ch(e,n){return e&&n?e===n?!0:e&&e.nodeType===3?!1:n&&n.nodeType===3?ch(e,n.parentNode):"contains"in e?e.contains(n):e.compareDocumentPosition?!!(e.compareDocumentPosition(n)&16):!1:!1}function fh(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var n=cs(e.document);n instanceof e.HTMLIFrameElement;){try{var i=typeof n.contentWindow.location.href=="string"}catch{i=!1}if(i)e=n.contentWindow;else break;n=cs(e.document)}return n}function Xc(e){var n=e&&e.nodeName&&e.nodeName.toLowerCase();return n&&(n==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||n==="textarea"||e.contentEditable==="true")}function AE(e,n){var i=fh(n);n=e.focusedElem;var l=e.selectionRange;if(i!==n&&n&&n.ownerDocument&&ch(n.ownerDocument.documentElement,n)){if(l!==null&&Xc(n)){if(e=l.start,i=l.end,i===void 0&&(i=e),"selectionStart"in n)n.selectionStart=e,n.selectionEnd=Math.min(i,n.value.length);else if(i=(e=n.ownerDocument||document)&&e.defaultView||window,i.getSelection){i=i.getSelection();var f=n.textContent.length,p=Math.min(l.start,f);l=l.end===void 0?p:Math.min(l.end,f),!i.extend&&p>l&&(f=l,l=p,p=f),f=uh(n,p);var b=uh(n,l);f&&b&&(i.rangeCount!==1||i.anchorNode!==f.node||i.anchorOffset!==f.offset||i.focusNode!==b.node||i.focusOffset!==b.offset)&&(e=e.createRange(),e.setStart(f.node,f.offset),i.removeAllRanges(),p>l?(i.addRange(e),i.extend(b.node,b.offset)):(e.setEnd(b.node,b.offset),i.addRange(e)))}}for(e=[],i=n;i=i.parentNode;)i.nodeType===1&&e.push({element:i,left:i.scrollLeft,top:i.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<e.length;n++)i=e[n],i.element.scrollLeft=i.left,i.element.scrollTop=i.top}}var RE=In&&"documentMode"in document&&11>=document.documentMode,eo=null,Gc=null,yi=null,Jc=!1;function dh(e,n,i){var l=i.window===i?i.document:i.nodeType===9?i:i.ownerDocument;Jc||eo==null||eo!==cs(l)||(l=eo,"selectionStart"in l&&Xc(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),yi&&gi(yi,l)||(yi=l,l=Zs(Gc,"onSelect"),0<l.length&&(n=new hs("onSelect","select",null,n,i),e.push({event:n,listeners:l}),n.target=eo)))}function ia(e,n){var i={};return i[e.toLowerCase()]=n.toLowerCase(),i["Webkit"+e]="webkit"+n,i["Moz"+e]="moz"+n,i}var to={animationend:ia("Animation","AnimationEnd"),animationiteration:ia("Animation","AnimationIteration"),animationstart:ia("Animation","AnimationStart"),transitionrun:ia("Transition","TransitionRun"),transitionstart:ia("Transition","TransitionStart"),transitioncancel:ia("Transition","TransitionCancel"),transitionend:ia("Transition","TransitionEnd")},Zc={},vh={};In&&(vh=document.createElement("div").style,"AnimationEvent"in window||(delete to.animationend.animation,delete to.animationiteration.animation,delete to.animationstart.animation),"TransitionEvent"in window||delete to.transitionend.transition);function la(e){if(Zc[e])return Zc[e];if(!to[e])return e;var n=to[e],i;for(i in n)if(n.hasOwnProperty(i)&&i in vh)return Zc[e]=n[i];return e}var ph=la("animationend"),hh=la("animationiteration"),mh=la("animationstart"),FE=la("transitionrun"),zE=la("transitionstart"),LE=la("transitioncancel"),gh=la("transitionend"),yh=new Map,bh="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll scrollEnd toggle touchMove waiting wheel".split(" ");function dn(e,n){yh.set(e,n),aa(n,[e])}var Qt=[],no=0,ef=0;function ys(){for(var e=no,n=ef=no=0;n<e;){var i=Qt[n];Qt[n++]=null;var l=Qt[n];Qt[n++]=null;var f=Qt[n];Qt[n++]=null;var p=Qt[n];if(Qt[n++]=null,l!==null&&f!==null){var b=l.pending;b===null?f.next=f:(f.next=b.next,b.next=f),l.pending=f}p!==0&&wh(i,f,p)}}function bs(e,n,i,l){Qt[no++]=e,Qt[no++]=n,Qt[no++]=i,Qt[no++]=l,ef|=l,e.lanes|=l,e=e.alternate,e!==null&&(e.lanes|=l)}function tf(e,n,i,l){return bs(e,n,i,l),ws(e)}function gr(e,n){return bs(e,null,null,n),ws(e)}function wh(e,n,i){e.lanes|=i;var l=e.alternate;l!==null&&(l.lanes|=i);for(var f=!1,p=e.return;p!==null;)p.childLanes|=i,l=p.alternate,l!==null&&(l.childLanes|=i),p.tag===22&&(e=p.stateNode,e===null||e._visibility&1||(f=!0)),e=p,p=p.return;f&&n!==null&&e.tag===3&&(p=e.stateNode,f=31-Yt(i),p=p.hiddenUpdates,e=p[f],e===null?p[f]=[n]:e.push(n),n.lane=i|536870912)}function ws(e){if(50<Wi)throw Wi=0,sd=null,Error(o(185));for(var n=e.return;n!==null;)e=n,n=e.return;return e.tag===3?e.stateNode:null}var ro={},Sh=new WeakMap;function Xt(e,n){if(typeof e=="object"&&e!==null){var i=Sh.get(e);return i!==void 0?i:(n={value:e,source:n,stack:ie(n)},Sh.set(e,n),n)}return{value:e,source:n,stack:ie(n)}}var ao=[],oo=0,Ss=null,ks=0,Gt=[],Jt=0,sa=null,Hn=1,Bn="";function ua(e,n){ao[oo++]=ks,ao[oo++]=Ss,Ss=e,ks=n}function kh(e,n,i){Gt[Jt++]=Hn,Gt[Jt++]=Bn,Gt[Jt++]=sa,sa=e;var l=Hn;e=Bn;var f=32-Yt(l)-1;l&=~(1<<f),i+=1;var p=32-Yt(n)+f;if(30<p){var b=f-f%5;p=(l&(1<<b)-1).toString(32),l>>=b,f-=b,Hn=1<<32-Yt(n)+f|i<<f|l,Bn=p+e}else Hn=1<<p|i<<f|l,Bn=e}function nf(e){e.return!==null&&(ua(e,1),kh(e,1,0))}function rf(e){for(;e===Ss;)Ss=ao[--oo],ao[oo]=null,ks=ao[--oo],ao[oo]=null;for(;e===sa;)sa=Gt[--Jt],Gt[Jt]=null,Bn=Gt[--Jt],Gt[Jt]=null,Hn=Gt[--Jt],Gt[Jt]=null}var Tt=null,ct=null,Ce=!1,vn=null,En=!1,af=Error(o(519));function ca(e){var n=Error(o(418,""));throw Si(Xt(n,e)),af}function Eh(e){var n=e.stateNode,i=e.type,l=e.memoizedProps;switch(n[mt]=e,n[Mt]=l,i){case"dialog":Te("cancel",n),Te("close",n);break;case"iframe":case"object":case"embed":Te("load",n);break;case"video":case"audio":for(i=0;i<Ui.length;i++)Te(Ui[i],n);break;case"source":Te("error",n);break;case"img":case"image":case"link":Te("error",n),Te("load",n);break;case"details":Te("toggle",n);break;case"input":Te("invalid",n),jp(n,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),us(n);break;case"select":Te("invalid",n);break;case"textarea":Te("invalid",n),Yp(n,l.value,l.defaultValue,l.children),us(n)}i=l.children,typeof i!="string"&&typeof i!="number"&&typeof i!="bigint"||n.textContent===""+i||l.suppressHydrationWarning===!0||Ng(n.textContent,i)?(l.popover!=null&&(Te("beforetoggle",n),Te("toggle",n)),l.onScroll!=null&&Te("scroll",n),l.onScrollEnd!=null&&Te("scrollend",n),l.onClick!=null&&(n.onclick=eu),n=!0):n=!1,n||ca(e)}function Dh(e){for(Tt=e.return;Tt;)switch(Tt.tag){case 3:case 27:En=!0;return;case 5:case 13:En=!1;return;default:Tt=Tt.return}}function bi(e){if(e!==Tt)return!1;if(!Ce)return Dh(e),Ce=!0,!1;var n=!1,i;if((i=e.tag!==3&&e.tag!==27)&&((i=e.tag===5)&&(i=e.type,i=!(i!=="form"&&i!=="button")||xd(e.type,e.memoizedProps)),i=!i),i&&(n=!0),n&&ct&&ca(e),Dh(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(o(317));e:{for(e=e.nextSibling,n=0;e;){if(e.nodeType===8)if(i=e.data,i==="/$"){if(n===0){ct=hn(e.nextSibling);break e}n--}else i!=="$"&&i!=="$!"&&i!=="$?"||n++;e=e.nextSibling}ct=null}}else ct=Tt?hn(e.stateNode.nextSibling):null;return!0}function wi(){ct=Tt=null,Ce=!1}function Si(e){vn===null?vn=[e]:vn.push(e)}var ki=Error(o(460)),xh=Error(o(474)),of={then:function(){}};function Th(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Es(){}function _h(e,n,i){switch(i=e[i],i===void 0?e.push(n):i!==n&&(n.then(Es,Es),n=i),n.status){case"fulfilled":return n.value;case"rejected":throw e=n.reason,e===ki?Error(o(483)):e;default:if(typeof n.status=="string")n.then(Es,Es);else{if(e=ze,e!==null&&100<e.shellSuspendCounter)throw Error(o(482));e=n,e.status="pending",e.then(function(l){if(n.status==="pending"){var f=n;f.status="fulfilled",f.value=l}},function(l){if(n.status==="pending"){var f=n;f.status="rejected",f.reason=l}})}switch(n.status){case"fulfilled":return n.value;case"rejected":throw e=n.reason,e===ki?Error(o(483)):e}throw Ei=n,ki}}var Ei=null;function Oh(){if(Ei===null)throw Error(o(459));var e=Ei;return Ei=null,e}var io=null,Di=0;function Ds(e){var n=Di;return Di+=1,io===null&&(io=[]),_h(io,e,n)}function xi(e,n){n=n.props.ref,e.ref=n!==void 0?n:null}function xs(e,n){throw n.$$typeof===u?Error(o(525)):(e=Object.prototype.toString.call(n),Error(o(31,e==="[object Object]"?"object with keys {"+Object.keys(n).join(", ")+"}":e)))}function Ch(e){var n=e._init;return n(e._payload)}function Ph(e){function n(z,M){if(e){var j=z.deletions;j===null?(z.deletions=[M],z.flags|=16):j.push(M)}}function i(z,M){if(!e)return null;for(;M!==null;)n(z,M),M=M.sibling;return null}function l(z){for(var M=new Map;z!==null;)z.key!==null?M.set(z.key,z):M.set(z.index,z),z=z.sibling;return M}function f(z,M){return z=Cr(z,M),z.index=0,z.sibling=null,z}function p(z,M,j){return z.index=j,e?(j=z.alternate,j!==null?(j=j.index,j<M?(z.flags|=33554434,M):j):(z.flags|=33554434,M)):(z.flags|=1048576,M)}function b(z){return e&&z.alternate===null&&(z.flags|=33554434),z}function D(z,M,j,X){return M===null||M.tag!==6?(M=ed(j,z.mode,X),M.return=z,M):(M=f(M,j),M.return=z,M)}function T(z,M,j,X){var de=j.type;return de===v?K(z,M,j.props.children,X,j.key):M!==null&&(M.elementType===de||typeof de=="object"&&de!==null&&de.$$typeof===_&&Ch(de)===M.type)?(M=f(M,j.props),xi(M,j),M.return=z,M):(M=Ws(j.type,j.key,j.props,null,z.mode,X),xi(M,j),M.return=z,M)}function N(z,M,j,X){return M===null||M.tag!==4||M.stateNode.containerInfo!==j.containerInfo||M.stateNode.implementation!==j.implementation?(M=td(j,z.mode,X),M.return=z,M):(M=f(M,j.children||[]),M.return=z,M)}function K(z,M,j,X,de){return M===null||M.tag!==7?(M=wa(j,z.mode,X,de),M.return=z,M):(M=f(M,j),M.return=z,M)}function Z(z,M,j){if(typeof M=="string"&&M!==""||typeof M=="number"||typeof M=="bigint")return M=ed(""+M,z.mode,j),M.return=z,M;if(typeof M=="object"&&M!==null){switch(M.$$typeof){case c:return j=Ws(M.type,M.key,M.props,null,z.mode,j),xi(j,M),j.return=z,j;case d:return M=td(M,z.mode,j),M.return=z,M;case _:var X=M._init;return M=X(M._payload),Z(z,M,j)}if(W(M)||B(M))return M=wa(M,z.mode,j,null),M.return=z,M;if(typeof M.then=="function")return Z(z,Ds(M),j);if(M.$$typeof===w)return Z(z,Ys(z,M),j);xs(z,M)}return null}function Y(z,M,j,X){var de=M!==null?M.key:null;if(typeof j=="string"&&j!==""||typeof j=="number"||typeof j=="bigint")return de!==null?null:D(z,M,""+j,X);if(typeof j=="object"&&j!==null){switch(j.$$typeof){case c:return j.key===de?T(z,M,j,X):null;case d:return j.key===de?N(z,M,j,X):null;case _:return de=j._init,j=de(j._payload),Y(z,M,j,X)}if(W(j)||B(j))return de!==null?null:K(z,M,j,X,null);if(typeof j.then=="function")return Y(z,M,Ds(j),X);if(j.$$typeof===w)return Y(z,M,Ys(z,j),X);xs(z,j)}return null}function U(z,M,j,X,de){if(typeof X=="string"&&X!==""||typeof X=="number"||typeof X=="bigint")return z=z.get(j)||null,D(M,z,""+X,de);if(typeof X=="object"&&X!==null){switch(X.$$typeof){case c:return z=z.get(X.key===null?j:X.key)||null,T(M,z,X,de);case d:return z=z.get(X.key===null?j:X.key)||null,N(M,z,X,de);case _:var Ee=X._init;return X=Ee(X._payload),U(z,M,j,X,de)}if(W(X)||B(X))return z=z.get(j)||null,K(M,z,X,de,null);if(typeof X.then=="function")return U(z,M,j,Ds(X),de);if(X.$$typeof===w)return U(z,M,j,Ys(M,X),de);xs(M,X)}return null}function pe(z,M,j,X){for(var de=null,Ee=null,he=M,be=M=0,ut=null;he!==null&&be<j.length;be++){he.index>be?(ut=he,he=null):ut=he.sibling;var Pe=Y(z,he,j[be],X);if(Pe===null){he===null&&(he=ut);break}e&&he&&Pe.alternate===null&&n(z,he),M=p(Pe,M,be),Ee===null?de=Pe:Ee.sibling=Pe,Ee=Pe,he=ut}if(be===j.length)return i(z,he),Ce&&ua(z,be),de;if(he===null){for(;be<j.length;be++)he=Z(z,j[be],X),he!==null&&(M=p(he,M,be),Ee===null?de=he:Ee.sibling=he,Ee=he);return Ce&&ua(z,be),de}for(he=l(he);be<j.length;be++)ut=U(he,z,be,j[be],X),ut!==null&&(e&&ut.alternate!==null&&he.delete(ut.key===null?be:ut.key),M=p(ut,M,be),Ee===null?de=ut:Ee.sibling=ut,Ee=ut);return e&&he.forEach(function(zr){return n(z,zr)}),Ce&&ua(z,be),de}function we(z,M,j,X){if(j==null)throw Error(o(151));for(var de=null,Ee=null,he=M,be=M=0,ut=null,Pe=j.next();he!==null&&!Pe.done;be++,Pe=j.next()){he.index>be?(ut=he,he=null):ut=he.sibling;var zr=Y(z,he,Pe.value,X);if(zr===null){he===null&&(he=ut);break}e&&he&&zr.alternate===null&&n(z,he),M=p(zr,M,be),Ee===null?de=zr:Ee.sibling=zr,Ee=zr,he=ut}if(Pe.done)return i(z,he),Ce&&ua(z,be),de;if(he===null){for(;!Pe.done;be++,Pe=j.next())Pe=Z(z,Pe.value,X),Pe!==null&&(M=p(Pe,M,be),Ee===null?de=Pe:Ee.sibling=Pe,Ee=Pe);return Ce&&ua(z,be),de}for(he=l(he);!Pe.done;be++,Pe=j.next())Pe=U(he,z,be,Pe.value,X),Pe!==null&&(e&&Pe.alternate!==null&&he.delete(Pe.key===null?be:Pe.key),M=p(Pe,M,be),Ee===null?de=Pe:Ee.sibling=Pe,Ee=Pe);return e&&he.forEach(function(G1){return n(z,G1)}),Ce&&ua(z,be),de}function Ke(z,M,j,X){if(typeof j=="object"&&j!==null&&j.type===v&&j.key===null&&(j=j.props.children),typeof j=="object"&&j!==null){switch(j.$$typeof){case c:e:{for(var de=j.key;M!==null;){if(M.key===de){if(de=j.type,de===v){if(M.tag===7){i(z,M.sibling),X=f(M,j.props.children),X.return=z,z=X;break e}}else if(M.elementType===de||typeof de=="object"&&de!==null&&de.$$typeof===_&&Ch(de)===M.type){i(z,M.sibling),X=f(M,j.props),xi(X,j),X.return=z,z=X;break e}i(z,M);break}else n(z,M);M=M.sibling}j.type===v?(X=wa(j.props.children,z.mode,X,j.key),X.return=z,z=X):(X=Ws(j.type,j.key,j.props,null,z.mode,X),xi(X,j),X.return=z,z=X)}return b(z);case d:e:{for(de=j.key;M!==null;){if(M.key===de)if(M.tag===4&&M.stateNode.containerInfo===j.containerInfo&&M.stateNode.implementation===j.implementation){i(z,M.sibling),X=f(M,j.children||[]),X.return=z,z=X;break e}else{i(z,M);break}else n(z,M);M=M.sibling}X=td(j,z.mode,X),X.return=z,z=X}return b(z);case _:return de=j._init,j=de(j._payload),Ke(z,M,j,X)}if(W(j))return pe(z,M,j,X);if(B(j)){if(de=B(j),typeof de!="function")throw Error(o(150));return j=de.call(j),we(z,M,j,X)}if(typeof j.then=="function")return Ke(z,M,Ds(j),X);if(j.$$typeof===w)return Ke(z,M,Ys(z,j),X);xs(z,j)}return typeof j=="string"&&j!==""||typeof j=="number"||typeof j=="bigint"?(j=""+j,M!==null&&M.tag===6?(i(z,M.sibling),X=f(M,j),X.return=z,z=X):(i(z,M),X=ed(j,z.mode,X),X.return=z,z=X),b(z)):i(z,M)}return function(z,M,j,X){try{Di=0;var de=Ke(z,M,j,X);return io=null,de}catch(he){if(he===ki)throw he;var Ee=nn(29,he,null,z.mode);return Ee.lanes=X,Ee.return=z,Ee}}}var fa=Ph(!0),Mh=Ph(!1),lo=ce(null),Ts=ce(0);function Nh(e,n){e=Zn,Me(Ts,e),Me(lo,n),Zn=e|n.baseLanes}function lf(){Me(Ts,Zn),Me(lo,lo.current)}function sf(){Zn=Ts.current,Oe(lo),Oe(Ts)}var Zt=ce(null),Dn=null;function yr(e){var n=e.alternate;Me(et,et.current&1),Me(Zt,e),Dn===null&&(n===null||lo.current!==null||n.memoizedState!==null)&&(Dn=e)}function Ah(e){if(e.tag===22){if(Me(et,et.current),Me(Zt,e),Dn===null){var n=e.alternate;n!==null&&n.memoizedState!==null&&(Dn=e)}}else br()}function br(){Me(et,et.current),Me(Zt,Zt.current)}function Wn(e){Oe(Zt),Dn===e&&(Dn=null),Oe(et)}var et=ce(0);function _s(e){for(var n=e;n!==null;){if(n.tag===13){var i=n.memoizedState;if(i!==null&&(i=i.dehydrated,i===null||i.data==="$?"||i.data==="$!"))return n}else if(n.tag===19&&n.memoizedProps.revealOrder!==void 0){if(n.flags&128)return n}else if(n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return null;n=n.return}n.sibling.return=n.return,n=n.sibling}return null}var jE=typeof AbortController<"u"?AbortController:function(){var e=[],n=this.signal={aborted:!1,addEventListener:function(i,l){e.push(l)}};this.abort=function(){n.aborted=!0,e.forEach(function(i){return i()})}},IE=t.unstable_scheduleCallback,YE=t.unstable_NormalPriority,tt={$$typeof:w,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function uf(){return{controller:new jE,data:new Map,refCount:0}}function Ti(e){e.refCount--,e.refCount===0&&IE(YE,function(){e.controller.abort()})}var _i=null,cf=0,so=0,uo=null;function HE(e,n){if(_i===null){var i=_i=[];cf=0,so=md(),uo={status:"pending",value:void 0,then:function(l){i.push(l)}}}return cf++,n.then(Rh,Rh),n}function Rh(){if(--cf===0&&_i!==null){uo!==null&&(uo.status="fulfilled");var e=_i;_i=null,so=0,uo=null;for(var n=0;n<e.length;n++)(0,e[n])()}}function BE(e,n){var i=[],l={status:"pending",value:null,reason:null,then:function(f){i.push(f)}};return e.then(function(){l.status="fulfilled",l.value=n;for(var f=0;f<i.length;f++)(0,i[f])(n)},function(f){for(l.status="rejected",l.reason=f,f=0;f<i.length;f++)(0,i[f])(void 0)}),l}var Fh=I.S;I.S=function(e,n){typeof n=="object"&&n!==null&&typeof n.then=="function"&&HE(e,n),Fh!==null&&Fh(e,n)};var da=ce(null);function ff(){var e=da.current;return e!==null?e:ze.pooledCache}function Os(e,n){n===null?Me(da,da.current):Me(da,n.pool)}function zh(){var e=ff();return e===null?null:{parent:tt._currentValue,pool:e}}var wr=0,ke=null,Ne=null,Ge=null,Cs=!1,co=!1,va=!1,Ps=0,Oi=0,fo=null,WE=0;function Xe(){throw Error(o(321))}function df(e,n){if(n===null)return!1;for(var i=0;i<n.length&&i<e.length;i++)if(!Ht(e[i],n[i]))return!1;return!0}function vf(e,n,i,l,f,p){return wr=p,ke=n,n.memoizedState=null,n.updateQueue=null,n.lanes=0,I.H=e===null||e.memoizedState===null?pa:Sr,va=!1,p=i(l,f),va=!1,co&&(p=jh(n,i,l,f)),Lh(e),p}function Lh(e){I.H=xn;var n=Ne!==null&&Ne.next!==null;if(wr=0,Ge=Ne=ke=null,Cs=!1,Oi=0,fo=null,n)throw Error(o(300));e===null||lt||(e=e.dependencies,e!==null&&Is(e)&&(lt=!0))}function jh(e,n,i,l){ke=e;var f=0;do{if(co&&(fo=null),Oi=0,co=!1,25<=f)throw Error(o(301));if(f+=1,Ge=Ne=null,e.updateQueue!=null){var p=e.updateQueue;p.lastEffect=null,p.events=null,p.stores=null,p.memoCache!=null&&(p.memoCache.index=0)}I.H=ha,p=n(i,l)}while(co);return p}function VE(){var e=I.H,n=e.useState()[0];return n=typeof n.then=="function"?Ci(n):n,e=e.useState()[0],(Ne!==null?Ne.memoizedState:null)!==e&&(ke.flags|=1024),n}function pf(){var e=Ps!==0;return Ps=0,e}function hf(e,n,i){n.updateQueue=e.updateQueue,n.flags&=-2053,e.lanes&=~i}function mf(e){if(Cs){for(e=e.memoizedState;e!==null;){var n=e.queue;n!==null&&(n.pending=null),e=e.next}Cs=!1}wr=0,Ge=Ne=ke=null,co=!1,Oi=Ps=0,fo=null}function At(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ge===null?ke.memoizedState=Ge=e:Ge=Ge.next=e,Ge}function Je(){if(Ne===null){var e=ke.alternate;e=e!==null?e.memoizedState:null}else e=Ne.next;var n=Ge===null?ke.memoizedState:Ge.next;if(n!==null)Ge=n,Ne=e;else{if(e===null)throw ke.alternate===null?Error(o(467)):Error(o(310));Ne=e,e={memoizedState:Ne.memoizedState,baseState:Ne.baseState,baseQueue:Ne.baseQueue,queue:Ne.queue,next:null},Ge===null?ke.memoizedState=Ge=e:Ge=Ge.next=e}return Ge}var Ms;Ms=function(){return{lastEffect:null,events:null,stores:null,memoCache:null}};function Ci(e){var n=Oi;return Oi+=1,fo===null&&(fo=[]),e=_h(fo,e,n),n=ke,(Ge===null?n.memoizedState:Ge.next)===null&&(n=n.alternate,I.H=n===null||n.memoizedState===null?pa:Sr),e}function Ns(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Ci(e);if(e.$$typeof===w)return gt(e)}throw Error(o(438,String(e)))}function gf(e){var n=null,i=ke.updateQueue;if(i!==null&&(n=i.memoCache),n==null){var l=ke.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(n={data:l.data.map(function(f){return f.slice()}),index:0})))}if(n==null&&(n={data:[],index:0}),i===null&&(i=Ms(),ke.updateQueue=i),i.memoCache=n,i=n.data[n.index],i===void 0)for(i=n.data[n.index]=Array(e),l=0;l<e;l++)i[l]=C;return n.index++,i}function Vn(e,n){return typeof n=="function"?n(e):n}function As(e){var n=Je();return yf(n,Ne,e)}function yf(e,n,i){var l=e.queue;if(l===null)throw Error(o(311));l.lastRenderedReducer=i;var f=e.baseQueue,p=l.pending;if(p!==null){if(f!==null){var b=f.next;f.next=p.next,p.next=b}n.baseQueue=f=p,l.pending=null}if(p=e.baseState,f===null)e.memoizedState=p;else{n=f.next;var D=b=null,T=null,N=n,K=!1;do{var Z=N.lane&-536870913;if(Z!==N.lane?(_e&Z)===Z:(wr&Z)===Z){var Y=N.revertLane;if(Y===0)T!==null&&(T=T.next={lane:0,revertLane:0,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null}),Z===so&&(K=!0);else if((wr&Y)===Y){N=N.next,Y===so&&(K=!0);continue}else Z={lane:0,revertLane:N.revertLane,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null},T===null?(D=T=Z,b=p):T=T.next=Z,ke.lanes|=Y,Pr|=Y;Z=N.action,va&&i(p,Z),p=N.hasEagerState?N.eagerState:i(p,Z)}else Y={lane:Z,revertLane:N.revertLane,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null},T===null?(D=T=Y,b=p):T=T.next=Y,ke.lanes|=Z,Pr|=Z;N=N.next}while(N!==null&&N!==n);if(T===null?b=p:T.next=D,!Ht(p,e.memoizedState)&&(lt=!0,K&&(i=uo,i!==null)))throw i;e.memoizedState=p,e.baseState=b,e.baseQueue=T,l.lastRenderedState=p}return f===null&&(l.lanes=0),[e.memoizedState,l.dispatch]}function bf(e){var n=Je(),i=n.queue;if(i===null)throw Error(o(311));i.lastRenderedReducer=e;var l=i.dispatch,f=i.pending,p=n.memoizedState;if(f!==null){i.pending=null;var b=f=f.next;do p=e(p,b.action),b=b.next;while(b!==f);Ht(p,n.memoizedState)||(lt=!0),n.memoizedState=p,n.baseQueue===null&&(n.baseState=p),i.lastRenderedState=p}return[p,l]}function Ih(e,n,i){var l=ke,f=Je(),p=Ce;if(p){if(i===void 0)throw Error(o(407));i=i()}else i=n();var b=!Ht((Ne||f).memoizedState,i);if(b&&(f.memoizedState=i,lt=!0),f=f.queue,kf(Bh.bind(null,l,f,e),[e]),f.getSnapshot!==n||b||Ge!==null&&Ge.memoizedState.tag&1){if(l.flags|=2048,vo(9,Hh.bind(null,l,f,i,n),{destroy:void 0},null),ze===null)throw Error(o(349));p||wr&60||Yh(l,n,i)}return i}function Yh(e,n,i){e.flags|=16384,e={getSnapshot:n,value:i},n=ke.updateQueue,n===null?(n=Ms(),ke.updateQueue=n,n.stores=[e]):(i=n.stores,i===null?n.stores=[e]:i.push(e))}function Hh(e,n,i,l){n.value=i,n.getSnapshot=l,Wh(n)&&Vh(e)}function Bh(e,n,i){return i(function(){Wh(n)&&Vh(e)})}function Wh(e){var n=e.getSnapshot;e=e.value;try{var i=n();return!Ht(e,i)}catch{return!0}}function Vh(e){var n=gr(e,2);n!==null&&_t(n,e,2)}function wf(e){var n=At();if(typeof e=="function"){var i=e;if(e=i(),va){pr(!0);try{i()}finally{pr(!1)}}}return n.memoizedState=n.baseState=e,n.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Vn,lastRenderedState:e},n}function Uh(e,n,i,l){return e.baseState=i,yf(e,Ne,typeof l=="function"?l:Vn)}function UE(e,n,i,l,f){if(zs(e))throw Error(o(485));if(e=n.action,e!==null){var p={payload:f,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(b){p.listeners.push(b)}};I.T!==null?i(!0):p.isTransition=!1,l(p),i=n.pending,i===null?(p.next=n.pending=p,$h(n,p)):(p.next=i.next,n.pending=i.next=p)}}function $h(e,n){var i=n.action,l=n.payload,f=e.state;if(n.isTransition){var p=I.T,b={};I.T=b;try{var D=i(f,l),T=I.S;T!==null&&T(b,D),Kh(e,n,D)}catch(N){Sf(e,n,N)}finally{I.T=p}}else try{p=i(f,l),Kh(e,n,p)}catch(N){Sf(e,n,N)}}function Kh(e,n,i){i!==null&&typeof i=="object"&&typeof i.then=="function"?i.then(function(l){qh(e,n,l)},function(l){return Sf(e,n,l)}):qh(e,n,i)}function qh(e,n,i){n.status="fulfilled",n.value=i,Qh(n),e.state=i,n=e.pending,n!==null&&(i=n.next,i===n?e.pending=null:(i=i.next,n.next=i,$h(e,i)))}function Sf(e,n,i){var l=e.pending;if(e.pending=null,l!==null){l=l.next;do n.status="rejected",n.reason=i,Qh(n),n=n.next;while(n!==l)}e.action=null}function Qh(e){e=e.listeners;for(var n=0;n<e.length;n++)(0,e[n])()}function Xh(e,n){return n}function Gh(e,n){if(Ce){var i=ze.formState;if(i!==null){e:{var l=ke;if(Ce){if(ct){t:{for(var f=ct,p=En;f.nodeType!==8;){if(!p){f=null;break t}if(f=hn(f.nextSibling),f===null){f=null;break t}}p=f.data,f=p==="F!"||p==="F"?f:null}if(f){ct=hn(f.nextSibling),l=f.data==="F!";break e}}ca(l)}l=!1}l&&(n=i[0])}}return i=At(),i.memoizedState=i.baseState=n,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Xh,lastRenderedState:n},i.queue=l,i=hm.bind(null,ke,l),l.dispatch=i,l=wf(!1),p=_f.bind(null,ke,!1,l.queue),l=At(),f={state:n,dispatch:null,action:e,pending:null},l.queue=f,i=UE.bind(null,ke,f,p,i),f.dispatch=i,l.memoizedState=e,[n,i,!1]}function Jh(e){var n=Je();return Zh(n,Ne,e)}function Zh(e,n,i){n=yf(e,n,Xh)[0],e=As(Vn)[0],n=typeof n=="object"&&n!==null&&typeof n.then=="function"?Ci(n):n;var l=Je(),f=l.queue,p=f.dispatch;return i!==l.memoizedState&&(ke.flags|=2048,vo(9,$E.bind(null,f,i),{destroy:void 0},null)),[n,p,e]}function $E(e,n){e.action=n}function em(e){var n=Je(),i=Ne;if(i!==null)return Zh(n,i,e);Je(),n=n.memoizedState,i=Je();var l=i.queue.dispatch;return i.memoizedState=e,[n,l,!1]}function vo(e,n,i,l){return e={tag:e,create:n,inst:i,deps:l,next:null},n=ke.updateQueue,n===null&&(n=Ms(),ke.updateQueue=n),i=n.lastEffect,i===null?n.lastEffect=e.next=e:(l=i.next,i.next=e,e.next=l,n.lastEffect=e),e}function tm(){return Je().memoizedState}function Rs(e,n,i,l){var f=At();ke.flags|=e,f.memoizedState=vo(1|n,i,{destroy:void 0},l===void 0?null:l)}function Fs(e,n,i,l){var f=Je();l=l===void 0?null:l;var p=f.memoizedState.inst;Ne!==null&&l!==null&&df(l,Ne.memoizedState.deps)?f.memoizedState=vo(n,i,p,l):(ke.flags|=e,f.memoizedState=vo(1|n,i,p,l))}function nm(e,n){Rs(8390656,8,e,n)}function kf(e,n){Fs(2048,8,e,n)}function rm(e,n){return Fs(4,2,e,n)}function am(e,n){return Fs(4,4,e,n)}function om(e,n){if(typeof n=="function"){e=e();var i=n(e);return function(){typeof i=="function"?i():n(null)}}if(n!=null)return e=e(),n.current=e,function(){n.current=null}}function im(e,n,i){i=i!=null?i.concat([e]):null,Fs(4,4,om.bind(null,n,e),i)}function Ef(){}function lm(e,n){var i=Je();n=n===void 0?null:n;var l=i.memoizedState;return n!==null&&df(n,l[1])?l[0]:(i.memoizedState=[e,n],e)}function sm(e,n){var i=Je();n=n===void 0?null:n;var l=i.memoizedState;if(n!==null&&df(n,l[1]))return l[0];if(l=e(),va){pr(!0);try{e()}finally{pr(!1)}}return i.memoizedState=[l,n],l}function Df(e,n,i){return i===void 0||wr&1073741824?e.memoizedState=n:(e.memoizedState=i,e=cg(),ke.lanes|=e,Pr|=e,i)}function um(e,n,i,l){return Ht(i,n)?i:lo.current!==null?(e=Df(e,i,l),Ht(e,n)||(lt=!0),e):wr&42?(e=cg(),ke.lanes|=e,Pr|=e,n):(lt=!0,e.memoizedState=i)}function cm(e,n,i,l,f){var p=L.p;L.p=p!==0&&8>p?p:8;var b=I.T,D={};I.T=D,_f(e,!1,n,i);try{var T=f(),N=I.S;if(N!==null&&N(D,T),T!==null&&typeof T=="object"&&typeof T.then=="function"){var K=BE(T,l);Pi(e,n,K,Ut(e))}else Pi(e,n,l,Ut(e))}catch(Z){Pi(e,n,{then:function(){},status:"rejected",reason:Z},Ut())}finally{L.p=p,I.T=b}}function KE(){}function xf(e,n,i,l){if(e.tag!==5)throw Error(o(476));var f=fm(e).queue;cm(e,f,n,G,i===null?KE:function(){return dm(e),i(l)})}function fm(e){var n=e.memoizedState;if(n!==null)return n;n={memoizedState:G,baseState:G,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Vn,lastRenderedState:G},next:null};var i={};return n.next={memoizedState:i,baseState:i,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Vn,lastRenderedState:i},next:null},e.memoizedState=n,e=e.alternate,e!==null&&(e.memoizedState=n),n}function dm(e){var n=fm(e).next.queue;Pi(e,n,{},Ut())}function Tf(){return gt(Xi)}function vm(){return Je().memoizedState}function pm(){return Je().memoizedState}function qE(e){for(var n=e.return;n!==null;){switch(n.tag){case 24:case 3:var i=Ut();e=Dr(i);var l=xr(n,e,i);l!==null&&(_t(l,n,i),Ai(l,n,i)),n={cache:uf()},e.payload=n;return}n=n.return}}function QE(e,n,i){var l=Ut();i={lane:l,revertLane:0,action:i,hasEagerState:!1,eagerState:null,next:null},zs(e)?mm(n,i):(i=tf(e,n,i,l),i!==null&&(_t(i,e,l),gm(i,n,l)))}function hm(e,n,i){var l=Ut();Pi(e,n,i,l)}function Pi(e,n,i,l){var f={lane:l,revertLane:0,action:i,hasEagerState:!1,eagerState:null,next:null};if(zs(e))mm(n,f);else{var p=e.alternate;if(e.lanes===0&&(p===null||p.lanes===0)&&(p=n.lastRenderedReducer,p!==null))try{var b=n.lastRenderedState,D=p(b,i);if(f.hasEagerState=!0,f.eagerState=D,Ht(D,b))return bs(e,n,f,0),ze===null&&ys(),!1}catch{}if(i=tf(e,n,f,l),i!==null)return _t(i,e,l),gm(i,n,l),!0}return!1}function _f(e,n,i,l){if(l={lane:2,revertLane:md(),action:l,hasEagerState:!1,eagerState:null,next:null},zs(e)){if(n)throw Error(o(479))}else n=tf(e,i,l,2),n!==null&&_t(n,e,2)}function zs(e){var n=e.alternate;return e===ke||n!==null&&n===ke}function mm(e,n){co=Cs=!0;var i=e.pending;i===null?n.next=n:(n.next=i.next,i.next=n),e.pending=n}function gm(e,n,i){if(i&4194176){var l=n.lanes;l&=e.pendingLanes,i|=l,n.lanes=i,Op(e,i)}}var xn={readContext:gt,use:Ns,useCallback:Xe,useContext:Xe,useEffect:Xe,useImperativeHandle:Xe,useLayoutEffect:Xe,useInsertionEffect:Xe,useMemo:Xe,useReducer:Xe,useRef:Xe,useState:Xe,useDebugValue:Xe,useDeferredValue:Xe,useTransition:Xe,useSyncExternalStore:Xe,useId:Xe};xn.useCacheRefresh=Xe,xn.useMemoCache=Xe,xn.useHostTransitionStatus=Xe,xn.useFormState=Xe,xn.useActionState=Xe,xn.useOptimistic=Xe;var pa={readContext:gt,use:Ns,useCallback:function(e,n){return At().memoizedState=[e,n===void 0?null:n],e},useContext:gt,useEffect:nm,useImperativeHandle:function(e,n,i){i=i!=null?i.concat([e]):null,Rs(4194308,4,om.bind(null,n,e),i)},useLayoutEffect:function(e,n){return Rs(4194308,4,e,n)},useInsertionEffect:function(e,n){Rs(4,2,e,n)},useMemo:function(e,n){var i=At();n=n===void 0?null:n;var l=e();if(va){pr(!0);try{e()}finally{pr(!1)}}return i.memoizedState=[l,n],l},useReducer:function(e,n,i){var l=At();if(i!==void 0){var f=i(n);if(va){pr(!0);try{i(n)}finally{pr(!1)}}}else f=n;return l.memoizedState=l.baseState=f,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:f},l.queue=e,e=e.dispatch=QE.bind(null,ke,e),[l.memoizedState,e]},useRef:function(e){var n=At();return e={current:e},n.memoizedState=e},useState:function(e){e=wf(e);var n=e.queue,i=hm.bind(null,ke,n);return n.dispatch=i,[e.memoizedState,i]},useDebugValue:Ef,useDeferredValue:function(e,n){var i=At();return Df(i,e,n)},useTransition:function(){var e=wf(!1);return e=cm.bind(null,ke,e.queue,!0,!1),At().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,n,i){var l=ke,f=At();if(Ce){if(i===void 0)throw Error(o(407));i=i()}else{if(i=n(),ze===null)throw Error(o(349));_e&60||Yh(l,n,i)}f.memoizedState=i;var p={value:i,getSnapshot:n};return f.queue=p,nm(Bh.bind(null,l,p,e),[e]),l.flags|=2048,vo(9,Hh.bind(null,l,p,i,n),{destroy:void 0},null),i},useId:function(){var e=At(),n=ze.identifierPrefix;if(Ce){var i=Bn,l=Hn;i=(l&~(1<<32-Yt(l)-1)).toString(32)+i,n=":"+n+"R"+i,i=Ps++,0<i&&(n+="H"+i.toString(32)),n+=":"}else i=WE++,n=":"+n+"r"+i.toString(32)+":";return e.memoizedState=n},useCacheRefresh:function(){return At().memoizedState=qE.bind(null,ke)}};pa.useMemoCache=gf,pa.useHostTransitionStatus=Tf,pa.useFormState=Gh,pa.useActionState=Gh,pa.useOptimistic=function(e){var n=At();n.memoizedState=n.baseState=e;var i={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return n.queue=i,n=_f.bind(null,ke,!0,i),i.dispatch=n,[e,n]};var Sr={readContext:gt,use:Ns,useCallback:lm,useContext:gt,useEffect:kf,useImperativeHandle:im,useInsertionEffect:rm,useLayoutEffect:am,useMemo:sm,useReducer:As,useRef:tm,useState:function(){return As(Vn)},useDebugValue:Ef,useDeferredValue:function(e,n){var i=Je();return um(i,Ne.memoizedState,e,n)},useTransition:function(){var e=As(Vn)[0],n=Je().memoizedState;return[typeof e=="boolean"?e:Ci(e),n]},useSyncExternalStore:Ih,useId:vm};Sr.useCacheRefresh=pm,Sr.useMemoCache=gf,Sr.useHostTransitionStatus=Tf,Sr.useFormState=Jh,Sr.useActionState=Jh,Sr.useOptimistic=function(e,n){var i=Je();return Uh(i,Ne,e,n)};var ha={readContext:gt,use:Ns,useCallback:lm,useContext:gt,useEffect:kf,useImperativeHandle:im,useInsertionEffect:rm,useLayoutEffect:am,useMemo:sm,useReducer:bf,useRef:tm,useState:function(){return bf(Vn)},useDebugValue:Ef,useDeferredValue:function(e,n){var i=Je();return Ne===null?Df(i,e,n):um(i,Ne.memoizedState,e,n)},useTransition:function(){var e=bf(Vn)[0],n=Je().memoizedState;return[typeof e=="boolean"?e:Ci(e),n]},useSyncExternalStore:Ih,useId:vm};ha.useCacheRefresh=pm,ha.useMemoCache=gf,ha.useHostTransitionStatus=Tf,ha.useFormState=em,ha.useActionState=em,ha.useOptimistic=function(e,n){var i=Je();return Ne!==null?Uh(i,Ne,e,n):(i.baseState=e,[e,i.queue.dispatch])};function Of(e,n,i,l){n=e.memoizedState,i=i(l,n),i=i==null?n:V({},n,i),e.memoizedState=i,e.lanes===0&&(e.updateQueue.baseState=i)}var Cf={isMounted:function(e){return(e=e._reactInternals)?se(e)===e:!1},enqueueSetState:function(e,n,i){e=e._reactInternals;var l=Ut(),f=Dr(l);f.payload=n,i!=null&&(f.callback=i),n=xr(e,f,l),n!==null&&(_t(n,e,l),Ai(n,e,l))},enqueueReplaceState:function(e,n,i){e=e._reactInternals;var l=Ut(),f=Dr(l);f.tag=1,f.payload=n,i!=null&&(f.callback=i),n=xr(e,f,l),n!==null&&(_t(n,e,l),Ai(n,e,l))},enqueueForceUpdate:function(e,n){e=e._reactInternals;var i=Ut(),l=Dr(i);l.tag=2,n!=null&&(l.callback=n),n=xr(e,l,i),n!==null&&(_t(n,e,i),Ai(n,e,i))}};function ym(e,n,i,l,f,p,b){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,p,b):n.prototype&&n.prototype.isPureReactComponent?!gi(i,l)||!gi(f,p):!0}function bm(e,n,i,l){e=n.state,typeof n.componentWillReceiveProps=="function"&&n.componentWillReceiveProps(i,l),typeof n.UNSAFE_componentWillReceiveProps=="function"&&n.UNSAFE_componentWillReceiveProps(i,l),n.state!==e&&Cf.enqueueReplaceState(n,n.state,null)}function ma(e,n){var i=n;if("ref"in n){i={};for(var l in n)l!=="ref"&&(i[l]=n[l])}if(e=e.defaultProps){i===n&&(i=V({},i));for(var f in e)i[f]===void 0&&(i[f]=e[f])}return i}var Ls=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var n=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(n))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function wm(e){Ls(e)}function Sm(e){console.error(e)}function km(e){Ls(e)}function js(e,n){try{var i=e.onUncaughtError;i(n.value,{componentStack:n.stack})}catch(l){setTimeout(function(){throw l})}}function Em(e,n,i){try{var l=e.onCaughtError;l(i.value,{componentStack:i.stack,errorBoundary:n.tag===1?n.stateNode:null})}catch(f){setTimeout(function(){throw f})}}function Pf(e,n,i){return i=Dr(i),i.tag=3,i.payload={element:null},i.callback=function(){js(e,n)},i}function Dm(e){return e=Dr(e),e.tag=3,e}function xm(e,n,i,l){var f=i.type.getDerivedStateFromError;if(typeof f=="function"){var p=l.value;e.payload=function(){return f(p)},e.callback=function(){Em(n,i,l)}}var b=i.stateNode;b!==null&&typeof b.componentDidCatch=="function"&&(e.callback=function(){Em(n,i,l),typeof f!="function"&&(Mr===null?Mr=new Set([this]):Mr.add(this));var D=l.stack;this.componentDidCatch(l.value,{componentStack:D!==null?D:""})})}function XE(e,n,i,l,f){if(i.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(n=i.alternate,n!==null&&Ni(n,i,f,!0),i=Zt.current,i!==null){switch(i.tag){case 13:return Dn===null?fd():i.alternate===null&&$e===0&&($e=3),i.flags&=-257,i.flags|=65536,i.lanes=f,l===of?i.flags|=16384:(n=i.updateQueue,n===null?i.updateQueue=new Set([l]):n.add(l),vd(e,l,f)),!1;case 22:return i.flags|=65536,l===of?i.flags|=16384:(n=i.updateQueue,n===null?(n={transitions:null,markerInstances:null,retryQueue:new Set([l])},i.updateQueue=n):(i=n.retryQueue,i===null?n.retryQueue=new Set([l]):i.add(l)),vd(e,l,f)),!1}throw Error(o(435,i.tag))}return vd(e,l,f),fd(),!1}if(Ce)return n=Zt.current,n!==null?(!(n.flags&65536)&&(n.flags|=256),n.flags|=65536,n.lanes=f,l!==af&&(e=Error(o(422),{cause:l}),Si(Xt(e,i)))):(l!==af&&(n=Error(o(423),{cause:l}),Si(Xt(n,i))),e=e.current.alternate,e.flags|=65536,f&=-f,e.lanes|=f,l=Xt(l,i),f=Pf(e.stateNode,l,f),Uf(e,f),$e!==4&&($e=2)),!1;var p=Error(o(520),{cause:l});if(p=Xt(p,i),Hi===null?Hi=[p]:Hi.push(p),$e!==4&&($e=2),n===null)return!0;l=Xt(l,i),i=n;do{switch(i.tag){case 3:return i.flags|=65536,e=f&-f,i.lanes|=e,e=Pf(i.stateNode,l,e),Uf(i,e),!1;case 1:if(n=i.type,p=i.stateNode,(i.flags&128)===0&&(typeof n.getDerivedStateFromError=="function"||p!==null&&typeof p.componentDidCatch=="function"&&(Mr===null||!Mr.has(p))))return i.flags|=65536,f&=-f,i.lanes|=f,f=Dm(f),xm(f,e,i,l),Uf(i,f),!1}i=i.return}while(i!==null);return!1}var Tm=Error(o(461)),lt=!1;function ft(e,n,i,l){n.child=e===null?Mh(n,null,i,l):fa(n,e.child,i,l)}function _m(e,n,i,l,f){i=i.render;var p=n.ref;if("ref"in l){var b={};for(var D in l)D!=="ref"&&(b[D]=l[D])}else b=l;return ya(n),l=vf(e,n,i,b,p,f),D=pf(),e!==null&&!lt?(hf(e,n,f),Un(e,n,f)):(Ce&&D&&nf(n),n.flags|=1,ft(e,n,l,f),n.child)}function Om(e,n,i,l,f){if(e===null){var p=i.type;return typeof p=="function"&&!Zf(p)&&p.defaultProps===void 0&&i.compare===null?(n.tag=15,n.type=p,Cm(e,n,p,l,f)):(e=Ws(i.type,null,l,n,n.mode,f),e.ref=n.ref,e.return=n,n.child=e)}if(p=e.child,!If(e,f)){var b=p.memoizedProps;if(i=i.compare,i=i!==null?i:gi,i(b,l)&&e.ref===n.ref)return Un(e,n,f)}return n.flags|=1,e=Cr(p,l),e.ref=n.ref,e.return=n,n.child=e}function Cm(e,n,i,l,f){if(e!==null){var p=e.memoizedProps;if(gi(p,l)&&e.ref===n.ref)if(lt=!1,n.pendingProps=l=p,If(e,f))e.flags&131072&&(lt=!0);else return n.lanes=e.lanes,Un(e,n,f)}return Mf(e,n,i,l,f)}function Pm(e,n,i){var l=n.pendingProps,f=l.children,p=(n.stateNode._pendingVisibility&2)!==0,b=e!==null?e.memoizedState:null;if(Mi(e,n),l.mode==="hidden"||p){if(n.flags&128){if(l=b!==null?b.baseLanes|i:i,e!==null){for(f=n.child=e.child,p=0;f!==null;)p=p|f.lanes|f.childLanes,f=f.sibling;n.childLanes=p&~l}else n.childLanes=0,n.child=null;return Mm(e,n,l,i)}if(i&536870912)n.memoizedState={baseLanes:0,cachePool:null},e!==null&&Os(n,b!==null?b.cachePool:null),b!==null?Nh(n,b):lf(),Ah(n);else return n.lanes=n.childLanes=536870912,Mm(e,n,b!==null?b.baseLanes|i:i,i)}else b!==null?(Os(n,b.cachePool),Nh(n,b),br(),n.memoizedState=null):(e!==null&&Os(n,null),lf(),br());return ft(e,n,f,i),n.child}function Mm(e,n,i,l){var f=ff();return f=f===null?null:{parent:tt._currentValue,pool:f},n.memoizedState={baseLanes:i,cachePool:f},e!==null&&Os(n,null),lf(),Ah(n),e!==null&&Ni(e,n,l,!0),null}function Mi(e,n){var i=n.ref;if(i===null)e!==null&&e.ref!==null&&(n.flags|=2097664);else{if(typeof i!="function"&&typeof i!="object")throw Error(o(284));(e===null||e.ref!==i)&&(n.flags|=2097664)}}function Mf(e,n,i,l,f){return ya(n),i=vf(e,n,i,l,void 0,f),l=pf(),e!==null&&!lt?(hf(e,n,f),Un(e,n,f)):(Ce&&l&&nf(n),n.flags|=1,ft(e,n,i,f),n.child)}function Nm(e,n,i,l,f,p){return ya(n),n.updateQueue=null,i=jh(n,l,i,f),Lh(e),l=pf(),e!==null&&!lt?(hf(e,n,p),Un(e,n,p)):(Ce&&l&&nf(n),n.flags|=1,ft(e,n,i,p),n.child)}function Am(e,n,i,l,f){if(ya(n),n.stateNode===null){var p=ro,b=i.contextType;typeof b=="object"&&b!==null&&(p=gt(b)),p=new i(l,p),n.memoizedState=p.state!==null&&p.state!==void 0?p.state:null,p.updater=Cf,n.stateNode=p,p._reactInternals=n,p=n.stateNode,p.props=l,p.state=n.memoizedState,p.refs={},Wf(n),b=i.contextType,p.context=typeof b=="object"&&b!==null?gt(b):ro,p.state=n.memoizedState,b=i.getDerivedStateFromProps,typeof b=="function"&&(Of(n,i,b,l),p.state=n.memoizedState),typeof i.getDerivedStateFromProps=="function"||typeof p.getSnapshotBeforeUpdate=="function"||typeof p.UNSAFE_componentWillMount!="function"&&typeof p.componentWillMount!="function"||(b=p.state,typeof p.componentWillMount=="function"&&p.componentWillMount(),typeof p.UNSAFE_componentWillMount=="function"&&p.UNSAFE_componentWillMount(),b!==p.state&&Cf.enqueueReplaceState(p,p.state,null),Fi(n,l,p,f),Ri(),p.state=n.memoizedState),typeof p.componentDidMount=="function"&&(n.flags|=4194308),l=!0}else if(e===null){p=n.stateNode;var D=n.memoizedProps,T=ma(i,D);p.props=T;var N=p.context,K=i.contextType;b=ro,typeof K=="object"&&K!==null&&(b=gt(K));var Z=i.getDerivedStateFromProps;K=typeof Z=="function"||typeof p.getSnapshotBeforeUpdate=="function",D=n.pendingProps!==D,K||typeof p.UNSAFE_componentWillReceiveProps!="function"&&typeof p.componentWillReceiveProps!="function"||(D||N!==b)&&bm(n,p,l,b),Er=!1;var Y=n.memoizedState;p.state=Y,Fi(n,l,p,f),Ri(),N=n.memoizedState,D||Y!==N||Er?(typeof Z=="function"&&(Of(n,i,Z,l),N=n.memoizedState),(T=Er||ym(n,i,T,l,Y,N,b))?(K||typeof p.UNSAFE_componentWillMount!="function"&&typeof p.componentWillMount!="function"||(typeof p.componentWillMount=="function"&&p.componentWillMount(),typeof p.UNSAFE_componentWillMount=="function"&&p.UNSAFE_componentWillMount()),typeof p.componentDidMount=="function"&&(n.flags|=4194308)):(typeof p.componentDidMount=="function"&&(n.flags|=4194308),n.memoizedProps=l,n.memoizedState=N),p.props=l,p.state=N,p.context=b,l=T):(typeof p.componentDidMount=="function"&&(n.flags|=4194308),l=!1)}else{p=n.stateNode,Vf(e,n),b=n.memoizedProps,K=ma(i,b),p.props=K,Z=n.pendingProps,Y=p.context,N=i.contextType,T=ro,typeof N=="object"&&N!==null&&(T=gt(N)),D=i.getDerivedStateFromProps,(N=typeof D=="function"||typeof p.getSnapshotBeforeUpdate=="function")||typeof p.UNSAFE_componentWillReceiveProps!="function"&&typeof p.componentWillReceiveProps!="function"||(b!==Z||Y!==T)&&bm(n,p,l,T),Er=!1,Y=n.memoizedState,p.state=Y,Fi(n,l,p,f),Ri();var U=n.memoizedState;b!==Z||Y!==U||Er||e!==null&&e.dependencies!==null&&Is(e.dependencies)?(typeof D=="function"&&(Of(n,i,D,l),U=n.memoizedState),(K=Er||ym(n,i,K,l,Y,U,T)||e!==null&&e.dependencies!==null&&Is(e.dependencies))?(N||typeof p.UNSAFE_componentWillUpdate!="function"&&typeof p.componentWillUpdate!="function"||(typeof p.componentWillUpdate=="function"&&p.componentWillUpdate(l,U,T),typeof p.UNSAFE_componentWillUpdate=="function"&&p.UNSAFE_componentWillUpdate(l,U,T)),typeof p.componentDidUpdate=="function"&&(n.flags|=4),typeof p.getSnapshotBeforeUpdate=="function"&&(n.flags|=1024)):(typeof p.componentDidUpdate!="function"||b===e.memoizedProps&&Y===e.memoizedState||(n.flags|=4),typeof p.getSnapshotBeforeUpdate!="function"||b===e.memoizedProps&&Y===e.memoizedState||(n.flags|=1024),n.memoizedProps=l,n.memoizedState=U),p.props=l,p.state=U,p.context=T,l=K):(typeof p.componentDidUpdate!="function"||b===e.memoizedProps&&Y===e.memoizedState||(n.flags|=4),typeof p.getSnapshotBeforeUpdate!="function"||b===e.memoizedProps&&Y===e.memoizedState||(n.flags|=1024),l=!1)}return p=l,Mi(e,n),l=(n.flags&128)!==0,p||l?(p=n.stateNode,i=l&&typeof i.getDerivedStateFromError!="function"?null:p.render(),n.flags|=1,e!==null&&l?(n.child=fa(n,e.child,null,f),n.child=fa(n,null,i,f)):ft(e,n,i,f),n.memoizedState=p.state,e=n.child):e=Un(e,n,f),e}function Rm(e,n,i,l){return wi(),n.flags|=256,ft(e,n,i,l),n.child}var Nf={dehydrated:null,treeContext:null,retryLane:0};function Af(e){return{baseLanes:e,cachePool:zh()}}function Rf(e,n,i){return e=e!==null?e.childLanes&~i:0,n&&(e|=rn),e}function Fm(e,n,i){var l=n.pendingProps,f=!1,p=(n.flags&128)!==0,b;if((b=p)||(b=e!==null&&e.memoizedState===null?!1:(et.current&2)!==0),b&&(f=!0,n.flags&=-129),b=(n.flags&32)!==0,n.flags&=-33,e===null){if(Ce){if(f?yr(n):br(),Ce){var D=ct,T;if(T=D){e:{for(T=D,D=En;T.nodeType!==8;){if(!D){D=null;break e}if(T=hn(T.nextSibling),T===null){D=null;break e}}D=T}D!==null?(n.memoizedState={dehydrated:D,treeContext:sa!==null?{id:Hn,overflow:Bn}:null,retryLane:536870912},T=nn(18,null,null,0),T.stateNode=D,T.return=n,n.child=T,Tt=n,ct=null,T=!0):T=!1}T||ca(n)}if(D=n.memoizedState,D!==null&&(D=D.dehydrated,D!==null))return D.data==="$!"?n.lanes=16:n.lanes=536870912,null;Wn(n)}return D=l.children,l=l.fallback,f?(br(),f=n.mode,D=zf({mode:"hidden",children:D},f),l=wa(l,f,i,null),D.return=n,l.return=n,D.sibling=l,n.child=D,f=n.child,f.memoizedState=Af(i),f.childLanes=Rf(e,b,i),n.memoizedState=Nf,l):(yr(n),Ff(n,D))}if(T=e.memoizedState,T!==null&&(D=T.dehydrated,D!==null)){if(p)n.flags&256?(yr(n),n.flags&=-257,n=Lf(e,n,i)):n.memoizedState!==null?(br(),n.child=e.child,n.flags|=128,n=null):(br(),f=l.fallback,D=n.mode,l=zf({mode:"visible",children:l.children},D),f=wa(f,D,i,null),f.flags|=2,l.return=n,f.return=n,l.sibling=f,n.child=l,fa(n,e.child,null,i),l=n.child,l.memoizedState=Af(i),l.childLanes=Rf(e,b,i),n.memoizedState=Nf,n=f);else if(yr(n),D.data==="$!"){if(b=D.nextSibling&&D.nextSibling.dataset,b)var N=b.dgst;b=N,l=Error(o(419)),l.stack="",l.digest=b,Si({value:l,source:null,stack:null}),n=Lf(e,n,i)}else if(lt||Ni(e,n,i,!1),b=(i&e.childLanes)!==0,lt||b){if(b=ze,b!==null){if(l=i&-i,l&42)l=1;else switch(l){case 2:l=1;break;case 8:l=4;break;case 32:l=16;break;case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:l=64;break;case 268435456:l=134217728;break;default:l=0}if(l=l&(b.suspendedLanes|i)?0:l,l!==0&&l!==T.retryLane)throw T.retryLane=l,gr(e,l),_t(b,e,l),Tm}D.data==="$?"||fd(),n=Lf(e,n,i)}else D.data==="$?"?(n.flags|=128,n.child=e.child,n=f1.bind(null,e),D._reactRetry=n,n=null):(e=T.treeContext,ct=hn(D.nextSibling),Tt=n,Ce=!0,vn=null,En=!1,e!==null&&(Gt[Jt++]=Hn,Gt[Jt++]=Bn,Gt[Jt++]=sa,Hn=e.id,Bn=e.overflow,sa=n),n=Ff(n,l.children),n.flags|=4096);return n}return f?(br(),f=l.fallback,D=n.mode,T=e.child,N=T.sibling,l=Cr(T,{mode:"hidden",children:l.children}),l.subtreeFlags=T.subtreeFlags&31457280,N!==null?f=Cr(N,f):(f=wa(f,D,i,null),f.flags|=2),f.return=n,l.return=n,l.sibling=f,n.child=l,l=f,f=n.child,D=e.child.memoizedState,D===null?D=Af(i):(T=D.cachePool,T!==null?(N=tt._currentValue,T=T.parent!==N?{parent:N,pool:N}:T):T=zh(),D={baseLanes:D.baseLanes|i,cachePool:T}),f.memoizedState=D,f.childLanes=Rf(e,b,i),n.memoizedState=Nf,l):(yr(n),i=e.child,e=i.sibling,i=Cr(i,{mode:"visible",children:l.children}),i.return=n,i.sibling=null,e!==null&&(b=n.deletions,b===null?(n.deletions=[e],n.flags|=16):b.push(e)),n.child=i,n.memoizedState=null,i)}function Ff(e,n){return n=zf({mode:"visible",children:n},e.mode),n.return=e,e.child=n}function zf(e,n){return lg(e,n,0,null)}function Lf(e,n,i){return fa(n,e.child,null,i),e=Ff(n,n.pendingProps.children),e.flags|=2,n.memoizedState=null,e}function zm(e,n,i){e.lanes|=n;var l=e.alternate;l!==null&&(l.lanes|=n),Hf(e.return,n,i)}function jf(e,n,i,l,f){var p=e.memoizedState;p===null?e.memoizedState={isBackwards:n,rendering:null,renderingStartTime:0,last:l,tail:i,tailMode:f}:(p.isBackwards=n,p.rendering=null,p.renderingStartTime=0,p.last=l,p.tail=i,p.tailMode=f)}function Lm(e,n,i){var l=n.pendingProps,f=l.revealOrder,p=l.tail;if(ft(e,n,l.children,i),l=et.current,l&2)l=l&1|2,n.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=n.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&zm(e,i,n);else if(e.tag===19)zm(e,i,n);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===n)break e;for(;e.sibling===null;){if(e.return===null||e.return===n)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}switch(Me(et,l),f){case"forwards":for(i=n.child,f=null;i!==null;)e=i.alternate,e!==null&&_s(e)===null&&(f=i),i=i.sibling;i=f,i===null?(f=n.child,n.child=null):(f=i.sibling,i.sibling=null),jf(n,!1,f,i,p);break;case"backwards":for(i=null,f=n.child,n.child=null;f!==null;){if(e=f.alternate,e!==null&&_s(e)===null){n.child=f;break}e=f.sibling,f.sibling=i,i=f,f=e}jf(n,!0,i,null,p);break;case"together":jf(n,!1,null,null,void 0);break;default:n.memoizedState=null}return n.child}function Un(e,n,i){if(e!==null&&(n.dependencies=e.dependencies),Pr|=n.lanes,!(i&n.childLanes))if(e!==null){if(Ni(e,n,i,!1),(i&n.childLanes)===0)return null}else return null;if(e!==null&&n.child!==e.child)throw Error(o(153));if(n.child!==null){for(e=n.child,i=Cr(e,e.pendingProps),n.child=i,i.return=n;e.sibling!==null;)e=e.sibling,i=i.sibling=Cr(e,e.pendingProps),i.return=n;i.sibling=null}return n.child}function If(e,n){return e.lanes&n?!0:(e=e.dependencies,!!(e!==null&&Is(e)))}function GE(e,n,i){switch(n.tag){case 3:Ba(n,n.stateNode.containerInfo),kr(n,tt,e.memoizedState.cache),wi();break;case 27:case 5:Wa(n);break;case 4:Ba(n,n.stateNode.containerInfo);break;case 10:kr(n,n.type,n.memoizedProps.value);break;case 13:var l=n.memoizedState;if(l!==null)return l.dehydrated!==null?(yr(n),n.flags|=128,null):i&n.child.childLanes?Fm(e,n,i):(yr(n),e=Un(e,n,i),e!==null?e.sibling:null);yr(n);break;case 19:var f=(e.flags&128)!==0;if(l=(i&n.childLanes)!==0,l||(Ni(e,n,i,!1),l=(i&n.childLanes)!==0),f){if(l)return Lm(e,n,i);n.flags|=128}if(f=n.memoizedState,f!==null&&(f.rendering=null,f.tail=null,f.lastEffect=null),Me(et,et.current),l)break;return null;case 22:case 23:return n.lanes=0,Pm(e,n,i);case 24:kr(n,tt,e.memoizedState.cache)}return Un(e,n,i)}function jm(e,n,i){if(e!==null)if(e.memoizedProps!==n.pendingProps)lt=!0;else{if(!If(e,i)&&!(n.flags&128))return lt=!1,GE(e,n,i);lt=!!(e.flags&131072)}else lt=!1,Ce&&n.flags&1048576&&kh(n,ks,n.index);switch(n.lanes=0,n.tag){case 16:e:{e=n.pendingProps;var l=n.elementType,f=l._init;if(l=f(l._payload),n.type=l,typeof l=="function")Zf(l)?(e=ma(l,e),n.tag=1,n=Am(null,n,l,e,i)):(n.tag=0,n=Mf(null,n,l,e,i));else{if(l!=null){if(f=l.$$typeof,f===k){n.tag=11,n=_m(null,n,l,e,i);break e}else if(f===x){n.tag=14,n=Om(null,n,l,e,i);break e}}throw n=Q(l)||l,Error(o(306,n,""))}}return n;case 0:return Mf(e,n,n.type,n.pendingProps,i);case 1:return l=n.type,f=ma(l,n.pendingProps),Am(e,n,l,f,i);case 3:e:{if(Ba(n,n.stateNode.containerInfo),e===null)throw Error(o(387));var p=n.pendingProps;f=n.memoizedState,l=f.element,Vf(e,n),Fi(n,p,null,i);var b=n.memoizedState;if(p=b.cache,kr(n,tt,p),p!==f.cache&&Bf(n,[tt],i,!0),Ri(),p=b.element,f.isDehydrated)if(f={element:p,isDehydrated:!1,cache:b.cache},n.updateQueue.baseState=f,n.memoizedState=f,n.flags&256){n=Rm(e,n,p,i);break e}else if(p!==l){l=Xt(Error(o(424)),n),Si(l),n=Rm(e,n,p,i);break e}else for(ct=hn(n.stateNode.containerInfo.firstChild),Tt=n,Ce=!0,vn=null,En=!0,i=Mh(n,null,p,i),n.child=i;i;)i.flags=i.flags&-3|4096,i=i.sibling;else{if(wi(),p===l){n=Un(e,n,i);break e}ft(e,n,p,i)}n=n.child}return n;case 26:return Mi(e,n),e===null?(i=Hg(n.type,null,n.pendingProps,null))?n.memoizedState=i:Ce||(i=n.type,e=n.pendingProps,l=tu(jt.current).createElement(i),l[mt]=n,l[Mt]=e,dt(l,i,e),it(l),n.stateNode=l):n.memoizedState=Hg(n.type,e.memoizedProps,n.pendingProps,e.memoizedState),null;case 27:return Wa(n),e===null&&Ce&&(l=n.stateNode=jg(n.type,n.pendingProps,jt.current),Tt=n,En=!0,ct=hn(l.firstChild)),l=n.pendingProps.children,e!==null||Ce?ft(e,n,l,i):n.child=fa(n,null,l,i),Mi(e,n),n.child;case 5:return e===null&&Ce&&((f=l=ct)&&(l=_1(l,n.type,n.pendingProps,En),l!==null?(n.stateNode=l,Tt=n,ct=hn(l.firstChild),En=!1,f=!0):f=!1),f||ca(n)),Wa(n),f=n.type,p=n.pendingProps,b=e!==null?e.memoizedProps:null,l=p.children,xd(f,p)?l=null:b!==null&&xd(f,b)&&(n.flags|=32),n.memoizedState!==null&&(f=vf(e,n,VE,null,null,i),Xi._currentValue=f),Mi(e,n),ft(e,n,l,i),n.child;case 6:return e===null&&Ce&&((e=i=ct)&&(i=O1(i,n.pendingProps,En),i!==null?(n.stateNode=i,Tt=n,ct=null,e=!0):e=!1),e||ca(n)),null;case 13:return Fm(e,n,i);case 4:return Ba(n,n.stateNode.containerInfo),l=n.pendingProps,e===null?n.child=fa(n,null,l,i):ft(e,n,l,i),n.child;case 11:return _m(e,n,n.type,n.pendingProps,i);case 7:return ft(e,n,n.pendingProps,i),n.child;case 8:return ft(e,n,n.pendingProps.children,i),n.child;case 12:return ft(e,n,n.pendingProps.children,i),n.child;case 10:return l=n.pendingProps,kr(n,n.type,l.value),ft(e,n,l.children,i),n.child;case 9:return f=n.type._context,l=n.pendingProps.children,ya(n),f=gt(f),l=l(f),n.flags|=1,ft(e,n,l,i),n.child;case 14:return Om(e,n,n.type,n.pendingProps,i);case 15:return Cm(e,n,n.type,n.pendingProps,i);case 19:return Lm(e,n,i);case 22:return Pm(e,n,i);case 24:return ya(n),l=gt(tt),e===null?(f=ff(),f===null&&(f=ze,p=uf(),f.pooledCache=p,p.refCount++,p!==null&&(f.pooledCacheLanes|=i),f=p),n.memoizedState={parent:l,cache:f},Wf(n),kr(n,tt,f)):(e.lanes&i&&(Vf(e,n),Fi(n,null,null,i),Ri()),f=e.memoizedState,p=n.memoizedState,f.parent!==l?(f={parent:l,cache:l},n.memoizedState=f,n.lanes===0&&(n.memoizedState=n.updateQueue.baseState=f),kr(n,tt,l)):(l=p.cache,kr(n,tt,l),l!==f.cache&&Bf(n,[tt],i,!0))),ft(e,n,n.pendingProps.children,i),n.child;case 29:throw n.pendingProps}throw Error(o(156,n.tag))}var Yf=ce(null),ga=null,$n=null;function kr(e,n,i){Me(Yf,n._currentValue),n._currentValue=i}function Kn(e){e._currentValue=Yf.current,Oe(Yf)}function Hf(e,n,i){for(;e!==null;){var l=e.alternate;if((e.childLanes&n)!==n?(e.childLanes|=n,l!==null&&(l.childLanes|=n)):l!==null&&(l.childLanes&n)!==n&&(l.childLanes|=n),e===i)break;e=e.return}}function Bf(e,n,i,l){var f=e.child;for(f!==null&&(f.return=e);f!==null;){var p=f.dependencies;if(p!==null){var b=f.child;p=p.firstContext;e:for(;p!==null;){var D=p;p=f;for(var T=0;T<n.length;T++)if(D.context===n[T]){p.lanes|=i,D=p.alternate,D!==null&&(D.lanes|=i),Hf(p.return,i,e),l||(b=null);break e}p=D.next}}else if(f.tag===18){if(b=f.return,b===null)throw Error(o(341));b.lanes|=i,p=b.alternate,p!==null&&(p.lanes|=i),Hf(b,i,e),b=null}else b=f.child;if(b!==null)b.return=f;else for(b=f;b!==null;){if(b===e){b=null;break}if(f=b.sibling,f!==null){f.return=b.return,b=f;break}b=b.return}f=b}}function Ni(e,n,i,l){e=null;for(var f=n,p=!1;f!==null;){if(!p){if(f.flags&524288)p=!0;else if(f.flags&262144)break}if(f.tag===10){var b=f.alternate;if(b===null)throw Error(o(387));if(b=b.memoizedProps,b!==null){var D=f.type;Ht(f.pendingProps.value,b.value)||(e!==null?e.push(D):e=[D])}}else if(f===dr.current){if(b=f.alternate,b===null)throw Error(o(387));b.memoizedState.memoizedState!==f.memoizedState.memoizedState&&(e!==null?e.push(Xi):e=[Xi])}f=f.return}e!==null&&Bf(n,e,i,l),n.flags|=262144}function Is(e){for(e=e.firstContext;e!==null;){if(!Ht(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function ya(e){ga=e,$n=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function gt(e){return Im(ga,e)}function Ys(e,n){return ga===null&&ya(e),Im(e,n)}function Im(e,n){var i=n._currentValue;if(n={context:n,memoizedValue:i,next:null},$n===null){if(e===null)throw Error(o(308));$n=n,e.dependencies={lanes:0,firstContext:n},e.flags|=524288}else $n=$n.next=n;return i}var Er=!1;function Wf(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Vf(e,n){e=e.updateQueue,n.updateQueue===e&&(n.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Dr(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function xr(e,n,i){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,We&2){var f=l.pending;return f===null?n.next=n:(n.next=f.next,f.next=n),l.pending=n,n=ws(e),wh(e,null,i),n}return bs(e,l,n,i),ws(e)}function Ai(e,n,i){if(n=n.updateQueue,n!==null&&(n=n.shared,(i&4194176)!==0)){var l=n.lanes;l&=e.pendingLanes,i|=l,n.lanes=i,Op(e,i)}}function Uf(e,n){var i=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,i===l)){var f=null,p=null;if(i=i.firstBaseUpdate,i!==null){do{var b={lane:i.lane,tag:i.tag,payload:i.payload,callback:null,next:null};p===null?f=p=b:p=p.next=b,i=i.next}while(i!==null);p===null?f=p=n:p=p.next=n}else f=p=n;i={baseState:l.baseState,firstBaseUpdate:f,lastBaseUpdate:p,shared:l.shared,callbacks:l.callbacks},e.updateQueue=i;return}e=i.lastBaseUpdate,e===null?i.firstBaseUpdate=n:e.next=n,i.lastBaseUpdate=n}var $f=!1;function Ri(){if($f){var e=uo;if(e!==null)throw e}}function Fi(e,n,i,l){$f=!1;var f=e.updateQueue;Er=!1;var p=f.firstBaseUpdate,b=f.lastBaseUpdate,D=f.shared.pending;if(D!==null){f.shared.pending=null;var T=D,N=T.next;T.next=null,b===null?p=N:b.next=N,b=T;var K=e.alternate;K!==null&&(K=K.updateQueue,D=K.lastBaseUpdate,D!==b&&(D===null?K.firstBaseUpdate=N:D.next=N,K.lastBaseUpdate=T))}if(p!==null){var Z=f.baseState;b=0,K=N=T=null,D=p;do{var Y=D.lane&-536870913,U=Y!==D.lane;if(U?(_e&Y)===Y:(l&Y)===Y){Y!==0&&Y===so&&($f=!0),K!==null&&(K=K.next={lane:0,tag:D.tag,payload:D.payload,callback:null,next:null});e:{var pe=e,we=D;Y=n;var Ke=i;switch(we.tag){case 1:if(pe=we.payload,typeof pe=="function"){Z=pe.call(Ke,Z,Y);break e}Z=pe;break e;case 3:pe.flags=pe.flags&-65537|128;case 0:if(pe=we.payload,Y=typeof pe=="function"?pe.call(Ke,Z,Y):pe,Y==null)break e;Z=V({},Z,Y);break e;case 2:Er=!0}}Y=D.callback,Y!==null&&(e.flags|=64,U&&(e.flags|=8192),U=f.callbacks,U===null?f.callbacks=[Y]:U.push(Y))}else U={lane:Y,tag:D.tag,payload:D.payload,callback:D.callback,next:null},K===null?(N=K=U,T=Z):K=K.next=U,b|=Y;if(D=D.next,D===null){if(D=f.shared.pending,D===null)break;U=D,D=U.next,U.next=null,f.lastBaseUpdate=U,f.shared.pending=null}}while(!0);K===null&&(T=Z),f.baseState=T,f.firstBaseUpdate=N,f.lastBaseUpdate=K,p===null&&(f.shared.lanes=0),Pr|=b,e.lanes=b,e.memoizedState=Z}}function Ym(e,n){if(typeof e!="function")throw Error(o(191,e));e.call(n)}function Hm(e,n){var i=e.callbacks;if(i!==null)for(e.callbacks=null,e=0;e<i.length;e++)Ym(i[e],n)}function zi(e,n){try{var i=n.updateQueue,l=i!==null?i.lastEffect:null;if(l!==null){var f=l.next;i=f;do{if((i.tag&e)===e){l=void 0;var p=i.create,b=i.inst;l=p(),b.destroy=l}i=i.next}while(i!==f)}}catch(D){Re(n,n.return,D)}}function Tr(e,n,i){try{var l=n.updateQueue,f=l!==null?l.lastEffect:null;if(f!==null){var p=f.next;l=p;do{if((l.tag&e)===e){var b=l.inst,D=b.destroy;if(D!==void 0){b.destroy=void 0,f=n;var T=i;try{D()}catch(N){Re(f,T,N)}}}l=l.next}while(l!==p)}}catch(N){Re(n,n.return,N)}}function Bm(e){var n=e.updateQueue;if(n!==null){var i=e.stateNode;try{Hm(n,i)}catch(l){Re(e,e.return,l)}}}function Wm(e,n,i){i.props=ma(e.type,e.memoizedProps),i.state=e.memoizedState;try{i.componentWillUnmount()}catch(l){Re(e,n,l)}}function ba(e,n){try{var i=e.ref;if(i!==null){var l=e.stateNode;switch(e.tag){case 26:case 27:case 5:var f=l;break;default:f=l}typeof i=="function"?e.refCleanup=i(f):i.current=f}}catch(p){Re(e,n,p)}}function Bt(e,n){var i=e.ref,l=e.refCleanup;if(i!==null)if(typeof l=="function")try{l()}catch(f){Re(e,n,f)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof i=="function")try{i(null)}catch(f){Re(e,n,f)}else i.current=null}function Vm(e){var n=e.type,i=e.memoizedProps,l=e.stateNode;try{e:switch(n){case"button":case"input":case"select":case"textarea":i.autoFocus&&l.focus();break e;case"img":i.src?l.src=i.src:i.srcSet&&(l.srcset=i.srcSet)}}catch(f){Re(e,e.return,f)}}function Um(e,n,i){try{var l=e.stateNode;k1(l,e.type,i,n),l[Mt]=n}catch(f){Re(e,e.return,f)}}function $m(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27||e.tag===4}function Kf(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||$m(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==27&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function qf(e,n,i){var l=e.tag;if(l===5||l===6)e=e.stateNode,n?i.nodeType===8?i.parentNode.insertBefore(e,n):i.insertBefore(e,n):(i.nodeType===8?(n=i.parentNode,n.insertBefore(e,i)):(n=i,n.appendChild(e)),i=i._reactRootContainer,i!=null||n.onclick!==null||(n.onclick=eu));else if(l!==4&&l!==27&&(e=e.child,e!==null))for(qf(e,n,i),e=e.sibling;e!==null;)qf(e,n,i),e=e.sibling}function Hs(e,n,i){var l=e.tag;if(l===5||l===6)e=e.stateNode,n?i.insertBefore(e,n):i.appendChild(e);else if(l!==4&&l!==27&&(e=e.child,e!==null))for(Hs(e,n,i),e=e.sibling;e!==null;)Hs(e,n,i),e=e.sibling}var qn=!1,Ue=!1,Qf=!1,Km=typeof WeakSet=="function"?WeakSet:Set,st=null,qm=!1;function JE(e,n){if(e=e.containerInfo,Ed=lu,e=fh(e),Xc(e)){if("selectionStart"in e)var i={start:e.selectionStart,end:e.selectionEnd};else e:{i=(i=e.ownerDocument)&&i.defaultView||window;var l=i.getSelection&&i.getSelection();if(l&&l.rangeCount!==0){i=l.anchorNode;var f=l.anchorOffset,p=l.focusNode;l=l.focusOffset;try{i.nodeType,p.nodeType}catch{i=null;break e}var b=0,D=-1,T=-1,N=0,K=0,Z=e,Y=null;t:for(;;){for(var U;Z!==i||f!==0&&Z.nodeType!==3||(D=b+f),Z!==p||l!==0&&Z.nodeType!==3||(T=b+l),Z.nodeType===3&&(b+=Z.nodeValue.length),(U=Z.firstChild)!==null;)Y=Z,Z=U;for(;;){if(Z===e)break t;if(Y===i&&++N===f&&(D=b),Y===p&&++K===l&&(T=b),(U=Z.nextSibling)!==null)break;Z=Y,Y=Z.parentNode}Z=U}i=D===-1||T===-1?null:{start:D,end:T}}else i=null}i=i||{start:0,end:0}}else i=null;for(Dd={focusedElem:e,selectionRange:i},lu=!1,st=n;st!==null;)if(n=st,e=n.child,(n.subtreeFlags&1028)!==0&&e!==null)e.return=n,st=e;else for(;st!==null;){switch(n=st,p=n.alternate,e=n.flags,n.tag){case 0:break;case 11:case 15:break;case 1:if(e&1024&&p!==null){e=void 0,i=n,f=p.memoizedProps,p=p.memoizedState,l=i.stateNode;try{var pe=ma(i.type,f,i.elementType===i.type);e=l.getSnapshotBeforeUpdate(pe,p),l.__reactInternalSnapshotBeforeUpdate=e}catch(we){Re(i,i.return,we)}}break;case 3:if(e&1024){if(e=n.stateNode.containerInfo,i=e.nodeType,i===9)Od(e);else if(i===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Od(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if(e&1024)throw Error(o(163))}if(e=n.sibling,e!==null){e.return=n.return,st=e;break}st=n.return}return pe=qm,qm=!1,pe}function Qm(e,n,i){var l=i.flags;switch(i.tag){case 0:case 11:case 15:Xn(e,i),l&4&&zi(5,i);break;case 1:if(Xn(e,i),l&4)if(e=i.stateNode,n===null)try{e.componentDidMount()}catch(D){Re(i,i.return,D)}else{var f=ma(i.type,n.memoizedProps);n=n.memoizedState;try{e.componentDidUpdate(f,n,e.__reactInternalSnapshotBeforeUpdate)}catch(D){Re(i,i.return,D)}}l&64&&Bm(i),l&512&&ba(i,i.return);break;case 3:if(Xn(e,i),l&64&&(l=i.updateQueue,l!==null)){if(e=null,i.child!==null)switch(i.child.tag){case 27:case 5:e=i.child.stateNode;break;case 1:e=i.child.stateNode}try{Hm(l,e)}catch(D){Re(i,i.return,D)}}break;case 26:Xn(e,i),l&512&&ba(i,i.return);break;case 27:case 5:Xn(e,i),n===null&&l&4&&Vm(i),l&512&&ba(i,i.return);break;case 12:Xn(e,i);break;case 13:Xn(e,i),l&4&&Jm(e,i);break;case 22:if(f=i.memoizedState!==null||qn,!f){n=n!==null&&n.memoizedState!==null||Ue;var p=qn,b=Ue;qn=f,(Ue=n)&&!b?_r(e,i,(i.subtreeFlags&8772)!==0):Xn(e,i),qn=p,Ue=b}l&512&&(i.memoizedProps.mode==="manual"?ba(i,i.return):Bt(i,i.return));break;default:Xn(e,i)}}function Xm(e){var n=e.alternate;n!==null&&(e.alternate=null,Xm(n)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(n=e.stateNode,n!==null&&Rc(n)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Ze=null,Wt=!1;function Qn(e,n,i){for(i=i.child;i!==null;)Gm(e,n,i),i=i.sibling}function Gm(e,n,i){if(It&&typeof It.onCommitFiberUnmount=="function")try{It.onCommitFiberUnmount(oi,i)}catch{}switch(i.tag){case 26:Ue||Bt(i,n),Qn(e,n,i),i.memoizedState?i.memoizedState.count--:i.stateNode&&(i=i.stateNode,i.parentNode.removeChild(i));break;case 27:Ue||Bt(i,n);var l=Ze,f=Wt;for(Ze=i.stateNode,Qn(e,n,i),i=i.stateNode,n=i.attributes;n.length;)i.removeAttributeNode(n[0]);Rc(i),Ze=l,Wt=f;break;case 5:Ue||Bt(i,n);case 6:f=Ze;var p=Wt;if(Ze=null,Qn(e,n,i),Ze=f,Wt=p,Ze!==null)if(Wt)try{e=Ze,l=i.stateNode,e.nodeType===8?e.parentNode.removeChild(l):e.removeChild(l)}catch(b){Re(i,n,b)}else try{Ze.removeChild(i.stateNode)}catch(b){Re(i,n,b)}break;case 18:Ze!==null&&(Wt?(n=Ze,i=i.stateNode,n.nodeType===8?_d(n.parentNode,i):n.nodeType===1&&_d(n,i),el(n)):_d(Ze,i.stateNode));break;case 4:l=Ze,f=Wt,Ze=i.stateNode.containerInfo,Wt=!0,Qn(e,n,i),Ze=l,Wt=f;break;case 0:case 11:case 14:case 15:Ue||Tr(2,i,n),Ue||Tr(4,i,n),Qn(e,n,i);break;case 1:Ue||(Bt(i,n),l=i.stateNode,typeof l.componentWillUnmount=="function"&&Wm(i,n,l)),Qn(e,n,i);break;case 21:Qn(e,n,i);break;case 22:Ue||Bt(i,n),Ue=(l=Ue)||i.memoizedState!==null,Qn(e,n,i),Ue=l;break;default:Qn(e,n,i)}}function Jm(e,n){if(n.memoizedState===null&&(e=n.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{el(e)}catch(i){Re(n,n.return,i)}}function ZE(e){switch(e.tag){case 13:case 19:var n=e.stateNode;return n===null&&(n=e.stateNode=new Km),n;case 22:return e=e.stateNode,n=e._retryCache,n===null&&(n=e._retryCache=new Km),n;default:throw Error(o(435,e.tag))}}function Xf(e,n){var i=ZE(e);n.forEach(function(l){var f=d1.bind(null,e,l);i.has(l)||(i.add(l),l.then(f,f))})}function en(e,n){var i=n.deletions;if(i!==null)for(var l=0;l<i.length;l++){var f=i[l],p=e,b=n,D=b;e:for(;D!==null;){switch(D.tag){case 27:case 5:Ze=D.stateNode,Wt=!1;break e;case 3:Ze=D.stateNode.containerInfo,Wt=!0;break e;case 4:Ze=D.stateNode.containerInfo,Wt=!0;break e}D=D.return}if(Ze===null)throw Error(o(160));Gm(p,b,f),Ze=null,Wt=!1,p=f.alternate,p!==null&&(p.return=null),f.return=null}if(n.subtreeFlags&13878)for(n=n.child;n!==null;)Zm(n,e),n=n.sibling}var pn=null;function Zm(e,n){var i=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:en(n,e),tn(e),l&4&&(Tr(3,e,e.return),zi(3,e),Tr(5,e,e.return));break;case 1:en(n,e),tn(e),l&512&&(Ue||i===null||Bt(i,i.return)),l&64&&qn&&(e=e.updateQueue,e!==null&&(l=e.callbacks,l!==null&&(i=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=i===null?l:i.concat(l))));break;case 26:var f=pn;if(en(n,e),tn(e),l&512&&(Ue||i===null||Bt(i,i.return)),l&4){var p=i!==null?i.memoizedState:null;if(l=e.memoizedState,i===null)if(l===null)if(e.stateNode===null){e:{l=e.type,i=e.memoizedProps,f=f.ownerDocument||f;t:switch(l){case"title":p=f.getElementsByTagName("title")[0],(!p||p[si]||p[mt]||p.namespaceURI==="http://www.w3.org/2000/svg"||p.hasAttribute("itemprop"))&&(p=f.createElement(l),f.head.insertBefore(p,f.querySelector("head > title"))),dt(p,l,i),p[mt]=e,it(p),l=p;break e;case"link":var b=Vg("link","href",f).get(l+(i.href||""));if(b){for(var D=0;D<b.length;D++)if(p=b[D],p.getAttribute("href")===(i.href==null?null:i.href)&&p.getAttribute("rel")===(i.rel==null?null:i.rel)&&p.getAttribute("title")===(i.title==null?null:i.title)&&p.getAttribute("crossorigin")===(i.crossOrigin==null?null:i.crossOrigin)){b.splice(D,1);break t}}p=f.createElement(l),dt(p,l,i),f.head.appendChild(p);break;case"meta":if(b=Vg("meta","content",f).get(l+(i.content||""))){for(D=0;D<b.length;D++)if(p=b[D],p.getAttribute("content")===(i.content==null?null:""+i.content)&&p.getAttribute("name")===(i.name==null?null:i.name)&&p.getAttribute("property")===(i.property==null?null:i.property)&&p.getAttribute("http-equiv")===(i.httpEquiv==null?null:i.httpEquiv)&&p.getAttribute("charset")===(i.charSet==null?null:i.charSet)){b.splice(D,1);break t}}p=f.createElement(l),dt(p,l,i),f.head.appendChild(p);break;default:throw Error(o(468,l))}p[mt]=e,it(p),l=p}e.stateNode=l}else Ug(f,e.type,e.stateNode);else e.stateNode=Wg(f,l,e.memoizedProps);else p!==l?(p===null?i.stateNode!==null&&(i=i.stateNode,i.parentNode.removeChild(i)):p.count--,l===null?Ug(f,e.type,e.stateNode):Wg(f,l,e.memoizedProps)):l===null&&e.stateNode!==null&&Um(e,e.memoizedProps,i.memoizedProps)}break;case 27:if(l&4&&e.alternate===null){f=e.stateNode,p=e.memoizedProps;try{for(var T=f.firstChild;T;){var N=T.nextSibling,K=T.nodeName;T[si]||K==="HEAD"||K==="BODY"||K==="SCRIPT"||K==="STYLE"||K==="LINK"&&T.rel.toLowerCase()==="stylesheet"||f.removeChild(T),T=N}for(var Z=e.type,Y=f.attributes;Y.length;)f.removeAttributeNode(Y[0]);dt(f,Z,p),f[mt]=e,f[Mt]=p}catch(pe){Re(e,e.return,pe)}}case 5:if(en(n,e),tn(e),l&512&&(Ue||i===null||Bt(i,i.return)),e.flags&32){f=e.stateNode;try{Xa(f,"")}catch(pe){Re(e,e.return,pe)}}l&4&&e.stateNode!=null&&(f=e.memoizedProps,Um(e,f,i!==null?i.memoizedProps:f)),l&1024&&(Qf=!0);break;case 6:if(en(n,e),tn(e),l&4){if(e.stateNode===null)throw Error(o(162));l=e.memoizedProps,i=e.stateNode;try{i.nodeValue=l}catch(pe){Re(e,e.return,pe)}}break;case 3:if(au=null,f=pn,pn=nu(n.containerInfo),en(n,e),pn=f,tn(e),l&4&&i!==null&&i.memoizedState.isDehydrated)try{el(n.containerInfo)}catch(pe){Re(e,e.return,pe)}Qf&&(Qf=!1,eg(e));break;case 4:l=pn,pn=nu(e.stateNode.containerInfo),en(n,e),tn(e),pn=l;break;case 12:en(n,e),tn(e);break;case 13:en(n,e),tn(e),e.child.flags&8192&&e.memoizedState!==null!=(i!==null&&i.memoizedState!==null)&&(od=Pt()),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,Xf(e,l)));break;case 22:if(l&512&&(Ue||i===null||Bt(i,i.return)),T=e.memoizedState!==null,N=i!==null&&i.memoizedState!==null,K=qn,Z=Ue,qn=K||T,Ue=Z||N,en(n,e),Ue=Z,qn=K,tn(e),n=e.stateNode,n._current=e,n._visibility&=-3,n._visibility|=n._pendingVisibility&2,l&8192&&(n._visibility=T?n._visibility&-2:n._visibility|1,T&&(n=qn||Ue,i===null||N||n||po(e)),e.memoizedProps===null||e.memoizedProps.mode!=="manual"))e:for(i=null,n=e;;){if(n.tag===5||n.tag===26||n.tag===27){if(i===null){N=i=n;try{if(f=N.stateNode,T)p=f.style,typeof p.setProperty=="function"?p.setProperty("display","none","important"):p.display="none";else{b=N.stateNode,D=N.memoizedProps.style;var U=D!=null&&D.hasOwnProperty("display")?D.display:null;b.style.display=U==null||typeof U=="boolean"?"":(""+U).trim()}}catch(pe){Re(N,N.return,pe)}}}else if(n.tag===6){if(i===null){N=n;try{N.stateNode.nodeValue=T?"":N.memoizedProps}catch(pe){Re(N,N.return,pe)}}}else if((n.tag!==22&&n.tag!==23||n.memoizedState===null||n===e)&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break e;for(;n.sibling===null;){if(n.return===null||n.return===e)break e;i===n&&(i=null),n=n.return}i===n&&(i=null),n.sibling.return=n.return,n=n.sibling}l&4&&(l=e.updateQueue,l!==null&&(i=l.retryQueue,i!==null&&(l.retryQueue=null,Xf(e,i))));break;case 19:en(n,e),tn(e),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,Xf(e,l)));break;case 21:break;default:en(n,e),tn(e)}}function tn(e){var n=e.flags;if(n&2){try{if(e.tag!==27){e:{for(var i=e.return;i!==null;){if($m(i)){var l=i;break e}i=i.return}throw Error(o(160))}switch(l.tag){case 27:var f=l.stateNode,p=Kf(e);Hs(e,p,f);break;case 5:var b=l.stateNode;l.flags&32&&(Xa(b,""),l.flags&=-33);var D=Kf(e);Hs(e,D,b);break;case 3:case 4:var T=l.stateNode.containerInfo,N=Kf(e);qf(e,N,T);break;default:throw Error(o(161))}}}catch(K){Re(e,e.return,K)}e.flags&=-3}n&4096&&(e.flags&=-4097)}function eg(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var n=e;eg(n),n.tag===5&&n.flags&1024&&n.stateNode.reset(),e=e.sibling}}function Xn(e,n){if(n.subtreeFlags&8772)for(n=n.child;n!==null;)Qm(e,n.alternate,n),n=n.sibling}function po(e){for(e=e.child;e!==null;){var n=e;switch(n.tag){case 0:case 11:case 14:case 15:Tr(4,n,n.return),po(n);break;case 1:Bt(n,n.return);var i=n.stateNode;typeof i.componentWillUnmount=="function"&&Wm(n,n.return,i),po(n);break;case 26:case 27:case 5:Bt(n,n.return),po(n);break;case 22:Bt(n,n.return),n.memoizedState===null&&po(n);break;default:po(n)}e=e.sibling}}function _r(e,n,i){for(i=i&&(n.subtreeFlags&8772)!==0,n=n.child;n!==null;){var l=n.alternate,f=e,p=n,b=p.flags;switch(p.tag){case 0:case 11:case 15:_r(f,p,i),zi(4,p);break;case 1:if(_r(f,p,i),l=p,f=l.stateNode,typeof f.componentDidMount=="function")try{f.componentDidMount()}catch(N){Re(l,l.return,N)}if(l=p,f=l.updateQueue,f!==null){var D=l.stateNode;try{var T=f.shared.hiddenCallbacks;if(T!==null)for(f.shared.hiddenCallbacks=null,f=0;f<T.length;f++)Ym(T[f],D)}catch(N){Re(l,l.return,N)}}i&&b&64&&Bm(p),ba(p,p.return);break;case 26:case 27:case 5:_r(f,p,i),i&&l===null&&b&4&&Vm(p),ba(p,p.return);break;case 12:_r(f,p,i);break;case 13:_r(f,p,i),i&&b&4&&Jm(f,p);break;case 22:p.memoizedState===null&&_r(f,p,i),ba(p,p.return);break;default:_r(f,p,i)}n=n.sibling}}function Gf(e,n){var i=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(i=e.memoizedState.cachePool.pool),e=null,n.memoizedState!==null&&n.memoizedState.cachePool!==null&&(e=n.memoizedState.cachePool.pool),e!==i&&(e!=null&&e.refCount++,i!=null&&Ti(i))}function Jf(e,n){e=null,n.alternate!==null&&(e=n.alternate.memoizedState.cache),n=n.memoizedState.cache,n!==e&&(n.refCount++,e!=null&&Ti(e))}function Or(e,n,i,l){if(n.subtreeFlags&10256)for(n=n.child;n!==null;)tg(e,n,i,l),n=n.sibling}function tg(e,n,i,l){var f=n.flags;switch(n.tag){case 0:case 11:case 15:Or(e,n,i,l),f&2048&&zi(9,n);break;case 3:Or(e,n,i,l),f&2048&&(e=null,n.alternate!==null&&(e=n.alternate.memoizedState.cache),n=n.memoizedState.cache,n!==e&&(n.refCount++,e!=null&&Ti(e)));break;case 12:if(f&2048){Or(e,n,i,l),e=n.stateNode;try{var p=n.memoizedProps,b=p.id,D=p.onPostCommit;typeof D=="function"&&D(b,n.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(T){Re(n,n.return,T)}}else Or(e,n,i,l);break;case 23:break;case 22:p=n.stateNode,n.memoizedState!==null?p._visibility&4?Or(e,n,i,l):Li(e,n):p._visibility&4?Or(e,n,i,l):(p._visibility|=4,ho(e,n,i,l,(n.subtreeFlags&10256)!==0)),f&2048&&Gf(n.alternate,n);break;case 24:Or(e,n,i,l),f&2048&&Jf(n.alternate,n);break;default:Or(e,n,i,l)}}function ho(e,n,i,l,f){for(f=f&&(n.subtreeFlags&10256)!==0,n=n.child;n!==null;){var p=e,b=n,D=i,T=l,N=b.flags;switch(b.tag){case 0:case 11:case 15:ho(p,b,D,T,f),zi(8,b);break;case 23:break;case 22:var K=b.stateNode;b.memoizedState!==null?K._visibility&4?ho(p,b,D,T,f):Li(p,b):(K._visibility|=4,ho(p,b,D,T,f)),f&&N&2048&&Gf(b.alternate,b);break;case 24:ho(p,b,D,T,f),f&&N&2048&&Jf(b.alternate,b);break;default:ho(p,b,D,T,f)}n=n.sibling}}function Li(e,n){if(n.subtreeFlags&10256)for(n=n.child;n!==null;){var i=e,l=n,f=l.flags;switch(l.tag){case 22:Li(i,l),f&2048&&Gf(l.alternate,l);break;case 24:Li(i,l),f&2048&&Jf(l.alternate,l);break;default:Li(i,l)}n=n.sibling}}var ji=8192;function mo(e){if(e.subtreeFlags&ji)for(e=e.child;e!==null;)ng(e),e=e.sibling}function ng(e){switch(e.tag){case 26:mo(e),e.flags&ji&&e.memoizedState!==null&&H1(pn,e.memoizedState,e.memoizedProps);break;case 5:mo(e);break;case 3:case 4:var n=pn;pn=nu(e.stateNode.containerInfo),mo(e),pn=n;break;case 22:e.memoizedState===null&&(n=e.alternate,n!==null&&n.memoizedState!==null?(n=ji,ji=16777216,mo(e),ji=n):mo(e));break;default:mo(e)}}function rg(e){var n=e.alternate;if(n!==null&&(e=n.child,e!==null)){n.child=null;do n=e.sibling,e.sibling=null,e=n;while(e!==null)}}function Ii(e){var n=e.deletions;if(e.flags&16){if(n!==null)for(var i=0;i<n.length;i++){var l=n[i];st=l,og(l,e)}rg(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)ag(e),e=e.sibling}function ag(e){switch(e.tag){case 0:case 11:case 15:Ii(e),e.flags&2048&&Tr(9,e,e.return);break;case 3:Ii(e);break;case 12:Ii(e);break;case 22:var n=e.stateNode;e.memoizedState!==null&&n._visibility&4&&(e.return===null||e.return.tag!==13)?(n._visibility&=-5,Bs(e)):Ii(e);break;default:Ii(e)}}function Bs(e){var n=e.deletions;if(e.flags&16){if(n!==null)for(var i=0;i<n.length;i++){var l=n[i];st=l,og(l,e)}rg(e)}for(e=e.child;e!==null;){switch(n=e,n.tag){case 0:case 11:case 15:Tr(8,n,n.return),Bs(n);break;case 22:i=n.stateNode,i._visibility&4&&(i._visibility&=-5,Bs(n));break;default:Bs(n)}e=e.sibling}}function og(e,n){for(;st!==null;){var i=st;switch(i.tag){case 0:case 11:case 15:Tr(8,i,n);break;case 23:case 22:if(i.memoizedState!==null&&i.memoizedState.cachePool!==null){var l=i.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:Ti(i.memoizedState.cache)}if(l=i.child,l!==null)l.return=i,st=l;else e:for(i=e;st!==null;){l=st;var f=l.sibling,p=l.return;if(Xm(l),l===i){st=null;break e}if(f!==null){f.return=p,st=f;break e}st=p}}}function e1(e,n,i,l){this.tag=e,this.key=i,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=n,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function nn(e,n,i,l){return new e1(e,n,i,l)}function Zf(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Cr(e,n){var i=e.alternate;return i===null?(i=nn(e.tag,n,e.key,e.mode),i.elementType=e.elementType,i.type=e.type,i.stateNode=e.stateNode,i.alternate=e,e.alternate=i):(i.pendingProps=n,i.type=e.type,i.flags=0,i.subtreeFlags=0,i.deletions=null),i.flags=e.flags&31457280,i.childLanes=e.childLanes,i.lanes=e.lanes,i.child=e.child,i.memoizedProps=e.memoizedProps,i.memoizedState=e.memoizedState,i.updateQueue=e.updateQueue,n=e.dependencies,i.dependencies=n===null?null:{lanes:n.lanes,firstContext:n.firstContext},i.sibling=e.sibling,i.index=e.index,i.ref=e.ref,i.refCleanup=e.refCleanup,i}function ig(e,n){e.flags&=31457282;var i=e.alternate;return i===null?(e.childLanes=0,e.lanes=n,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=i.childLanes,e.lanes=i.lanes,e.child=i.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=i.memoizedProps,e.memoizedState=i.memoizedState,e.updateQueue=i.updateQueue,e.type=i.type,n=i.dependencies,e.dependencies=n===null?null:{lanes:n.lanes,firstContext:n.firstContext}),e}function Ws(e,n,i,l,f,p){var b=0;if(l=e,typeof e=="function")Zf(e)&&(b=1);else if(typeof e=="string")b=I1(e,i,ot.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case v:return wa(i.children,f,p,n);case h:b=8,f|=24;break;case m:return e=nn(12,i,n,f|2),e.elementType=m,e.lanes=p,e;case S:return e=nn(13,i,n,f),e.elementType=S,e.lanes=p,e;case E:return e=nn(19,i,n,f),e.elementType=E,e.lanes=p,e;case F:return lg(i,f,p,n);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case g:case w:b=10;break e;case y:b=9;break e;case k:b=11;break e;case x:b=14;break e;case _:b=16,l=null;break e}b=29,i=Error(o(130,e===null?"null":typeof e,"")),l=null}return n=nn(b,i,n,f),n.elementType=e,n.type=l,n.lanes=p,n}function wa(e,n,i,l){return e=nn(7,e,l,n),e.lanes=i,e}function lg(e,n,i,l){e=nn(22,e,l,n),e.elementType=F,e.lanes=i;var f={_visibility:1,_pendingVisibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null,_current:null,detach:function(){var p=f._current;if(p===null)throw Error(o(456));if(!(f._pendingVisibility&2)){var b=gr(p,2);b!==null&&(f._pendingVisibility|=2,_t(b,p,2))}},attach:function(){var p=f._current;if(p===null)throw Error(o(456));if(f._pendingVisibility&2){var b=gr(p,2);b!==null&&(f._pendingVisibility&=-3,_t(b,p,2))}}};return e.stateNode=f,e}function ed(e,n,i){return e=nn(6,e,null,n),e.lanes=i,e}function td(e,n,i){return n=nn(4,e.children!==null?e.children:[],e.key,n),n.lanes=i,n.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},n}function Gn(e){e.flags|=4}function sg(e,n){if(n.type!=="stylesheet"||n.state.loading&4)e.flags&=-16777217;else if(e.flags|=16777216,!$g(n)){if(n=Zt.current,n!==null&&((_e&4194176)===_e?Dn!==null:(_e&62914560)!==_e&&!(_e&536870912)||n!==Dn))throw Ei=of,xh;e.flags|=8192}}function Vs(e,n){n!==null&&(e.flags|=4),e.flags&16384&&(n=e.tag!==22?Tp():536870912,e.lanes|=n,yo|=n)}function Yi(e,n){if(!Ce)switch(e.tailMode){case"hidden":n=e.tail;for(var i=null;n!==null;)n.alternate!==null&&(i=n),n=n.sibling;i===null?e.tail=null:i.sibling=null;break;case"collapsed":i=e.tail;for(var l=null;i!==null;)i.alternate!==null&&(l=i),i=i.sibling;l===null?n||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function Be(e){var n=e.alternate!==null&&e.alternate.child===e.child,i=0,l=0;if(n)for(var f=e.child;f!==null;)i|=f.lanes|f.childLanes,l|=f.subtreeFlags&31457280,l|=f.flags&31457280,f.return=e,f=f.sibling;else for(f=e.child;f!==null;)i|=f.lanes|f.childLanes,l|=f.subtreeFlags,l|=f.flags,f.return=e,f=f.sibling;return e.subtreeFlags|=l,e.childLanes=i,n}function t1(e,n,i){var l=n.pendingProps;switch(rf(n),n.tag){case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Be(n),null;case 1:return Be(n),null;case 3:return i=n.stateNode,l=null,e!==null&&(l=e.memoizedState.cache),n.memoizedState.cache!==l&&(n.flags|=2048),Kn(tt),jn(),i.pendingContext&&(i.context=i.pendingContext,i.pendingContext=null),(e===null||e.child===null)&&(bi(n)?Gn(n):e===null||e.memoizedState.isDehydrated&&!(n.flags&256)||(n.flags|=1024,vn!==null&&(ud(vn),vn=null))),Be(n),null;case 26:return i=n.memoizedState,e===null?(Gn(n),i!==null?(Be(n),sg(n,i)):(Be(n),n.flags&=-16777217)):i?i!==e.memoizedState?(Gn(n),Be(n),sg(n,i)):(Be(n),n.flags&=-16777217):(e.memoizedProps!==l&&Gn(n),Be(n),n.flags&=-16777217),null;case 27:Va(n),i=jt.current;var f=n.type;if(e!==null&&n.stateNode!=null)e.memoizedProps!==l&&Gn(n);else{if(!l){if(n.stateNode===null)throw Error(o(166));return Be(n),null}e=ot.current,bi(n)?Eh(n):(e=jg(f,l,i),n.stateNode=e,Gn(n))}return Be(n),null;case 5:if(Va(n),i=n.type,e!==null&&n.stateNode!=null)e.memoizedProps!==l&&Gn(n);else{if(!l){if(n.stateNode===null)throw Error(o(166));return Be(n),null}if(e=ot.current,bi(n))Eh(n);else{switch(f=tu(jt.current),e){case 1:e=f.createElementNS("http://www.w3.org/2000/svg",i);break;case 2:e=f.createElementNS("http://www.w3.org/1998/Math/MathML",i);break;default:switch(i){case"svg":e=f.createElementNS("http://www.w3.org/2000/svg",i);break;case"math":e=f.createElementNS("http://www.w3.org/1998/Math/MathML",i);break;case"script":e=f.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof l.is=="string"?f.createElement("select",{is:l.is}):f.createElement("select"),l.multiple?e.multiple=!0:l.size&&(e.size=l.size);break;default:e=typeof l.is=="string"?f.createElement(i,{is:l.is}):f.createElement(i)}}e[mt]=n,e[Mt]=l;e:for(f=n.child;f!==null;){if(f.tag===5||f.tag===6)e.appendChild(f.stateNode);else if(f.tag!==4&&f.tag!==27&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===n)break e;for(;f.sibling===null;){if(f.return===null||f.return===n)break e;f=f.return}f.sibling.return=f.return,f=f.sibling}n.stateNode=e;e:switch(dt(e,i,l),i){case"button":case"input":case"select":case"textarea":e=!!l.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Gn(n)}}return Be(n),n.flags&=-16777217,null;case 6:if(e&&n.stateNode!=null)e.memoizedProps!==l&&Gn(n);else{if(typeof l!="string"&&n.stateNode===null)throw Error(o(166));if(e=jt.current,bi(n)){if(e=n.stateNode,i=n.memoizedProps,l=null,f=Tt,f!==null)switch(f.tag){case 27:case 5:l=f.memoizedProps}e[mt]=n,e=!!(e.nodeValue===i||l!==null&&l.suppressHydrationWarning===!0||Ng(e.nodeValue,i)),e||ca(n)}else e=tu(e).createTextNode(l),e[mt]=n,n.stateNode=e}return Be(n),null;case 13:if(l=n.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(f=bi(n),l!==null&&l.dehydrated!==null){if(e===null){if(!f)throw Error(o(318));if(f=n.memoizedState,f=f!==null?f.dehydrated:null,!f)throw Error(o(317));f[mt]=n}else wi(),!(n.flags&128)&&(n.memoizedState=null),n.flags|=4;Be(n),f=!1}else vn!==null&&(ud(vn),vn=null),f=!0;if(!f)return n.flags&256?(Wn(n),n):(Wn(n),null)}if(Wn(n),n.flags&128)return n.lanes=i,n;if(i=l!==null,e=e!==null&&e.memoizedState!==null,i){l=n.child,f=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(f=l.alternate.memoizedState.cachePool.pool);var p=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(p=l.memoizedState.cachePool.pool),p!==f&&(l.flags|=2048)}return i!==e&&i&&(n.child.flags|=8192),Vs(n,n.updateQueue),Be(n),null;case 4:return jn(),e===null&&wd(n.stateNode.containerInfo),Be(n),null;case 10:return Kn(n.type),Be(n),null;case 19:if(Oe(et),f=n.memoizedState,f===null)return Be(n),null;if(l=(n.flags&128)!==0,p=f.rendering,p===null)if(l)Yi(f,!1);else{if($e!==0||e!==null&&e.flags&128)for(e=n.child;e!==null;){if(p=_s(e),p!==null){for(n.flags|=128,Yi(f,!1),e=p.updateQueue,n.updateQueue=e,Vs(n,e),n.subtreeFlags=0,e=i,i=n.child;i!==null;)ig(i,e),i=i.sibling;return Me(et,et.current&1|2),n.child}e=e.sibling}f.tail!==null&&Pt()>Us&&(n.flags|=128,l=!0,Yi(f,!1),n.lanes=4194304)}else{if(!l)if(e=_s(p),e!==null){if(n.flags|=128,l=!0,e=e.updateQueue,n.updateQueue=e,Vs(n,e),Yi(f,!0),f.tail===null&&f.tailMode==="hidden"&&!p.alternate&&!Ce)return Be(n),null}else 2*Pt()-f.renderingStartTime>Us&&i!==536870912&&(n.flags|=128,l=!0,Yi(f,!1),n.lanes=4194304);f.isBackwards?(p.sibling=n.child,n.child=p):(e=f.last,e!==null?e.sibling=p:n.child=p,f.last=p)}return f.tail!==null?(n=f.tail,f.rendering=n,f.tail=n.sibling,f.renderingStartTime=Pt(),n.sibling=null,e=et.current,Me(et,l?e&1|2:e&1),n):(Be(n),null);case 22:case 23:return Wn(n),sf(),l=n.memoizedState!==null,e!==null?e.memoizedState!==null!==l&&(n.flags|=8192):l&&(n.flags|=8192),l?i&536870912&&!(n.flags&128)&&(Be(n),n.subtreeFlags&6&&(n.flags|=8192)):Be(n),i=n.updateQueue,i!==null&&Vs(n,i.retryQueue),i=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(i=e.memoizedState.cachePool.pool),l=null,n.memoizedState!==null&&n.memoizedState.cachePool!==null&&(l=n.memoizedState.cachePool.pool),l!==i&&(n.flags|=2048),e!==null&&Oe(da),null;case 24:return i=null,e!==null&&(i=e.memoizedState.cache),n.memoizedState.cache!==i&&(n.flags|=2048),Kn(tt),Be(n),null;case 25:return null}throw Error(o(156,n.tag))}function n1(e,n){switch(rf(n),n.tag){case 1:return e=n.flags,e&65536?(n.flags=e&-65537|128,n):null;case 3:return Kn(tt),jn(),e=n.flags,e&65536&&!(e&128)?(n.flags=e&-65537|128,n):null;case 26:case 27:case 5:return Va(n),null;case 13:if(Wn(n),e=n.memoizedState,e!==null&&e.dehydrated!==null){if(n.alternate===null)throw Error(o(340));wi()}return e=n.flags,e&65536?(n.flags=e&-65537|128,n):null;case 19:return Oe(et),null;case 4:return jn(),null;case 10:return Kn(n.type),null;case 22:case 23:return Wn(n),sf(),e!==null&&Oe(da),e=n.flags,e&65536?(n.flags=e&-65537|128,n):null;case 24:return Kn(tt),null;case 25:return null;default:return null}}function ug(e,n){switch(rf(n),n.tag){case 3:Kn(tt),jn();break;case 26:case 27:case 5:Va(n);break;case 4:jn();break;case 13:Wn(n);break;case 19:Oe(et);break;case 10:Kn(n.type);break;case 22:case 23:Wn(n),sf(),e!==null&&Oe(da);break;case 24:Kn(tt)}}var r1={getCacheForType:function(e){var n=gt(tt),i=n.data.get(e);return i===void 0&&(i=e(),n.data.set(e,i)),i}},a1=typeof WeakMap=="function"?WeakMap:Map,We=0,ze=null,De=null,_e=0,Le=0,Vt=null,Jn=!1,go=!1,nd=!1,Zn=0,$e=0,Pr=0,Sa=0,rd=0,rn=0,yo=0,Hi=null,Tn=null,ad=!1,od=0,Us=1/0,$s=null,Mr=null,Ks=!1,ka=null,Bi=0,id=0,ld=null,Wi=0,sd=null;function Ut(){if(We&2&&_e!==0)return _e&-_e;if(I.T!==null){var e=so;return e!==0?e:md()}return Pp()}function cg(){rn===0&&(rn=!(_e&536870912)||Ce?xp():536870912);var e=Zt.current;return e!==null&&(e.flags|=32),rn}function _t(e,n,i){(e===ze&&Le===2||e.cancelPendingCommit!==null)&&(bo(e,0),er(e,_e,rn,!1)),li(e,i),(!(We&2)||e!==ze)&&(e===ze&&(!(We&2)&&(Sa|=i),$e===4&&er(e,_e,rn,!1)),_n(e))}function fg(e,n,i){if(We&6)throw Error(o(327));var l=!i&&(n&60)===0&&(n&e.expiredLanes)===0||ii(e,n),f=l?l1(e,n):dd(e,n,!0),p=l;do{if(f===0){go&&!l&&er(e,n,0,!1);break}else if(f===6)er(e,n,0,!Jn);else{if(i=e.current.alternate,p&&!o1(i)){f=dd(e,n,!1),p=!1;continue}if(f===2){if(p=n,e.errorRecoveryDisabledLanes&p)var b=0;else b=e.pendingLanes&-536870913,b=b!==0?b:b&536870912?536870912:0;if(b!==0){n=b;e:{var D=e;f=Hi;var T=D.current.memoizedState.isDehydrated;if(T&&(bo(D,b).flags|=256),b=dd(D,b,!1),b!==2){if(nd&&!T){D.errorRecoveryDisabledLanes|=p,Sa|=p,f=4;break e}p=Tn,Tn=f,p!==null&&ud(p)}f=b}if(p=!1,f!==2)continue}}if(f===1){bo(e,0),er(e,n,0,!0);break}e:{switch(l=e,f){case 0:case 1:throw Error(o(345));case 4:if((n&4194176)===n){er(l,n,rn,!Jn);break e}break;case 2:Tn=null;break;case 3:case 5:break;default:throw Error(o(329))}if(l.finishedWork=i,l.finishedLanes=n,(n&62914560)===n&&(p=od+300-Pt(),10<p)){if(er(l,n,rn,!Jn),is(l,0)!==0)break e;l.timeoutHandle=Fg(dg.bind(null,l,i,Tn,$s,ad,n,rn,Sa,yo,Jn,2,-0,0),p);break e}dg(l,i,Tn,$s,ad,n,rn,Sa,yo,Jn,0,-0,0)}}break}while(!0);_n(e)}function ud(e){Tn===null?Tn=e:Tn.push.apply(Tn,e)}function dg(e,n,i,l,f,p,b,D,T,N,K,Z,Y){var U=n.subtreeFlags;if((U&8192||(U&16785408)===16785408)&&(Qi={stylesheets:null,count:0,unsuspend:Y1},ng(n),n=B1(),n!==null)){e.cancelPendingCommit=n(bg.bind(null,e,i,l,f,b,D,T,1,Z,Y)),er(e,p,b,!N);return}bg(e,i,l,f,b,D,T,K,Z,Y)}function o1(e){for(var n=e;;){var i=n.tag;if((i===0||i===11||i===15)&&n.flags&16384&&(i=n.updateQueue,i!==null&&(i=i.stores,i!==null)))for(var l=0;l<i.length;l++){var f=i[l],p=f.getSnapshot;f=f.value;try{if(!Ht(p(),f))return!1}catch{return!1}}if(i=n.child,n.subtreeFlags&16384&&i!==null)i.return=n,n=i;else{if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return!0;n=n.return}n.sibling.return=n.return,n=n.sibling}}return!0}function er(e,n,i,l){n&=~rd,n&=~Sa,e.suspendedLanes|=n,e.pingedLanes&=~n,l&&(e.warmLanes|=n),l=e.expirationTimes;for(var f=n;0<f;){var p=31-Yt(f),b=1<<p;l[p]=-1,f&=~b}i!==0&&_p(e,i,n)}function qs(){return We&6?!0:(Vi(0),!1)}function cd(){if(De!==null){if(Le===0)var e=De.return;else e=De,$n=ga=null,mf(e),io=null,Di=0,e=De;for(;e!==null;)ug(e.alternate,e),e=e.return;De=null}}function bo(e,n){e.finishedWork=null,e.finishedLanes=0;var i=e.timeoutHandle;i!==-1&&(e.timeoutHandle=-1,D1(i)),i=e.cancelPendingCommit,i!==null&&(e.cancelPendingCommit=null,i()),cd(),ze=e,De=i=Cr(e.current,null),_e=n,Le=0,Vt=null,Jn=!1,go=ii(e,n),nd=!1,yo=rn=rd=Sa=Pr=$e=0,Tn=Hi=null,ad=!1,n&8&&(n|=n&32);var l=e.entangledLanes;if(l!==0)for(e=e.entanglements,l&=n;0<l;){var f=31-Yt(l),p=1<<f;n|=e[f],l&=~p}return Zn=n,ys(),i}function vg(e,n){ke=null,I.H=xn,n===ki?(n=Oh(),Le=3):n===xh?(n=Oh(),Le=4):Le=n===Tm?8:n!==null&&typeof n=="object"&&typeof n.then=="function"?6:1,Vt=n,De===null&&($e=1,js(e,Xt(n,e.current)))}function pg(){var e=I.H;return I.H=xn,e===null?xn:e}function hg(){var e=I.A;return I.A=r1,e}function fd(){$e=4,Jn||(_e&4194176)!==_e&&Zt.current!==null||(go=!0),!(Pr&134217727)&&!(Sa&134217727)||ze===null||er(ze,_e,rn,!1)}function dd(e,n,i){var l=We;We|=2;var f=pg(),p=hg();(ze!==e||_e!==n)&&($s=null,bo(e,n)),n=!1;var b=$e;e:do try{if(Le!==0&&De!==null){var D=De,T=Vt;switch(Le){case 8:cd(),b=6;break e;case 3:case 2:case 6:Zt.current===null&&(n=!0);var N=Le;if(Le=0,Vt=null,wo(e,D,T,N),i&&go){b=0;break e}break;default:N=Le,Le=0,Vt=null,wo(e,D,T,N)}}i1(),b=$e;break}catch(K){vg(e,K)}while(!0);return n&&e.shellSuspendCounter++,$n=ga=null,We=l,I.H=f,I.A=p,De===null&&(ze=null,_e=0,ys()),b}function i1(){for(;De!==null;)mg(De)}function l1(e,n){var i=We;We|=2;var l=pg(),f=hg();ze!==e||_e!==n?($s=null,Us=Pt()+500,bo(e,n)):go=ii(e,n);e:do try{if(Le!==0&&De!==null){n=De;var p=Vt;t:switch(Le){case 1:Le=0,Vt=null,wo(e,n,p,1);break;case 2:if(Th(p)){Le=0,Vt=null,gg(n);break}n=function(){Le===2&&ze===e&&(Le=7),_n(e)},p.then(n,n);break e;case 3:Le=7;break e;case 4:Le=5;break e;case 7:Th(p)?(Le=0,Vt=null,gg(n)):(Le=0,Vt=null,wo(e,n,p,7));break;case 5:var b=null;switch(De.tag){case 26:b=De.memoizedState;case 5:case 27:var D=De;if(!b||$g(b)){Le=0,Vt=null;var T=D.sibling;if(T!==null)De=T;else{var N=D.return;N!==null?(De=N,Qs(N)):De=null}break t}}Le=0,Vt=null,wo(e,n,p,5);break;case 6:Le=0,Vt=null,wo(e,n,p,6);break;case 8:cd(),$e=6;break e;default:throw Error(o(462))}}s1();break}catch(K){vg(e,K)}while(!0);return $n=ga=null,I.H=l,I.A=f,We=i,De!==null?0:(ze=null,_e=0,ys(),$e)}function s1(){for(;De!==null&&!ts();)mg(De)}function mg(e){var n=jm(e.alternate,e,Zn);e.memoizedProps=e.pendingProps,n===null?Qs(e):De=n}function gg(e){var n=e,i=n.alternate;switch(n.tag){case 15:case 0:n=Nm(i,n,n.pendingProps,n.type,void 0,_e);break;case 11:n=Nm(i,n,n.pendingProps,n.type.render,n.ref,_e);break;case 5:mf(n);default:ug(i,n),n=De=ig(n,Zn),n=jm(i,n,Zn)}e.memoizedProps=e.pendingProps,n===null?Qs(e):De=n}function wo(e,n,i,l){$n=ga=null,mf(n),io=null,Di=0;var f=n.return;try{if(XE(e,f,n,i,_e)){$e=1,js(e,Xt(i,e.current)),De=null;return}}catch(p){if(f!==null)throw De=f,p;$e=1,js(e,Xt(i,e.current)),De=null;return}n.flags&32768?(Ce||l===1?e=!0:go||_e&536870912?e=!1:(Jn=e=!0,(l===2||l===3||l===6)&&(l=Zt.current,l!==null&&l.tag===13&&(l.flags|=16384))),yg(n,e)):Qs(n)}function Qs(e){var n=e;do{if(n.flags&32768){yg(n,Jn);return}e=n.return;var i=t1(n.alternate,n,Zn);if(i!==null){De=i;return}if(n=n.sibling,n!==null){De=n;return}De=n=e}while(n!==null);$e===0&&($e=5)}function yg(e,n){do{var i=n1(e.alternate,e);if(i!==null){i.flags&=32767,De=i;return}if(i=e.return,i!==null&&(i.flags|=32768,i.subtreeFlags=0,i.deletions=null),!n&&(e=e.sibling,e!==null)){De=e;return}De=e=i}while(e!==null);$e=6,De=null}function bg(e,n,i,l,f,p,b,D,T,N){var K=I.T,Z=L.p;try{L.p=2,I.T=null,u1(e,n,i,l,Z,f,p,b,D,T,N)}finally{I.T=K,L.p=Z}}function u1(e,n,i,l,f,p,b,D){do So();while(ka!==null);if(We&6)throw Error(o(327));var T=e.finishedWork;if(l=e.finishedLanes,T===null)return null;if(e.finishedWork=null,e.finishedLanes=0,T===e.current)throw Error(o(177));e.callbackNode=null,e.callbackPriority=0,e.cancelPendingCommit=null;var N=T.lanes|T.childLanes;if(N|=ef,Hk(e,l,N,p,b,D),e===ze&&(De=ze=null,_e=0),!(T.subtreeFlags&10256)&&!(T.flags&10256)||Ks||(Ks=!0,id=N,ld=i,v1(ta,function(){return So(),null})),i=(T.flags&15990)!==0,T.subtreeFlags&15990||i?(i=I.T,I.T=null,p=L.p,L.p=2,b=We,We|=4,JE(e,T),Zm(T,e),AE(Dd,e.containerInfo),lu=!!Ed,Dd=Ed=null,e.current=T,Qm(e,T.alternate,T),ns(),We=b,L.p=p,I.T=i):e.current=T,Ks?(Ks=!1,ka=e,Bi=l):wg(e,N),N=e.pendingLanes,N===0&&(Mr=null),zk(T.stateNode),_n(e),n!==null)for(f=e.onRecoverableError,T=0;T<n.length;T++)N=n[T],f(N.value,{componentStack:N.stack});return Bi&3&&So(),N=e.pendingLanes,l&4194218&&N&42?e===sd?Wi++:(Wi=0,sd=e):Wi=0,Vi(0),null}function wg(e,n){(e.pooledCacheLanes&=n)===0&&(n=e.pooledCache,n!=null&&(e.pooledCache=null,Ti(n)))}function So(){if(ka!==null){var e=ka,n=id;id=0;var i=Cp(Bi),l=I.T,f=L.p;try{if(L.p=32>i?32:i,I.T=null,ka===null)var p=!1;else{i=ld,ld=null;var b=ka,D=Bi;if(ka=null,Bi=0,We&6)throw Error(o(331));var T=We;if(We|=4,ag(b.current),tg(b,b.current,D,i),We=T,Vi(0,!1),It&&typeof It.onPostCommitFiberRoot=="function")try{It.onPostCommitFiberRoot(oi,b)}catch{}p=!0}return p}finally{L.p=f,I.T=l,wg(e,n)}}return!1}function Sg(e,n,i){n=Xt(i,n),n=Pf(e.stateNode,n,2),e=xr(e,n,2),e!==null&&(li(e,2),_n(e))}function Re(e,n,i){if(e.tag===3)Sg(e,e,i);else for(;n!==null;){if(n.tag===3){Sg(n,e,i);break}else if(n.tag===1){var l=n.stateNode;if(typeof n.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(Mr===null||!Mr.has(l))){e=Xt(i,e),i=Dm(2),l=xr(n,i,2),l!==null&&(xm(i,l,n,e),li(l,2),_n(l));break}}n=n.return}}function vd(e,n,i){var l=e.pingCache;if(l===null){l=e.pingCache=new a1;var f=new Set;l.set(n,f)}else f=l.get(n),f===void 0&&(f=new Set,l.set(n,f));f.has(i)||(nd=!0,f.add(i),e=c1.bind(null,e,n,i),n.then(e,e))}function c1(e,n,i){var l=e.pingCache;l!==null&&l.delete(n),e.pingedLanes|=e.suspendedLanes&i,e.warmLanes&=~i,ze===e&&(_e&i)===i&&($e===4||$e===3&&(_e&62914560)===_e&&300>Pt()-od?!(We&2)&&bo(e,0):rd|=i,yo===_e&&(yo=0)),_n(e)}function kg(e,n){n===0&&(n=Tp()),e=gr(e,n),e!==null&&(li(e,n),_n(e))}function f1(e){var n=e.memoizedState,i=0;n!==null&&(i=n.retryLane),kg(e,i)}function d1(e,n){var i=0;switch(e.tag){case 13:var l=e.stateNode,f=e.memoizedState;f!==null&&(i=f.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(o(314))}l!==null&&l.delete(n),kg(e,i)}function v1(e,n){return fn(e,n)}var Xs=null,ko=null,pd=!1,Gs=!1,hd=!1,Ea=0;function _n(e){e!==ko&&e.next===null&&(ko===null?Xs=ko=e:ko=ko.next=e),Gs=!0,pd||(pd=!0,h1(p1))}function Vi(e,n){if(!hd&&Gs){hd=!0;do for(var i=!1,l=Xs;l!==null;){if(e!==0){var f=l.pendingLanes;if(f===0)var p=0;else{var b=l.suspendedLanes,D=l.pingedLanes;p=(1<<31-Yt(42|e)+1)-1,p&=f&~(b&~D),p=p&201326677?p&201326677|1:p?p|2:0}p!==0&&(i=!0,xg(l,p))}else p=_e,p=is(l,l===ze?p:0),!(p&3)||ii(l,p)||(i=!0,xg(l,p));l=l.next}while(i);hd=!1}}function p1(){Gs=pd=!1;var e=0;Ea!==0&&(E1()&&(e=Ea),Ea=0);for(var n=Pt(),i=null,l=Xs;l!==null;){var f=l.next,p=Eg(l,n);p===0?(l.next=null,i===null?Xs=f:i.next=f,f===null&&(ko=i)):(i=l,(e!==0||p&3)&&(Gs=!0)),l=f}Vi(e)}function Eg(e,n){for(var i=e.suspendedLanes,l=e.pingedLanes,f=e.expirationTimes,p=e.pendingLanes&-62914561;0<p;){var b=31-Yt(p),D=1<<b,T=f[b];T===-1?(!(D&i)||D&l)&&(f[b]=Yk(D,n)):T<=n&&(e.expiredLanes|=D),p&=~D}if(n=ze,i=_e,i=is(e,e===n?i:0),l=e.callbackNode,i===0||e===n&&Le===2||e.cancelPendingCommit!==null)return l!==null&&l!==null&&vr(l),e.callbackNode=null,e.callbackPriority=0;if(!(i&3)||ii(e,i)){if(n=i&-i,n===e.callbackPriority)return n;switch(l!==null&&vr(l),Cp(i)){case 2:case 8:i=ai;break;case 32:i=ta;break;case 268435456:i=Dp;break;default:i=ta}return l=Dg.bind(null,e),i=fn(i,l),e.callbackPriority=n,e.callbackNode=i,n}return l!==null&&l!==null&&vr(l),e.callbackPriority=2,e.callbackNode=null,2}function Dg(e,n){var i=e.callbackNode;if(So()&&e.callbackNode!==i)return null;var l=_e;return l=is(e,e===ze?l:0),l===0?null:(fg(e,l,n),Eg(e,Pt()),e.callbackNode!=null&&e.callbackNode===i?Dg.bind(null,e):null)}function xg(e,n){if(So())return null;fg(e,n,!0)}function h1(e){x1(function(){We&6?fn(ri,e):e()})}function md(){return Ea===0&&(Ea=xp()),Ea}function Tg(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:fs(""+e)}function _g(e,n){var i=n.ownerDocument.createElement("input");return i.name=n.name,i.value=n.value,e.id&&i.setAttribute("form",e.id),n.parentNode.insertBefore(i,n),e=new FormData(e),i.parentNode.removeChild(i),e}function m1(e,n,i,l,f){if(n==="submit"&&i&&i.stateNode===f){var p=Tg((f[Mt]||null).action),b=l.submitter;b&&(n=(n=b[Mt]||null)?Tg(n.formAction):b.getAttribute("formAction"),n!==null&&(p=n,b=null));var D=new hs("action","action",null,l,f);e.push({event:D,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(Ea!==0){var T=b?_g(f,b):new FormData(f);xf(i,{pending:!0,data:T,method:f.method,action:p},null,T)}}else typeof p=="function"&&(D.preventDefault(),T=b?_g(f,b):new FormData(f),xf(i,{pending:!0,data:T,method:f.method,action:p},p,T))},currentTarget:f}]})}}for(var gd=0;gd<bh.length;gd++){var yd=bh[gd],g1=yd.toLowerCase(),y1=yd[0].toUpperCase()+yd.slice(1);dn(g1,"on"+y1)}dn(ph,"onAnimationEnd"),dn(hh,"onAnimationIteration"),dn(mh,"onAnimationStart"),dn("dblclick","onDoubleClick"),dn("focusin","onFocus"),dn("focusout","onBlur"),dn(FE,"onTransitionRun"),dn(zE,"onTransitionStart"),dn(LE,"onTransitionCancel"),dn(gh,"onTransitionEnd"),qa("onMouseEnter",["mouseout","mouseover"]),qa("onMouseLeave",["mouseout","mouseover"]),qa("onPointerEnter",["pointerout","pointerover"]),qa("onPointerLeave",["pointerout","pointerover"]),aa("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),aa("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),aa("onBeforeInput",["compositionend","keypress","textInput","paste"]),aa("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),aa("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),aa("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ui="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),b1=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Ui));function Og(e,n){n=(n&4)!==0;for(var i=0;i<e.length;i++){var l=e[i],f=l.event;l=l.listeners;e:{var p=void 0;if(n)for(var b=l.length-1;0<=b;b--){var D=l[b],T=D.instance,N=D.currentTarget;if(D=D.listener,T!==p&&f.isPropagationStopped())break e;p=D,f.currentTarget=N;try{p(f)}catch(K){Ls(K)}f.currentTarget=null,p=T}else for(b=0;b<l.length;b++){if(D=l[b],T=D.instance,N=D.currentTarget,D=D.listener,T!==p&&f.isPropagationStopped())break e;p=D,f.currentTarget=N;try{p(f)}catch(K){Ls(K)}f.currentTarget=null,p=T}}}}function Te(e,n){var i=n[Ac];i===void 0&&(i=n[Ac]=new Set);var l=e+"__bubble";i.has(l)||(Cg(n,e,2,!1),i.add(l))}function bd(e,n,i){var l=0;n&&(l|=4),Cg(i,e,l,n)}var Js="_reactListening"+Math.random().toString(36).slice(2);function wd(e){if(!e[Js]){e[Js]=!0,Np.forEach(function(i){i!=="selectionchange"&&(b1.has(i)||bd(i,!1,e),bd(i,!0,e))});var n=e.nodeType===9?e:e.ownerDocument;n===null||n[Js]||(n[Js]=!0,bd("selectionchange",!1,n))}}function Cg(e,n,i,l){switch(Jg(n)){case 2:var f=U1;break;case 8:f=$1;break;default:f=Ad}i=f.bind(null,n,i,e),f=void 0,!Hc||n!=="touchstart"&&n!=="touchmove"&&n!=="wheel"||(f=!0),l?f!==void 0?e.addEventListener(n,i,{capture:!0,passive:f}):e.addEventListener(n,i,!0):f!==void 0?e.addEventListener(n,i,{passive:f}):e.addEventListener(n,i,!1)}function Sd(e,n,i,l,f){var p=l;if(!(n&1)&&!(n&2)&&l!==null)e:for(;;){if(l===null)return;var b=l.tag;if(b===3||b===4){var D=l.stateNode.containerInfo;if(D===f||D.nodeType===8&&D.parentNode===f)break;if(b===4)for(b=l.return;b!==null;){var T=b.tag;if((T===3||T===4)&&(T=b.stateNode.containerInfo,T===f||T.nodeType===8&&T.parentNode===f))return;b=b.return}for(;D!==null;){if(b=ra(D),b===null)return;if(T=b.tag,T===5||T===6||T===26||T===27){l=p=b;continue e}D=D.parentNode}}l=l.return}Vp(function(){var N=p,K=Ic(i),Z=[];e:{var Y=yh.get(e);if(Y!==void 0){var U=hs,pe=e;switch(e){case"keypress":if(vs(i)===0)break e;case"keydown":case"keyup":U=dE;break;case"focusin":pe="focus",U=Uc;break;case"focusout":pe="blur",U=Uc;break;case"beforeblur":case"afterblur":U=Uc;break;case"click":if(i.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":U=Kp;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":U=eE;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":U=hE;break;case ph:case hh:case mh:U=rE;break;case gh:U=gE;break;case"scroll":case"scrollend":U=Jk;break;case"wheel":U=bE;break;case"copy":case"cut":case"paste":U=oE;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":U=Qp;break;case"toggle":case"beforetoggle":U=SE}var we=(n&4)!==0,Ke=!we&&(e==="scroll"||e==="scrollend"),z=we?Y!==null?Y+"Capture":null:Y;we=[];for(var M=N,j;M!==null;){var X=M;if(j=X.stateNode,X=X.tag,X!==5&&X!==26&&X!==27||j===null||z===null||(X=ci(M,z),X!=null&&we.push($i(M,X,j))),Ke)break;M=M.return}0<we.length&&(Y=new U(Y,pe,null,i,K),Z.push({event:Y,listeners:we}))}}if(!(n&7)){e:{if(Y=e==="mouseover"||e==="pointerover",U=e==="mouseout"||e==="pointerout",Y&&i!==jc&&(pe=i.relatedTarget||i.fromElement)&&(ra(pe)||pe[Ua]))break e;if((U||Y)&&(Y=K.window===K?K:(Y=K.ownerDocument)?Y.defaultView||Y.parentWindow:window,U?(pe=i.relatedTarget||i.toElement,U=N,pe=pe?ra(pe):null,pe!==null&&(Ke=se(pe),we=pe.tag,pe!==Ke||we!==5&&we!==27&&we!==6)&&(pe=null)):(U=null,pe=N),U!==pe)){if(we=Kp,X="onMouseLeave",z="onMouseEnter",M="mouse",(e==="pointerout"||e==="pointerover")&&(we=Qp,X="onPointerLeave",z="onPointerEnter",M="pointer"),Ke=U==null?Y:ui(U),j=pe==null?Y:ui(pe),Y=new we(X,M+"leave",U,i,K),Y.target=Ke,Y.relatedTarget=j,X=null,ra(K)===N&&(we=new we(z,M+"enter",pe,i,K),we.target=j,we.relatedTarget=Ke,X=we),Ke=X,U&&pe)t:{for(we=U,z=pe,M=0,j=we;j;j=Eo(j))M++;for(j=0,X=z;X;X=Eo(X))j++;for(;0<M-j;)we=Eo(we),M--;for(;0<j-M;)z=Eo(z),j--;for(;M--;){if(we===z||z!==null&&we===z.alternate)break t;we=Eo(we),z=Eo(z)}we=null}else we=null;U!==null&&Pg(Z,Y,U,we,!1),pe!==null&&Ke!==null&&Pg(Z,Ke,pe,we,!0)}}e:{if(Y=N?ui(N):window,U=Y.nodeName&&Y.nodeName.toLowerCase(),U==="select"||U==="input"&&Y.type==="file")var de=rh;else if(th(Y))if(ah)de=ME;else{de=CE;var Ee=OE}else U=Y.nodeName,!U||U.toLowerCase()!=="input"||Y.type!=="checkbox"&&Y.type!=="radio"?N&&Lc(N.elementType)&&(de=rh):de=PE;if(de&&(de=de(e,N))){nh(Z,de,i,K);break e}Ee&&Ee(e,Y,N),e==="focusout"&&N&&Y.type==="number"&&N.memoizedProps.value!=null&&zc(Y,"number",Y.value)}switch(Ee=N?ui(N):window,e){case"focusin":(th(Ee)||Ee.contentEditable==="true")&&(eo=Ee,Gc=N,yi=null);break;case"focusout":yi=Gc=eo=null;break;case"mousedown":Jc=!0;break;case"contextmenu":case"mouseup":case"dragend":Jc=!1,dh(Z,i,K);break;case"selectionchange":if(RE)break;case"keydown":case"keyup":dh(Z,i,K)}var he;if(Kc)e:{switch(e){case"compositionstart":var be="onCompositionStart";break e;case"compositionend":be="onCompositionEnd";break e;case"compositionupdate":be="onCompositionUpdate";break e}be=void 0}else Za?Zp(e,i)&&(be="onCompositionEnd"):e==="keydown"&&i.keyCode===229&&(be="onCompositionStart");be&&(Xp&&i.locale!=="ko"&&(Za||be!=="onCompositionStart"?be==="onCompositionEnd"&&Za&&(he=Up()):(mr=K,Bc="value"in mr?mr.value:mr.textContent,Za=!0)),Ee=Zs(N,be),0<Ee.length&&(be=new qp(be,e,null,i,K),Z.push({event:be,listeners:Ee}),he?be.data=he:(he=eh(i),he!==null&&(be.data=he)))),(he=EE?DE(e,i):xE(e,i))&&(be=Zs(N,"onBeforeInput"),0<be.length&&(Ee=new qp("onBeforeInput","beforeinput",null,i,K),Z.push({event:Ee,listeners:be}),Ee.data=he)),m1(Z,e,N,i,K)}Og(Z,n)})}function $i(e,n,i){return{instance:e,listener:n,currentTarget:i}}function Zs(e,n){for(var i=n+"Capture",l=[];e!==null;){var f=e,p=f.stateNode;f=f.tag,f!==5&&f!==26&&f!==27||p===null||(f=ci(e,i),f!=null&&l.unshift($i(e,f,p)),f=ci(e,n),f!=null&&l.push($i(e,f,p))),e=e.return}return l}function Eo(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function Pg(e,n,i,l,f){for(var p=n._reactName,b=[];i!==null&&i!==l;){var D=i,T=D.alternate,N=D.stateNode;if(D=D.tag,T!==null&&T===l)break;D!==5&&D!==26&&D!==27||N===null||(T=N,f?(N=ci(i,p),N!=null&&b.unshift($i(i,N,T))):f||(N=ci(i,p),N!=null&&b.push($i(i,N,T)))),i=i.return}b.length!==0&&e.push({event:n,listeners:b})}var w1=/\r\n?/g,S1=/\u0000|\uFFFD/g;function Mg(e){return(typeof e=="string"?e:""+e).replace(w1,`
`).replace(S1,"")}function Ng(e,n){return n=Mg(n),Mg(e)===n}function eu(){}function Ae(e,n,i,l,f,p){switch(i){case"children":typeof l=="string"?n==="body"||n==="textarea"&&l===""||Xa(e,l):(typeof l=="number"||typeof l=="bigint")&&n!=="body"&&Xa(e,""+l);break;case"className":ss(e,"class",l);break;case"tabIndex":ss(e,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":ss(e,i,l);break;case"style":Bp(e,l,p);break;case"data":if(n!=="object"){ss(e,"data",l);break}case"src":case"href":if(l===""&&(n!=="a"||i!=="href")){e.removeAttribute(i);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(i);break}l=fs(""+l),e.setAttribute(i,l);break;case"action":case"formAction":if(typeof l=="function"){e.setAttribute(i,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof p=="function"&&(i==="formAction"?(n!=="input"&&Ae(e,n,"name",f.name,f,null),Ae(e,n,"formEncType",f.formEncType,f,null),Ae(e,n,"formMethod",f.formMethod,f,null),Ae(e,n,"formTarget",f.formTarget,f,null)):(Ae(e,n,"encType",f.encType,f,null),Ae(e,n,"method",f.method,f,null),Ae(e,n,"target",f.target,f,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(i);break}l=fs(""+l),e.setAttribute(i,l);break;case"onClick":l!=null&&(e.onclick=eu);break;case"onScroll":l!=null&&Te("scroll",e);break;case"onScrollEnd":l!=null&&Te("scrollend",e);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(o(61));if(i=l.__html,i!=null){if(f.children!=null)throw Error(o(60));e.innerHTML=i}}break;case"multiple":e.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":e.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){e.removeAttribute("xlink:href");break}i=fs(""+l),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",i);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(i,""+l):e.removeAttribute(i);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(i,""):e.removeAttribute(i);break;case"capture":case"download":l===!0?e.setAttribute(i,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(i,l):e.removeAttribute(i);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?e.setAttribute(i,l):e.removeAttribute(i);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?e.removeAttribute(i):e.setAttribute(i,l);break;case"popover":Te("beforetoggle",e),Te("toggle",e),ls(e,"popover",l);break;case"xlinkActuate":Yn(e,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":Yn(e,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":Yn(e,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":Yn(e,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":Yn(e,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":Yn(e,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":Yn(e,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":Yn(e,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":Yn(e,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":ls(e,"is",l);break;case"innerText":case"textContent":break;default:(!(2<i.length)||i[0]!=="o"&&i[0]!=="O"||i[1]!=="n"&&i[1]!=="N")&&(i=Xk.get(i)||i,ls(e,i,l))}}function kd(e,n,i,l,f,p){switch(i){case"style":Bp(e,l,p);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(o(61));if(i=l.__html,i!=null){if(f.children!=null)throw Error(o(60));e.innerHTML=i}}break;case"children":typeof l=="string"?Xa(e,l):(typeof l=="number"||typeof l=="bigint")&&Xa(e,""+l);break;case"onScroll":l!=null&&Te("scroll",e);break;case"onScrollEnd":l!=null&&Te("scrollend",e);break;case"onClick":l!=null&&(e.onclick=eu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Ap.hasOwnProperty(i))e:{if(i[0]==="o"&&i[1]==="n"&&(f=i.endsWith("Capture"),n=i.slice(2,f?i.length-7:void 0),p=e[Mt]||null,p=p!=null?p[i]:null,typeof p=="function"&&e.removeEventListener(n,p,f),typeof l=="function")){typeof p!="function"&&p!==null&&(i in e?e[i]=null:e.hasAttribute(i)&&e.removeAttribute(i)),e.addEventListener(n,l,f);break e}i in e?e[i]=l:l===!0?e.setAttribute(i,""):ls(e,i,l)}}}function dt(e,n,i){switch(n){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Te("error",e),Te("load",e);var l=!1,f=!1,p;for(p in i)if(i.hasOwnProperty(p)){var b=i[p];if(b!=null)switch(p){case"src":l=!0;break;case"srcSet":f=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,n));default:Ae(e,n,p,b,i,null)}}f&&Ae(e,n,"srcSet",i.srcSet,i,null),l&&Ae(e,n,"src",i.src,i,null);return;case"input":Te("invalid",e);var D=p=b=f=null,T=null,N=null;for(l in i)if(i.hasOwnProperty(l)){var K=i[l];if(K!=null)switch(l){case"name":f=K;break;case"type":b=K;break;case"checked":T=K;break;case"defaultChecked":N=K;break;case"value":p=K;break;case"defaultValue":D=K;break;case"children":case"dangerouslySetInnerHTML":if(K!=null)throw Error(o(137,n));break;default:Ae(e,n,l,K,i,null)}}jp(e,p,D,T,N,b,f,!1),us(e);return;case"select":Te("invalid",e),l=b=p=null;for(f in i)if(i.hasOwnProperty(f)&&(D=i[f],D!=null))switch(f){case"value":p=D;break;case"defaultValue":b=D;break;case"multiple":l=D;default:Ae(e,n,f,D,i,null)}n=p,i=b,e.multiple=!!l,n!=null?Qa(e,!!l,n,!1):i!=null&&Qa(e,!!l,i,!0);return;case"textarea":Te("invalid",e),p=f=l=null;for(b in i)if(i.hasOwnProperty(b)&&(D=i[b],D!=null))switch(b){case"value":l=D;break;case"defaultValue":f=D;break;case"children":p=D;break;case"dangerouslySetInnerHTML":if(D!=null)throw Error(o(91));break;default:Ae(e,n,b,D,i,null)}Yp(e,l,f,p),us(e);return;case"option":for(T in i)if(i.hasOwnProperty(T)&&(l=i[T],l!=null))switch(T){case"selected":e.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:Ae(e,n,T,l,i,null)}return;case"dialog":Te("cancel",e),Te("close",e);break;case"iframe":case"object":Te("load",e);break;case"video":case"audio":for(l=0;l<Ui.length;l++)Te(Ui[l],e);break;case"image":Te("error",e),Te("load",e);break;case"details":Te("toggle",e);break;case"embed":case"source":case"link":Te("error",e),Te("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(N in i)if(i.hasOwnProperty(N)&&(l=i[N],l!=null))switch(N){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,n));default:Ae(e,n,N,l,i,null)}return;default:if(Lc(n)){for(K in i)i.hasOwnProperty(K)&&(l=i[K],l!==void 0&&kd(e,n,K,l,i,void 0));return}}for(D in i)i.hasOwnProperty(D)&&(l=i[D],l!=null&&Ae(e,n,D,l,i,null))}function k1(e,n,i,l){switch(n){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var f=null,p=null,b=null,D=null,T=null,N=null,K=null;for(U in i){var Z=i[U];if(i.hasOwnProperty(U)&&Z!=null)switch(U){case"checked":break;case"value":break;case"defaultValue":T=Z;default:l.hasOwnProperty(U)||Ae(e,n,U,null,l,Z)}}for(var Y in l){var U=l[Y];if(Z=i[Y],l.hasOwnProperty(Y)&&(U!=null||Z!=null))switch(Y){case"type":p=U;break;case"name":f=U;break;case"checked":N=U;break;case"defaultChecked":K=U;break;case"value":b=U;break;case"defaultValue":D=U;break;case"children":case"dangerouslySetInnerHTML":if(U!=null)throw Error(o(137,n));break;default:U!==Z&&Ae(e,n,Y,U,l,Z)}}Fc(e,b,D,T,N,K,p,f);return;case"select":U=b=D=Y=null;for(p in i)if(T=i[p],i.hasOwnProperty(p)&&T!=null)switch(p){case"value":break;case"multiple":U=T;default:l.hasOwnProperty(p)||Ae(e,n,p,null,l,T)}for(f in l)if(p=l[f],T=i[f],l.hasOwnProperty(f)&&(p!=null||T!=null))switch(f){case"value":Y=p;break;case"defaultValue":D=p;break;case"multiple":b=p;default:p!==T&&Ae(e,n,f,p,l,T)}n=D,i=b,l=U,Y!=null?Qa(e,!!i,Y,!1):!!l!=!!i&&(n!=null?Qa(e,!!i,n,!0):Qa(e,!!i,i?[]:"",!1));return;case"textarea":U=Y=null;for(D in i)if(f=i[D],i.hasOwnProperty(D)&&f!=null&&!l.hasOwnProperty(D))switch(D){case"value":break;case"children":break;default:Ae(e,n,D,null,l,f)}for(b in l)if(f=l[b],p=i[b],l.hasOwnProperty(b)&&(f!=null||p!=null))switch(b){case"value":Y=f;break;case"defaultValue":U=f;break;case"children":break;case"dangerouslySetInnerHTML":if(f!=null)throw Error(o(91));break;default:f!==p&&Ae(e,n,b,f,l,p)}Ip(e,Y,U);return;case"option":for(var pe in i)if(Y=i[pe],i.hasOwnProperty(pe)&&Y!=null&&!l.hasOwnProperty(pe))switch(pe){case"selected":e.selected=!1;break;default:Ae(e,n,pe,null,l,Y)}for(T in l)if(Y=l[T],U=i[T],l.hasOwnProperty(T)&&Y!==U&&(Y!=null||U!=null))switch(T){case"selected":e.selected=Y&&typeof Y!="function"&&typeof Y!="symbol";break;default:Ae(e,n,T,Y,l,U)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var we in i)Y=i[we],i.hasOwnProperty(we)&&Y!=null&&!l.hasOwnProperty(we)&&Ae(e,n,we,null,l,Y);for(N in l)if(Y=l[N],U=i[N],l.hasOwnProperty(N)&&Y!==U&&(Y!=null||U!=null))switch(N){case"children":case"dangerouslySetInnerHTML":if(Y!=null)throw Error(o(137,n));break;default:Ae(e,n,N,Y,l,U)}return;default:if(Lc(n)){for(var Ke in i)Y=i[Ke],i.hasOwnProperty(Ke)&&Y!==void 0&&!l.hasOwnProperty(Ke)&&kd(e,n,Ke,void 0,l,Y);for(K in l)Y=l[K],U=i[K],!l.hasOwnProperty(K)||Y===U||Y===void 0&&U===void 0||kd(e,n,K,Y,l,U);return}}for(var z in i)Y=i[z],i.hasOwnProperty(z)&&Y!=null&&!l.hasOwnProperty(z)&&Ae(e,n,z,null,l,Y);for(Z in l)Y=l[Z],U=i[Z],!l.hasOwnProperty(Z)||Y===U||Y==null&&U==null||Ae(e,n,Z,Y,l,U)}var Ed=null,Dd=null;function tu(e){return e.nodeType===9?e:e.ownerDocument}function Ag(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Rg(e,n){if(e===0)switch(n){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&n==="foreignObject"?0:e}function xd(e,n){return e==="textarea"||e==="noscript"||typeof n.children=="string"||typeof n.children=="number"||typeof n.children=="bigint"||typeof n.dangerouslySetInnerHTML=="object"&&n.dangerouslySetInnerHTML!==null&&n.dangerouslySetInnerHTML.__html!=null}var Td=null;function E1(){var e=window.event;return e&&e.type==="popstate"?e===Td?!1:(Td=e,!0):(Td=null,!1)}var Fg=typeof setTimeout=="function"?setTimeout:void 0,D1=typeof clearTimeout=="function"?clearTimeout:void 0,zg=typeof Promise=="function"?Promise:void 0,x1=typeof queueMicrotask=="function"?queueMicrotask:typeof zg<"u"?function(e){return zg.resolve(null).then(e).catch(T1)}:Fg;function T1(e){setTimeout(function(){throw e})}function _d(e,n){var i=n,l=0;do{var f=i.nextSibling;if(e.removeChild(i),f&&f.nodeType===8)if(i=f.data,i==="/$"){if(l===0){e.removeChild(f),el(n);return}l--}else i!=="$"&&i!=="$?"&&i!=="$!"||l++;i=f}while(i);el(n)}function Od(e){var n=e.firstChild;for(n&&n.nodeType===10&&(n=n.nextSibling);n;){var i=n;switch(n=n.nextSibling,i.nodeName){case"HTML":case"HEAD":case"BODY":Od(i),Rc(i);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(i.rel.toLowerCase()==="stylesheet")continue}e.removeChild(i)}}function _1(e,n,i,l){for(;e.nodeType===1;){var f=i;if(e.nodeName.toLowerCase()!==n.toLowerCase()){if(!l&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(l){if(!e[si])switch(n){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(p=e.getAttribute("rel"),p==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(p!==f.rel||e.getAttribute("href")!==(f.href==null?null:f.href)||e.getAttribute("crossorigin")!==(f.crossOrigin==null?null:f.crossOrigin)||e.getAttribute("title")!==(f.title==null?null:f.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(p=e.getAttribute("src"),(p!==(f.src==null?null:f.src)||e.getAttribute("type")!==(f.type==null?null:f.type)||e.getAttribute("crossorigin")!==(f.crossOrigin==null?null:f.crossOrigin))&&p&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(n==="input"&&e.type==="hidden"){var p=f.name==null?null:""+f.name;if(f.type==="hidden"&&e.getAttribute("name")===p)return e}else return e;if(e=hn(e.nextSibling),e===null)break}return null}function O1(e,n,i){if(n==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!i||(e=hn(e.nextSibling),e===null))return null;return e}function hn(e){for(;e!=null;e=e.nextSibling){var n=e.nodeType;if(n===1||n===3)break;if(n===8){if(n=e.data,n==="$"||n==="$!"||n==="$?"||n==="F!"||n==="F")break;if(n==="/$")return null}}return e}function Lg(e){e=e.previousSibling;for(var n=0;e;){if(e.nodeType===8){var i=e.data;if(i==="$"||i==="$!"||i==="$?"){if(n===0)return e;n--}else i==="/$"&&n++}e=e.previousSibling}return null}function jg(e,n,i){switch(n=tu(i),e){case"html":if(e=n.documentElement,!e)throw Error(o(452));return e;case"head":if(e=n.head,!e)throw Error(o(453));return e;case"body":if(e=n.body,!e)throw Error(o(454));return e;default:throw Error(o(451))}}var an=new Map,Ig=new Set;function nu(e){return typeof e.getRootNode=="function"?e.getRootNode():e.ownerDocument}var tr=L.d;L.d={f:C1,r:P1,D:M1,C:N1,L:A1,m:R1,X:z1,S:F1,M:L1};function C1(){var e=tr.f(),n=qs();return e||n}function P1(e){var n=$a(e);n!==null&&n.tag===5&&n.type==="form"?dm(n):tr.r(e)}var Do=typeof document>"u"?null:document;function Yg(e,n,i){var l=Do;if(l&&typeof n=="string"&&n){var f=qt(n);f='link[rel="'+e+'"][href="'+f+'"]',typeof i=="string"&&(f+='[crossorigin="'+i+'"]'),Ig.has(f)||(Ig.add(f),e={rel:e,crossOrigin:i,href:n},l.querySelector(f)===null&&(n=l.createElement("link"),dt(n,"link",e),it(n),l.head.appendChild(n)))}}function M1(e){tr.D(e),Yg("dns-prefetch",e,null)}function N1(e,n){tr.C(e,n),Yg("preconnect",e,n)}function A1(e,n,i){tr.L(e,n,i);var l=Do;if(l&&e&&n){var f='link[rel="preload"][as="'+qt(n)+'"]';n==="image"&&i&&i.imageSrcSet?(f+='[imagesrcset="'+qt(i.imageSrcSet)+'"]',typeof i.imageSizes=="string"&&(f+='[imagesizes="'+qt(i.imageSizes)+'"]')):f+='[href="'+qt(e)+'"]';var p=f;switch(n){case"style":p=xo(e);break;case"script":p=To(e)}an.has(p)||(e=V({rel:"preload",href:n==="image"&&i&&i.imageSrcSet?void 0:e,as:n},i),an.set(p,e),l.querySelector(f)!==null||n==="style"&&l.querySelector(Ki(p))||n==="script"&&l.querySelector(qi(p))||(n=l.createElement("link"),dt(n,"link",e),it(n),l.head.appendChild(n)))}}function R1(e,n){tr.m(e,n);var i=Do;if(i&&e){var l=n&&typeof n.as=="string"?n.as:"script",f='link[rel="modulepreload"][as="'+qt(l)+'"][href="'+qt(e)+'"]',p=f;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":p=To(e)}if(!an.has(p)&&(e=V({rel:"modulepreload",href:e},n),an.set(p,e),i.querySelector(f)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(i.querySelector(qi(p)))return}l=i.createElement("link"),dt(l,"link",e),it(l),i.head.appendChild(l)}}}function F1(e,n,i){tr.S(e,n,i);var l=Do;if(l&&e){var f=Ka(l).hoistableStyles,p=xo(e);n=n||"default";var b=f.get(p);if(!b){var D={loading:0,preload:null};if(b=l.querySelector(Ki(p)))D.loading=5;else{e=V({rel:"stylesheet",href:e,"data-precedence":n},i),(i=an.get(p))&&Cd(e,i);var T=b=l.createElement("link");it(T),dt(T,"link",e),T._p=new Promise(function(N,K){T.onload=N,T.onerror=K}),T.addEventListener("load",function(){D.loading|=1}),T.addEventListener("error",function(){D.loading|=2}),D.loading|=4,ru(b,n,l)}b={type:"stylesheet",instance:b,count:1,state:D},f.set(p,b)}}}function z1(e,n){tr.X(e,n);var i=Do;if(i&&e){var l=Ka(i).hoistableScripts,f=To(e),p=l.get(f);p||(p=i.querySelector(qi(f)),p||(e=V({src:e,async:!0},n),(n=an.get(f))&&Pd(e,n),p=i.createElement("script"),it(p),dt(p,"link",e),i.head.appendChild(p)),p={type:"script",instance:p,count:1,state:null},l.set(f,p))}}function L1(e,n){tr.M(e,n);var i=Do;if(i&&e){var l=Ka(i).hoistableScripts,f=To(e),p=l.get(f);p||(p=i.querySelector(qi(f)),p||(e=V({src:e,async:!0,type:"module"},n),(n=an.get(f))&&Pd(e,n),p=i.createElement("script"),it(p),dt(p,"link",e),i.head.appendChild(p)),p={type:"script",instance:p,count:1,state:null},l.set(f,p))}}function Hg(e,n,i,l){var f=(f=jt.current)?nu(f):null;if(!f)throw Error(o(446));switch(e){case"meta":case"title":return null;case"style":return typeof i.precedence=="string"&&typeof i.href=="string"?(n=xo(i.href),i=Ka(f).hoistableStyles,l=i.get(n),l||(l={type:"style",instance:null,count:0,state:null},i.set(n,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(i.rel==="stylesheet"&&typeof i.href=="string"&&typeof i.precedence=="string"){e=xo(i.href);var p=Ka(f).hoistableStyles,b=p.get(e);if(b||(f=f.ownerDocument||f,b={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},p.set(e,b),(p=f.querySelector(Ki(e)))&&!p._p&&(b.instance=p,b.state.loading=5),an.has(e)||(i={rel:"preload",as:"style",href:i.href,crossOrigin:i.crossOrigin,integrity:i.integrity,media:i.media,hrefLang:i.hrefLang,referrerPolicy:i.referrerPolicy},an.set(e,i),p||j1(f,e,i,b.state))),n&&l===null)throw Error(o(528,""));return b}if(n&&l!==null)throw Error(o(529,""));return null;case"script":return n=i.async,i=i.src,typeof i=="string"&&n&&typeof n!="function"&&typeof n!="symbol"?(n=To(i),i=Ka(f).hoistableScripts,l=i.get(n),l||(l={type:"script",instance:null,count:0,state:null},i.set(n,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,e))}}function xo(e){return'href="'+qt(e)+'"'}function Ki(e){return'link[rel="stylesheet"]['+e+"]"}function Bg(e){return V({},e,{"data-precedence":e.precedence,precedence:null})}function j1(e,n,i,l){e.querySelector('link[rel="preload"][as="style"]['+n+"]")?l.loading=1:(n=e.createElement("link"),l.preload=n,n.addEventListener("load",function(){return l.loading|=1}),n.addEventListener("error",function(){return l.loading|=2}),dt(n,"link",i),it(n),e.head.appendChild(n))}function To(e){return'[src="'+qt(e)+'"]'}function qi(e){return"script[async]"+e}function Wg(e,n,i){if(n.count++,n.instance===null)switch(n.type){case"style":var l=e.querySelector('style[data-href~="'+qt(i.href)+'"]');if(l)return n.instance=l,it(l),l;var f=V({},i,{"data-href":i.href,"data-precedence":i.precedence,href:null,precedence:null});return l=(e.ownerDocument||e).createElement("style"),it(l),dt(l,"style",f),ru(l,i.precedence,e),n.instance=l;case"stylesheet":f=xo(i.href);var p=e.querySelector(Ki(f));if(p)return n.state.loading|=4,n.instance=p,it(p),p;l=Bg(i),(f=an.get(f))&&Cd(l,f),p=(e.ownerDocument||e).createElement("link"),it(p);var b=p;return b._p=new Promise(function(D,T){b.onload=D,b.onerror=T}),dt(p,"link",l),n.state.loading|=4,ru(p,i.precedence,e),n.instance=p;case"script":return p=To(i.src),(f=e.querySelector(qi(p)))?(n.instance=f,it(f),f):(l=i,(f=an.get(p))&&(l=V({},i),Pd(l,f)),e=e.ownerDocument||e,f=e.createElement("script"),it(f),dt(f,"link",l),e.head.appendChild(f),n.instance=f);case"void":return null;default:throw Error(o(443,n.type))}else n.type==="stylesheet"&&!(n.state.loading&4)&&(l=n.instance,n.state.loading|=4,ru(l,i.precedence,e));return n.instance}function ru(e,n,i){for(var l=i.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),f=l.length?l[l.length-1]:null,p=f,b=0;b<l.length;b++){var D=l[b];if(D.dataset.precedence===n)p=D;else if(p!==f)break}p?p.parentNode.insertBefore(e,p.nextSibling):(n=i.nodeType===9?i.head:i,n.insertBefore(e,n.firstChild))}function Cd(e,n){e.crossOrigin==null&&(e.crossOrigin=n.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=n.referrerPolicy),e.title==null&&(e.title=n.title)}function Pd(e,n){e.crossOrigin==null&&(e.crossOrigin=n.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=n.referrerPolicy),e.integrity==null&&(e.integrity=n.integrity)}var au=null;function Vg(e,n,i){if(au===null){var l=new Map,f=au=new Map;f.set(i,l)}else f=au,l=f.get(i),l||(l=new Map,f.set(i,l));if(l.has(e))return l;for(l.set(e,null),i=i.getElementsByTagName(e),f=0;f<i.length;f++){var p=i[f];if(!(p[si]||p[mt]||e==="link"&&p.getAttribute("rel")==="stylesheet")&&p.namespaceURI!=="http://www.w3.org/2000/svg"){var b=p.getAttribute(n)||"";b=e+b;var D=l.get(b);D?D.push(p):l.set(b,[p])}}return l}function Ug(e,n,i){e=e.ownerDocument||e,e.head.insertBefore(i,n==="title"?e.querySelector("head > title"):null)}function I1(e,n,i){if(i===1||n.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof n.precedence!="string"||typeof n.href!="string"||n.href==="")break;return!0;case"link":if(typeof n.rel!="string"||typeof n.href!="string"||n.href===""||n.onLoad||n.onError)break;switch(n.rel){case"stylesheet":return e=n.disabled,typeof n.precedence=="string"&&e==null;default:return!0}case"script":if(n.async&&typeof n.async!="function"&&typeof n.async!="symbol"&&!n.onLoad&&!n.onError&&n.src&&typeof n.src=="string")return!0}return!1}function $g(e){return!(e.type==="stylesheet"&&!(e.state.loading&3))}var Qi=null;function Y1(){}function H1(e,n,i){if(Qi===null)throw Error(o(475));var l=Qi;if(n.type==="stylesheet"&&(typeof i.media!="string"||matchMedia(i.media).matches!==!1)&&!(n.state.loading&4)){if(n.instance===null){var f=xo(i.href),p=e.querySelector(Ki(f));if(p){e=p._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(l.count++,l=ou.bind(l),e.then(l,l)),n.state.loading|=4,n.instance=p,it(p);return}p=e.ownerDocument||e,i=Bg(i),(f=an.get(f))&&Cd(i,f),p=p.createElement("link"),it(p);var b=p;b._p=new Promise(function(D,T){b.onload=D,b.onerror=T}),dt(p,"link",i),n.instance=p}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(n,e),(e=n.state.preload)&&!(n.state.loading&3)&&(l.count++,n=ou.bind(l),e.addEventListener("load",n),e.addEventListener("error",n))}}function B1(){if(Qi===null)throw Error(o(475));var e=Qi;return e.stylesheets&&e.count===0&&Md(e,e.stylesheets),0<e.count?function(n){var i=setTimeout(function(){if(e.stylesheets&&Md(e,e.stylesheets),e.unsuspend){var l=e.unsuspend;e.unsuspend=null,l()}},6e4);return e.unsuspend=n,function(){e.unsuspend=null,clearTimeout(i)}}:null}function ou(){if(this.count--,this.count===0){if(this.stylesheets)Md(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var iu=null;function Md(e,n){e.stylesheets=null,e.unsuspend!==null&&(e.count++,iu=new Map,n.forEach(W1,e),iu=null,ou.call(e))}function W1(e,n){if(!(n.state.loading&4)){var i=iu.get(e);if(i)var l=i.get(null);else{i=new Map,iu.set(e,i);for(var f=e.querySelectorAll("link[data-precedence],style[data-precedence]"),p=0;p<f.length;p++){var b=f[p];(b.nodeName==="LINK"||b.getAttribute("media")!=="not all")&&(i.set(b.dataset.precedence,b),l=b)}l&&i.set(null,l)}f=n.instance,b=f.getAttribute("data-precedence"),p=i.get(b)||l,p===l&&i.set(null,f),i.set(b,f),this.count++,l=ou.bind(this),f.addEventListener("load",l),f.addEventListener("error",l),p?p.parentNode.insertBefore(f,p.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(f,e.firstChild)),n.state.loading|=4}}var Xi={$$typeof:w,Provider:null,Consumer:null,_currentValue:G,_currentValue2:G,_threadCount:0};function V1(e,n,i,l,f,p,b,D){this.tag=1,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Nc(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.finishedLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Nc(0),this.hiddenUpdates=Nc(null),this.identifierPrefix=l,this.onUncaughtError=f,this.onCaughtError=p,this.onRecoverableError=b,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=D,this.incompleteTransitions=new Map}function Kg(e,n,i,l,f,p,b,D,T,N,K,Z){return e=new V1(e,n,i,b,D,T,N,Z),n=1,p===!0&&(n|=24),p=nn(3,null,null,n),e.current=p,p.stateNode=e,n=uf(),n.refCount++,e.pooledCache=n,n.refCount++,p.memoizedState={element:l,isDehydrated:i,cache:n},Wf(p),e}function qg(e){return e?(e=ro,e):ro}function Qg(e,n,i,l,f,p){f=qg(f),l.context===null?l.context=f:l.pendingContext=f,l=Dr(n),l.payload={element:i},p=p===void 0?null:p,p!==null&&(l.callback=p),i=xr(e,l,n),i!==null&&(_t(i,e,n),Ai(i,e,n))}function Xg(e,n){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var i=e.retryLane;e.retryLane=i!==0&&i<n?i:n}}function Nd(e,n){Xg(e,n),(e=e.alternate)&&Xg(e,n)}function Gg(e){if(e.tag===13){var n=gr(e,67108864);n!==null&&_t(n,e,67108864),Nd(e,67108864)}}var lu=!0;function U1(e,n,i,l){var f=I.T;I.T=null;var p=L.p;try{L.p=2,Ad(e,n,i,l)}finally{L.p=p,I.T=f}}function $1(e,n,i,l){var f=I.T;I.T=null;var p=L.p;try{L.p=8,Ad(e,n,i,l)}finally{L.p=p,I.T=f}}function Ad(e,n,i,l){if(lu){var f=Rd(l);if(f===null)Sd(e,n,l,su,i),Zg(e,l);else if(q1(f,e,n,i,l))l.stopPropagation();else if(Zg(e,l),n&4&&-1<K1.indexOf(e)){for(;f!==null;){var p=$a(f);if(p!==null)switch(p.tag){case 3:if(p=p.stateNode,p.current.memoizedState.isDehydrated){var b=na(p.pendingLanes);if(b!==0){var D=p;for(D.pendingLanes|=2,D.entangledLanes|=2;b;){var T=1<<31-Yt(b);D.entanglements[1]|=T,b&=~T}_n(p),!(We&6)&&(Us=Pt()+500,Vi(0))}}break;case 13:D=gr(p,2),D!==null&&_t(D,p,2),qs(),Nd(p,2)}if(p=Rd(l),p===null&&Sd(e,n,l,su,i),p===f)break;f=p}f!==null&&l.stopPropagation()}else Sd(e,n,l,null,i)}}function Rd(e){return e=Ic(e),Fd(e)}var su=null;function Fd(e){if(su=null,e=ra(e),e!==null){var n=se(e);if(n===null)e=null;else{var i=n.tag;if(i===13){if(e=ge(n),e!==null)return e;e=null}else if(i===3){if(n.stateNode.current.memoizedState.isDehydrated)return n.tag===3?n.stateNode.containerInfo:null;e=null}else n!==e&&(e=null)}}return su=e,null}function Jg(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(rs()){case ri:return 2;case ai:return 8;case ta:case Ak:return 32;case Dp:return 268435456;default:return 32}default:return 32}}var zd=!1,Nr=null,Ar=null,Rr=null,Gi=new Map,Ji=new Map,Fr=[],K1="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Zg(e,n){switch(e){case"focusin":case"focusout":Nr=null;break;case"dragenter":case"dragleave":Ar=null;break;case"mouseover":case"mouseout":Rr=null;break;case"pointerover":case"pointerout":Gi.delete(n.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ji.delete(n.pointerId)}}function Zi(e,n,i,l,f,p){return e===null||e.nativeEvent!==p?(e={blockedOn:n,domEventName:i,eventSystemFlags:l,nativeEvent:p,targetContainers:[f]},n!==null&&(n=$a(n),n!==null&&Gg(n)),e):(e.eventSystemFlags|=l,n=e.targetContainers,f!==null&&n.indexOf(f)===-1&&n.push(f),e)}function q1(e,n,i,l,f){switch(n){case"focusin":return Nr=Zi(Nr,e,n,i,l,f),!0;case"dragenter":return Ar=Zi(Ar,e,n,i,l,f),!0;case"mouseover":return Rr=Zi(Rr,e,n,i,l,f),!0;case"pointerover":var p=f.pointerId;return Gi.set(p,Zi(Gi.get(p)||null,e,n,i,l,f)),!0;case"gotpointercapture":return p=f.pointerId,Ji.set(p,Zi(Ji.get(p)||null,e,n,i,l,f)),!0}return!1}function ey(e){var n=ra(e.target);if(n!==null){var i=se(n);if(i!==null){if(n=i.tag,n===13){if(n=ge(i),n!==null){e.blockedOn=n,Bk(e.priority,function(){if(i.tag===13){var l=Ut(),f=gr(i,l);f!==null&&_t(f,i,l),Nd(i,l)}});return}}else if(n===3&&i.stateNode.current.memoizedState.isDehydrated){e.blockedOn=i.tag===3?i.stateNode.containerInfo:null;return}}}e.blockedOn=null}function uu(e){if(e.blockedOn!==null)return!1;for(var n=e.targetContainers;0<n.length;){var i=Rd(e.nativeEvent);if(i===null){i=e.nativeEvent;var l=new i.constructor(i.type,i);jc=l,i.target.dispatchEvent(l),jc=null}else return n=$a(i),n!==null&&Gg(n),e.blockedOn=i,!1;n.shift()}return!0}function ty(e,n,i){uu(e)&&i.delete(n)}function Q1(){zd=!1,Nr!==null&&uu(Nr)&&(Nr=null),Ar!==null&&uu(Ar)&&(Ar=null),Rr!==null&&uu(Rr)&&(Rr=null),Gi.forEach(ty),Ji.forEach(ty)}function cu(e,n){e.blockedOn===n&&(e.blockedOn=null,zd||(zd=!0,t.unstable_scheduleCallback(t.unstable_NormalPriority,Q1)))}var fu=null;function ny(e){fu!==e&&(fu=e,t.unstable_scheduleCallback(t.unstable_NormalPriority,function(){fu===e&&(fu=null);for(var n=0;n<e.length;n+=3){var i=e[n],l=e[n+1],f=e[n+2];if(typeof l!="function"){if(Fd(l||i)===null)continue;break}var p=$a(i);p!==null&&(e.splice(n,3),n-=3,xf(p,{pending:!0,data:f,method:i.method,action:l},l,f))}}))}function el(e){function n(T){return cu(T,e)}Nr!==null&&cu(Nr,e),Ar!==null&&cu(Ar,e),Rr!==null&&cu(Rr,e),Gi.forEach(n),Ji.forEach(n);for(var i=0;i<Fr.length;i++){var l=Fr[i];l.blockedOn===e&&(l.blockedOn=null)}for(;0<Fr.length&&(i=Fr[0],i.blockedOn===null);)ey(i),i.blockedOn===null&&Fr.shift();if(i=(e.ownerDocument||e).$$reactFormReplay,i!=null)for(l=0;l<i.length;l+=3){var f=i[l],p=i[l+1],b=f[Mt]||null;if(typeof p=="function")b||ny(i);else if(b){var D=null;if(p&&p.hasAttribute("formAction")){if(f=p,b=p[Mt]||null)D=b.formAction;else if(Fd(f)!==null)continue}else D=b.action;typeof D=="function"?i[l+1]=D:(i.splice(l,3),l-=3),ny(i)}}}function Ld(e){this._internalRoot=e}du.prototype.render=Ld.prototype.render=function(e){var n=this._internalRoot;if(n===null)throw Error(o(409));var i=n.current,l=Ut();Qg(i,l,e,n,null,null)},du.prototype.unmount=Ld.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var n=e.containerInfo;e.tag===0&&So(),Qg(e.current,2,null,e,null,null),qs(),n[Ua]=null}};function du(e){this._internalRoot=e}du.prototype.unstable_scheduleHydration=function(e){if(e){var n=Pp();e={blockedOn:null,target:e,priority:n};for(var i=0;i<Fr.length&&n!==0&&n<Fr[i].priority;i++);Fr.splice(i,0,e),i===0&&ey(e)}};var ry=r.version;if(ry!=="19.0.0")throw Error(o(527,ry,"19.0.0"));L.findDOMNode=function(e){var n=e._reactInternals;if(n===void 0)throw typeof e.render=="function"?Error(o(188)):(e=Object.keys(e).join(","),Error(o(268,e)));return e=P(n),e=e!==null?H(e):null,e=e===null?null:e.stateNode,e};var X1={bundleType:0,version:"19.0.0",rendererPackageName:"react-dom",currentDispatcherRef:I,findFiberByHostInstance:ra,reconcilerVersion:"19.0.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var vu=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!vu.isDisabled&&vu.supportsFiber)try{oi=vu.inject(X1),It=vu}catch{}}return nl.createRoot=function(e,n){if(!s(e))throw Error(o(299));var i=!1,l="",f=wm,p=Sm,b=km,D=null;return n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(l=n.identifierPrefix),n.onUncaughtError!==void 0&&(f=n.onUncaughtError),n.onCaughtError!==void 0&&(p=n.onCaughtError),n.onRecoverableError!==void 0&&(b=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(D=n.unstable_transitionCallbacks)),n=Kg(e,1,!1,null,null,i,l,f,p,b,D,null),e[Ua]=n.current,wd(e.nodeType===8?e.parentNode:e),new Ld(n)},nl.hydrateRoot=function(e,n,i){if(!s(e))throw Error(o(299));var l=!1,f="",p=wm,b=Sm,D=km,T=null,N=null;return i!=null&&(i.unstable_strictMode===!0&&(l=!0),i.identifierPrefix!==void 0&&(f=i.identifierPrefix),i.onUncaughtError!==void 0&&(p=i.onUncaughtError),i.onCaughtError!==void 0&&(b=i.onCaughtError),i.onRecoverableError!==void 0&&(D=i.onRecoverableError),i.unstable_transitionCallbacks!==void 0&&(T=i.unstable_transitionCallbacks),i.formState!==void 0&&(N=i.formState)),n=Kg(e,1,!0,n,i??null,l,f,p,b,D,T,N),n.context=qg(null),i=n.current,l=Ut(),f=Dr(l),f.callback=null,xr(i,f,l),n.current.lanes=l,li(n,l),_n(n),e[Ua]=n.current,wd(e),new du(n)},nl.version="19.0.0",nl}function vw(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(vw)}catch(t){console.error(t)}}vw();fw.exports=oD();var iD=fw.exports;const lD=Ct(iD),fy=t=>{let r;const a=new Set,o=(h,m)=>{const g=typeof h=="function"?h(r):h;if(!Object.is(g,r)){const y=r;r=m??(typeof g!="object"||g===null)?g:Object.assign({},r,g),a.forEach(w=>w(r,y))}},s=()=>r,d={setState:o,getState:s,getInitialState:()=>v,subscribe:h=>(a.add(h),()=>a.delete(h))},v=r=t(o,s,d);return d},sD=t=>t?fy(t):fy,uD=t=>t;function cD(t,r=uD){const a=R.useSyncExternalStore(t.subscribe,()=>r(t.getState()),()=>r(t.getInitialState()));return R.useDebugValue(a),a}const dy=t=>{const r=sD(t),a=o=>cD(r,o);return Object.assign(a,r),a},Lo=t=>t?dy(t):dy,Pn=Lo(t=>({files:null,app:null,setFiles:r=>t({files:r}),setApp:r=>t({app:r})}));var xe={};Object.defineProperty(xe,"__esModule",{value:!0});var Ve=ee;const rv="YYYY-MM-DD",av="gggg-[W]ww",pw="YYYY-MM",hw="YYYY-[Q]Q",mw="YYYY";function Pl(t){var a,o;const r=window.app.plugins.getPlugin("periodic-notes");return r&&((o=(a=r.settings)==null?void 0:a[t])==null?void 0:o.enabled)}function Ml(){var t,r,a,o;try{const{internalPlugins:s,plugins:u}=window.app;if(Pl("daily")){const{format:h,folder:m,template:g}=((r=(t=u.getPlugin("periodic-notes"))==null?void 0:t.settings)==null?void 0:r.daily)||{};return{format:h||rv,folder:(m==null?void 0:m.trim())||"",template:(g==null?void 0:g.trim())||""}}const{folder:c,format:d,template:v}=((o=(a=s.getPluginById("daily-notes"))==null?void 0:a.instance)==null?void 0:o.options)||{};return{format:d||rv,folder:(c==null?void 0:c.trim())||"",template:(v==null?void 0:v.trim())||""}}catch{}}function Nl(){var t,r,a,o,s,u,c;try{const d=window.app.plugins,v=(t=d.getPlugin("calendar"))==null?void 0:t.options,h=(a=(r=d.getPlugin("periodic-notes"))==null?void 0:r.settings)==null?void 0:a.weekly;if(Pl("weekly"))return{format:h.format||av,folder:((o=h.folder)==null?void 0:o.trim())||"",template:((s=h.template)==null?void 0:s.trim())||""};const m=v||{};return{format:m.weeklyNoteFormat||av,folder:((u=m.weeklyNoteFolder)==null?void 0:u.trim())||"",template:((c=m.weeklyNoteTemplate)==null?void 0:c.trim())||""}}catch{}}function Al(){var r,a,o,s;const t=window.app.plugins;try{const u=Pl("monthly")&&((a=(r=t.getPlugin("periodic-notes"))==null?void 0:r.settings)==null?void 0:a.monthly)||{};return{format:u.format||pw,folder:((o=u.folder)==null?void 0:o.trim())||"",template:((s=u.template)==null?void 0:s.trim())||""}}catch{}}function Rl(){var r,a,o,s;const t=window.app.plugins;try{const u=Pl("quarterly")&&((a=(r=t.getPlugin("periodic-notes"))==null?void 0:r.settings)==null?void 0:a.quarterly)||{};return{format:u.format||hw,folder:((o=u.folder)==null?void 0:o.trim())||"",template:((s=u.template)==null?void 0:s.trim())||""}}catch{}}function Fl(){var r,a,o,s;const t=window.app.plugins;try{const u=Pl("yearly")&&((a=(r=t.getPlugin("periodic-notes"))==null?void 0:r.settings)==null?void 0:a.yearly)||{};return{format:u.format||mw,folder:((o=u.folder)==null?void 0:o.trim())||"",template:((s=u.template)==null?void 0:s.trim())||""}}catch{}}function gw(...t){let r=[];for(let o=0,s=t.length;o<s;o++)r=r.concat(t[o].split("/"));const a=[];for(let o=0,s=r.length;o<s;o++){const u=r[o];!u||u==="."||a.push(u)}return r[0]===""&&a.unshift(""),a.join("/")}function fD(t){let r=t.substring(t.lastIndexOf("/")+1);return r.lastIndexOf(".")!=-1&&(r=r.substring(0,r.lastIndexOf("."))),r}async function dD(t){const r=t.replace(/\\/g,"/").split("/");if(r.pop(),r.length){const a=gw(...r);window.app.vault.getAbstractFileByPath(a)||await window.app.vault.createFolder(a)}}async function zl(t,r){r.endsWith(".md")||(r+=".md");const a=Ve.normalizePath(gw(t,r));return await dD(a),a}async function jo(t){const{metadataCache:r,vault:a}=window.app,o=Ve.normalizePath(t);if(o==="/")return Promise.resolve(["",null]);try{const s=r.getFirstLinkpathDest(o,""),u=await a.cachedRead(s),c=window.app.foldManager.load(s);return[u,c]}catch(s){return console.error(`Failed to read the daily note template '${o}'`,s),new Ve.Notice("Failed to read the daily note template"),["",null]}}function wn(t,r="day"){const a=t.clone().startOf(r).format();return`${r}-${a}`}function yw(t){return t.replace(/\[[^\]]*\]/g,"")}function vD(t,r){if(r==="week"){const a=yw(t);return/w{1,2}/i.test(a)&&(/M{1,4}/.test(a)||/D{1,4}/.test(a))}return!1}function Io(t,r){return bw(t.basename,r)}function pD(t,r){return bw(fD(t),r)}function bw(t,r){const o={day:Ml,week:Nl,month:Al,quarter:Rl,year:Fl}[r]().format.split("/").pop(),s=window.moment(t,o,!0);if(!s.isValid())return null;if(vD(o,r)&&r==="week"){const u=yw(o);if(/w{1,2}/i.test(u))return window.moment(t,o.replace(/M{1,4}/g,"").replace(/D{1,4}/g,""),!1)}return s}class hD extends Error{}async function ww(t){const r=window.app,{vault:a}=r,o=window.moment,{template:s,format:u,folder:c}=Ml(),[d,v]=await jo(s),h=t.format(u),m=await zl(c,h);try{const g=await a.create(m,d.replace(/{{\s*date\s*}}/gi,h).replace(/{{\s*time\s*}}/gi,o().format("HH:mm")).replace(/{{\s*title\s*}}/gi,h).replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(y,w,k,S,E,x)=>{const _=o(),F=t.clone().set({hour:_.get("hour"),minute:_.get("minute"),second:_.get("second")});return k&&F.add(parseInt(S,10),E),x?F.format(x.substring(1).trim()):F.format(u)}).replace(/{{\s*yesterday\s*}}/gi,t.clone().subtract(1,"day").format(u)).replace(/{{\s*tomorrow\s*}}/gi,t.clone().add(1,"d").format(u)));return r.foldManager.save(g,v),g}catch(g){console.error(`Failed to create file: '${m}'`,g),new Ve.Notice("Unable to create new file.")}}function mD(t,r){return r[wn(t,"day")]??null}function gD(){const{vault:t}=window.app,{folder:r}=Ml(),a=t.getAbstractFileByPath(Ve.normalizePath(r));if(!a)throw new hD("Failed to find daily notes folder");const o={};return Ve.Vault.recurseChildren(a,s=>{if(s instanceof Ve.TFile){const u=Io(s,"day");if(u){const c=wn(u,"day");o[c]=s}}}),o}class yD extends Error{}function bD(){const{moment:t}=window;let r=t.localeData()._week.dow;const a=["sunday","monday","tuesday","wednesday","thursday","friday","saturday"];for(;r;)a.push(a.shift()),r--;return a}function wD(t){return bD().indexOf(t.toLowerCase())}async function Sw(t){const{vault:r}=window.app,{template:a,format:o,folder:s}=Nl(),[u,c]=await jo(a),d=t.format(o),v=await zl(s,d);try{const h=await r.create(v,u.replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(m,g,y,w,k,S)=>{const E=window.moment(),x=t.clone().set({hour:E.get("hour"),minute:E.get("minute"),second:E.get("second")});return y&&x.add(parseInt(w,10),k),S?x.format(S.substring(1).trim()):x.format(o)}).replace(/{{\s*title\s*}}/gi,d).replace(/{{\s*time\s*}}/gi,window.moment().format("HH:mm")).replace(/{{\s*(sunday|monday|tuesday|wednesday|thursday|friday|saturday)\s*:(.*?)}}/gi,(m,g,y)=>{const w=wD(g);return t.weekday(w).format(y.trim())}));return window.app.foldManager.save(h,c),h}catch(h){console.error(`Failed to create file: '${v}'`,h),new Ve.Notice("Unable to create new file.")}}function SD(t,r){return r[wn(t,"week")]??null}function kD(){const t={};if(!Ew())return t;const{vault:r}=window.app,{folder:a}=Nl(),o=r.getAbstractFileByPath(Ve.normalizePath(a));if(!o)throw new yD("Failed to find weekly notes folder");return Ve.Vault.recurseChildren(o,s=>{if(s instanceof Ve.TFile){const u=Io(s,"week");if(u){const c=wn(u,"week");t[c]=s}}}),t}class ED extends Error{}async function kw(t){const{vault:r}=window.app,{template:a,format:o,folder:s}=Al(),[u,c]=await jo(a),d=t.format(o),v=await zl(s,d);try{const h=await r.create(v,u.replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(m,g,y,w,k,S)=>{const E=window.moment(),x=t.clone().set({hour:E.get("hour"),minute:E.get("minute"),second:E.get("second")});return y&&x.add(parseInt(w,10),k),S?x.format(S.substring(1).trim()):x.format(o)}).replace(/{{\s*date\s*}}/gi,d).replace(/{{\s*time\s*}}/gi,window.moment().format("HH:mm")).replace(/{{\s*title\s*}}/gi,d));return window.app.foldManager.save(h,c),h}catch(h){console.error(`Failed to create file: '${v}'`,h),new Ve.Notice("Unable to create new file.")}}function DD(t,r){return r[wn(t,"month")]??null}function xD(){const t={};if(!Dw())return t;const{vault:r}=window.app,{folder:a}=Al(),o=r.getAbstractFileByPath(Ve.normalizePath(a));if(!o)throw new ED("Failed to find monthly notes folder");return Ve.Vault.recurseChildren(o,s=>{if(s instanceof Ve.TFile){const u=Io(s,"month");if(u){const c=wn(u,"month");t[c]=s}}}),t}class TD extends Error{}async function _D(t){const{vault:r}=window.app,{template:a,format:o,folder:s}=Rl(),[u,c]=await jo(a),d=t.format(o),v=await zl(s,d);try{const h=await r.create(v,u.replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(m,g,y,w,k,S)=>{const E=window.moment(),x=t.clone().set({hour:E.get("hour"),minute:E.get("minute"),second:E.get("second")});return y&&x.add(parseInt(w,10),k),S?x.format(S.substring(1).trim()):x.format(o)}).replace(/{{\s*date\s*}}/gi,d).replace(/{{\s*time\s*}}/gi,window.moment().format("HH:mm")).replace(/{{\s*title\s*}}/gi,d));return window.app.foldManager.save(h,c),h}catch(h){console.error(`Failed to create file: '${v}'`,h),new Ve.Notice("Unable to create new file.")}}function OD(t,r){return r[wn(t,"quarter")]??null}function CD(){const t={};if(!xw())return t;const{vault:r}=window.app,{folder:a}=Rl(),o=r.getAbstractFileByPath(Ve.normalizePath(a));if(!o)throw new TD("Failed to find quarterly notes folder");return Ve.Vault.recurseChildren(o,s=>{if(s instanceof Ve.TFile){const u=Io(s,"quarter");if(u){const c=wn(u,"quarter");t[c]=s}}}),t}class PD extends Error{}async function MD(t){const{vault:r}=window.app,{template:a,format:o,folder:s}=Fl(),[u,c]=await jo(a),d=t.format(o),v=await zl(s,d);try{const h=await r.create(v,u.replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(m,g,y,w,k,S)=>{const E=window.moment(),x=t.clone().set({hour:E.get("hour"),minute:E.get("minute"),second:E.get("second")});return y&&x.add(parseInt(w,10),k),S?x.format(S.substring(1).trim()):x.format(o)}).replace(/{{\s*date\s*}}/gi,d).replace(/{{\s*time\s*}}/gi,window.moment().format("HH:mm")).replace(/{{\s*title\s*}}/gi,d));return window.app.foldManager.save(h,c),h}catch(h){console.error(`Failed to create file: '${v}'`,h),new Ve.Notice("Unable to create new file.")}}function ND(t,r){return r[wn(t,"year")]??null}function AD(){const t={};if(!Tw())return t;const{vault:r}=window.app,{folder:a}=Fl(),o=r.getAbstractFileByPath(Ve.normalizePath(a));if(!o)throw new PD("Failed to find yearly notes folder");return Ve.Vault.recurseChildren(o,s=>{if(s instanceof Ve.TFile){const u=Io(s,"year");if(u){const c=wn(u,"year");t[c]=s}}}),t}function RD(){var o,s;const{app:t}=window,r=t.internalPlugins.plugins["daily-notes"];if(r&&r.enabled)return!0;const a=t.plugins.getPlugin("periodic-notes");return a&&((s=(o=a.settings)==null?void 0:o.daily)==null?void 0:s.enabled)}function Ew(){var a,o;const{app:t}=window;if(t.plugins.getPlugin("calendar"))return!0;const r=t.plugins.getPlugin("periodic-notes");return r&&((o=(a=r.settings)==null?void 0:a.weekly)==null?void 0:o.enabled)}function Dw(){var a,o;const{app:t}=window,r=t.plugins.getPlugin("periodic-notes");return r&&((o=(a=r.settings)==null?void 0:a.monthly)==null?void 0:o.enabled)}function xw(){var a,o;const{app:t}=window,r=t.plugins.getPlugin("periodic-notes");return r&&((o=(a=r.settings)==null?void 0:a.quarterly)==null?void 0:o.enabled)}function Tw(){var a,o;const{app:t}=window,r=t.plugins.getPlugin("periodic-notes");return r&&((o=(a=r.settings)==null?void 0:a.yearly)==null?void 0:o.enabled)}function FD(t){const r={day:Ml,week:Nl,month:Al,quarter:Rl,year:Fl}[t];return r()}function zD(t,r){return{day:ww,month:kw,week:Sw}[t](r)}xe.DEFAULT_DAILY_NOTE_FORMAT=rv;xe.DEFAULT_MONTHLY_NOTE_FORMAT=pw;xe.DEFAULT_QUARTERLY_NOTE_FORMAT=hw;xe.DEFAULT_WEEKLY_NOTE_FORMAT=av;xe.DEFAULT_YEARLY_NOTE_FORMAT=mw;xe.appHasDailyNotesPluginLoaded=RD;xe.appHasMonthlyNotesPluginLoaded=Dw;xe.appHasQuarterlyNotesPluginLoaded=xw;xe.appHasWeeklyNotesPluginLoaded=Ew;xe.appHasYearlyNotesPluginLoaded=Tw;var _w=xe.createDailyNote=ww;xe.createMonthlyNote=kw;xe.createPeriodicNote=zD;xe.createQuarterlyNote=_D;xe.createWeeklyNote=Sw;xe.createYearlyNote=MD;var dl=xe.getAllDailyNotes=gD;xe.getAllMonthlyNotes=xD;xe.getAllQuarterlyNotes=CD;xe.getAllWeeklyNotes=kD;xe.getAllYearlyNotes=AD;var Ll=xe.getDailyNote=mD,Ow=xe.getDailyNoteSettings=Ml,xu=xe.getDateFromFile=Io;xe.getDateFromPath=pD;xe.getDateUID=wn;xe.getMonthlyNote=DD;xe.getMonthlyNoteSettings=Al;xe.getPeriodicNoteSettings=FD;xe.getQuarterlyNote=OD;xe.getQuarterlyNoteSettings=Rl;xe.getTemplateInfo=jo;xe.getWeeklyNote=SD;xe.getWeeklyNoteSettings=Nl;xe.getYearlyNote=ND;xe.getYearlyNoteSettings=Fl;class LD{getState(){return Pn.getState()}setApp(r){return Pn.getState().setApp(r),r}initAllFiles(){const r=dl();Pn.getState().setFiles(r)}async getAllFiles(){const r=dl();return Pn.getState().setFiles(r),r}async getMyAllDailyNotes(){return this.getAllFiles()}async createDailyNote(r){if(!this.getState().app)throw new Error("App not initialized");return await _w(r)}async getDailyNoteByEvent(r){const a=this.getState().files||await this.getAllFiles();return Ll(r,a)}getFile(r){const a=this.getState().app;return a&&a.vault.getFileByPath(r.path)||null}getDailyNotePath(){return Ow().folder}async readFileContent(r){const a=this.getState().app;if(!a)throw new Error("App not initialized");const o=a.vault.getAbstractFileByPath(r);if(!o||!(o instanceof ee.TFile))throw new Error(`File not found: ${r}`);return await a.vault.read(o)}async writeFileContent(r,a){const o=this.getState().app;if(!o)throw new Error("App not initialized");const s=o.vault.getAbstractFileByPath(r);if(!s||!(s instanceof ee.TFile))throw new Error(`File not found: ${r}`);return await o.vault.modify(s,a)}}const Qe=new LD,vy={get(t,r){if(!r)return{};const a={};for(const o of t)try{const s=r.loadLocalStorage(o);if(s!==null){const u=JSON.parse(s);a[o]=u}}catch(s){console.error("Get storage failed in ",o,s)}return a},set(t,r){if(r)for(const a in t)try{const o=JSON.stringify(t[a]);r.saveLocalStorage(a,o)}catch(o){console.error("Save storage failed in ",a,o)}},remove(t,r){if(r)for(const a of t)try{r.saveLocalStorage(a,null)}catch(o){console.error("Remove storage failed in ",a,o)}},emitStorageChangedEvent(){var r;const t=document.createElement("iframe");t.style.display="none",document.body.appendChild(t),(r=t.contentWindow)==null||r.localStorage.setItem("t",Date.now().toString()),t.remove()}},Da=Lo(t=>({markEventId:"",editEventId:"",shouldSplitEventWord:!0,shouldHideImageUrl:!0,shouldUseMarkdownParser:!0,useTinyUndoHistoryCache:!1,isMobileView:!1,showSiderbarInMobileView:!1,pluginSetting:null,setMarkEventId:r=>t(a=>r===a.markEventId?a:{markEventId:r}),setEditEventId:r=>t(a=>r===a.editEventId?a:{editEventId:r}),setMobileView:r=>t(a=>r===a.isMobileView?a:{isMobileView:r}),setShowSiderbarInMobileView:r=>t(a=>r===a.showSiderbarInMobileView?a:{showSiderbarInMobileView:r}),setAppSetting:r=>t(a=>({...a,...r})),setPluginSetting:r=>t(a=>({...a,pluginSetting:r}))}));class jD{constructor(){ye(this,"getState",()=>Da.getState());ye(this,"setEditEventId",r=>{Da.getState().setEditEventId(r)});ye(this,"setMarkEventId",r=>{Da.getState().setMarkEventId(r)});ye(this,"setIsMobileView",r=>{Da.getState().setMobileView(r)});ye(this,"setShowSiderbarInMobileView",r=>{Da.getState().setShowSiderbarInMobileView(r)});ye(this,"setAppSetting",r=>{Da.getState().setAppSetting(r),vy.set(r,Pn.getState().app)});ye(this,"setPluginSetting",r=>{Da.getState().setPluginSetting(r)});const r={shouldHideImageUrl:!0,shouldUseMarkdownParser:!0};try{const a=Pn.getState().app;if(a){const o=vy.get(["shouldHideImageUrl","shouldUseMarkdownParser"],a);(o==null?void 0:o.shouldHideImageUrl)!==void 0&&(r.shouldHideImageUrl=o.shouldHideImageUrl),(o==null?void 0:o.shouldUseMarkdownParser)!==void 0&&(r.shouldUseMarkdownParser=o.shouldUseMarkdownParser)}}catch(a){console.error("Failed to load cached settings:",a)}this.setAppSetting(r)}}const Vr=new jD,St=Lo((t,r)=>({hash:"",query:{tag:"",duration:null,text:"",eventType:"",filter:"",contentRegex:"",folderPaths:[],metadataKeys:[],metadataValues:{}},setQuery:a=>{const o=r().query;JSON.stringify(a)!==JSON.stringify(o)&&t(s=>({...s,query:a}))},setQueryFilter:a=>{const o=r().query.filter;a!==o&&t(s=>({...s,query:{...s.query,filter:a}}))},setTagQuery:a=>{const o=r().query.tag;a!==o&&t(s=>({...s,query:{...s.query,tag:a}}))},setDurationQuery:a=>{const o=r().query.duration,s=JSON.stringify(a),u=JSON.stringify(o);s!==u&&t(c=>({...c,query:{...c.query,duration:a}}))},setEventType:a=>{const o=r().query.eventType;a!==o&&t(s=>({...s,query:{...s.query,eventType:a}}))},setText:a=>{const o=r().query.text;a!==o&&t(s=>({...s,query:{...s.query,text:a}}))},setHash:a=>{const o=r().hash;a!==o&&t(s=>({...s,hash:a}))},setContentRegex:a=>{const o=r().query.contentRegex||"";a!==o&&t(s=>({...s,query:{...s.query,contentRegex:a}}))},setFolderPaths:a=>{const o=r().query.folderPaths||[],s=JSON.stringify(a),u=JSON.stringify(o);s!==u&&t(c=>({...c,query:{...c.query,folderPaths:a}}))},setMetadataKeys:a=>{const o=r().query.metadataKeys||[],s=JSON.stringify(a),u=JSON.stringify(o);s!==u&&t(c=>({...c,query:{...c.query,metadataKeys:a}}))},setMetadataValues:a=>{const o=r().query.metadataValues||{},s=JSON.stringify(a),u=JSON.stringify(o);s!==u&&t(c=>({...c,query:{...c.query,metadataValues:a}}))}}));class ID{constructor(){ye(this,"getState",()=>St.getState());ye(this,"clearQuery",()=>{St.getState().setQuery({tag:"",duration:null,text:"",eventType:"",filter:"",contentRegex:"",folderPaths:[],metadataKeys:[],metadataValues:{}})});ye(this,"setQuery",r=>{St.getState().setQuery(r)});ye(this,"setHash",r=>{St.getState().setHash(r)});ye(this,"setEventTypeQuery",(r="")=>{St.getState().setEventType(r)});ye(this,"setEventFilter",r=>{St.getState().setQueryFilter(r),this.applyWorkspaceFilter(r)});ye(this,"setTextQuery",r=>{St.getState().setText(r)});ye(this,"setTagQuery",r=>{St.getState().setTagQuery(r)});ye(this,"setFromAndToQuery",(r,a)=>{const o=r&&a?{from:r,to:a}:null;St.getState().setDurationQuery(o)});ye(this,"setContentRegex",r=>{St.getState().setContentRegex(r)});ye(this,"setFolderPaths",r=>{St.getState().setFolderPaths(r)});ye(this,"setMetadataKeys",r=>{St.getState().setMetadataKeys(r)});ye(this,"setMetadataValues",r=>{St.getState().setMetadataValues(r)});ye(this,"applyWorkspaceFilter",r=>{const o=Vr.getState().pluginSetting.WorkspaceFilters.find(u=>u.id===r&&u.isEnabled);if(!o)return;const s=St.getState();o.eventTypes.length>0?s.setEventType(o.eventTypes[0]||""):s.setEventType(""),s.setContentRegex(o.contentRegex||""),s.setFolderPaths(o.folderPaths||[]),s.setMetadataKeys(o.metadataKeys||[]),s.setMetadataValues(o.metadataValues||{})})}}const or=new ID;function YD(){return parseInt(ee.moment().format("x"))}function HD(){return ee.Platform.isDesktop?ee.Platform.isWin?"Windows":ee.Platform.isMacOS?"MacOS":"Linux":"Unknown"}function jl(t){return typeof t=="string"&&(t=t.replaceAll("-","/")),new Date(t).getTime()}function BD(t){const r=new Date(jl(t));return new Date(r.getFullYear(),r.getMonth(),r.getDate()).getTime()}function WD(t){const r=new Date(jl(t)),a=r.getFullYear(),o=r.getMonth()+1,s=r.getDate();return`${a}/${o}/${s}`}function VD(t){const r=new Date(jl(t)),a=r.getHours(),o=r.getMinutes(),s=a<10?"0"+a:a,u=o<10?"0"+o:o;return`${s}:${u}`}function UD(t){const r=new Date(jl(t)),a=r.getFullYear(),o=r.getMonth()+1,s=r.getDate(),u=r.getHours(),c=r.getMinutes(),d=o<10?"0"+o:o,v=s<10?"0"+s:s,h=u<10?"0"+u:u,m=c<10?"0"+c:c;return`${a}/${d}/${v} ${h}:${m}:00`}function $D(t){return Array.from(new Set(t))}function KD(t){const r=new Set,a=[];for(const o of t)r.has(o.id)||(r.add(o.id),a.push(o));return a}function Cw(t){const r=[],a=Object.keys(t).sort();for(const o of a){const s=t[o];s&&(typeof s=="object"?r.push(...Cw(s).split("&")):r.push(`${o}=${s}`))}return r.join("&")}function qD(t){const r={},a=t.split("&");for(const o of a){const[s,u]=o.split("=");s&&u&&(r[s]=u)}return r}function Pw(t){if(!t)return{};const r={},a=Object.keys(t).sort();for(const o of a){const s=t[o];if(typeof s=="object"){const u=Pw(JSON.parse(JSON.stringify(s)));u&&Object.keys(u).length>0&&(r[o]=u)}else s&&(r[o]=s)}return r}async function QD(t){if(navigator.clipboard&&navigator.clipboard.writeText)try{await navigator.clipboard.writeText(t)}catch(r){console.warn("Copy to clipboard failed.",r)}else console.warn("Copy to clipboard failed, methods not supports.")}function XD(t){return new Promise(r=>{const a=new Image;a.onload=()=>{const{width:o,height:s}=a;o>0&&s>0?r({width:o,height:s}):r({width:0,height:0})},a.onerror=()=>{r({width:0,height:0})},a.className="hidden",a.src=t,document.body.appendChild(a),a.remove()})}const Lr={getNowTimeStamp:YD,getOSVersion:HD,getTimeStampByDate:jl,getDateStampByDate:BD,getDateString:WD,getTimeString:VD,getDateTimeString:UD,dedupe:$D,dedupeObjectWithId:KD,transformObjectToParamsString:Cw,transformParamsStringToObject:qD,filterObjectNullKeys:Pw,copyTextToClipboard:QD,getImageSize:XD},vt=Lo((t,r)=>({events:[],allEvents:[],filteredEvents:[],tags:[],forceUpdateCounter:0,setEvents:a=>{const o=r().events,s=Lr.dedupeObjectWithId(a.sort((c,d)=>Lr.getTimeStampByDate(d.start)-Lr.getTimeStampByDate(c.start)));if(a===o)return;if(a.length!==o.length){t({events:s,allEvents:s});return}a.some((c,d)=>{const v=o[d];return c.id!==v.id||c.start!==v.start||c.end!==v.end||c.title!==v.title})&&t({events:s,allEvents:s})},setFilteredEvents:a=>{const o=Lr.dedupeObjectWithId(a.sort((s,u)=>Lr.getTimeStampByDate(u.start)-Lr.getTimeStampByDate(s.start)));t({events:o,filteredEvents:o})},setTags:a=>t({tags:a}),insertEvent:a=>{const o=r().events,s=r().allEvents,u=o.some(d=>d.id===a.id),c=s.some(d=>d.id===a.id);(!u||!c)&&t(d=>({events:Lr.dedupeObjectWithId([a,...d.events]),allEvents:Lr.dedupeObjectWithId([a,...d.allEvents])}))},deleteEventById:a=>t(o=>({events:o.events.filter(s=>s.id!==a),allEvents:o.allEvents.filter(s=>s.id!==a)})),editEvent:a=>t(o=>{const s=o.events.findIndex(v=>v.id===a.id),u=o.allEvents.findIndex(v=>v.id===a.id);if(s===-1&&u===-1)return o;const c=[...o.events],d=[...o.allEvents];return s!==-1&&(c[s]={...c[s],...a}),u!==-1&&(d[u]={...d[u],...a}),{events:c,allEvents:d}}),updateEvent:(a,o)=>{const s=r().events,u=r().allEvents,c=s.findIndex(m=>m.id===a),d=u.findIndex(m=>m.id===a);if(c===-1&&d===-1)return;const v=[...s],h=[...u];c!==-1&&(v[c]={...v[c],...o}),d!==-1&&(h[d]={...h[d],...o}),t({events:v,allEvents:h})},setForceUpdate:()=>{t(a=>({forceUpdateCounter:a.forceUpdateCounter+1}))}}));function Vu(){return/\s(📅|📆|(@{)|(\[due::))\s?(\d{4}-\d{2}-\d{2})(\])?/}function GD(t){return Vu().test(t)}function JD(t){var r;return(r=Vu().exec(t))==null?void 0:r[1]}function ZD(t){var r;return(r=Vu().exec(t))==null?void 0:r[0]}function Uu(){return/⏲\s?(\d{1,2}):(\d{2})/}function Mw(){return/(\d{1,2}):(\d{2})-(\d{1,2}):(\d{2})/}function ex(t){const r=Mw().exec(t);if(r)return parseInt(r[1]);const a=/^-\s*(\d{1,2}):(\d{2})/.exec(t);if(a)return parseInt(a[1]);const o=Uu().exec(t);return o?parseInt(o[1]):void 0}function tx(t){const r=Mw().exec(t);if(r)return parseInt(r[2]);const a=/^-\s*(\d{1,2}):(\d{2})/.exec(t);if(a)return parseInt(a[2]);const o=Uu().exec(t);return o?parseInt(o[2]):void 0}function Rn(t){return t.split(/\r?\n/)}function ov(t){const r=ex(t),a=tx(t);if(r!==void 0&&a!==void 0)return{hour:r,minute:a}}function nx(t){if(!GD(t))return{hasDate:!1};const a=JD(t),o=ZD(t),s=o==null?void 0:o.match(/(\d{4}-\d{2}-\d{2})/),u=s?s[0]:void 0;return{hasDate:!0,label:a,date:o,rawDate:u}}function rx(t){const r=ov(t),a=nx(t);return{hasEvent:!!r||a.hasDate,time:r,date:a}}function ax(t){return Uu().test(t)||Vu().test(t)}const Nw=()=>Qe.getDailyNotePath(),zn=async(t,r="An error occurred")=>{try{return await t()}catch(a){return console.error(`${r}: `,a),null}},ox={" ":"Todo",x:"Done",X:"Done","-":"Cancelled",">":"Forwarded",D:"Deferred","/":"InProgress","?":"Question","!":"Important",i:"Info",B:"Bookmark",P:"Pro",C:"Con",b:"Brainstorming",E:"Example",Q:"Quote",N:"Note",W:"Win",L:"Lose","+":"Add",R:"Reviewed"},kt={LIST_ITEM:/^(\s*)(-|\*|\+|\d+\.)\s+(.*)$/,TASK:/^(\s*)(-|\*|\+|\d+\.)\s+\[(.)\]\s+(.*)$/,TIME_STANDARD:/(\d{1,2}):(\d{2})(?::(\d{2}))?/g,TIME_WITH_TAG:/<time>(\d{1,2}):(\d{2})(?::(\d{2}))?<\/time>/g,END_TIME:/⏲\s?(\d{1,2}):(\d{2})(?::(\d{2}))?/g,TIME_RANGE:/(\d{1,2}):(\d{2})(?::(\d{2}))?-(\d{1,2}):(\d{2})(?::(\d{2}))?/g,DUE_DATE:/\s(📅|📆|(@{)|(\[due::))\s?(\d{4}-\d{2}-\d{2})(\])?/g,START_DATE:/🛫\s?(\d{4}-\d{2}-\d{2})/g,SCHEDULED_DATE:/[⏳⌛]\s?(\d{4}-\d{2}-\d{2})/g,DONE_DATE:/✅\s?(\d{4}-\d{2}-\d{2})/g,RECURRENCE:/🔁([a-zA-Z0-9, !]+)$/,BLOCK_LINK:/\s\^([a-zA-Z0-9-]+)$/};function ix(t){const r={},a=[...t.matchAll(kt.TIME_RANGE)];if(a.length>0){const u=a[0];return r.startTime={hour:parseInt(u[1]),minute:parseInt(u[2]),second:u[3]?parseInt(u[3]):void 0,isEndTime:!1},r.endTime={hour:parseInt(u[4]),minute:parseInt(u[5]),second:u[6]?parseInt(u[6]):void 0,isEndTime:!0},r}const o=[...t.matchAll(kt.END_TIME)][0];o&&(r.endTime={hour:parseInt(o[1]),minute:parseInt(o[2]),second:o[3]?parseInt(o[3]):void 0,isEndTime:!0});const s=[...t.matchAll(kt.TIME_WITH_TAG)];if(s.length>0){const u=s[0];r.startTime={hour:parseInt(u[1]),minute:parseInt(u[2]),second:u[3]?parseInt(u[3]):void 0,isEndTime:!1}}else{const u=[...t.matchAll(kt.TIME_STANDARD)];if(u.length>0){const c=u[0],d={hour:parseInt(c[1]),minute:parseInt(c[2]),second:c[3]?parseInt(c[3]):void 0,isEndTime:!1};if(!r.endTime||r.endTime.hour!==d.hour||r.endTime.minute!==d.minute)r.startTime=d;else if(u.length>1){const v=u[1];r.startTime={hour:parseInt(v[1]),minute:parseInt(v[2]),second:v[3]?parseInt(v[3]):void 0,isEndTime:!1}}}}return r}function lx(t){const r=[],a=[...t.matchAll(kt.DUE_DATE)];for(const c of a)r.push({date:c[4],moment:ee.moment(c[4],"YYYY-MM-DD"),type:"due",rawMatch:c[0]});const o=[...t.matchAll(kt.START_DATE)];for(const c of o)r.push({date:c[1],moment:ee.moment(c[1],"YYYY-MM-DD"),type:"start",rawMatch:c[0]});const s=[...t.matchAll(kt.SCHEDULED_DATE)];for(const c of s)r.push({date:c[1],moment:ee.moment(c[1],"YYYY-MM-DD"),type:"scheduled",rawMatch:c[0]});const u=[...t.matchAll(kt.DONE_DATE)];for(const c of u)r.push({date:c[1],moment:ee.moment(c[1],"YYYY-MM-DD"),type:"done",rawMatch:c[0]});return r}function sx(t){const r=t.match(kt.RECURRENCE);return r?r[1].trim():void 0}function ux(t){const r=t.match(kt.BLOCK_LINK);return r?r[1]:void 0}function cx(t,r,a,o){let s=t;for(const u of r)u.rawMatch&&(s=s.replace(u.rawMatch,""));return a&&(s=s.replace(`🔁${a}`,"")),o&&(s=s.replace(`^${o}`,"")),s=s.replace(/⏲\s?\d{1,2}:\d{2}(:\d{2})?/g,""),s.trim()}function Aw(t){const r={originalLine:t,content:"",indentation:"",isTask:!1,isListItem:!1,listMarker:"",dates:[],hasRecurrence:!1},a=t.match(kt.TASK);if(a)r.isTask=!0,r.isListItem=!0,r.indentation=a[1],r.listMarker=a[2],r.statusCharacter=a[3],r.taskStatus=ox[a[3]]||"Unknown",r.content=a[4];else{const u=t.match(kt.LIST_ITEM);u?(r.isListItem=!0,r.indentation=u[1],r.listMarker=u[2],r.content=u[3],r.taskStatus="NotATask"):(r.content=t,r.taskStatus="NotATask")}const o=ix(r.content);r.startTime=o.startTime,r.endTime=o.endTime,r.dates=lx(r.content);const s=sx(r.content);return r.hasRecurrence=!!s,r.recurrenceRule=s,r.blockLink=ux(r.content),r.content=cx(r.content,r.dates,r.recurrenceRule,r.blockLink),r}function Rw(t,r){if(r.ProcessEntriesBelow==="")return!0;try{return new RegExp(r.ProcessEntriesBelow.replace(/([.?*+^$[\]\\(){}|-])/g,"\\$1"),"").test(t)}catch(a){return console.error("Invalid regex pattern in ProcessEntriesBelow setting",a),!1}}function Fw(t){return kt.TIME_STANDARD.test(t)||kt.TIME_WITH_TAG.test(t)||kt.END_TIME.test(t)||kt.TASK.test(t)}function zw(t,r,a,o){const s=t.isTask;let u=t.startTime!==void 0;!t.startTime&&!!t.endTime&&t.endTime&&(u=!0);const d=t.dates.some(E=>E.type==="due"),v=t.dates.some(E=>E.type==="start");if(!s&&!u&&!d)return null;let h=r.clone(),m=r.clone(),g;for(const E of t.dates)E.type==="due"?(g=E.moment.clone(),m=E.moment.clone()):E.type==="start"&&(h=E.moment.clone());let y=!1;s&&!u?(y=!0,g&&!v&&(h=g.clone())):u&&(h.hour(t.startTime.hour),h.minute(t.startTime.minute),h.second(t.startTime.second||0),t.endTime?(m.hour(t.endTime.hour),m.minute(t.endTime.minute),m.second(t.endTime.second||0)):m=h.clone().add(30,"minutes"),d&&(t.endTime||(m.hour(t.startTime.hour),m.minute(t.startTime.minute),m.second(t.startTime.second||0))));const w=`${h.format("YYYYMMDDHHmm")}00${a}`;let k="default";if(t.isTask)switch(t.taskStatus){case"Todo":k="TASK-TODO";break;case"Done":k="TASK-DONE";break;case"Cancelled":k="TASK-CANCELLED";break;case"Forwarded":k="TASK-FORWARDED";break;case"Deferred":k="TASK-DEFERRED";break;case"InProgress":k="TASK-IN_PROGRESS";break;case"Question":k="TASK-QUESTION";break;case"Add":k="TASK-ADD";break;case"Reviewed":k="TASK-REVIEWED";break;case"Important":k="TASK-IMPORTANT";break;case"Info":k="TASK-INFO";break;case"Bookmark":k="TASK-BOOKMARK";break;case"Pro":k="TASK-PRO";break;case"Con":k="TASK-CON";break;case"Brainstorming":k="TASK-BRAINSTORMING";break;case"Example":k="TASK-EXAMPLE";break;case"Quote":k="TASK-QUOTE";break;case"Note":k="TASK-NOTE";break;case"Win":k="TASK-WIN";break;case"Lose":k="TASK-LOSE";break;default:k="default"}const S={id:w,title:t.content,start:h.toDate(),end:m.toDate(),allDay:y,eventType:k,path:o};return t.recurrenceRule&&(S.recurrenceRule=t.recurrenceRule),t.blockLink&&(S.blockLink=t.blockLink),S}function Yo(t){if(!t||!t.startsWith("TASK-"))return null;switch(t.split("-")[1]){case"TODO":return" ";case"DONE":return"x";case"CANCELLED":return"-";case"IN_PROGRESS":return"/";case"IMPORTANT":return"!";case"QUESTION":return"?";case"REVIEW":return">";case"IDEA":return"i";case"PRO":return"+";case"CON":return"-";case"BRAINSTORMING":return"b";case"EXAMPLE":return"e";case"QUOTE":return"q";case"NOTE":return"n";case"WIN":return"w";case"LOSE":return"l";default:return" "}}async function py(t,r,a,o,s,u,c,d){return await zn(async()=>{const{app:v}=Qe.getState(),h=await dl(),m=t.slice(0,13)+"00",g=ee.moment(m,"YYYYMMDDHHmmSS"),y=ee.moment(s),w=ee.moment(u),k=ee.moment(c),S=!g.isSame(y,"day"),E=!k.isSame(w,"day"),x=y.isSame(w,"day"),_=!y.isSame(g,"minute")||!w.isSame(k,"minute"),F=o.startsWith("TASK-")&&!r.match(/^\d{1,2}:\d{2}/),C=t;let A;if(F){const B=v.vault.getFileByPath(d);if(!B)throw new Error(`Daily note not found for date: ${g.format("YYYY-MM-DD")}`);const q=await v.vault.read(B),Q=Rn(q),I=Dv(Q,t,r,g,o);if(I===-1)throw new Error("Could not find the event line in the file");const V=$u(r,a);let J;F&&!a.match(/^\d{1,2}:\d{2}/)?J=Ku(V,g,y,w,o):J=Il(V,y,w,o),Q[I]=J;const le=Q.join(`
`);return await v.vault.modify(B,le),{id:t,title:V,start:y.toDate(),end:w.toDate(),allDay:!0,eventType:o,originalEventId:C,path:B.path}}return _&&!S&&!E?A=await hy(t,r,a,o,g,y,w,h,v):!S&&E?A=await fx(t,r,a,o,g,y,w,v,d):S?A=await dx(t,r,a,o,g,y,w,x,h,v,d):A=await hy(t,r,a,o,g,y,w,h,v),{...A,originalEventId:C}},"Failed to update event")}async function hy(t,r,a,o,s,u,c,d,v){const h=o.startsWith("TASK-")&&!r.match(/^\d{1,2}:\d{2}/),m=a.match(/^\d{1,2}:\d{2}/),g=Ll(s,d);if(!g)throw new Error(`Daily note not found for date: ${s.format("YYYY-MM-DD")}`);const y=await v.vault.read(g),w=Rn(y);let k=-1;for(let _=0;_<w.length;_++)if(w[_].includes(r)){k=_;break}if(k===-1)throw new Error("Could not find the event line in the file");const S=$u(r,a);let E;h&&!m?E=Ku(S,s,u,c,o):E=Il(S,u,c,o),w[k]=E;const x=w.join(`
`);return await v.vault.modify(g,x),{id:t,title:S,start:u.toDate(),end:c.toDate(),allDay:h&&!m,eventType:o,path:g.path}}async function fx(t,r,a,o,s,u,c,d,v){const h=o.startsWith("TASK-")&&!r.match(/^\d{1,2}:\d{2}/),m=a.match(/^\d{1,2}:\d{2}/),g=d.vault.getFileByPath(v);if(!g)throw new Error(`Daily note not found for date: ${s.format("YYYY-MM-DD")}`);const y=await d.vault.read(g),w=Rn(y),k=Dv(w,t,r,s,o);if(k===-1)throw new Error("Could not find the event line in the file");const S=$u(r,a),E=u.isSame(c,"day");let x;if(h&&!m)x=Ku(S,s,u,c,o);else if(E){const F=u.format("HH:mm"),C=c.format("HH:mm"),A=Yo(o);x=A?`- [${A}] ${F}-${C} ${S}`:`- ${F}-${C} ${S}`}else x=Il(S,u,c,o);w[k]=x;const _=w.join(`
`);return await d.vault.modify(g,_),{id:t,title:S,start:u.toDate(),end:c.toDate(),allDay:h&&!m,eventType:o||"default",path:g.path}}async function dx(t,r,a,o,s,u,c,d,v,h,m){const g=o.startsWith("TASK-")&&!r.match(/^\d{1,2}:\d{2}/),y=a.match(/^\d{1,2}:\d{2}/),w=h.vault.getFileByPath(m);let k=Ll(u,v);if(!w)throw new Error(`Original daily note not found for date: ${s.format("YYYY-MM-DD")}`);k||(k=await Qe.createDailyNote(c));const S=await h.vault.read(w),E=Rn(S),x=Dv(E,t,r,s,o);if(x===-1)throw new Error("Could not find the event line in the file");const _=$u(r,a),F=Yo(o);let C;if(g&&!y)C=Ku(_,s,u,c,o);else if(d){const V=u.format("HH:mm"),J=c.format("HH:mm");C=F?`- [${F}] ${V}-${J} ${_}`:`- ${V}-${J} ${_}`}else C=Il(_,u,c,o);E.splice(x,1);const A=E.join(`
`);await h.vault.modify(w,A);const B=await h.vault.read(k);let q=Rn(B);const Q=vx(q,o);Q!==-1?q.splice(Q,0,C):q.push(C);const I=q.join(`
`);return await h.vault.modify(k,I),{id:t,title:_,start:u.toDate(),end:c.toDate(),allDay:g&&!y,eventType:o,path:k.path}}function $u(t,r){let a=r;return a=a.replace(/^\d{1,2}:\d{2}(-\d{1,2}:\d{2})?\s+/,"").trim(),a=a.replace(/⏲\s?\d{1,2}:\d{2}/g,"").trim(),a=a.replace(/📅\s?\d{4}-\d{2}-\d{2}/g,"").trim(),a=a.replace(/\d{1,2}:\d{2}-\d{1,2}:\d{2}/g,"").trim(),a===""&&t&&(a=t.replace(/^\d{1,2}:\d{2}(-\d{1,2}:\d{2})?\s+/,"").trim().replace(/⏲\s?\d{1,2}:\d{2}/g,"").trim().replace(/📅\s?\d{4}-\d{2}-\d{2}/g,"").trim().replace(/\d{1,2}:\d{2}-\d{1,2}:\d{2}/g,"").trim()),a}function Il(t,r,a,o){const s=r.format("HH"),u=r.format("mm"),c=Yo(o),d=t.match(/\s(\^[a-zA-Z0-9]{2,})$/),v=d?d[1]:"";let h=v?t.replace(d[0],""):t;const m=r.isSame(a,"day");let g;return m?g=c?`- [${c}] ${s}:${u}-${a.format("HH:mm")} ${h}`:`- ${s}:${u}-${a.format("HH:mm")} ${h}`:g=c?`- [${c}] ${h} 🛫 ${r.format("YYYY-MM-DD")} 📅 ${a.format("YYYY-MM-DD")}`:`- ${h} 🛫 ${r.format("YYYY-MM-DD")} 📅 ${a.format("YYYY-MM-DD")}`,v&&(g+=` ${v}`),g}function Ku(t,r,a,o,s){const u=t.match(/\s(\^[a-zA-Z0-9]{2,})$/),c=u?u[1]:"",d=Yo(s);let v=c?t.replace(u[0],""):t,h=d===null?`- ${v}`:`- [${d}] ${v}`;const m=!r.isSame(a,"day");return(!a.isSame(o,"day")||m)&&(h+=` 🛫 ${a.format("YYYY-MM-DD")}`),h+=` 📅 ${o.format("YYYY-MM-DD")}`,c&&(h+=` ${c}`),h}function vx(t,r){if(r==="TASK-TODO"||r==="default"){let u=-1;for(let c=0;c<t.length;c++){const d=t[c];(d.match(/^- \d{1,2}:\d{2}/)||d.includes("- [ ]")||d.includes("- [x]")||d.includes("- [-]")||d.startsWith("- ")&&(d.includes(" 📅 ")||d.includes(" 🛫 ")))&&(u=c+1)}if(u>0)return u}const{pluginSetting:a}=Vr.getState(),o=a.InsertAfter;if(o&&o.trim()!==""){for(let u=0;u<t.length;u++)if(t[u].includes(o)){let c=u+1;for(;c<t.length&&t[c].trim()==="";)c++;return c}}const s=a.ProcessEntriesBelow;if(s&&s.trim()!==""){for(let u=0;u<t.length;u++)if(t[u].includes(s))return u+1}if(t.length===0)return 0;for(let u=0;u<t.length;u++)if(t[u].startsWith("#"))return u+1;return t.length}function Dv(t,r,a,o,s){const u=r.slice(0,12),c=r.match(/_L(\d+)$/);if(c){const h=parseInt(c[1]);if(h<t.length&&t[h].includes(a))return h}const d=Yo(s);for(let h=0;h<t.length;h++){const m=t[h];if(m.includes(a)&&m.startsWith("- ")){if(s.startsWith("TASK-")&&(m.includes(`- [${d}]`)||m.match(/- \[[^\]]\]/)))return h;const g=ov(m);if(g){const{hour:y,minute:w}=g;if(o.clone().set({hour:y,minute:w}).format("YYYYMMDDHHmm")===u)return h}else if(m.trim()===`- ${a.trim()}`||m.includes(`- ${a.trim()} 📅`)||m.includes(`- ${a.trim()} 🛫`)||s.startsWith("TASK-")&&m.includes(`- [${d}] ${a.trim()}`))return h}}for(let h=0;h<t.length;h++)if(t[h].includes(a))return h;const v=Uu();for(let h=0;h<t.length;h++){const m=t[h];if(m.startsWith("- ")&&v.test(m)){const g=ov(m);if(g){const{hour:y,minute:w}=g;if(o.clone().set({hour:y,minute:w}).format("YYYYMMDDHHmm")===u)return h}}}return-1}function Lw(t){const r=[];let a=t;for(;a.contains(`
`);){const o=a.indexOf(`
`);r.push(a.slice(0,o)),a=a.slice(o+1)}return a.length>0&&r.push(a),r}async function my(t,r,a){return await zn(async()=>{const{vault:o}=Qe.getState().app,s=Vr.getState().pluginSetting,u=(r instanceof Date,ee.moment(r)),c=(a instanceof Date,ee.moment(a)),d=a&&!u.isSame(c,"day"),v=Il(t,u,c,d?"TASK-TODO":"default"),h=await px(u,v,o,s.InsertAfter),m=Aw(v);return zw(m,u,h.lineNum,h.file.path)},"Failed to create event")}async function px(t,r,a,o){const s=await dl(),u=Ll(t,s);if(u){const c=await a.read(u),d=Rn(c);let v=1;if(d.length>0){let m=0;for(const g of d)g.startsWith("- ")&&m++;v=m+1}const h=await gy(o,r,c);return await a.modify(u,h.content),{file:u,lineNum:v}}else{const c=await _w(t),d=await a.read(c),v=await gy(o,r,d);return await a.modify(c,v.content),{file:c,lineNum:1}}}async function gy(t,r,a){return await zn(async()=>{const o=Lw(a);let s=!1,u=0;for(let c=0;c<o.length;c++)if(o[c].contains(t)){s=!0,u=c;break}return s?await hx(r,a,u,s):{content:r+`
`+a,posNum:0}},"Failed to insert after handler")}async function hx(t,r,a,o){return await zn(async()=>{const s=Lw(r);if(o){const u=a+1;return s.splice(u,0,t),{content:s.join(`
`),posNum:u}}else return s.splice(0,0,t),{content:s.join(`
`),posNum:0}},"Failed to insert text after position")}async function yy(t){return await zn(async()=>{const{vault:r,metadataCache:a}=Qe.getState().app;if(!/\d{14,}/.test(t))throw new Error("Invalid event ID format");const s=Nw()+"/delete.md",u=a.getFirstLinkpathDest("",s);if(!(u instanceof ee.TFile))return;const c=await r.read(u),d=Rn(c);if(d.length===0)return;const v=parseInt(t.slice(14)),h=d[v-1];if(/^- (.+)$/.test(h)){const m=c.replace(h,"");await r.modify(u,m)}},"Failed to permanently delete event")}const mx=async t=>await zn(async()=>{const{metadataCache:r,vault:a}=Qe.getState().app,s=Nw()+"/delete.md",u=r.getFirstLinkpathDest("",s),c=ee.moment(),d=c.format("YYYY/MM/DD HH:mm:ss");if(u instanceof ee.TFile){const v=await a.read(u),h=Rn(v);let m;h.length===1&&h[0]===""?m=1:m=h.length+1;const g=c.format("YYYYMMDDHHmmss")+m;return await by(u,v,t,g),d}else{const v=ee.normalizePath(s),h=await gx(v),g=c.format("YYYYMMDDHHmmss")+1;return await by(h,"",t,g),d}},"Failed to send event to delete"),by=async(t,r,a,o)=>await zn(async()=>{const{vault:s}=Qe.getState().app;let u;return r===""?u=a+" deletedAt: "+o:u=r+`
`+a+" deletedAt: "+o,await s.modify(t,u),!0},"Failed to create delete event in file"),gx=async t=>await zn(async()=>{const{vault:r}=Qe.getState().app;try{return await r.create(t,"")}catch(a){throw console.error(`Failed to create file: '${t}'`,a),new ee.Notice("Unable to create new file."),a}},"Failed to create delete file"),yx={},bx={},wx={},Sx={},jw={welcome:"Welcome to the Big Calendar","Open big calendar successfully":"Open big calendar successfully","Open big calendar":"Open big calendar",Sunday:"Sunday",Monday:"Monday","Regular Options":"Regular Options","First Day of Week":"First Day of Week","Insert after heading":"Insert after heading","Choose the first day of the week. Sunday is the default.":"Choose the first day of the week. Sunday is the default.","You should set the same heading below if you want to insert and process events below the same heading.":"You should set the same heading below if you want to insert and process events below the same heading.","Process Events below":"Process events below","Only entries below this string/section in your notes will be processed. If it does not exist no notes will be processed for that file.":"Only entries below this string/section in your notes will be processed. If it does not exist no notes will be processed for that file.","Experimental Options":"Experimental Options",'Set default event composition, you should use {TIME} as "HH:mm" and {CONTENT} as content. "{TIME} {CONTENT}" by default':'Set default event composition, you should use {TIME} as "HH:mm" and {CONTENT} as content. "{TIME} {CONTENT}" by default',"Default Event Composition":"Default Event Composition","Say Thank You":"Say Thank You",Donate:"Donate","If you like this plugin, consider donating to support continued development:":"If you like this plugin, consider donating to support continued development:","Close big calendar successfully":"Close big calendar successfully","Your daily notes folder is not set correctly. Please check your settings.":"Your daily notes folder is not set correctly. Please check your settings.","Select a date":"Select a date",Today:"Today",Cancel:"Cancel","Go to date":"Go to date","Workspace Filters":"Workspace Filters","Configure filter settings":"Configure filter settings",Edit:"Edit",Delete:"Delete","Add Filter":"Add Filter","Default Filter":"Default Filter","Choose the default filter to apply when opening the calendar":"Choose the default filter to apply when opening the calendar","Edit Filter":"Edit Filter","Filter Name":"Filter Name","Content Regex":"Content Regex","Regular expression to match against event content":"Regular expression to match against event content","Event Types":"Event Types","Types of events to include in this filter":"Types of events to include in this filter","Add Event Type":"Add Event Type","Enter event type":"Enter event type",Add:"Add","Folder Paths":"Folder Paths","Folder paths to include in this filter":"Folder paths to include in this filter","Add Folder Path":"Add Folder Path","Enter folder path":"Enter folder path","Metadata Keys":"Metadata Keys","Metadata keys that should exist in the file":"Metadata keys that should exist in the file","Add Metadata Key":"Add Metadata Key","Enter metadata key":"Enter metadata key","Metadata Values":"Metadata Values","Key-value pairs for matching specific metadata values":"Key-value pairs for matching specific metadata values","Add Metadata Value":"Add Metadata Value",Key:"Key",Value:"Value","Enter metadata value":"Enter metadata value",Remove:"Remove",Save:"Save","Workspace Filter":"Workspace Filter",None:"None","Enter regex pattern":"Enter regex pattern","Event Type":"Event Type",All:"All","Not Tagged":"Not Tagged",Linked:"Linked",Imaged:"Imaged",Connected:"Connected","Clear Filters":"Clear Filters",Filter:"Filter","Task - Todo":"Task - Todo","Task - Done":"Task - Done","Task - Cancelled":"Task - Cancelled","Task - Forwarded":"Task - Forwarded","Task - Deferred":"Task - Deferred","Task - In Progress":"Task - In Progress","Task - Question":"Task - Question","Task - Add":"Task - Add","Task - Reviewed":"Task - Reviewed","Task - Important":"Task - Important","Task - Info":"Task - Info","Task - Bookmark":"Task - Bookmark","Task - Pro":"Task - Pro","Task - Con":"Task - Con","Task - Brainstorming":"Task - Brainstorming","Task - Example":"Task - Example","Task - Quote":"Task - Quote","Task - Note":"Task - Note","Task - Win":"Task - Win","Task - Lose":"Task - Lose",Default:"Default"},kx={},Ex={},Dx={},xx={},Tx={},_x={},Ox={},Cx={},Px={},Mx={},Nx={},Ax={},Rx={},Fx={},zx={},Lx={},jx={welcome:"欢迎使用大日历","Open big calendar successfully":"成功打开大日历","Open big calendar":"打开大日历",Sunday:"星期日",Monday:"星期一","Regular Options":"常规选项","First Day of Week":"一周开始","Choose the first day of the week. Sunday is the default.":"选择一周的开始日期。默认为星期日。","Insert after heading":"在指定标题后插入事件","You should set the same heading below if you want to insert and process events below the same heading.":"你如果想要插入标题的同时显示对应标题下的事件，你必须保证当前设置与下方的解析设置是一致的。当为空时插入到文末","Process Events below":"解析指定标题后的事件","Only entries below this string/section in your notes will be processed. If it does not exist no notes will be processed for that file.":"只有在设置的标题后的事件 才会被解析。当为空时解析全文的事件","Experimental Options":"实验性选项",'Set default event composition, you should use {TIME} as "HH:mm" and {CONTENT} as content. "{TIME} {CONTENT}" by default':'设置默认事件组成，你可以使用 {TIME} 作为 "HH:mm" 和 {CONTENT} 作为内容。默认为 "{TIME} {CONTENT}"',"Default Event Composition":"默认事件组成","Say Thank You":"Say Thank You",Donate:"捐赠","If you like this plugin, consider donating to support continued development:":"如果你喜欢这个插件，而且也希望给我买鸡腿，那么可以考虑 Github 页面右边的 Sponsor~","Close big calendar successfully":"成功关闭大日历","Your daily notes folder is not set correctly. Please check your settings.":"你的日记笔记文件夹设置不正确。请检查你的设置。","Select a date":"选择一个日期",Today:"今天",Cancel:"取消","Go to date":"跳转到日期","Workspace Filters":"工作区过滤器","Configure filter settings":"配置过滤器设置",Edit:"编辑",Delete:"删除","Add Filter":"添加过滤器","Default Filter":"默认过滤器","Choose the default filter to apply when opening the calendar":"选择打开日历时应用的默认过滤器","Edit Filter":"编辑过滤器","Filter Name":"过滤器名称","Content Regex":"内容正则表达式","Regular expression to match against event content":"用于匹配事件内容的正则表达式","Event Types":"事件类型","Types of events to include in this filter":"此过滤器包含的事件类型","Add Event Type":"添加事件类型","Enter event type":"输入事件类型",Add:"添加","Folder Paths":"文件夹路径","Folder paths to include in this filter":"此过滤器包含的文件夹路径","Add Folder Path":"添加文件夹路径","Enter folder path":"输入文件夹路径","Metadata Keys":"元数据键","Metadata keys that should exist in the file":"文件中应存在的元数据键","Add Metadata Key":"添加元数据键","Enter metadata key":"输入元数据键","Metadata Values":"元数据值","Key-value pairs for matching specific metadata values":"用于匹配特定元数据值的键值对","Add Metadata Value":"添加元数据值",Key:"键",Value:"值","Enter metadata value":"输入元数据值",Remove:"移除",Save:"保存","Workspace Filter":"工作区过滤器",None:"无","Enter regex pattern":"输入正则表达式模式","Event Type":"事件类型",All:"全部","Not Tagged":"未标记",Linked:"已链接",Imaged:"图像",Connected:"已连接","Clear Filters":"清除过滤器",Filter:"过滤器","Task - Todo":"任务 - 待办","Task - Done":"任务 - 完成","Task - Cancelled":"任务 - 取消","Task - Forwarded":"任务 - 转发","Task - Deferred":"任务 - 延期","Task - In Progress":"任务 - 进行中","Task - Question":"任务 - 问题","Task - Add":"任务 - 添加","Task - Reviewed":"任务 - 已审阅","Task - Important":"任务 - 重要","Task - Info":"任务 - 信息","Task - Bookmark":"任务 - 书签","Task - Pro":"任务 - 赞成","Task - Con":"任务 - 反对","Task - Brainstorming":"任务 - 头脑风暴","Task - Example":"任务 - 示例","Task - Quote":"任务 - 引用","Task - Note":"任务 - 笔记","Task - Win":"任务 - 赢","Task - Lose":"任务 - 输",Default:"默认"},Ix={},Yx={ar:yx,cs:bx,da:wx,de:Sx,en:jw,"en-gb":kx,es:Ex,fr:Dx,hi:xx,id:Tx,it:_x,ja:Ox,ko:Cx,nl:Px,nn:Mx,pl:Nx,pt:Ax,"pt-br":Rx,ro:Fx,ru:zx,tr:Lx,"zh-cn":jx,"zh-tw":Ix},wy=Yx[ee.moment.locale()];function re(t){return wy&&wy[t]||jw[t]}async function Hx(t){return await xv(async()=>{if(!t)return 0;const{vault:r}=Qe.getState().app,a=Vr.getState().pluginSetting,o=await r.read(t),s=Rn(o),u=s.findIndex(d=>Rw(d,a));if(u===-1)return 0;let c=0;for(let d=u===0?0:u+1;d<s.length;d++){const v=s[d];if(/^#{1,} /g.test(v)&&a.ProcessEntriesBelow.trim()!=="")break;Fw(v)&&c++}return c},"Failed to get remaining events")}async function Iw(t,r){return await xv(async()=>{if(!t)return[];const{vault:a}=Qe.getState().app,o=Vr.getState().pluginSetting;if(!await Hx(t))return[];const u=await a.read(t),c=Rn(u),d=xu(t,"day"),v=[],h=c.findIndex(g=>Rw(g,o));if(h===-1)return[];let m=h===0?0:h+1;for(;m<c.length;){const g=c[m];if(/^#{1,} /g.test(g)&&o.ProcessEntriesBelow.trim()!=="")break;const y=Aw(g);if(Fw(g)){const w=zw(y,d,m,t.path);w&&(v.push(w),r&&r.push(w))}m++}return v},"Failed to get events from daily note")}async function Bx(t,r,a){try{const s=Qe.getState().app.metadataCache.getFileCache(t);if(!s||!s.frontmatter)return!1;const u=s.frontmatter;if(r.length>0&&r.some(d=>!Object.prototype.hasOwnProperty.call(u,d)))return!1;if(Object.keys(a).length>0){for(const[c,d]of Object.entries(a))if(u[c]!==d)return!1}return!0}catch(o){return console.error("Error checking file metadata:",o),!1}}async function Wx(t){return await xv(async()=>{const r=[],{folder:a}=Ow(),o=or.getState().query;if(!t)return[];if(!t.vault.getFolderByPath(a))return new ee.Notice(re("Your daily notes folder is not set correctly. Please check your settings.")),[];const u=dl(),c=o.metadataKeys&&o.metadataKeys.length>0||o.metadataValues&&Object.keys(o.metadataValues).length>0;for(const d in u)if(u[d]instanceof ee.TFile){const v=u[d];if(c&&!await Bx(v,o.metadataKeys||[],o.metadataValues||{}))continue;const h=await Iw(v,[]);r.push(...h)}return r},"Failed to get events")}async function xv(t,r){try{return await t()}catch(a){throw console.error(`${r}: ${a}`),a}}async function Vx(t){return await zn(async()=>{const{files:r,app:a}=Qe.getState(),{vault:o}=a,s=Vr.getState().pluginSetting;if(!/\d{14,}/.test(t))throw new Error("Invalid event ID format");const u=t.slice(0,14),c=parseInt(t.slice(14)),d=ee.moment(u,"YYYYMMDDHHmmSS"),v=Ll(d,r);if(!v)throw new Error(`Daily note not found for date: ${d.format("YYYY-MM-DD")}`);const h=await o.read(v),m=Ux(h);if(c>=m.length)throw new Error(`Event line ${c} not found in file`);const g=$x(m[c],s),y="- "+t+" "+g,w=m[c],k=h.replace(w,"");await o.modify(v,k);const S=await mx(y);return{id:t,title:g,deletedAt:S,eventType:"HIDDEN",start:d.toDate(),end:d.toDate(),allDay:!1}},"Failed to hide event")}const Ux=t=>t.split(/\r?\n/),$x=(t,r)=>{var s;let a;return r.DefaultEventComposition!==""&&/{TIME}/g.test(r.DefaultEventComposition)&&/{CONTENT}/g.test(r.DefaultEventComposition)?a="^\\s*[\\-\\*]\\s(\\[(.{1})\\]\\s?)?"+r.DefaultEventComposition.replace(/{TIME}/g,"(\\<time\\>)?((\\d{1,2})\\:(\\d{2}))?(\\<\\/time\\>)?").replace(/{CONTENT}/g,"(.*)$"):a="^\\s*[\\-\\*]\\s(\\[(.{1})\\]\\s?)?(\\<time\\>)?((\\d{1,2})\\:(\\d{2}))?(\\<\\/time\\>)?\\s?(.*)$",((s=new RegExp(a,"").exec(t))==null?void 0:s[8])||""};class Kx{constructor(){ye(this,"initialized",!1);ye(this,"fileEventMap",new Map)}getState(){return vt.getState()}async fetchAllEvents(r){try{const a=await Wx(r),o=Array.isArray(a)?[...a]:[];return this.fileEventMap.clear(),o.forEach(s=>{var c;const u=Qe.getFile(s);if(u){const d=u.path;this.fileEventMap.has(d)||this.fileEventMap.set(d,[]),(c=this.fileEventMap.get(d))==null||c.push(s)}}),vt.getState().setEvents(o),this.initialized||(this.initialized=!0),o}catch(a){return console.error("Failed to fetch events:",a),[]}}filterEvents(r){try{const a=vt.getState().allEvents;if(!r||Object.keys(r).length===0)return vt.getState().setFilteredEvents(a),a;let o=[...a];if(r.eventType&&(o=o.filter(s=>s.eventType===r.eventType)),r.contentText){const s=r.contentText.toLowerCase();o=o.filter(u=>u.title.toLowerCase().includes(s))}if(r.contentRegex)try{const s=new RegExp(r.contentRegex);o=o.filter(u=>s.test(u.title))}catch{console.error("Invalid regex pattern:",r.contentRegex)}if(r.folderPaths&&r.folderPaths.length>0&&(o=o.filter(s=>s.path?r.folderPaths.some(u=>s.path.startsWith(u)):!1)),r.startDate&&r.endDate){const s=new Date(r.startDate).getTime(),u=new Date(r.endDate).getTime();o=o.filter(c=>{const d=new Date(c.start).getTime();return d>=s&&d<=u})}else if(r.startDate){const s=new Date(r.startDate).getTime();o=o.filter(u=>new Date(u.start).getTime()>=s)}else if(r.endDate){const s=new Date(r.endDate).getTime();o=o.filter(u=>new Date(u.start).getTime()<=s)}return vt.getState().setFilteredEvents(o),o}catch(a){return console.error("Failed to filter events:",a),vt.getState().allEvents}}pushEvent(r){return vt.getState().insertEvent({...r}),r}getEventById(r){const{events:a}=this.getState();return a.find(o=>o.id===r)}async hideEventById(r){vt.getState().deleteEventById(r);try{return await Vx(r),!0}catch(a){return console.error("Failed to hide event:",a),!1}}async deleteEventById(r){vt.getState().deleteEventById(r);try{return await yy(r),!0}catch(a){return console.error("Failed to delete event:",a),!1}}async editEvent(r,a,o,s){try{if(a&&o&&r.id&&r.title){const u=r.id,c=await py(r.id,r.originalContent||r.title,s||r.title,r.eventType||"default",a,o,new Date(r.end),r.path);if(c.id!==u){const v=c.originalEventId||u;vt.getState().deleteEventById(v);const h={...c};delete h.originalEventId,vt.getState().insertEvent(h);const m=this.getEventFile(h),g=m?m.path:null;if(r.path){const w=(this.fileEventMap.get(r.path)||[]).filter(k=>k.id!==v);w.length===0?this.fileEventMap.delete(r.path):this.fileEventMap.set(r.path,w)}if(g){const y=this.fileEventMap.get(g)||[];y.some(w=>w.id===h.id)||(y.push(h),this.fileEventMap.set(g,y)),h.path=g}}else if(vt.getState().editEvent(c),r.path){const v=this.fileEventMap.get(r.path)||[],h=v.findIndex(m=>m.id===c.originalEventId);if(h!==-1){const m=[...v];m[h]=c,this.fileEventMap.set(r.path,m)}}const d={...c};return delete d.originalEventId,d}return r}catch(u){return console.error("Failed to edit event:",u),null}}clearEvents(){vt.getState().setEvents([]),this.fileEventMap.clear()}clearEventsForFile(r){if(!this.fileEventMap.has(r))return;const a=[...this.getState().events],o=this.fileEventMap.get(r)||[],s=a.filter(u=>!o.some(c=>c.id===u.id));vt.getState().setEvents(s),this.fileEventMap.delete(r)}async fetchEventsFromFile(r,a){try{const o=[];if(await Iw(a,o),!Array.isArray(o))return[];const s=this.fileEventMap.get(a.path)||[],u=[];for(const v of o){const h=s.find(m=>m.id===v.id||m.title===v.title);!h||h.id!==v.id||h.title!==v.title||new Date(h.end).getTime()!==new Date(v.end).getTime()?u.push(v):u.push(h)}this.fileEventMap.set(a.path,u);const d=[...this.getState().events.filter(v=>v.path!==a.path),...u];return vt.getState().setEvents(d),this.initialized||(this.initialized=!0),u}catch(o){return console.error(`Failed to fetch events from file ${a.path}:`,o),[]}}async createEvent(r,a,o){return await my(r,a,o)}async updateEvent(r,a,o,s,u){const c=this.getEventById(r);return c?await this.editEvent({...c,title:a,eventType:o},s,u):null}parseEventFromLine(r){var u;if(!ax(r))return null;const a=rx(r);if(!a.hasEvent)return null;let o=r.trim();o=o.replace(/^- \d{1,2}:\d{2}(-\d{1,2}:\d{2})?\s+/,"- "),o=o.replace(/⏲\s?\d{1,2}:\d{2}/g,"").trim(),o=o.replace(/📅\s?\d{4}-\d{2}-\d{2}/g,"").trim();const s={title:o};if((u=a.date)!=null&&u.hasDate&&a.date.rawDate){const c=a.date.rawDate;s.start=new Date(c),s.end=new Date(c)}if(a.time){const{hour:c,minute:d}=a.time;s.start&&s.start.setHours(c,d,0,0),s.end&&s.end.setHours(c+1,d,0,0)}return s}async updateEventInFile(r,a,o,s,u,c,d){return await py(r,"",a,o,s,u,c,d)}async createEventInFile(r,a){const o=await my(r,a,"");return typeof o=="string"?o:o.id.toString()}async deleteEventFromFile(r){await yy(r)}getEventFile(r){return Qe.getFile(r)}}const nt=new Kx;function ir(t){"@babel/helpers - typeof";return ir=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(r){return typeof r}:function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},ir(t)}function qx(t,r){if(ir(t)!="object"||!t)return t;var a=t[Symbol.toPrimitive];if(a!==void 0){var o=a.call(t,r);if(ir(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(r==="string"?String:Number)(t)}function Yw(t){var r=qx(t,"string");return ir(r)=="symbol"?r:r+""}function _a(t,r,a){return(r=Yw(r))in t?Object.defineProperty(t,r,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[r]=a,t}function Sy(t,r){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);r&&(o=o.filter(function(s){return Object.getOwnPropertyDescriptor(t,s).enumerable})),a.push.apply(a,o)}return a}function Fe(t){for(var r=1;r<arguments.length;r++){var a=arguments[r]!=null?arguments[r]:{};r%2?Sy(Object(a),!0).forEach(function(o){_a(t,o,a[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):Sy(Object(a)).forEach(function(o){Object.defineProperty(t,o,Object.getOwnPropertyDescriptor(a,o))})}return t}function qu(t,r){if(t==null)return{};var a={};for(var o in t)if({}.hasOwnProperty.call(t,o)){if(r.indexOf(o)!==-1)continue;a[o]=t[o]}return a}function Fn(t,r){if(t==null)return{};var a,o,s=qu(t,r);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(t);for(o=0;o<u.length;o++)a=u[o],r.indexOf(a)===-1&&{}.propertyIsEnumerable.call(t,a)&&(s[a]=t[a])}return s}function rt(t,r){if(!(t instanceof r))throw new TypeError("Cannot call a class as a function")}function ky(t,r){for(var a=0;a<r.length;a++){var o=r[a];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,Yw(o.key),o)}}function at(t,r,a){return r&&ky(t.prototype,r),a&&ky(t,a),Object.defineProperty(t,"prototype",{writable:!1}),t}function Nu(t){return Nu=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Nu(t)}function Hw(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Hw=function(){return!!t})()}function Qx(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Xx(t,r){if(r&&(ir(r)=="object"||typeof r=="function"))return r;if(r!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Qx(t)}function Et(t,r,a){return r=Nu(r),Xx(t,Hw()?Reflect.construct(r,a||[],Nu(t).constructor):r.apply(t,a))}function Au(t,r){return Au=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(a,o){return a.__proto__=o,a},Au(t,r)}function Dt(t,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(r&&r.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),r&&Au(t,r)}function Bw(t){if(Array.isArray(t))return t}function Gx(t,r){var a=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(a!=null){var o,s,u,c,d=[],v=!0,h=!1;try{if(u=(a=a.call(t)).next,r===0){if(Object(a)!==a)return;v=!1}else for(;!(v=(o=u.call(a)).done)&&(d.push(o.value),d.length!==r);v=!0);}catch(m){h=!0,s=m}finally{try{if(!v&&a.return!=null&&(c=a.return(),Object(c)!==c))return}finally{if(h)throw s}}return d}}function iv(t,r){(r==null||r>t.length)&&(r=t.length);for(var a=0,o=Array(r);a<r;a++)o[a]=t[a];return o}function Tv(t,r){if(t){if(typeof t=="string")return iv(t,r);var a={}.toString.call(t).slice(8,-1);return a==="Object"&&t.constructor&&(a=t.constructor.name),a==="Map"||a==="Set"?Array.from(t):a==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?iv(t,r):void 0}}function Ww(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function on(t,r){return Bw(t)||Gx(t,r)||Tv(t,r)||Ww()}function Vw(t){var r,a,o="";if(typeof t=="string"||typeof t=="number")o+=t;else if(typeof t=="object")if(Array.isArray(t))for(r=0;r<t.length;r++)t[r]&&(a=Vw(t[r]))&&(o&&(o+=" "),o+=a);else for(r in t)t[r]&&(o&&(o+=" "),o+=r);return o}function He(){for(var t,r,a=0,o="";a<arguments.length;)(t=arguments[a++])&&(r=Vw(t))&&(o&&(o+=" "),o+=r);return o}const Jx=Object.freeze(Object.defineProperty({__proto__:null,clsx:He,default:He},Symbol.toStringTag,{value:"Module"}));function qe(){return qe=Object.assign?Object.assign.bind():function(t){for(var r=1;r<arguments.length;r++){var a=arguments[r];for(var o in a)({}).hasOwnProperty.call(a,o)&&(t[o]=a[o])}return t},qe.apply(null,arguments)}var Zx=function(t,r,a,o,s,u,c,d){if(!t){var v;if(r===void 0)v=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var h=[a,o,s,u,c,d],m=0;v=new Error(r.replace(/%s/g,function(){return h[m++]})),v.name="Invariant Violation"}throw v.framesToPop=1,v}},eT=Zx;const vl=Ct(eT);var tT=function(){};function nT(t,r){var a={};return Object.keys(t).forEach(function(o){a[Tu(o)]=tT}),a}function Ey(t,r){return t[r]!==void 0}function Tu(t){return"default"+t.charAt(0).toUpperCase()+t.substr(1)}function rT(t){return!!t&&(typeof t!="function"||t.prototype&&t.prototype.isReactComponent)}function aT(t,r){t.prototype=Object.create(r.prototype),t.prototype.constructor=t,Au(t,r)}function Uw(){var t=this.constructor.getDerivedStateFromProps(this.props,this.state);t!=null&&this.setState(t)}function $w(t){function r(a){var o=this.constructor.getDerivedStateFromProps(t,a);return o??null}this.setState(r.bind(this))}function Kw(t,r){try{var a=this.props,o=this.state;this.props=t,this.state=r,this.__reactInternalSnapshotFlag=!0,this.__reactInternalSnapshot=this.getSnapshotBeforeUpdate(a,o)}finally{this.props=a,this.state=o}}Uw.__suppressDeprecationWarning=!0;$w.__suppressDeprecationWarning=!0;Kw.__suppressDeprecationWarning=!0;function oT(t){var r=t.prototype;if(!r||!r.isReactComponent)throw new Error("Can only polyfill class components");if(typeof t.getDerivedStateFromProps!="function"&&typeof r.getSnapshotBeforeUpdate!="function")return t;var a=null,o=null,s=null;if(typeof r.componentWillMount=="function"?a="componentWillMount":typeof r.UNSAFE_componentWillMount=="function"&&(a="UNSAFE_componentWillMount"),typeof r.componentWillReceiveProps=="function"?o="componentWillReceiveProps":typeof r.UNSAFE_componentWillReceiveProps=="function"&&(o="UNSAFE_componentWillReceiveProps"),typeof r.componentWillUpdate=="function"?s="componentWillUpdate":typeof r.UNSAFE_componentWillUpdate=="function"&&(s="UNSAFE_componentWillUpdate"),a!==null||o!==null||s!==null){var u=t.displayName||t.name,c=typeof t.getDerivedStateFromProps=="function"?"getDerivedStateFromProps()":"getSnapshotBeforeUpdate()";throw Error(`Unsafe legacy lifecycles will not be called for components using new component APIs.

`+u+" uses "+c+" but also contains the following legacy lifecycles:"+(a!==null?`
  `+a:"")+(o!==null?`
  `+o:"")+(s!==null?`
  `+s:"")+`

The above lifecycles should be removed. Learn more about this warning here:
https://fb.me/react-async-component-lifecycle-hooks`)}if(typeof t.getDerivedStateFromProps=="function"&&(r.componentWillMount=Uw,r.componentWillReceiveProps=$w),typeof r.getSnapshotBeforeUpdate=="function"){if(typeof r.componentDidUpdate!="function")throw new Error("Cannot polyfill getSnapshotBeforeUpdate() for components that do not define componentDidUpdate() on the prototype");r.componentWillUpdate=Kw;var d=r.componentDidUpdate;r.componentDidUpdate=function(h,m,g){var y=this.__reactInternalSnapshotFlag?this.__reactInternalSnapshot:g;d.call(this,h,m,y)}}return t}var iT="/Users/<USER>/src/uncontrollable/src/uncontrollable.js";function qw(t,r,a){a===void 0&&(a=[]);var o=t.displayName||t.name||"Component",s=rT(t),u=Object.keys(r),c=u.map(Tu);s||!a.length||vl(!1);var d=function(h){aT(m,h);function m(){for(var y,w=arguments.length,k=new Array(w),S=0;S<w;S++)k[S]=arguments[S];y=h.call.apply(h,[this].concat(k))||this,y.handlers=Object.create(null),u.forEach(function(x){var _=r[x],F=function(A){if(y.props[_]){var B;y._notifying=!0;for(var q=arguments.length,Q=new Array(q>1?q-1:0),I=1;I<q;I++)Q[I-1]=arguments[I];(B=y.props)[_].apply(B,[A].concat(Q)),y._notifying=!1}y.unmounted||y.setState(function(V){var J,le=V.values;return{values:qe(Object.create(null),le,(J={},J[x]=A,J))}})};y.handlers[_]=F}),a.length&&(y.attachRef=function(x){y.inner=x});var E=Object.create(null);return u.forEach(function(x){E[x]=y.props[Tu(x)]}),y.state={values:E,prevProps:{}},y}var g=m.prototype;return g.shouldComponentUpdate=function(){return!this._notifying},m.getDerivedStateFromProps=function(w,k){var S=k.values,E=k.prevProps,x={values:qe(Object.create(null),S),prevProps:{}};return u.forEach(function(_){x.prevProps[_]=w[_],!Ey(w,_)&&Ey(E,_)&&(x.values[_]=w[Tu(_)])}),x},g.componentWillUnmount=function(){this.unmounted=!0},g.render=function(){var w=this,k=this.props,S=k.innerRef,E=qu(k,["innerRef"]);c.forEach(function(_){delete E[_]});var x={};return u.forEach(function(_){var F=w.props[_];x[_]=F!==void 0?F:w.state.values[_]}),R.createElement(t,qe({},E,x,this.handlers,{ref:S||this.attachRef}))},m}(R.Component);oT(d),d.displayName="Uncontrolled("+o+")",d.propTypes=qe({innerRef:function(){}},nT(r)),a.forEach(function(h){d.prototype[h]=function(){var g;return(g=this.inner)[h].apply(g,arguments)}});var v=d;return R.forwardRef&&(v=R.forwardRef(function(h,m){return R.createElement(d,qe({},h,{innerRef:m,__source:{fileName:iT,lineNumber:128},__self:this}))}),v.propTypes=d.propTypes),v.ControlledComponent=t,v.deferControlTo=function(h,m,g){return m===void 0&&(m={}),qw(h,qe({},r,m),g)},v}var Qw={exports:{}},Hd,Dy;function lT(){if(Dy)return Hd;Dy=1;var t="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";return Hd=t,Hd}var Bd,xy;function sT(){if(xy)return Bd;xy=1;var t=lT();function r(){}function a(){}return a.resetWarningCache=r,Bd=function(){function o(c,d,v,h,m,g){if(g!==t){var y=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw y.name="Invariant Violation",y}}o.isRequired=o;function s(){return o}var u={array:o,bigint:o,bool:o,func:o,number:o,object:o,string:o,symbol:o,any:o,arrayOf:s,element:o,elementType:o,instanceOf:s,node:o,objectOf:s,oneOf:s,oneOfType:s,shape:s,exact:s,checkPropTypes:a,resetWarningCache:r};return u.PropTypes=u,u},Bd}Qw.exports=sT()();var _v=Qw.exports;const oe=Ct(_v);var Ru="milliseconds",pl="seconds",hl="minutes",ml="hours",Pa="day",Co="week",gl="month",Ma="year",Na="decade",Aa="century",Xw={milliseconds:1,seconds:1e3,minutes:60*1e3,hours:60*60*1e3,day:24*60*60*1e3,week:7*24*60*60*1e3},uT={month:1,year:12,decade:10*12,century:100*12};function cT(t){return[31,fT(t),31,30,31,30,31,31,30,31,30,31]}function fT(t){return t%4===0&&t%100!==0||t%400===0?29:28}function bn(t,r,a){switch(t=new Date(t),a){case Ru:case pl:case hl:case ml:case Pa:case Co:return dT(t,r*Xw[a]);case gl:case Ma:case Na:case Aa:return vT(t,r*uT[a])}throw new TypeError('Invalid units: "'+a+'"')}function dT(t,r){var a=new Date(+t+r);return pT(t,a)}function vT(t,r){var a=t.getFullYear(),o=t.getMonth(),s=t.getDate(),u=a*12+o+r,c=Math.trunc(u/12),d=u%12,v=Math.min(s,cT(c)[d]),h=new Date(t);return h.setFullYear(c),h.setDate(1),h.setMonth(d),h.setDate(v),h}function pT(t,r){var a=t.getTimezoneOffset(),o=r.getTimezoneOffset(),s=o-a;return new Date(+r+s*Xw.minutes)}function yl(t,r,a){return bn(t,-r,a)}function ht(t,r,a){switch(t=new Date(t),r){case Aa:case Na:case Ma:t=zu(t,0);case gl:t=t0(t,1);case Co:case Pa:t=Sl(t,0);case ml:t=Po(t,0);case hl:t=wl(t,0);case pl:t=bl(t,0)}return r===Na&&(t=yl(t,Ra(t)%10,"year")),r===Aa&&(t=yl(t,Ra(t)%100,"year")),r===Co&&(t=n0(t,0,a)),t}function Fu(t,r,a){switch(t=new Date(t),t=ht(t,r,a),r){case Aa:case Na:case Ma:case gl:case Co:t=bn(t,1,r),t=yl(t,1,Pa),t.setHours(23,59,59,999);break;case Pa:t.setHours(23,59,59,999);break;case ml:case hl:case pl:t=bn(t,1,r),t=yl(t,1,Ru)}return t}var Yl=Bo(function(t,r){return t===r}),Ov=Bo(function(t,r){return t!==r}),Qu=Bo(function(t,r){return t>r}),Hl=Bo(function(t,r){return t>=r}),Cv=Bo(function(t,r){return t<r}),Ho=Bo(function(t,r){return t<=r});function Gw(){return new Date(Math.min.apply(Math,arguments))}function Jw(){return new Date(Math.max.apply(Math,arguments))}function Zw(t,r,a,o){return o=o||"day",(!r||Hl(t,r,o))&&(!a||Ho(t,a,o))}var bl=Ur("Milliseconds"),wl=Ur("Seconds"),Po=Ur("Minutes"),Sl=Ur("Hours"),e0=Ur("Day"),t0=Ur("Date"),zu=Ur("Month"),Ra=Ur("FullYear");function hT(t,r){return r===void 0?Ra(ht(t,Na)):bn(t,r+10,Ma)}function mT(t,r){return r===void 0?Ra(ht(t,Aa)):bn(t,r+100,Ma)}function n0(t,r,a){var o=(e0(t)+7-(a||0))%7;return r===void 0?o:bn(t,r-o,Pa)}function gT(t,r,a,o){var s,u,c;switch(a){case Ru:case pl:case hl:case ml:case Pa:case Co:s=r.getTime()-t.getTime();break;case gl:case Ma:case Na:case Aa:s=(Ra(r)-Ra(t))*12+zu(r)-zu(t);break;default:throw new TypeError('Invalid units: "'+a+'"')}switch(a){case Ru:u=1;break;case pl:u=1e3;break;case hl:u=1e3*60;break;case ml:u=1e3*60*60;break;case Pa:u=1e3*60*60*24;break;case Co:u=1e3*60*60*24*7;break;case gl:u=1;break;case Ma:u=12;break;case Na:u=120;break;case Aa:u=1200;break;default:throw new TypeError('Invalid units: "'+a+'"')}return c=s/u,o?c:Math.round(c)}function Ur(t){var r=function(a){switch(a){case"Milliseconds":return 36e5;case"Seconds":return 3600;case"Minutes":return 60;case"Hours":return 1;default:return null}}(t);return function(a,o){if(o===void 0)return a["get"+t]();var s=new Date(a);return s["set"+t](o),r&&s["get"+t]()!=o&&(t==="Hours"||o>=r&&s.getHours()-a.getHours()<Math.floor(o/r))&&s["set"+t](o+r),s}}function Bo(t){return function(r,a,o){return t(+ht(r,o),+ht(a,o))}}const Ty=Object.freeze(Object.defineProperty({__proto__:null,add:bn,century:mT,date:t0,day:e0,decade:hT,diff:gT,endOf:Fu,eq:Yl,gt:Qu,gte:Hl,hours:Sl,inRange:Zw,lt:Cv,lte:Ho,max:Jw,milliseconds:bl,min:Gw,minutes:Po,month:zu,neq:Ov,seconds:wl,startOf:ht,subtract:yl,weekday:n0,year:Ra},Symbol.toStringTag,{value:"Module"}));function yT(t){if(Array.isArray(t))return iv(t)}function r0(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function bT(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function _u(t){return yT(t)||r0(t)||Tv(t)||bT()}function wT(t,r,a){var o=-1,s=t.length;r<0&&(r=-r>s?0:s+r),a=a>s?s:a,a<0&&(a+=s),s=r>a?0:a-r>>>0,r>>>=0;for(var u=Array(s);++o<s;)u[o]=t[o+r];return u}var a0=wT;function ST(t,r){return t===r||t!==t&&r!==r}var Bl=ST,kT=typeof gn=="object"&&gn&&gn.Object===Object&&gn,o0=kT,ET=o0,DT=typeof self=="object"&&self&&self.Object===Object&&self,xT=ET||DT||Function("return this")(),Ln=xT,TT=Ln,_T=TT.Symbol,Wo=_T,_y=Wo,i0=Object.prototype,OT=i0.hasOwnProperty,CT=i0.toString,rl=_y?_y.toStringTag:void 0;function PT(t){var r=OT.call(t,rl),a=t[rl];try{t[rl]=void 0;var o=!0}catch{}var s=CT.call(t);return o&&(r?t[rl]=a:delete t[rl]),s}var MT=PT,NT=Object.prototype,AT=NT.toString;function RT(t){return AT.call(t)}var FT=RT,Oy=Wo,zT=MT,LT=FT,jT="[object Null]",IT="[object Undefined]",Cy=Oy?Oy.toStringTag:void 0;function YT(t){return t==null?t===void 0?IT:jT:Cy&&Cy in Object(t)?zT(t):LT(t)}var Vo=YT;function HT(t){var r=typeof t;return t!=null&&(r=="object"||r=="function")}var ur=HT,BT=Vo,WT=ur,VT="[object AsyncFunction]",UT="[object Function]",$T="[object GeneratorFunction]",KT="[object Proxy]";function qT(t){if(!WT(t))return!1;var r=BT(t);return r==UT||r==$T||r==VT||r==KT}var Pv=qT,QT=9007199254740991;function XT(t){return typeof t=="number"&&t>-1&&t%1==0&&t<=QT}var Mv=XT,GT=Pv,JT=Mv;function ZT(t){return t!=null&&JT(t.length)&&!GT(t)}var Wl=ZT,e_=9007199254740991,t_=/^(?:0|[1-9]\d*)$/;function n_(t,r){var a=typeof t;return r=r??e_,!!r&&(a=="number"||a!="symbol"&&t_.test(t))&&t>-1&&t%1==0&&t<r}var Nv=n_,r_=Bl,a_=Wl,o_=Nv,i_=ur;function l_(t,r,a){if(!i_(a))return!1;var o=typeof r;return(o=="number"?a_(a)&&o_(r,a.length):o=="string"&&r in a)?r_(a[r],t):!1}var Xu=l_,s_=/\s/;function u_(t){for(var r=t.length;r--&&s_.test(t.charAt(r)););return r}var c_=u_,f_=c_,d_=/^\s+/;function v_(t){return t&&t.slice(0,f_(t)+1).replace(d_,"")}var p_=v_;function h_(t){return t!=null&&typeof t=="object"}var $r=h_,m_=Vo,g_=$r,y_="[object Symbol]";function b_(t){return typeof t=="symbol"||g_(t)&&m_(t)==y_}var Vl=b_,w_=p_,Py=ur,S_=Vl,My=NaN,k_=/^[-+]0x[0-9a-f]+$/i,E_=/^0b[01]+$/i,D_=/^0o[0-7]+$/i,x_=parseInt;function T_(t){if(typeof t=="number")return t;if(S_(t))return My;if(Py(t)){var r=typeof t.valueOf=="function"?t.valueOf():t;t=Py(r)?r+"":r}if(typeof t!="string")return t===0?t:+t;t=w_(t);var a=E_.test(t);return a||D_.test(t)?x_(t.slice(2),a?2:8):k_.test(t)?My:+t}var __=T_,O_=__,C_=1/0,P_=17976931348623157e292;function M_(t){if(!t)return t===0?t:0;if(t=O_(t),t===C_||t===-1/0){var r=t<0?-1:1;return r*P_}return t===t?t:0}var l0=M_,N_=l0;function A_(t){var r=N_(t),a=r%1;return r===r?a?r-a:r:0}var s0=A_,R_=a0,F_=Xu,z_=s0,L_=Math.ceil,j_=Math.max;function I_(t,r,a){(a?F_(t,r,a):r===void 0)?r=1:r=j_(z_(r),0);var o=t==null?0:t.length;if(!o||r<1)return[];for(var s=0,u=0,c=Array(L_(o/r));s<o;)c[u++]=R_(t,s,s+=r);return c}var Y_=I_;const H_=Ct(Y_);function Kr(t){return t&&t.ownerDocument||document}function Av(t){var r=Kr(t);return r&&r.defaultView||window}function Rv(t,r){return Av(t).getComputedStyle(t,r)}var B_=/([A-Z])/g;function u0(t){return t.replace(B_,"-$1").toLowerCase()}var W_=/^ms-/;function pu(t){return u0(t).replace(W_,"-ms-")}var V_=/^((translate|rotate|scale)(X|Y|Z|3d)?|matrix(3d)?|perspective|skew(X|Y)?)$/i;function c0(t){return!!(t&&V_.test(t))}function pt(t,r){var a="",o="";if(typeof r=="string")return t.style.getPropertyValue(pu(r))||Rv(t).getPropertyValue(pu(r));Object.keys(r).forEach(function(s){var u=r[s];!u&&u!==0?t.style.removeProperty(pu(s)):c0(s)?o+=s+"("+u+") ":a+=pu(s)+": "+u+";"}),o&&(a+="transform: "+o+";"),t.style.cssText+=";"+a}function lr(t,r){if(t.contains)return t.contains(r);if(t.compareDocumentPosition)return t===r||!!(t.compareDocumentPosition(r)&16)}const U_=Object.freeze(Object.defineProperty({__proto__:null,default:lr},Symbol.toStringTag,{value:"Module"}));function f0(t){return"nodeType"in t&&t.nodeType===document.DOCUMENT_NODE}function Gu(t){return"window"in t&&t.window===t?t:f0(t)&&t.defaultView||!1}function d0(t){var r=t==="pageXOffset"?"scrollLeft":"scrollTop";function a(o,s){var u=Gu(o);if(s===void 0)return u?u[t]:o[r];u?u.scrollTo(u[t],s):o[r]=s}return a}const Ju=d0("pageXOffset"),Mo=d0("pageYOffset");function yn(t){var r=Kr(t),a={top:0,left:0,height:0,width:0},o=r&&r.documentElement;return!o||!lr(o,t)||(t.getBoundingClientRect!==void 0&&(a=t.getBoundingClientRect()),a={top:a.top+Mo(o)-(o.clientTop||0),left:a.left+Ju(o)-(o.clientLeft||0),width:a.width,height:a.height}),a}var $_=function(r){return!!r&&"offsetParent"in r};function Fv(t){for(var r=Kr(t),a=t&&t.offsetParent;$_(a)&&a.nodeName!=="HTML"&&pt(a,"position")==="static";)a=a.offsetParent;return a||r.documentElement}var K_=function(r){return r.nodeName&&r.nodeName.toLowerCase()};function Zu(t,r){var a={top:0,left:0},o;if(pt(t,"position")==="fixed")o=t.getBoundingClientRect();else{var s=r||Fv(t);o=yn(t),K_(s)!=="html"&&(a=yn(s));var u=String(pt(s,"borderTopWidth")||0);a.top+=parseInt(u,10)-Mo(s)||0;var c=String(pt(s,"borderLeftWidth")||0);a.left+=parseInt(c,10)-Ju(s)||0}var d=String(pt(t,"marginTop")||0),v=String(pt(t,"marginLeft")||0);return qe({},o,{top:o.top-a.top-(parseInt(d,10)||0),left:o.left-a.left-(parseInt(v,10)||0)})}const zv=!!(typeof window<"u"&&window.document&&window.document.createElement);var Ny=new Date().getTime();function q_(t){var r=new Date().getTime(),a=Math.max(0,16-(r-Ny)),o=setTimeout(t,a);return Ny=r,o}var Q_=["","webkit","moz","o","ms"],lv="clearTimeout",sv=q_,Ay=function(r,a){return r+(r?a[0].toUpperCase()+a.substr(1):a)+"AnimationFrame"};zv&&Q_.some(function(t){var r=Ay(t,"request");return r in window&&(lv=Ay(t,"cancel"),sv=function(o){return window[r](o)}),!!sv});var kl=function(r){typeof window[lv]=="function"&&window[lv](r)},Ul=sv,Wd;function ec(t,r){if(!Wd){var a=document.body,o=a.matches||a.matchesSelector||a.webkitMatchesSelector||a.mozMatchesSelector||a.msMatchesSelector;Wd=function(u,c){return o.call(u,c)}}return Wd(t,r)}var X_=Function.prototype.bind.call(Function.prototype.call,[].slice);function tc(t,r){return X_(t.querySelectorAll(r))}var uv=!1,cv=!1;try{var Vd={get passive(){return uv=!0},get once(){return cv=uv=!0}};zv&&(window.addEventListener("test",Vd,Vd),window.removeEventListener("test",Vd,!0))}catch{}function Lv(t,r,a,o){if(o&&typeof o!="boolean"&&!cv){var s=o.once,u=o.capture,c=a;!cv&&s&&(c=a.__once||function d(v){this.removeEventListener(r,d,u),a.call(this,v)},a.__once=c),t.addEventListener(r,c,uv?o:u)}t.addEventListener(r,a,o)}function G_(t){const r=te.useRef(t);return te.useEffect(()=>{r.current=t},[t]),r}function Ry(t){const r=G_(t);return te.useCallback(function(...a){return r.current&&r.current(...a)},[r])}function Fy(){return te.useState(null)}function J_(){const t=te.useRef(!0),r=te.useRef(()=>t.current);return te.useEffect(()=>(t.current=!0,()=>{t.current=!1}),[]),r.current}function Z_(t){const r=J_();return[t[0],te.useCallback(a=>{if(r())return t[1](a)},[r,t[1]])]}var Ft="top",ln="bottom",sn="right",zt="left",jv="auto",$l=[Ft,ln,sn,zt],No="start",El="end",eO="clippingParents",v0="viewport",al="popper",tO="reference",zy=$l.reduce(function(t,r){return t.concat([r+"-"+No,r+"-"+El])},[]),Iv=[].concat($l,[jv]).reduce(function(t,r){return t.concat([r,r+"-"+No,r+"-"+El])},[]),nO="beforeRead",rO="read",aO="afterRead",oO="beforeMain",iO="main",lO="afterMain",sO="beforeWrite",uO="write",cO="afterWrite",fO=[nO,rO,aO,oO,iO,lO,sO,uO,cO];function Nn(t){return t.split("-")[0]}function $t(t){if(t==null)return window;if(t.toString()!=="[object Window]"){var r=t.ownerDocument;return r&&r.defaultView||window}return t}function Fa(t){var r=$t(t).Element;return t instanceof r||t instanceof Element}function An(t){var r=$t(t).HTMLElement;return t instanceof r||t instanceof HTMLElement}function Yv(t){if(typeof ShadowRoot>"u")return!1;var r=$t(t).ShadowRoot;return t instanceof r||t instanceof ShadowRoot}var Oa=Math.max,Lu=Math.min,Ao=Math.round;function fv(){var t=navigator.userAgentData;return t!=null&&t.brands&&Array.isArray(t.brands)?t.brands.map(function(r){return r.brand+"/"+r.version}).join(" "):navigator.userAgent}function p0(){return!/^((?!chrome|android).)*safari/i.test(fv())}function Ro(t,r,a){r===void 0&&(r=!1),a===void 0&&(a=!1);var o=t.getBoundingClientRect(),s=1,u=1;r&&An(t)&&(s=t.offsetWidth>0&&Ao(o.width)/t.offsetWidth||1,u=t.offsetHeight>0&&Ao(o.height)/t.offsetHeight||1);var c=Fa(t)?$t(t):window,d=c.visualViewport,v=!p0()&&a,h=(o.left+(v&&d?d.offsetLeft:0))/s,m=(o.top+(v&&d?d.offsetTop:0))/u,g=o.width/s,y=o.height/u;return{width:g,height:y,top:m,right:h+g,bottom:m+y,left:h,x:h,y:m}}function Hv(t){var r=Ro(t),a=t.offsetWidth,o=t.offsetHeight;return Math.abs(r.width-a)<=1&&(a=r.width),Math.abs(r.height-o)<=1&&(o=r.height),{x:t.offsetLeft,y:t.offsetTop,width:a,height:o}}function h0(t,r){var a=r.getRootNode&&r.getRootNode();if(t.contains(r))return!0;if(a&&Yv(a)){var o=r;do{if(o&&t.isSameNode(o))return!0;o=o.parentNode||o.host}while(o)}return!1}function Br(t){return t?(t.nodeName||"").toLowerCase():null}function sr(t){return $t(t).getComputedStyle(t)}function dO(t){return["table","td","th"].indexOf(Br(t))>=0}function qr(t){return((Fa(t)?t.ownerDocument:t.document)||window.document).documentElement}function nc(t){return Br(t)==="html"?t:t.assignedSlot||t.parentNode||(Yv(t)?t.host:null)||qr(t)}function Ly(t){return!An(t)||sr(t).position==="fixed"?null:t.offsetParent}function vO(t){var r=/firefox/i.test(fv()),a=/Trident/i.test(fv());if(a&&An(t)){var o=sr(t);if(o.position==="fixed")return null}var s=nc(t);for(Yv(s)&&(s=s.host);An(s)&&["html","body"].indexOf(Br(s))<0;){var u=sr(s);if(u.transform!=="none"||u.perspective!=="none"||u.contain==="paint"||["transform","perspective"].indexOf(u.willChange)!==-1||r&&u.willChange==="filter"||r&&u.filter&&u.filter!=="none")return s;s=s.parentNode}return null}function Kl(t){for(var r=$t(t),a=Ly(t);a&&dO(a)&&sr(a).position==="static";)a=Ly(a);return a&&(Br(a)==="html"||Br(a)==="body"&&sr(a).position==="static")?r:a||vO(t)||r}function Bv(t){return["top","bottom"].indexOf(t)>=0?"x":"y"}function cl(t,r,a){return Oa(t,Lu(r,a))}function pO(t,r,a){var o=cl(t,r,a);return o>a?a:o}function m0(){return{top:0,right:0,bottom:0,left:0}}function g0(t){return Object.assign({},m0(),t)}function y0(t,r){return r.reduce(function(a,o){return a[o]=t,a},{})}var hO=function(r,a){return r=typeof r=="function"?r(Object.assign({},a.rects,{placement:a.placement})):r,g0(typeof r!="number"?r:y0(r,$l))};function mO(t){var r,a=t.state,o=t.name,s=t.options,u=a.elements.arrow,c=a.modifiersData.popperOffsets,d=Nn(a.placement),v=Bv(d),h=[zt,sn].indexOf(d)>=0,m=h?"height":"width";if(!(!u||!c)){var g=hO(s.padding,a),y=Hv(u),w=v==="y"?Ft:zt,k=v==="y"?ln:sn,S=a.rects.reference[m]+a.rects.reference[v]-c[v]-a.rects.popper[m],E=c[v]-a.rects.reference[v],x=Kl(u),_=x?v==="y"?x.clientHeight||0:x.clientWidth||0:0,F=S/2-E/2,C=g[w],A=_-y[m]-g[k],B=_/2-y[m]/2+F,q=cl(C,B,A),Q=v;a.modifiersData[o]=(r={},r[Q]=q,r.centerOffset=q-B,r)}}function gO(t){var r=t.state,a=t.options,o=a.element,s=o===void 0?"[data-popper-arrow]":o;s!=null&&(typeof s=="string"&&(s=r.elements.popper.querySelector(s),!s)||h0(r.elements.popper,s)&&(r.elements.arrow=s))}const yO={name:"arrow",enabled:!0,phase:"main",fn:mO,effect:gO,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Fo(t){return t.split("-")[1]}var bO={top:"auto",right:"auto",bottom:"auto",left:"auto"};function wO(t,r){var a=t.x,o=t.y,s=r.devicePixelRatio||1;return{x:Ao(a*s)/s||0,y:Ao(o*s)/s||0}}function jy(t){var r,a=t.popper,o=t.popperRect,s=t.placement,u=t.variation,c=t.offsets,d=t.position,v=t.gpuAcceleration,h=t.adaptive,m=t.roundOffsets,g=t.isFixed,y=c.x,w=y===void 0?0:y,k=c.y,S=k===void 0?0:k,E=typeof m=="function"?m({x:w,y:S}):{x:w,y:S};w=E.x,S=E.y;var x=c.hasOwnProperty("x"),_=c.hasOwnProperty("y"),F=zt,C=Ft,A=window;if(h){var B=Kl(a),q="clientHeight",Q="clientWidth";if(B===$t(a)&&(B=qr(a),sr(B).position!=="static"&&d==="absolute"&&(q="scrollHeight",Q="scrollWidth")),s===Ft||(s===zt||s===sn)&&u===El){C=ln;var I=g&&B===A&&A.visualViewport?A.visualViewport.height:B[q];S-=I-o.height,S*=v?1:-1}if(s===zt||(s===Ft||s===ln)&&u===El){F=sn;var V=g&&B===A&&A.visualViewport?A.visualViewport.width:B[Q];w-=V-o.width,w*=v?1:-1}}var J=Object.assign({position:d},h&&bO),le=m===!0?wO({x:w,y:S},$t(a)):{x:w,y:S};if(w=le.x,S=le.y,v){var ae;return Object.assign({},J,(ae={},ae[C]=_?"0":"",ae[F]=x?"0":"",ae.transform=(A.devicePixelRatio||1)<=1?"translate("+w+"px, "+S+"px)":"translate3d("+w+"px, "+S+"px, 0)",ae))}return Object.assign({},J,(r={},r[C]=_?S+"px":"",r[F]=x?w+"px":"",r.transform="",r))}function SO(t){var r=t.state,a=t.options,o=a.gpuAcceleration,s=o===void 0?!0:o,u=a.adaptive,c=u===void 0?!0:u,d=a.roundOffsets,v=d===void 0?!0:d,h={placement:Nn(r.placement),variation:Fo(r.placement),popper:r.elements.popper,popperRect:r.rects.popper,gpuAcceleration:s,isFixed:r.options.strategy==="fixed"};r.modifiersData.popperOffsets!=null&&(r.styles.popper=Object.assign({},r.styles.popper,jy(Object.assign({},h,{offsets:r.modifiersData.popperOffsets,position:r.options.strategy,adaptive:c,roundOffsets:v})))),r.modifiersData.arrow!=null&&(r.styles.arrow=Object.assign({},r.styles.arrow,jy(Object.assign({},h,{offsets:r.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:v})))),r.attributes.popper=Object.assign({},r.attributes.popper,{"data-popper-placement":r.placement})}const kO={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:SO,data:{}};var hu={passive:!0};function EO(t){var r=t.state,a=t.instance,o=t.options,s=o.scroll,u=s===void 0?!0:s,c=o.resize,d=c===void 0?!0:c,v=$t(r.elements.popper),h=[].concat(r.scrollParents.reference,r.scrollParents.popper);return u&&h.forEach(function(m){m.addEventListener("scroll",a.update,hu)}),d&&v.addEventListener("resize",a.update,hu),function(){u&&h.forEach(function(m){m.removeEventListener("scroll",a.update,hu)}),d&&v.removeEventListener("resize",a.update,hu)}}const DO={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:EO,data:{}};var xO={left:"right",right:"left",bottom:"top",top:"bottom"};function Ou(t){return t.replace(/left|right|bottom|top/g,function(r){return xO[r]})}var TO={start:"end",end:"start"};function Iy(t){return t.replace(/start|end/g,function(r){return TO[r]})}function Wv(t){var r=$t(t),a=r.pageXOffset,o=r.pageYOffset;return{scrollLeft:a,scrollTop:o}}function Vv(t){return Ro(qr(t)).left+Wv(t).scrollLeft}function _O(t,r){var a=$t(t),o=qr(t),s=a.visualViewport,u=o.clientWidth,c=o.clientHeight,d=0,v=0;if(s){u=s.width,c=s.height;var h=p0();(h||!h&&r==="fixed")&&(d=s.offsetLeft,v=s.offsetTop)}return{width:u,height:c,x:d+Vv(t),y:v}}function OO(t){var r,a=qr(t),o=Wv(t),s=(r=t.ownerDocument)==null?void 0:r.body,u=Oa(a.scrollWidth,a.clientWidth,s?s.scrollWidth:0,s?s.clientWidth:0),c=Oa(a.scrollHeight,a.clientHeight,s?s.scrollHeight:0,s?s.clientHeight:0),d=-o.scrollLeft+Vv(t),v=-o.scrollTop;return sr(s||a).direction==="rtl"&&(d+=Oa(a.clientWidth,s?s.clientWidth:0)-u),{width:u,height:c,x:d,y:v}}function Uv(t){var r=sr(t),a=r.overflow,o=r.overflowX,s=r.overflowY;return/auto|scroll|overlay|hidden/.test(a+s+o)}function b0(t){return["html","body","#document"].indexOf(Br(t))>=0?t.ownerDocument.body:An(t)&&Uv(t)?t:b0(nc(t))}function fl(t,r){var a;r===void 0&&(r=[]);var o=b0(t),s=o===((a=t.ownerDocument)==null?void 0:a.body),u=$t(o),c=s?[u].concat(u.visualViewport||[],Uv(o)?o:[]):o,d=r.concat(c);return s?d:d.concat(fl(nc(c)))}function dv(t){return Object.assign({},t,{left:t.x,top:t.y,right:t.x+t.width,bottom:t.y+t.height})}function CO(t,r){var a=Ro(t,!1,r==="fixed");return a.top=a.top+t.clientTop,a.left=a.left+t.clientLeft,a.bottom=a.top+t.clientHeight,a.right=a.left+t.clientWidth,a.width=t.clientWidth,a.height=t.clientHeight,a.x=a.left,a.y=a.top,a}function Yy(t,r,a){return r===v0?dv(_O(t,a)):Fa(r)?CO(r,a):dv(OO(qr(t)))}function PO(t){var r=fl(nc(t)),a=["absolute","fixed"].indexOf(sr(t).position)>=0,o=a&&An(t)?Kl(t):t;return Fa(o)?r.filter(function(s){return Fa(s)&&h0(s,o)&&Br(s)!=="body"}):[]}function MO(t,r,a,o){var s=r==="clippingParents"?PO(t):[].concat(r),u=[].concat(s,[a]),c=u[0],d=u.reduce(function(v,h){var m=Yy(t,h,o);return v.top=Oa(m.top,v.top),v.right=Lu(m.right,v.right),v.bottom=Lu(m.bottom,v.bottom),v.left=Oa(m.left,v.left),v},Yy(t,c,o));return d.width=d.right-d.left,d.height=d.bottom-d.top,d.x=d.left,d.y=d.top,d}function w0(t){var r=t.reference,a=t.element,o=t.placement,s=o?Nn(o):null,u=o?Fo(o):null,c=r.x+r.width/2-a.width/2,d=r.y+r.height/2-a.height/2,v;switch(s){case Ft:v={x:c,y:r.y-a.height};break;case ln:v={x:c,y:r.y+r.height};break;case sn:v={x:r.x+r.width,y:d};break;case zt:v={x:r.x-a.width,y:d};break;default:v={x:r.x,y:r.y}}var h=s?Bv(s):null;if(h!=null){var m=h==="y"?"height":"width";switch(u){case No:v[h]=v[h]-(r[m]/2-a[m]/2);break;case El:v[h]=v[h]+(r[m]/2-a[m]/2);break}}return v}function Dl(t,r){r===void 0&&(r={});var a=r,o=a.placement,s=o===void 0?t.placement:o,u=a.strategy,c=u===void 0?t.strategy:u,d=a.boundary,v=d===void 0?eO:d,h=a.rootBoundary,m=h===void 0?v0:h,g=a.elementContext,y=g===void 0?al:g,w=a.altBoundary,k=w===void 0?!1:w,S=a.padding,E=S===void 0?0:S,x=g0(typeof E!="number"?E:y0(E,$l)),_=y===al?tO:al,F=t.rects.popper,C=t.elements[k?_:y],A=MO(Fa(C)?C:C.contextElement||qr(t.elements.popper),v,m,c),B=Ro(t.elements.reference),q=w0({reference:B,element:F,placement:s}),Q=dv(Object.assign({},F,q)),I=y===al?Q:B,V={top:A.top-I.top+x.top,bottom:I.bottom-A.bottom+x.bottom,left:A.left-I.left+x.left,right:I.right-A.right+x.right},J=t.modifiersData.offset;if(y===al&&J){var le=J[s];Object.keys(V).forEach(function(ae){var fe=[sn,ln].indexOf(ae)>=0?1:-1,me=[Ft,ln].indexOf(ae)>=0?"y":"x";V[ae]+=le[me]*fe})}return V}function NO(t,r){r===void 0&&(r={});var a=r,o=a.placement,s=a.boundary,u=a.rootBoundary,c=a.padding,d=a.flipVariations,v=a.allowedAutoPlacements,h=v===void 0?Iv:v,m=Fo(o),g=m?d?zy:zy.filter(function(k){return Fo(k)===m}):$l,y=g.filter(function(k){return h.indexOf(k)>=0});y.length===0&&(y=g);var w=y.reduce(function(k,S){return k[S]=Dl(t,{placement:S,boundary:s,rootBoundary:u,padding:c})[Nn(S)],k},{});return Object.keys(w).sort(function(k,S){return w[k]-w[S]})}function AO(t){if(Nn(t)===jv)return[];var r=Ou(t);return[Iy(t),r,Iy(r)]}function RO(t){var r=t.state,a=t.options,o=t.name;if(!r.modifiersData[o]._skip){for(var s=a.mainAxis,u=s===void 0?!0:s,c=a.altAxis,d=c===void 0?!0:c,v=a.fallbackPlacements,h=a.padding,m=a.boundary,g=a.rootBoundary,y=a.altBoundary,w=a.flipVariations,k=w===void 0?!0:w,S=a.allowedAutoPlacements,E=r.options.placement,x=Nn(E),_=x===E,F=v||(_||!k?[Ou(E)]:AO(E)),C=[E].concat(F).reduce(function(L,G){return L.concat(Nn(G)===jv?NO(r,{placement:G,boundary:m,rootBoundary:g,padding:h,flipVariations:k,allowedAutoPlacements:S}):G)},[]),A=r.rects.reference,B=r.rects.popper,q=new Map,Q=!0,I=C[0],V=0;V<C.length;V++){var J=C[V],le=Nn(J),ae=Fo(J)===No,fe=[Ft,ln].indexOf(le)>=0,me=fe?"width":"height",$=Dl(r,{placement:J,boundary:m,rootBoundary:g,altBoundary:y,padding:h}),ie=fe?ae?sn:zt:ae?ln:Ft;A[me]>B[me]&&(ie=Ou(ie));var se=Ou(ie),ge=[];if(u&&ge.push($[le]<=0),d&&ge.push($[ie]<=0,$[se]<=0),ge.every(function(L){return L})){I=J,Q=!1;break}q.set(J,ge)}if(Q)for(var O=k?3:1,P=function(G){var ne=C.find(function(ve){var ce=q.get(ve);if(ce)return ce.slice(0,G).every(function(Oe){return Oe})});if(ne)return I=ne,"break"},H=O;H>0;H--){var W=P(H);if(W==="break")break}r.placement!==I&&(r.modifiersData[o]._skip=!0,r.placement=I,r.reset=!0)}}const FO={name:"flip",enabled:!0,phase:"main",fn:RO,requiresIfExists:["offset"],data:{_skip:!1}};function Hy(t,r,a){return a===void 0&&(a={x:0,y:0}),{top:t.top-r.height-a.y,right:t.right-r.width+a.x,bottom:t.bottom-r.height+a.y,left:t.left-r.width-a.x}}function By(t){return[Ft,sn,ln,zt].some(function(r){return t[r]>=0})}function zO(t){var r=t.state,a=t.name,o=r.rects.reference,s=r.rects.popper,u=r.modifiersData.preventOverflow,c=Dl(r,{elementContext:"reference"}),d=Dl(r,{altBoundary:!0}),v=Hy(c,o),h=Hy(d,s,u),m=By(v),g=By(h);r.modifiersData[a]={referenceClippingOffsets:v,popperEscapeOffsets:h,isReferenceHidden:m,hasPopperEscaped:g},r.attributes.popper=Object.assign({},r.attributes.popper,{"data-popper-reference-hidden":m,"data-popper-escaped":g})}const LO={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:zO};function jO(t,r,a){var o=Nn(t),s=[zt,Ft].indexOf(o)>=0?-1:1,u=typeof a=="function"?a(Object.assign({},r,{placement:t})):a,c=u[0],d=u[1];return c=c||0,d=(d||0)*s,[zt,sn].indexOf(o)>=0?{x:d,y:c}:{x:c,y:d}}function IO(t){var r=t.state,a=t.options,o=t.name,s=a.offset,u=s===void 0?[0,0]:s,c=Iv.reduce(function(m,g){return m[g]=jO(g,r.rects,u),m},{}),d=c[r.placement],v=d.x,h=d.y;r.modifiersData.popperOffsets!=null&&(r.modifiersData.popperOffsets.x+=v,r.modifiersData.popperOffsets.y+=h),r.modifiersData[o]=c}const YO={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:IO};function HO(t){var r=t.state,a=t.name;r.modifiersData[a]=w0({reference:r.rects.reference,element:r.rects.popper,placement:r.placement})}const BO={name:"popperOffsets",enabled:!0,phase:"read",fn:HO,data:{}};function WO(t){return t==="x"?"y":"x"}function VO(t){var r=t.state,a=t.options,o=t.name,s=a.mainAxis,u=s===void 0?!0:s,c=a.altAxis,d=c===void 0?!1:c,v=a.boundary,h=a.rootBoundary,m=a.altBoundary,g=a.padding,y=a.tether,w=y===void 0?!0:y,k=a.tetherOffset,S=k===void 0?0:k,E=Dl(r,{boundary:v,rootBoundary:h,padding:g,altBoundary:m}),x=Nn(r.placement),_=Fo(r.placement),F=!_,C=Bv(x),A=WO(C),B=r.modifiersData.popperOffsets,q=r.rects.reference,Q=r.rects.popper,I=typeof S=="function"?S(Object.assign({},r.rects,{placement:r.placement})):S,V=typeof I=="number"?{mainAxis:I,altAxis:I}:Object.assign({mainAxis:0,altAxis:0},I),J=r.modifiersData.offset?r.modifiersData.offset[r.placement]:null,le={x:0,y:0};if(B){if(u){var ae,fe=C==="y"?Ft:zt,me=C==="y"?ln:sn,$=C==="y"?"height":"width",ie=B[C],se=ie+E[fe],ge=ie-E[me],O=w?-Q[$]/2:0,P=_===No?q[$]:Q[$],H=_===No?-Q[$]:-q[$],W=r.elements.arrow,L=w&&W?Hv(W):{width:0,height:0},G=r.modifiersData["arrow#persistent"]?r.modifiersData["arrow#persistent"].padding:m0(),ne=G[fe],ve=G[me],ce=cl(0,q[$],L[$]),Oe=F?q[$]/2-O-ce-ne-V.mainAxis:P-ce-ne-V.mainAxis,Me=F?-q[$]/2+O+ce+ve+V.mainAxis:H+ce+ve+V.mainAxis,ot=r.elements.arrow&&Kl(r.elements.arrow),cn=ot?C==="y"?ot.clientTop||0:ot.clientLeft||0:0,jt=(ae=J==null?void 0:J[C])!=null?ae:0,dr=ie+Oe-jt-cn,Ba=ie+Me-jt,jn=cl(w?Lu(se,dr):se,ie,w?Oa(ge,Ba):ge);B[C]=jn,le[C]=jn-ie}if(d){var Wa,Va=C==="x"?Ft:zt,ni=C==="x"?ln:sn,fn=B[A],vr=A==="y"?"height":"width",ts=fn+E[Va],ns=fn-E[ni],Pt=[Ft,zt].indexOf(x)!==-1,rs=(Wa=J==null?void 0:J[A])!=null?Wa:0,ri=Pt?ts:fn-q[vr]-Q[vr]-rs+V.altAxis,ai=Pt?fn+q[vr]+Q[vr]-rs-V.altAxis:ns,ta=w&&Pt?pO(ri,fn,ai):cl(w?ri:ts,fn,w?ai:ns);B[A]=ta,le[A]=ta-fn}r.modifiersData[o]=le}}const UO={name:"preventOverflow",enabled:!0,phase:"main",fn:VO,requiresIfExists:["offset"]};function $O(t){return{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}}function KO(t){return t===$t(t)||!An(t)?Wv(t):$O(t)}function qO(t){var r=t.getBoundingClientRect(),a=Ao(r.width)/t.offsetWidth||1,o=Ao(r.height)/t.offsetHeight||1;return a!==1||o!==1}function QO(t,r,a){a===void 0&&(a=!1);var o=An(r),s=An(r)&&qO(r),u=qr(r),c=Ro(t,s,a),d={scrollLeft:0,scrollTop:0},v={x:0,y:0};return(o||!o&&!a)&&((Br(r)!=="body"||Uv(u))&&(d=KO(r)),An(r)?(v=Ro(r,!0),v.x+=r.clientLeft,v.y+=r.clientTop):u&&(v.x=Vv(u))),{x:c.left+d.scrollLeft-v.x,y:c.top+d.scrollTop-v.y,width:c.width,height:c.height}}function XO(t){var r=new Map,a=new Set,o=[];t.forEach(function(u){r.set(u.name,u)});function s(u){a.add(u.name);var c=[].concat(u.requires||[],u.requiresIfExists||[]);c.forEach(function(d){if(!a.has(d)){var v=r.get(d);v&&s(v)}}),o.push(u)}return t.forEach(function(u){a.has(u.name)||s(u)}),o}function GO(t){var r=XO(t);return fO.reduce(function(a,o){return a.concat(r.filter(function(s){return s.phase===o}))},[])}function JO(t){var r;return function(){return r||(r=new Promise(function(a){Promise.resolve().then(function(){r=void 0,a(t())})})),r}}function ZO(t){var r=t.reduce(function(a,o){var s=a[o.name];return a[o.name]=s?Object.assign({},s,o,{options:Object.assign({},s.options,o.options),data:Object.assign({},s.data,o.data)}):o,a},{});return Object.keys(r).map(function(a){return r[a]})}var Wy={placement:"bottom",modifiers:[],strategy:"absolute"};function Vy(){for(var t=arguments.length,r=new Array(t),a=0;a<t;a++)r[a]=arguments[a];return!r.some(function(o){return!(o&&typeof o.getBoundingClientRect=="function")})}function eC(t){t===void 0&&(t={});var r=t,a=r.defaultModifiers,o=a===void 0?[]:a,s=r.defaultOptions,u=s===void 0?Wy:s;return function(d,v,h){h===void 0&&(h=u);var m={placement:"bottom",orderedModifiers:[],options:Object.assign({},Wy,u),modifiersData:{},elements:{reference:d,popper:v},attributes:{},styles:{}},g=[],y=!1,w={state:m,setOptions:function(x){var _=typeof x=="function"?x(m.options):x;S(),m.options=Object.assign({},u,m.options,_),m.scrollParents={reference:Fa(d)?fl(d):d.contextElement?fl(d.contextElement):[],popper:fl(v)};var F=GO(ZO([].concat(o,m.options.modifiers)));return m.orderedModifiers=F.filter(function(C){return C.enabled}),k(),w.update()},forceUpdate:function(){if(!y){var x=m.elements,_=x.reference,F=x.popper;if(Vy(_,F)){m.rects={reference:QO(_,Kl(F),m.options.strategy==="fixed"),popper:Hv(F)},m.reset=!1,m.placement=m.options.placement,m.orderedModifiers.forEach(function(V){return m.modifiersData[V.name]=Object.assign({},V.data)});for(var C=0;C<m.orderedModifiers.length;C++){if(m.reset===!0){m.reset=!1,C=-1;continue}var A=m.orderedModifiers[C],B=A.fn,q=A.options,Q=q===void 0?{}:q,I=A.name;typeof B=="function"&&(m=B({state:m,options:Q,name:I,instance:w})||m)}}}},update:JO(function(){return new Promise(function(E){w.forceUpdate(),E(m)})}),destroy:function(){S(),y=!0}};if(!Vy(d,v))return w;w.setOptions(h).then(function(E){!y&&h.onFirstUpdate&&h.onFirstUpdate(E)});function k(){m.orderedModifiers.forEach(function(E){var x=E.name,_=E.options,F=_===void 0?{}:_,C=E.effect;if(typeof C=="function"){var A=C({state:m,name:x,instance:w,options:F}),B=function(){};g.push(A||B)}})}function S(){g.forEach(function(E){return E()}),g=[]}return w}}var tC=eC({defaultModifiers:[LO,BO,kO,DO,YO,FO,UO,yO]}),Uy=function(r){return{position:r,top:"0",left:"0",opacity:"0",pointerEvents:"none"}},nC={name:"applyStyles",enabled:!1},rC={name:"ariaDescribedBy",enabled:!0,phase:"afterWrite",effect:function(r){var a=r.state;return function(){var o=a.elements,s=o.reference,u=o.popper;if("removeAttribute"in s){var c=(s.getAttribute("aria-describedby")||"").split(",").filter(function(d){return d.trim()!==u.id});c.length?s.setAttribute("aria-describedby",c.join(",")):s.removeAttribute("aria-describedby")}}},fn:function(r){var a,o=r.state,s=o.elements,u=s.popper,c=s.reference,d=(a=u.getAttribute("role"))==null?void 0:a.toLowerCase();if(u.id&&d==="tooltip"&&"setAttribute"in c){var v=c.getAttribute("aria-describedby");if(v&&v.split(",").indexOf(u.id)!==-1)return;c.setAttribute("aria-describedby",v?v+","+u.id:u.id)}}},aC=[];function oC(t,r,a){var o=a===void 0?{}:a,s=o.enabled,u=s===void 0?!0:s,c=o.placement,d=c===void 0?"bottom":c,v=o.strategy,h=v===void 0?"absolute":v,m=o.modifiers,g=m===void 0?aC:m,y=qu(o,["enabled","placement","strategy","modifiers"]),w=te.useRef(),k=te.useCallback(function(){var C;(C=w.current)==null||C.update()},[]),S=te.useCallback(function(){var C;(C=w.current)==null||C.forceUpdate()},[]),E=Z_(te.useState({placement:d,update:k,forceUpdate:S,attributes:{},styles:{popper:Uy(h),arrow:{}}})),x=E[0],_=E[1],F=te.useMemo(function(){return{name:"updateStateModifier",enabled:!0,phase:"write",requires:["computeStyles"],fn:function(A){var B=A.state,q={},Q={};Object.keys(B.elements).forEach(function(I){q[I]=B.styles[I],Q[I]=B.attributes[I]}),_({state:B,styles:q,attributes:Q,update:k,forceUpdate:S,placement:B.placement})}}},[k,S,_]);return te.useEffect(function(){!w.current||!u||w.current.setOptions({placement:d,strategy:h,modifiers:[].concat(g,[F,nC])})},[h,d,F,u]),te.useEffect(function(){if(!(!u||t==null||r==null))return w.current=tC(t,r,qe({},y,{placement:d,strategy:h,modifiers:[].concat(g,[rC,F])})),function(){w.current!=null&&(w.current.destroy(),w.current=void 0,_(function(C){return qe({},C,{attributes:{},styles:{popper:Uy(h)}})}))}},[u,t,r]),x}function $v(t,r,a,o){var s=o&&typeof o!="boolean"?o.capture:o;t.removeEventListener(r,a,s),a.__once&&t.removeEventListener(r,a.__once,s)}function Mn(t,r,a,o){return Lv(t,r,a,o),function(){$v(t,r,a,o)}}const iC=Object.freeze(Object.defineProperty({__proto__:null,default:Mn},Symbol.toStringTag,{value:"Module"}));var lC=function(){},sC=lC;const uC=Ct(sC);var cC=dw();const S0=Ct(cC);function fC(t){return t&&"setState"in t?S0.findDOMNode(t):t??null}const dC=function(t){return Kr(fC(t))};var vC=27,$y=function(){};function pC(t){return t.button===0}function hC(t){return!!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey)}var Ky=function(r){return r&&("current"in r?r.current:r)};function mC(t,r,a){var o=a===void 0?{}:a,s=o.disabled,u=o.clickTrigger,c=u===void 0?"click":u,d=te.useRef(!1),v=r||$y,h=te.useCallback(function(y){var w,k=Ky(t);uC(!!k,"RootClose captured a close event but does not have a ref to compare it to. useRootClose(), should be passed a ref that resolves to a DOM node"),d.current=!k||hC(y)||!pC(y)||!!lr(k,(w=y.composedPath==null?void 0:y.composedPath()[0])!=null?w:y.target)},[t]),m=Ry(function(y){d.current||v(y)}),g=Ry(function(y){y.keyCode===vC&&v(y)});te.useEffect(function(){if(!(s||t==null)){var y=window.event,w=dC(Ky(t)),k=Mn(w,c,h,!0),S=Mn(w,c,function(_){if(_===y){y=void 0;return}m(_)}),E=Mn(w,"keyup",function(_){if(_===y){y=void 0;return}g(_)}),x=[];return"ontouchstart"in w.documentElement&&(x=[].slice.call(w.body.children).map(function(_){return Mn(_,"mousemove",$y)})),function(){k(),S(),E(),x.forEach(function(_){return _()})}}},[t,s,c,h,m,g])}function gC(t){var r={};return Array.isArray(t)?(t==null||t.forEach(function(a){r[a.name]=a}),r):t||r}function yC(t){return t===void 0&&(t={}),Array.isArray(t)?t:Object.keys(t).map(function(r){return t[r].name=r,t[r]})}function bC(t){var r,a,o,s,u=t.enabled,c=t.enableEvents,d=t.placement,v=t.flip,h=t.offset,m=t.fixed,g=t.containerPadding,y=t.arrowElement,w=t.popperConfig,k=w===void 0?{}:w,S=gC(k.modifiers);return qe({},k,{placement:d,enabled:u,strategy:m?"fixed":k.strategy,modifiers:yC(qe({},S,{eventListeners:{enabled:c},preventOverflow:qe({},S.preventOverflow,{options:g?qe({padding:g},(r=S.preventOverflow)==null?void 0:r.options):(a=S.preventOverflow)==null?void 0:a.options}),offset:{options:qe({offset:h},(o=S.offset)==null?void 0:o.options)},arrow:qe({},S.arrow,{enabled:!!y,options:qe({},(s=S.arrow)==null?void 0:s.options,{element:y})}),flip:qe({enabled:!!v},S.flip)}))})}function k0(t){t===void 0&&(t=Kr());try{var r=t.activeElement;return!r||!r.nodeName?null:r}catch{return t.body}}function rc(t,r){return t.classList?!!r&&t.classList.contains(r):(" "+(t.className.baseVal||t.className)+" ").indexOf(" "+r+" ")!==-1}function ac(t,r){t.classList?t.classList.add(r):rc(t,r)||(typeof t.className=="string"?t.className=t.className+" "+r:t.setAttribute("class",(t.className&&t.className.baseVal||"")+" "+r))}function qy(t,r){return t.replace(new RegExp("(^|\\s)"+r+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")}function oc(t,r){t.classList?t.classList.remove(r):typeof t.className=="string"?t.className=qy(t.className,r):t.setAttribute("class",qy(t.className&&t.className.baseVal||"",r))}var mu;function ql(t){if((!mu&&mu!==0||t)&&zv){var r=document.createElement("div");r.style.position="absolute",r.style.top="-9999px",r.style.width="50px",r.style.height="50px",r.style.overflow="scroll",document.body.appendChild(r),mu=r.offsetWidth-r.clientWidth,document.body.removeChild(r)}return mu}var Ud=function(r){var a;return typeof document>"u"?null:r==null?Kr().body:(typeof r=="function"&&(r=r()),r&&"current"in r&&(r=r.current),(a=r)!=null&&a.nodeType&&r||null)};function Qy(t,r){var a=te.useState(function(){return Ud(t)}),o=a[0],s=a[1];if(!o){var u=Ud(t);u&&s(u)}return te.useEffect(function(){},[r,o]),te.useEffect(function(){var c=Ud(t);c!==o&&s(c)},[t,o]),o}const Xy=t=>!t||typeof t=="function"?t:r=>{t.current=r};function wC(t,r){const a=Xy(t),o=Xy(r);return s=>{a&&a(s),o&&o(s)}}function SC(t,r){return te.useMemo(()=>wC(t,r),[t,r])}var Kv=R.forwardRef(function(t,r){var a=t.flip,o=t.offset,s=t.placement,u=t.containerPadding,c=u===void 0?5:u,d=t.popperConfig,v=d===void 0?{}:d,h=t.transition,m=Fy(),g=m[0],y=m[1],w=Fy(),k=w[0],S=w[1],E=SC(y,r),x=Qy(t.container),_=Qy(t.target),F=te.useState(!t.show),C=F[0],A=F[1],B=oC(_,g,bC({placement:s,enableEvents:!!t.show,containerPadding:c||5,flip:a,offset:o,arrowElement:k,popperConfig:v})),q=B.styles,Q=B.attributes,I=qu(B,["styles","attributes"]);t.show?C&&A(!1):!t.transition&&!C&&A(!0);var V=function(){A(!0),t.onExited&&t.onExited.apply(t,arguments)},J=t.show||h&&!C;if(mC(g,t.onHide,{disabled:!t.rootClose||t.rootCloseDisabled,clickTrigger:t.rootCloseEvent}),!J)return null;var le=t.children(qe({},I,{show:!!t.show,props:qe({},Q.popper,{style:q.popper,ref:E}),arrowProps:qe({},Q.arrow,{style:q.arrow,ref:S})}));if(h){var ae=t.onExit,fe=t.onExiting,me=t.onEnter,$=t.onEntering,ie=t.onEntered;le=R.createElement(h,{in:t.show,appear:!0,onExit:ae,onExiting:fe,onExited:V,onEnter:me,onEntering:$,onEntered:ie},le)}return x?S0.createPortal(le,x):null});Kv.displayName="Overlay";Kv.propTypes={show:oe.bool,placement:oe.oneOf(Iv),target:oe.any,container:oe.any,flip:oe.bool,children:oe.func.isRequired,containerPadding:oe.number,popperConfig:oe.object,rootClose:oe.bool,rootCloseEvent:oe.oneOf(["click","mousedown"]),rootCloseDisabled:oe.bool,onHide:function(r){for(var a=arguments.length,o=new Array(a>1?a-1:0),s=1;s<a;s++)o[s-1]=arguments[s];if(r.rootClose){var u;return(u=oe.func).isRequired.apply(u,[r].concat(o))}return oe.func.apply(oe,[r].concat(o))},transition:oe.elementType,onEnter:oe.func,onEntering:oe.func,onEntered:oe.func,onExit:oe.func,onExiting:oe.func,onExited:oe.func};function kC(){this.__data__=[],this.size=0}var EC=kC,DC=Bl;function xC(t,r){for(var a=t.length;a--;)if(DC(t[a][0],r))return a;return-1}var ic=xC,TC=ic,_C=Array.prototype,OC=_C.splice;function CC(t){var r=this.__data__,a=TC(r,t);if(a<0)return!1;var o=r.length-1;return a==o?r.pop():OC.call(r,a,1),--this.size,!0}var PC=CC,MC=ic;function NC(t){var r=this.__data__,a=MC(r,t);return a<0?void 0:r[a][1]}var AC=NC,RC=ic;function FC(t){return RC(this.__data__,t)>-1}var zC=FC,LC=ic;function jC(t,r){var a=this.__data__,o=LC(a,t);return o<0?(++this.size,a.push([t,r])):a[o][1]=r,this}var IC=jC,YC=EC,HC=PC,BC=AC,WC=zC,VC=IC;function Uo(t){var r=-1,a=t==null?0:t.length;for(this.clear();++r<a;){var o=t[r];this.set(o[0],o[1])}}Uo.prototype.clear=YC;Uo.prototype.delete=HC;Uo.prototype.get=BC;Uo.prototype.has=WC;Uo.prototype.set=VC;var lc=Uo,UC=lc;function $C(){this.__data__=new UC,this.size=0}var KC=$C;function qC(t){var r=this.__data__,a=r.delete(t);return this.size=r.size,a}var QC=qC;function XC(t){return this.__data__.get(t)}var GC=XC;function JC(t){return this.__data__.has(t)}var ZC=JC,e2=Ln,t2=e2["__core-js_shared__"],n2=t2,$d=n2,Gy=function(){var t=/[^.]+$/.exec($d&&$d.keys&&$d.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();function r2(t){return!!Gy&&Gy in t}var a2=r2,o2=Function.prototype,i2=o2.toString;function l2(t){if(t!=null){try{return i2.call(t)}catch{}try{return t+""}catch{}}return""}var E0=l2,s2=Pv,u2=a2,c2=ur,f2=E0,d2=/[\\^$.*+?()[\]{}|]/g,v2=/^\[object .+?Constructor\]$/,p2=Function.prototype,h2=Object.prototype,m2=p2.toString,g2=h2.hasOwnProperty,y2=RegExp("^"+m2.call(g2).replace(d2,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function b2(t){if(!c2(t)||u2(t))return!1;var r=s2(t)?y2:v2;return r.test(f2(t))}var w2=b2;function S2(t,r){return t==null?void 0:t[r]}var k2=S2,E2=w2,D2=k2;function x2(t,r){var a=D2(t,r);return E2(a)?a:void 0}var La=x2,T2=La,_2=Ln,O2=T2(_2,"Map"),qv=O2,C2=La,P2=C2(Object,"create"),sc=P2,Jy=sc;function M2(){this.__data__=Jy?Jy(null):{},this.size=0}var N2=M2;function A2(t){var r=this.has(t)&&delete this.__data__[t];return this.size-=r?1:0,r}var R2=A2,F2=sc,z2="__lodash_hash_undefined__",L2=Object.prototype,j2=L2.hasOwnProperty;function I2(t){var r=this.__data__;if(F2){var a=r[t];return a===z2?void 0:a}return j2.call(r,t)?r[t]:void 0}var Y2=I2,H2=sc,B2=Object.prototype,W2=B2.hasOwnProperty;function V2(t){var r=this.__data__;return H2?r[t]!==void 0:W2.call(r,t)}var U2=V2,$2=sc,K2="__lodash_hash_undefined__";function q2(t,r){var a=this.__data__;return this.size+=this.has(t)?0:1,a[t]=$2&&r===void 0?K2:r,this}var Q2=q2,X2=N2,G2=R2,J2=Y2,Z2=U2,eP=Q2;function $o(t){var r=-1,a=t==null?0:t.length;for(this.clear();++r<a;){var o=t[r];this.set(o[0],o[1])}}$o.prototype.clear=X2;$o.prototype.delete=G2;$o.prototype.get=J2;$o.prototype.has=Z2;$o.prototype.set=eP;var tP=$o,Zy=tP,nP=lc,rP=qv;function aP(){this.size=0,this.__data__={hash:new Zy,map:new(rP||nP),string:new Zy}}var oP=aP;function iP(t){var r=typeof t;return r=="string"||r=="number"||r=="symbol"||r=="boolean"?t!=="__proto__":t===null}var lP=iP,sP=lP;function uP(t,r){var a=t.__data__;return sP(r)?a[typeof r=="string"?"string":"hash"]:a.map}var uc=uP,cP=uc;function fP(t){var r=cP(this,t).delete(t);return this.size-=r?1:0,r}var dP=fP,vP=uc;function pP(t){return vP(this,t).get(t)}var hP=pP,mP=uc;function gP(t){return mP(this,t).has(t)}var yP=gP,bP=uc;function wP(t,r){var a=bP(this,t),o=a.size;return a.set(t,r),this.size+=a.size==o?0:1,this}var SP=wP,kP=oP,EP=dP,DP=hP,xP=yP,TP=SP;function Ko(t){var r=-1,a=t==null?0:t.length;for(this.clear();++r<a;){var o=t[r];this.set(o[0],o[1])}}Ko.prototype.clear=kP;Ko.prototype.delete=EP;Ko.prototype.get=DP;Ko.prototype.has=xP;Ko.prototype.set=TP;var Qv=Ko,_P=lc,OP=qv,CP=Qv,PP=200;function MP(t,r){var a=this.__data__;if(a instanceof _P){var o=a.__data__;if(!OP||o.length<PP-1)return o.push([t,r]),this.size=++a.size,this;a=this.__data__=new CP(o)}return a.set(t,r),this.size=a.size,this}var NP=MP,AP=lc,RP=KC,FP=QC,zP=GC,LP=ZC,jP=NP;function qo(t){var r=this.__data__=new AP(t);this.size=r.size}qo.prototype.clear=RP;qo.prototype.delete=FP;qo.prototype.get=zP;qo.prototype.has=LP;qo.prototype.set=jP;var Xv=qo,IP="__lodash_hash_undefined__";function YP(t){return this.__data__.set(t,IP),this}var HP=YP;function BP(t){return this.__data__.has(t)}var WP=BP,VP=Qv,UP=HP,$P=WP;function ju(t){var r=-1,a=t==null?0:t.length;for(this.__data__=new VP;++r<a;)this.add(t[r])}ju.prototype.add=ju.prototype.push=UP;ju.prototype.has=$P;var KP=ju;function qP(t,r){for(var a=-1,o=t==null?0:t.length;++a<o;)if(r(t[a],a,t))return!0;return!1}var QP=qP;function XP(t,r){return t.has(r)}var GP=XP,JP=KP,ZP=QP,eM=GP,tM=1,nM=2;function rM(t,r,a,o,s,u){var c=a&tM,d=t.length,v=r.length;if(d!=v&&!(c&&v>d))return!1;var h=u.get(t),m=u.get(r);if(h&&m)return h==r&&m==t;var g=-1,y=!0,w=a&nM?new JP:void 0;for(u.set(t,r),u.set(r,t);++g<d;){var k=t[g],S=r[g];if(o)var E=c?o(S,k,g,r,t,u):o(k,S,g,t,r,u);if(E!==void 0){if(E)continue;y=!1;break}if(w){if(!ZP(r,function(x,_){if(!eM(w,_)&&(k===x||s(k,x,a,o,u)))return w.push(_)})){y=!1;break}}else if(!(k===S||s(k,S,a,o,u))){y=!1;break}}return u.delete(t),u.delete(r),y}var D0=rM,aM=Ln,oM=aM.Uint8Array,x0=oM;function iM(t){var r=-1,a=Array(t.size);return t.forEach(function(o,s){a[++r]=[s,o]}),a}var lM=iM;function sM(t){var r=-1,a=Array(t.size);return t.forEach(function(o){a[++r]=o}),a}var uM=sM,eb=Wo,tb=x0,cM=Bl,fM=D0,dM=lM,vM=uM,pM=1,hM=2,mM="[object Boolean]",gM="[object Date]",yM="[object Error]",bM="[object Map]",wM="[object Number]",SM="[object RegExp]",kM="[object Set]",EM="[object String]",DM="[object Symbol]",xM="[object ArrayBuffer]",TM="[object DataView]",nb=eb?eb.prototype:void 0,Kd=nb?nb.valueOf:void 0;function _M(t,r,a,o,s,u,c){switch(a){case TM:if(t.byteLength!=r.byteLength||t.byteOffset!=r.byteOffset)return!1;t=t.buffer,r=r.buffer;case xM:return!(t.byteLength!=r.byteLength||!u(new tb(t),new tb(r)));case mM:case gM:case wM:return cM(+t,+r);case yM:return t.name==r.name&&t.message==r.message;case SM:case EM:return t==r+"";case bM:var d=dM;case kM:var v=o&pM;if(d||(d=vM),t.size!=r.size&&!v)return!1;var h=c.get(t);if(h)return h==r;o|=hM,c.set(t,r);var m=fM(d(t),d(r),o,s,u,c);return c.delete(t),m;case DM:if(Kd)return Kd.call(t)==Kd.call(r)}return!1}var OM=_M;function CM(t,r){for(var a=-1,o=r.length,s=t.length;++a<o;)t[s+a]=r[a];return t}var Gv=CM,PM=Array.isArray,un=PM,MM=Gv,NM=un;function AM(t,r,a){var o=r(t);return NM(t)?o:MM(o,a(t))}var T0=AM;function RM(t,r){for(var a=-1,o=t==null?0:t.length,s=0,u=[];++a<o;){var c=t[a];r(c,a,t)&&(u[s++]=c)}return u}var FM=RM;function zM(){return[]}var _0=zM,LM=FM,jM=_0,IM=Object.prototype,YM=IM.propertyIsEnumerable,rb=Object.getOwnPropertySymbols,HM=rb?function(t){return t==null?[]:(t=Object(t),LM(rb(t),function(r){return YM.call(t,r)}))}:jM,Jv=HM;function BM(t,r){for(var a=-1,o=Array(t);++a<t;)o[a]=r(a);return o}var WM=BM,VM=Vo,UM=$r,$M="[object Arguments]";function KM(t){return UM(t)&&VM(t)==$M}var qM=KM,ab=qM,QM=$r,O0=Object.prototype,XM=O0.hasOwnProperty,GM=O0.propertyIsEnumerable,JM=ab(function(){return arguments}())?ab:function(t){return QM(t)&&XM.call(t,"callee")&&!GM.call(t,"callee")},Zv=JM,Iu={exports:{}};function ZM(){return!1}var eN=ZM;Iu.exports;(function(t,r){var a=Ln,o=eN,s=r&&!r.nodeType&&r,u=s&&!0&&t&&!t.nodeType&&t,c=u&&u.exports===s,d=c?a.Buffer:void 0,v=d?d.isBuffer:void 0,h=v||o;t.exports=h})(Iu,Iu.exports);var cc=Iu.exports,tN=Vo,nN=Mv,rN=$r,aN="[object Arguments]",oN="[object Array]",iN="[object Boolean]",lN="[object Date]",sN="[object Error]",uN="[object Function]",cN="[object Map]",fN="[object Number]",dN="[object Object]",vN="[object RegExp]",pN="[object Set]",hN="[object String]",mN="[object WeakMap]",gN="[object ArrayBuffer]",yN="[object DataView]",bN="[object Float32Array]",wN="[object Float64Array]",SN="[object Int8Array]",kN="[object Int16Array]",EN="[object Int32Array]",DN="[object Uint8Array]",xN="[object Uint8ClampedArray]",TN="[object Uint16Array]",_N="[object Uint32Array]",Ie={};Ie[bN]=Ie[wN]=Ie[SN]=Ie[kN]=Ie[EN]=Ie[DN]=Ie[xN]=Ie[TN]=Ie[_N]=!0;Ie[aN]=Ie[oN]=Ie[gN]=Ie[iN]=Ie[yN]=Ie[lN]=Ie[sN]=Ie[uN]=Ie[cN]=Ie[fN]=Ie[dN]=Ie[vN]=Ie[pN]=Ie[hN]=Ie[mN]=!1;function ON(t){return rN(t)&&nN(t.length)&&!!Ie[tN(t)]}var CN=ON;function PN(t){return function(r){return t(r)}}var fc=PN,Yu={exports:{}};Yu.exports;(function(t,r){var a=o0,o=r&&!r.nodeType&&r,s=o&&!0&&t&&!t.nodeType&&t,u=s&&s.exports===o,c=u&&a.process,d=function(){try{var v=s&&s.require&&s.require("util").types;return v||c&&c.binding&&c.binding("util")}catch{}}();t.exports=d})(Yu,Yu.exports);var ep=Yu.exports,MN=CN,NN=fc,ob=ep,ib=ob&&ob.isTypedArray,AN=ib?NN(ib):MN,tp=AN,RN=WM,FN=Zv,zN=un,LN=cc,jN=Nv,IN=tp,YN=Object.prototype,HN=YN.hasOwnProperty;function BN(t,r){var a=zN(t),o=!a&&FN(t),s=!a&&!o&&LN(t),u=!a&&!o&&!s&&IN(t),c=a||o||s||u,d=c?RN(t.length,String):[],v=d.length;for(var h in t)(r||HN.call(t,h))&&!(c&&(h=="length"||s&&(h=="offset"||h=="parent")||u&&(h=="buffer"||h=="byteLength"||h=="byteOffset")||jN(h,v)))&&d.push(h);return d}var C0=BN,WN=Object.prototype;function VN(t){var r=t&&t.constructor,a=typeof r=="function"&&r.prototype||WN;return t===a}var np=VN;function UN(t,r){return function(a){return t(r(a))}}var P0=UN,$N=P0,KN=$N(Object.keys,Object),qN=KN,QN=np,XN=qN,GN=Object.prototype,JN=GN.hasOwnProperty;function ZN(t){if(!QN(t))return XN(t);var r=[];for(var a in Object(t))JN.call(t,a)&&a!="constructor"&&r.push(a);return r}var eA=ZN,tA=C0,nA=eA,rA=Wl;function aA(t){return rA(t)?tA(t):nA(t)}var Ql=aA,oA=T0,iA=Jv,lA=Ql;function sA(t){return oA(t,lA,iA)}var M0=sA,lb=M0,uA=1,cA=Object.prototype,fA=cA.hasOwnProperty;function dA(t,r,a,o,s,u){var c=a&uA,d=lb(t),v=d.length,h=lb(r),m=h.length;if(v!=m&&!c)return!1;for(var g=v;g--;){var y=d[g];if(!(c?y in r:fA.call(r,y)))return!1}var w=u.get(t),k=u.get(r);if(w&&k)return w==r&&k==t;var S=!0;u.set(t,r),u.set(r,t);for(var E=c;++g<v;){y=d[g];var x=t[y],_=r[y];if(o)var F=c?o(_,x,y,r,t,u):o(x,_,y,t,r,u);if(!(F===void 0?x===_||s(x,_,a,o,u):F)){S=!1;break}E||(E=y=="constructor")}if(S&&!E){var C=t.constructor,A=r.constructor;C!=A&&"constructor"in t&&"constructor"in r&&!(typeof C=="function"&&C instanceof C&&typeof A=="function"&&A instanceof A)&&(S=!1)}return u.delete(t),u.delete(r),S}var vA=dA,pA=La,hA=Ln,mA=pA(hA,"DataView"),gA=mA,yA=La,bA=Ln,wA=yA(bA,"Promise"),SA=wA,kA=La,EA=Ln,DA=kA(EA,"Set"),xA=DA,TA=La,_A=Ln,OA=TA(_A,"WeakMap"),CA=OA,vv=gA,pv=qv,hv=SA,mv=xA,gv=CA,N0=Vo,Qo=E0,sb="[object Map]",PA="[object Object]",ub="[object Promise]",cb="[object Set]",fb="[object WeakMap]",db="[object DataView]",MA=Qo(vv),NA=Qo(pv),AA=Qo(hv),RA=Qo(mv),FA=Qo(gv),Ta=N0;(vv&&Ta(new vv(new ArrayBuffer(1)))!=db||pv&&Ta(new pv)!=sb||hv&&Ta(hv.resolve())!=ub||mv&&Ta(new mv)!=cb||gv&&Ta(new gv)!=fb)&&(Ta=function(t){var r=N0(t),a=r==PA?t.constructor:void 0,o=a?Qo(a):"";if(o)switch(o){case MA:return db;case NA:return sb;case AA:return ub;case RA:return cb;case FA:return fb}return r});var dc=Ta,qd=Xv,zA=D0,LA=OM,jA=vA,vb=dc,pb=un,hb=cc,IA=tp,YA=1,mb="[object Arguments]",gb="[object Array]",gu="[object Object]",HA=Object.prototype,yb=HA.hasOwnProperty;function BA(t,r,a,o,s,u){var c=pb(t),d=pb(r),v=c?gb:vb(t),h=d?gb:vb(r);v=v==mb?gu:v,h=h==mb?gu:h;var m=v==gu,g=h==gu,y=v==h;if(y&&hb(t)){if(!hb(r))return!1;c=!0,m=!1}if(y&&!m)return u||(u=new qd),c||IA(t)?zA(t,r,a,o,s,u):LA(t,r,v,a,o,s,u);if(!(a&YA)){var w=m&&yb.call(t,"__wrapped__"),k=g&&yb.call(r,"__wrapped__");if(w||k){var S=w?t.value():t,E=k?r.value():r;return u||(u=new qd),s(S,E,a,o,u)}}return y?(u||(u=new qd),jA(t,r,a,o,s,u)):!1}var WA=BA,VA=WA,bb=$r;function A0(t,r,a,o,s){return t===r?!0:t==null||r==null||!bb(t)&&!bb(r)?t!==t&&r!==r:VA(t,r,a,o,A0,s)}var rp=A0,UA=rp;function $A(t,r){return UA(t,r)}var R0=$A;const KA=Ct(R0);function Ca(t,r){var a=Gu(t);return a?a.innerHeight:r?t.clientHeight:yn(t).height}function Xl(t,r,a){t.closest&&!a&&t.closest(r);var o=t;do{if(ec(o,r))return o;o=o.parentElement}while(o&&o!==a&&o.nodeType===document.ELEMENT_NODE);return null}const qA=Object.freeze(Object.defineProperty({__proto__:null,default:Xl},Symbol.toStringTag,{value:"Module"}));function QA(t,r,a,o){for(var s=t.length,u=a+(o?1:-1);o?u--:++u<s;)if(r(t[u],u,t))return u;return-1}var XA=QA,GA=Xv,JA=rp,ZA=1,eR=2;function tR(t,r,a,o){var s=a.length,u=s,c=!o;if(t==null)return!u;for(t=Object(t);s--;){var d=a[s];if(c&&d[2]?d[1]!==t[d[0]]:!(d[0]in t))return!1}for(;++s<u;){d=a[s];var v=d[0],h=t[v],m=d[1];if(c&&d[2]){if(h===void 0&&!(v in t))return!1}else{var g=new GA;if(o)var y=o(h,m,v,t,r,g);if(!(y===void 0?JA(m,h,ZA|eR,o,g):y))return!1}}return!0}var nR=tR,rR=ur;function aR(t){return t===t&&!rR(t)}var F0=aR,oR=F0,iR=Ql;function lR(t){for(var r=iR(t),a=r.length;a--;){var o=r[a],s=t[o];r[a]=[o,s,oR(s)]}return r}var sR=lR;function uR(t,r){return function(a){return a==null?!1:a[t]===r&&(r!==void 0||t in Object(a))}}var z0=uR,cR=nR,fR=sR,dR=z0;function vR(t){var r=fR(t);return r.length==1&&r[0][2]?dR(r[0][0],r[0][1]):function(a){return a===t||cR(a,t,r)}}var pR=vR,hR=un,mR=Vl,gR=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,yR=/^\w*$/;function bR(t,r){if(hR(t))return!1;var a=typeof t;return a=="number"||a=="symbol"||a=="boolean"||t==null||mR(t)?!0:yR.test(t)||!gR.test(t)||r!=null&&t in Object(r)}var ap=bR,L0=Qv,wR="Expected a function";function op(t,r){if(typeof t!="function"||r!=null&&typeof r!="function")throw new TypeError(wR);var a=function(){var o=arguments,s=r?r.apply(this,o):o[0],u=a.cache;if(u.has(s))return u.get(s);var c=t.apply(this,o);return a.cache=u.set(s,c)||u,c};return a.cache=new(op.Cache||L0),a}op.Cache=L0;var SR=op,kR=SR,ER=500;function DR(t){var r=kR(t,function(o){return a.size===ER&&a.clear(),o}),a=r.cache;return r}var xR=DR,TR=xR,_R=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,OR=/\\(\\)?/g,CR=TR(function(t){var r=[];return t.charCodeAt(0)===46&&r.push(""),t.replace(_R,function(a,o,s,u){r.push(s?u.replace(OR,"$1"):o||a)}),r}),PR=CR;function MR(t,r){for(var a=-1,o=t==null?0:t.length,s=Array(o);++a<o;)s[a]=r(t[a],a,t);return s}var ip=MR,wb=Wo,NR=ip,AR=un,RR=Vl,Sb=wb?wb.prototype:void 0,kb=Sb?Sb.toString:void 0;function j0(t){if(typeof t=="string")return t;if(AR(t))return NR(t,j0)+"";if(RR(t))return kb?kb.call(t):"";var r=t+"";return r=="0"&&1/t==-1/0?"-0":r}var FR=j0,zR=FR;function LR(t){return t==null?"":zR(t)}var jR=LR,IR=un,YR=ap,HR=PR,BR=jR;function WR(t,r){return IR(t)?t:YR(t,r)?[t]:HR(BR(t))}var vc=WR,VR=Vl;function UR(t){if(typeof t=="string"||VR(t))return t;var r=t+"";return r=="0"&&1/t==-1/0?"-0":r}var Gl=UR,$R=vc,KR=Gl;function qR(t,r){r=$R(r,t);for(var a=0,o=r.length;t!=null&&a<o;)t=t[KR(r[a++])];return a&&a==o?t:void 0}var pc=qR,QR=pc;function XR(t,r,a){var o=t==null?void 0:QR(t,r);return o===void 0?a:o}var GR=XR;function JR(t,r){return t!=null&&r in Object(t)}var ZR=JR,eF=vc,tF=Zv,nF=un,rF=Nv,aF=Mv,oF=Gl;function iF(t,r,a){r=eF(r,t);for(var o=-1,s=r.length,u=!1;++o<s;){var c=oF(r[o]);if(!(u=t!=null&&a(t,c)))break;t=t[c]}return u||++o!=s?u:(s=t==null?0:t.length,!!s&&aF(s)&&rF(c,s)&&(nF(t)||tF(t)))}var lF=iF,sF=ZR,uF=lF;function cF(t,r){return t!=null&&uF(t,r,sF)}var fF=cF,dF=rp,vF=GR,pF=fF,hF=ap,mF=F0,gF=z0,yF=Gl,bF=1,wF=2;function SF(t,r){return hF(t)&&mF(r)?gF(yF(t),r):function(a){var o=vF(a,t);return o===void 0&&o===r?pF(a,t):dF(r,o,bF|wF)}}var kF=SF;function EF(t){return t}var hc=EF;function DF(t){return function(r){return r==null?void 0:r[t]}}var xF=DF,TF=pc;function _F(t){return function(r){return TF(r,t)}}var OF=_F,CF=xF,PF=OF,MF=ap,NF=Gl;function AF(t){return MF(t)?CF(NF(t)):PF(t)}var RF=AF,FF=pR,zF=kF,LF=hc,jF=un,IF=RF;function YF(t){return typeof t=="function"?t:t==null?LF:typeof t=="object"?jF(t)?zF(t[0],t[1]):FF(t):IF(t)}var mc=YF,HF=XA,BF=mc,WF=s0,VF=Math.max;function UF(t,r,a){var o=t==null?0:t.length;if(!o)return-1;var s=a==null?0:WF(a);return s<0&&(s=VF(o+s,0)),HF(t,BF(r),s)}var I0=UF;const $F=Ct(I0);var KF=Math.ceil,qF=Math.max;function QF(t,r,a,o){for(var s=-1,u=qF(KF((r-t)/(a||1)),0),c=Array(u);u--;)c[o?u:++s]=t,t+=a;return c}var XF=QF,GF=XF,JF=Xu,Qd=l0;function ZF(t){return function(r,a,o){return o&&typeof o!="number"&&JF(r,a,o)&&(a=o=void 0),r=Qd(r),a===void 0?(a=r,r=0):a=Qd(a),o=o===void 0?r<a?1:-1:Qd(o),GF(r,a,o,t)}}var ez=ZF,tz=ez,nz=tz(),rz=nz;const az=Ct(rz);var Eb=Number.isNaN||function(r){return typeof r=="number"&&r!==r};function oz(t,r){return!!(t===r||Eb(t)&&Eb(r))}function iz(t,r){if(t.length!==r.length)return!1;for(var a=0;a<t.length;a++)if(!oz(t[a],r[a]))return!1;return!0}function Y0(t,r){r===void 0&&(r=iz);var a=null;function o(){for(var s=[],u=0;u<arguments.length;u++)s[u]=arguments[u];if(a&&a.lastThis===this&&r(s,a.lastArgs))return a.lastResult;var c=t.apply(this,s);return a={lastResult:c,lastArgs:s,lastThis:this},c}return o.clear=function(){a=null},o}function xl(t,r){var a=Gu(t);return a?a.innerWidth:r?t.clientWidth:yn(t).width}var Db=Wo,lz=Zv,sz=un,xb=Db?Db.isConcatSpreadable:void 0;function uz(t){return sz(t)||lz(t)||!!(xb&&t&&t[xb])}var cz=uz,fz=Gv,dz=cz;function H0(t,r,a,o,s){var u=-1,c=t.length;for(a||(a=dz),s||(s=[]);++u<c;){var d=t[u];r>0&&a(d)?r>1?H0(d,r-1,a,o,s):fz(s,d):o||(s[s.length]=d)}return s}var B0=H0;function vz(t){return function(r,a,o){for(var s=-1,u=Object(r),c=o(r),d=c.length;d--;){var v=c[t?d:++s];if(a(u[v],v,u)===!1)break}return r}}var pz=vz,hz=pz,mz=hz(),gz=mz,yz=gz,bz=Ql;function wz(t,r){return t&&yz(t,r,bz)}var lp=wz,Sz=Wl;function kz(t,r){return function(a,o){if(a==null)return a;if(!Sz(a))return t(a,o);for(var s=a.length,u=r?s:-1,c=Object(a);(r?u--:++u<s)&&o(c[u],u,c)!==!1;);return a}}var Ez=kz,Dz=lp,xz=Ez,Tz=xz(Dz),_z=Tz,Oz=_z,Cz=Wl;function Pz(t,r){var a=-1,o=Cz(t)?Array(t.length):[];return Oz(t,function(s,u,c){o[++a]=r(s,u,c)}),o}var Mz=Pz;function Nz(t,r){var a=t.length;for(t.sort(r);a--;)t[a]=t[a].value;return t}var Az=Nz,Tb=Vl;function Rz(t,r){if(t!==r){var a=t!==void 0,o=t===null,s=t===t,u=Tb(t),c=r!==void 0,d=r===null,v=r===r,h=Tb(r);if(!d&&!h&&!u&&t>r||u&&c&&v&&!d&&!h||o&&c&&v||!a&&v||!s)return 1;if(!o&&!u&&!h&&t<r||h&&a&&s&&!o&&!u||d&&a&&s||!c&&s||!v)return-1}return 0}var Fz=Rz,zz=Fz;function Lz(t,r,a){for(var o=-1,s=t.criteria,u=r.criteria,c=s.length,d=a.length;++o<c;){var v=zz(s[o],u[o]);if(v){if(o>=d)return v;var h=a[o];return v*(h=="desc"?-1:1)}}return t.index-r.index}var jz=Lz,Xd=ip,Iz=pc,Yz=mc,Hz=Mz,Bz=Az,Wz=fc,Vz=jz,Uz=hc,$z=un;function Kz(t,r,a){r.length?r=Xd(r,function(u){return $z(u)?function(c){return Iz(c,u.length===1?u[0]:u)}:u}):r=[Uz];var o=-1;r=Xd(r,Wz(Yz));var s=Hz(t,function(u,c,d){var v=Xd(r,function(h){return h(u)});return{criteria:v,index:++o,value:u}});return Bz(s,function(u,c){return Vz(u,c,a)})}var qz=Kz;function Qz(t,r,a){switch(a.length){case 0:return t.call(r);case 1:return t.call(r,a[0]);case 2:return t.call(r,a[0],a[1]);case 3:return t.call(r,a[0],a[1],a[2])}return t.apply(r,a)}var Xz=Qz,Gz=Xz,_b=Math.max;function Jz(t,r,a){return r=_b(r===void 0?t.length-1:r,0),function(){for(var o=arguments,s=-1,u=_b(o.length-r,0),c=Array(u);++s<u;)c[s]=o[r+s];s=-1;for(var d=Array(r+1);++s<r;)d[s]=o[s];return d[r]=a(c),Gz(t,this,d)}}var W0=Jz;function Zz(t){return function(){return t}}var eL=Zz,tL=La,nL=function(){try{var t=tL(Object,"defineProperty");return t({},"",{}),t}catch{}}(),V0=nL,rL=eL,Ob=V0,aL=hc,oL=Ob?function(t,r){return Ob(t,"toString",{configurable:!0,enumerable:!1,value:rL(r),writable:!0})}:aL,iL=oL,lL=800,sL=16,uL=Date.now;function cL(t){var r=0,a=0;return function(){var o=uL(),s=sL-(o-a);if(a=o,s>0){if(++r>=lL)return arguments[0]}else r=0;return t.apply(void 0,arguments)}}var fL=cL,dL=iL,vL=fL,pL=vL(dL),U0=pL,hL=hc,mL=W0,gL=U0;function yL(t,r){return gL(mL(t,r,hL),t+"")}var $0=yL,bL=B0,wL=qz,SL=$0,Cb=Xu,kL=SL(function(t,r){if(t==null)return[];var a=r.length;return a>1&&Cb(t,r[0],r[1])?r=[]:a>2&&Cb(r[0],r[1],r[2])&&(r=[r[0]]),wL(t,bL(r,1),[])}),EL=kL;const DL=Ct(EL);function K0(t){return Bw(t)||r0(t)||Tv(t)||Ww()}function xL(t){var r=[];if(t!=null)for(var a in Object(t))r.push(a);return r}var TL=xL,_L=ur,OL=np,CL=TL,PL=Object.prototype,ML=PL.hasOwnProperty;function NL(t){if(!_L(t))return CL(t);var r=OL(t),a=[];for(var o in t)o=="constructor"&&(r||!ML.call(t,o))||a.push(o);return a}var AL=NL,RL=C0,FL=AL,zL=Wl;function LL(t){return zL(t)?RL(t,!0):FL(t)}var gc=LL,jL=$0,IL=Bl,YL=Xu,HL=gc,q0=Object.prototype,BL=q0.hasOwnProperty,WL=jL(function(t,r){t=Object(t);var a=-1,o=r.length,s=o>2?r[2]:void 0;for(s&&YL(r[0],r[1],s)&&(o=1);++a<o;)for(var u=r[a],c=HL(u),d=-1,v=c.length;++d<v;){var h=c[d],m=t[h];(m===void 0||IL(m,q0[h])&&!BL.call(t,h))&&(t[h]=u[h])}return t}),VL=WL;const UL=Ct(VL);var Pb=V0;function $L(t,r,a){r=="__proto__"&&Pb?Pb(t,r,{configurable:!0,enumerable:!0,value:a,writable:!0}):t[r]=a}var sp=$L,KL=sp,qL=lp,QL=mc;function XL(t,r){var a={};return r=QL(r),qL(t,function(o,s,u){KL(a,s,r(o,s,u))}),a}var GL=XL;const JL=Ct(GL);function ZL(t,r){for(var a=-1,o=t==null?0:t.length;++a<o&&r(t[a],a,t)!==!1;);return t}var Q0=ZL,ej=sp,tj=Bl,nj=Object.prototype,rj=nj.hasOwnProperty;function aj(t,r,a){var o=t[r];(!(rj.call(t,r)&&tj(o,a))||a===void 0&&!(r in t))&&ej(t,r,a)}var X0=aj,oj=X0,ij=sp;function lj(t,r,a,o){var s=!a;a||(a={});for(var u=-1,c=r.length;++u<c;){var d=r[u],v=o?o(a[d],t[d],d,a,t):void 0;v===void 0&&(v=t[d]),s?ij(a,d,v):oj(a,d,v)}return a}var Jl=lj,sj=Jl,uj=Ql;function cj(t,r){return t&&sj(r,uj(r),t)}var fj=cj,dj=Jl,vj=gc;function pj(t,r){return t&&dj(r,vj(r),t)}var hj=pj,Hu={exports:{}};Hu.exports;(function(t,r){var a=Ln,o=r&&!r.nodeType&&r,s=o&&!0&&t&&!t.nodeType&&t,u=s&&s.exports===o,c=u?a.Buffer:void 0,d=c?c.allocUnsafe:void 0;function v(h,m){if(m)return h.slice();var g=h.length,y=d?d(g):new h.constructor(g);return h.copy(y),y}t.exports=v})(Hu,Hu.exports);var mj=Hu.exports;function gj(t,r){var a=-1,o=t.length;for(r||(r=Array(o));++a<o;)r[a]=t[a];return r}var yj=gj,bj=Jl,wj=Jv;function Sj(t,r){return bj(t,wj(t),r)}var kj=Sj,Ej=P0,Dj=Ej(Object.getPrototypeOf,Object),yc=Dj,xj=Gv,Tj=yc,_j=Jv,Oj=_0,Cj=Object.getOwnPropertySymbols,Pj=Cj?function(t){for(var r=[];t;)xj(r,_j(t)),t=Tj(t);return r}:Oj,G0=Pj,Mj=Jl,Nj=G0;function Aj(t,r){return Mj(t,Nj(t),r)}var Rj=Aj,Fj=T0,zj=G0,Lj=gc;function jj(t){return Fj(t,Lj,zj)}var J0=jj,Ij=Object.prototype,Yj=Ij.hasOwnProperty;function Hj(t){var r=t.length,a=new t.constructor(r);return r&&typeof t[0]=="string"&&Yj.call(t,"index")&&(a.index=t.index,a.input=t.input),a}var Bj=Hj,Mb=x0;function Wj(t){var r=new t.constructor(t.byteLength);return new Mb(r).set(new Mb(t)),r}var up=Wj,Vj=up;function Uj(t,r){var a=r?Vj(t.buffer):t.buffer;return new t.constructor(a,t.byteOffset,t.byteLength)}var $j=Uj,Kj=/\w*$/;function qj(t){var r=new t.constructor(t.source,Kj.exec(t));return r.lastIndex=t.lastIndex,r}var Qj=qj,Nb=Wo,Ab=Nb?Nb.prototype:void 0,Rb=Ab?Ab.valueOf:void 0;function Xj(t){return Rb?Object(Rb.call(t)):{}}var Gj=Xj,Jj=up;function Zj(t,r){var a=r?Jj(t.buffer):t.buffer;return new t.constructor(a,t.byteOffset,t.length)}var eI=Zj,tI=up,nI=$j,rI=Qj,aI=Gj,oI=eI,iI="[object Boolean]",lI="[object Date]",sI="[object Map]",uI="[object Number]",cI="[object RegExp]",fI="[object Set]",dI="[object String]",vI="[object Symbol]",pI="[object ArrayBuffer]",hI="[object DataView]",mI="[object Float32Array]",gI="[object Float64Array]",yI="[object Int8Array]",bI="[object Int16Array]",wI="[object Int32Array]",SI="[object Uint8Array]",kI="[object Uint8ClampedArray]",EI="[object Uint16Array]",DI="[object Uint32Array]";function xI(t,r,a){var o=t.constructor;switch(r){case pI:return tI(t);case iI:case lI:return new o(+t);case hI:return nI(t,a);case mI:case gI:case yI:case bI:case wI:case SI:case kI:case EI:case DI:return oI(t,a);case sI:return new o;case uI:case dI:return new o(t);case cI:return rI(t);case fI:return new o;case vI:return aI(t)}}var TI=xI,_I=ur,Fb=Object.create,OI=function(){function t(){}return function(r){if(!_I(r))return{};if(Fb)return Fb(r);t.prototype=r;var a=new t;return t.prototype=void 0,a}}(),Z0=OI,CI=Z0,PI=yc,MI=np;function NI(t){return typeof t.constructor=="function"&&!MI(t)?CI(PI(t)):{}}var AI=NI,RI=dc,FI=$r,zI="[object Map]";function LI(t){return FI(t)&&RI(t)==zI}var jI=LI,II=jI,YI=fc,zb=ep,Lb=zb&&zb.isMap,HI=Lb?YI(Lb):II,BI=HI,WI=dc,VI=$r,UI="[object Set]";function $I(t){return VI(t)&&WI(t)==UI}var KI=$I,qI=KI,QI=fc,jb=ep,Ib=jb&&jb.isSet,XI=Ib?QI(Ib):qI,GI=XI,JI=Xv,ZI=Q0,e3=X0,t3=fj,n3=hj,r3=mj,a3=yj,o3=kj,i3=Rj,l3=M0,s3=J0,u3=dc,c3=Bj,f3=TI,d3=AI,v3=un,p3=cc,h3=BI,m3=ur,g3=GI,y3=Ql,b3=gc,w3=1,S3=2,k3=4,eS="[object Arguments]",E3="[object Array]",D3="[object Boolean]",x3="[object Date]",T3="[object Error]",tS="[object Function]",_3="[object GeneratorFunction]",O3="[object Map]",C3="[object Number]",nS="[object Object]",P3="[object RegExp]",M3="[object Set]",N3="[object String]",A3="[object Symbol]",R3="[object WeakMap]",F3="[object ArrayBuffer]",z3="[object DataView]",L3="[object Float32Array]",j3="[object Float64Array]",I3="[object Int8Array]",Y3="[object Int16Array]",H3="[object Int32Array]",B3="[object Uint8Array]",W3="[object Uint8ClampedArray]",V3="[object Uint16Array]",U3="[object Uint32Array]",je={};je[eS]=je[E3]=je[F3]=je[z3]=je[D3]=je[x3]=je[L3]=je[j3]=je[I3]=je[Y3]=je[H3]=je[O3]=je[C3]=je[nS]=je[P3]=je[M3]=je[N3]=je[A3]=je[B3]=je[W3]=je[V3]=je[U3]=!0;je[T3]=je[tS]=je[R3]=!1;function Cu(t,r,a,o,s,u){var c,d=r&w3,v=r&S3,h=r&k3;if(a&&(c=s?a(t,o,s,u):a(t)),c!==void 0)return c;if(!m3(t))return t;var m=v3(t);if(m){if(c=c3(t),!d)return a3(t,c)}else{var g=u3(t),y=g==tS||g==_3;if(p3(t))return r3(t,d);if(g==nS||g==eS||y&&!s){if(c=v||y?{}:d3(t),!d)return v?i3(t,n3(c,t)):o3(t,t3(c,t))}else{if(!je[g])return s?t:{};c=f3(t,g,d)}}u||(u=new JI);var w=u.get(t);if(w)return w;u.set(t,c),g3(t)?t.forEach(function(E){c.add(Cu(E,r,a,E,t,u))}):h3(t)&&t.forEach(function(E,x){c.set(x,Cu(E,r,a,x,t,u))});var k=h?v?s3:l3:v?b3:y3,S=m?void 0:k(t);return ZI(S||t,function(E,x){S&&(x=E,E=t[x]),e3(c,x,Cu(E,r,a,x,t,u))}),c}var $3=Cu;function K3(t){var r=t==null?0:t.length;return r?t[r-1]:void 0}var q3=K3,Q3=pc,X3=a0;function G3(t,r){return r.length<2?t:Q3(t,X3(r,0,-1))}var J3=G3,Z3=vc,e4=q3,t4=J3,n4=Gl;function r4(t,r){return r=Z3(r,t),t=t4(t,r),t==null||delete t[n4(e4(r))]}var a4=r4,o4=Vo,i4=yc,l4=$r,s4="[object Object]",u4=Function.prototype,c4=Object.prototype,rS=u4.toString,f4=c4.hasOwnProperty,d4=rS.call(Object);function v4(t){if(!l4(t)||o4(t)!=s4)return!1;var r=i4(t);if(r===null)return!0;var a=f4.call(r,"constructor")&&r.constructor;return typeof a=="function"&&a instanceof a&&rS.call(a)==d4}var p4=v4,h4=p4;function m4(t){return h4(t)?void 0:t}var g4=m4,y4=B0;function b4(t){var r=t==null?0:t.length;return r?y4(t,1):[]}var w4=b4,S4=w4,k4=W0,E4=U0;function D4(t){return E4(k4(t,void 0,S4),t+"")}var x4=D4,T4=ip,_4=$3,O4=a4,C4=vc,P4=Jl,M4=g4,N4=x4,A4=J0,R4=1,F4=2,z4=4,L4=N4(function(t,r){var a={};if(t==null)return a;var o=!1;r=T4(r,function(u){return u=C4(u,t),o||(o=u.length>1),u}),P4(t,A4(t),a),o&&(a=_4(a,R4|F4|z4,M4));for(var s=r.length;s--;)O4(a,r[s]);return a}),j4=L4;const I4=Ct(j4);var Y4=Q0,H4=Z0,B4=lp,W4=mc,V4=yc,U4=un,$4=cc,K4=Pv,q4=ur,Q4=tp;function X4(t,r,a){var o=U4(t),s=o||$4(t)||Q4(t);if(r=W4(r),a==null){var u=t&&t.constructor;s?a=o?new u:[]:q4(t)?a=K4(u)?H4(V4(t)):{}:a={}}return(s?Y4:B4)(t,function(c,d,v){return r(a,c,d,v)}),a}var G4=X4;const J4=Ct(G4);var Z4={exports:{}};(function(t,r){(function(a,o){t.exports=o()})(gn,function(){return function(a,o,s){o.prototype.isBetween=function(u,c,d,v){var h=s(u),m=s(c),g=(v=v||"()")[0]==="(",y=v[1]===")";return(g?this.isAfter(h,d):!this.isBefore(h,d))&&(y?this.isBefore(m,d):!this.isAfter(m,d))||(g?this.isBefore(h,d):!this.isAfter(h,d))&&(y?this.isAfter(m,d):!this.isBefore(m,d))}}})})(Z4);var e6={exports:{}};(function(t,r){(function(a,o){t.exports=o()})(gn,function(){return function(a,o){o.prototype.isSameOrAfter=function(s,u){return this.isSame(s,u)||this.isAfter(s,u)}}})})(e6);var t6={exports:{}};(function(t,r){(function(a,o){t.exports=o()})(gn,function(){return function(a,o){o.prototype.isSameOrBefore=function(s,u){return this.isSame(s,u)||this.isBefore(s,u)}}})})(t6);var n6={exports:{}};(function(t,r){(function(a,o){t.exports=o()})(gn,function(){return function(a,o,s){var u=o.prototype,c=function(g){return g&&(g.indexOf?g:g.s)},d=function(g,y,w,k,S){var E=g.name?g:g.$locale(),x=c(E[y]),_=c(E[w]),F=x||_.map(function(A){return A.slice(0,k)});if(!S)return F;var C=E.weekStart;return F.map(function(A,B){return F[(B+(C||0))%7]})},v=function(){return s.Ls[s.locale()]},h=function(g,y){return g.formats[y]||function(w){return w.replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,function(k,S,E){return S||E.slice(1)})}(g.formats[y.toUpperCase()])},m=function(){var g=this;return{months:function(y){return y?y.format("MMMM"):d(g,"months")},monthsShort:function(y){return y?y.format("MMM"):d(g,"monthsShort","months",3)},firstDayOfWeek:function(){return g.$locale().weekStart||0},weekdays:function(y){return y?y.format("dddd"):d(g,"weekdays")},weekdaysMin:function(y){return y?y.format("dd"):d(g,"weekdaysMin","weekdays",2)},weekdaysShort:function(y){return y?y.format("ddd"):d(g,"weekdaysShort","weekdays",3)},longDateFormat:function(y){return h(g.$locale(),y)},meridiem:this.$locale().meridiem,ordinal:this.$locale().ordinal}};u.localeData=function(){return m.bind(this)()},s.localeData=function(){var g=v();return{firstDayOfWeek:function(){return g.weekStart||0},weekdays:function(){return s.weekdays()},weekdaysShort:function(){return s.weekdaysShort()},weekdaysMin:function(){return s.weekdaysMin()},months:function(){return s.months()},monthsShort:function(){return s.monthsShort()},longDateFormat:function(y){return h(g,y)},meridiem:g.meridiem,ordinal:g.ordinal}},s.months=function(){return d(v(),"months")},s.monthsShort=function(){return d(v(),"monthsShort","months",3)},s.weekdays=function(g){return d(v(),"weekdays",null,null,g)},s.weekdaysShort=function(g){return d(v(),"weekdaysShort","weekdays",3,g)},s.weekdaysMin=function(g){return d(v(),"weekdaysMin","weekdays",2,g)}}})})(n6);var r6={exports:{}};(function(t,r){(function(a,o){t.exports=o()})(gn,function(){var a={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"};return function(o,s,u){var c=s.prototype,d=c.format;u.en.formats=a,c.format=function(v){v===void 0&&(v="YYYY-MM-DDTHH:mm:ssZ");var h=this.$locale().formats,m=function(g,y){return g.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,function(w,k,S){var E=S&&S.toUpperCase();return k||y[S]||a[S]||y[E].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,function(x,_,F){return _||F.slice(1)})})}(v,h===void 0?{}:h);return d.call(this,m)}}})})(r6);var a6={exports:{}};(function(t,r){(function(a,o){t.exports=o()})(gn,function(){return function(a,o,s){var u=function(c,d){if(!d||!d.length||d.length===1&&!d[0]||d.length===1&&Array.isArray(d[0])&&!d[0].length)return null;var v;d.length===1&&d[0].length>0&&(d=d[0]),v=(d=d.filter(function(m){return m}))[0];for(var h=1;h<d.length;h+=1)d[h].isValid()&&!d[h][c](v)||(v=d[h]);return v};s.max=function(){var c=[].slice.call(arguments,0);return u("isAfter",c)},s.min=function(){var c=[].slice.call(arguments,0);return u("isBefore",c)}}})})(a6);var o6={exports:{}};(function(t,r){(function(a,o){t.exports=o()})(gn,function(){var a="minute",o=/[+-]\d\d(?::?\d\d)?/g,s=/([+-]|\d\d)/g;return function(u,c,d){var v=c.prototype;d.utc=function(S){var E={date:S,utc:!0,args:arguments};return new c(E)},v.utc=function(S){var E=d(this.toDate(),{locale:this.$L,utc:!0});return S?E.add(this.utcOffset(),a):E},v.local=function(){return d(this.toDate(),{locale:this.$L,utc:!1})};var h=v.parse;v.parse=function(S){S.utc&&(this.$u=!0),this.$utils().u(S.$offset)||(this.$offset=S.$offset),h.call(this,S)};var m=v.init;v.init=function(){if(this.$u){var S=this.$d;this.$y=S.getUTCFullYear(),this.$M=S.getUTCMonth(),this.$D=S.getUTCDate(),this.$W=S.getUTCDay(),this.$H=S.getUTCHours(),this.$m=S.getUTCMinutes(),this.$s=S.getUTCSeconds(),this.$ms=S.getUTCMilliseconds()}else m.call(this)};var g=v.utcOffset;v.utcOffset=function(S,E){var x=this.$utils().u;if(x(S))return this.$u?0:x(this.$offset)?g.call(this):this.$offset;if(typeof S=="string"&&(S=function(A){A===void 0&&(A="");var B=A.match(o);if(!B)return null;var q=(""+B[0]).match(s)||["-",0,0],Q=q[0],I=60*+q[1]+ +q[2];return I===0?0:Q==="+"?I:-I}(S),S===null))return this;var _=Math.abs(S)<=16?60*S:S,F=this;if(E)return F.$offset=_,F.$u=S===0,F;if(S!==0){var C=this.$u?this.toDate().getTimezoneOffset():-1*this.utcOffset();(F=this.local().add(_+C,a)).$offset=_,F.$x.$localOffset=C}else F=this.utc();return F};var y=v.format;v.format=function(S){var E=S||(this.$u?"YYYY-MM-DDTHH:mm:ss[Z]":"");return y.call(this,E)},v.valueOf=function(){var S=this.$utils().u(this.$offset)?0:this.$offset+(this.$x.$localOffset||this.$d.getTimezoneOffset());return this.$d.valueOf()-6e4*S},v.isUTC=function(){return!!this.$u},v.toISOString=function(){return this.toDate().toISOString()},v.toString=function(){return this.toDate().toUTCString()};var w=v.toDate;v.toDate=function(S){return S==="s"&&this.$offset?d(this.format("YYYY-MM-DD HH:mm:ss:SSS")).toDate():w.call(this)};var k=v.diff;v.diff=function(S,E,x){if(S&&this.$u===S.$u)return k.call(this,S,E,x);var _=this.local(),F=d(S).local();return k.call(_,F,E,x)}}})})(o6);function ar(t){return t.children}var Lt={PREVIOUS:"PREV",NEXT:"NEXT",TODAY:"TODAY",DATE:"DATE"},Ot={MONTH:"month",WEEK:"week",WORK_WEEK:"work_week",DAY:"day",AGENDA:"agenda"},Yb=Object.keys(Ot).map(function(t){return Ot[t]});oe.oneOfType([oe.string,oe.func]);oe.any;oe.func;oe.oneOfType([oe.arrayOf(oe.oneOf(Yb)),oe.objectOf(function(t,r){var a=Yb.indexOf(r)!==-1&&typeof t[r]=="boolean";if(a)return null;for(var o=arguments.length,s=new Array(o>2?o-2:0),u=2;u<o;u++)s[u-2]=arguments[u];return oe.elementType.apply(oe,[t,r].concat(s))})]);oe.oneOfType([oe.oneOf(["overlap","no-overlap"]),oe.func]);var Hb={seconds:1e3,minutes:1e3*60,hours:1e3*60*60,day:1e3*60*60*24};function aS(t,r){var a=ht(t,"month");return ht(a,"week",r.startOfWeek())}function oS(t,r){var a=Fu(t,"month");return Fu(a,"week",r.startOfWeek())}function i6(t,r){for(var a=aS(t,r),o=oS(t,r),s=[];Ho(a,o,"day");)s.push(a),a=bn(a,1,"day");return s}function l6(t,r){var a=ht(t,r);return Yl(a,t)?a:bn(a,1,r)}function s6(t,r){for(var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"day",o=t,s=[];Ho(o,r,a);)s.push(o),o=bn(o,1,a);return s}function u6(t,r){return r==null&&t==null?null:(r==null&&(r=new Date),t==null&&(t=new Date),t=ht(t,"day"),t=Sl(t,Sl(r)),t=Po(t,Po(r)),t=wl(t,wl(r)),bl(t,bl(r)))}function Bb(t){return Sl(t)===0&&Po(t)===0&&wl(t)===0&&bl(t)===0}function c6(t,r,a,o){return a==="day"&&(a="date"),Math.abs(Ty[a](t,void 0,o)-Ty[a](r,void 0,o))}function cp(t,r,a){return!a||a==="milliseconds"?Math.abs(+t-+r):Math.round(Math.abs(+ht(t,a)/Hb[a]-+ht(r,a)/Hb[a]))}var f6=oe.oneOfType([oe.string,oe.func]);function d6(t,r,a,o,s){var u=typeof o=="function"?o(a,s,t):r.call(t,a,o,s);return vl(u==null||typeof u=="string","`localizer format(..)` must return a string, null, or undefined"),u}function v6(t,r,a){return new Date(t.getFullYear(),t.getMonth(),t.getDate(),0,r+a,0,0)}function fp(t,r){return t.getTimezoneOffset()-r.getTimezoneOffset()}function p6(t,r){return cp(t,r,"minutes")+fp(t,r)}function h6(t){var r=ht(t,"day");return cp(r,t,"minutes")+fp(r,t)}function m6(t,r){return Cv(t,r,"day")}function g6(t,r,a){var o=Yl(t,r,"minutes");return o?Hl(r,a,"minutes"):Qu(r,a,"minutes")}function yv(t,r){return c6(t,r,"day")}function y6(t){var r=t.evtA,a=r.start,o=r.end,s=r.allDay,u=t.evtB,c=u.start,d=u.end,v=u.allDay,h=+ht(a,"day")-+ht(c,"day"),m=yv(a,o),g=yv(c,d);return h||g-m||!!v-!!s||+a-+c||+o-+d}function b6(t){var r=t.event,a=r.start,o=r.end,s=t.range,u=s.start,c=s.end,d=ht(a,"day"),v=Ho(d,c,"day"),h=Ov(d,o,"minutes"),m=h?Qu(o,u,"minutes"):Hl(o,u,"minutes");return v&&m}function w6(t,r){return Yl(t,r,"day")}function S6(t,r){return Bb(t)&&Bb(r)}var k6=at(function t(r){var a=this;rt(this,t),vl(typeof r.format=="function","date localizer `format(..)` must be a function"),vl(typeof r.firstOfWeek=="function","date localizer `firstOfWeek(..)` must be a function"),this.propType=r.propType||f6,this.formats=r.formats,this.format=function(){for(var o=arguments.length,s=new Array(o),u=0;u<o;u++)s[u]=arguments[u];return d6.apply(void 0,[a,r.format].concat(s))},this.startOfWeek=r.firstOfWeek,this.merge=r.merge||u6,this.inRange=r.inRange||Zw,this.lt=r.lt||Cv,this.lte=r.lte||Ho,this.gt=r.gt||Qu,this.gte=r.gte||Hl,this.eq=r.eq||Yl,this.neq=r.neq||Ov,this.startOf=r.startOf||ht,this.endOf=r.endOf||Fu,this.add=r.add||bn,this.range=r.range||s6,this.diff=r.diff||cp,this.ceil=r.ceil||l6,this.min=r.min||Gw,this.max=r.max||Jw,this.minutes=r.minutes||Po,this.daySpan=r.daySpan||yv,this.firstVisibleDay=r.firstVisibleDay||aS,this.lastVisibleDay=r.lastVisibleDay||oS,this.visibleDays=r.visibleDays||i6,this.getSlotDate=r.getSlotDate||v6,this.getTimezoneOffset=r.getTimezoneOffset||function(o){return o.getTimezoneOffset()},this.getDstOffset=r.getDstOffset||fp,this.getTotalMin=r.getTotalMin||p6,this.getMinutesFromMidnight=r.getMinutesFromMidnight||h6,this.continuesPrior=r.continuesPrior||m6,this.continuesAfter=r.continuesAfter||g6,this.sortEvents=r.sortEvents||y6,this.inEventRange=r.inEventRange||b6,this.isSameDate=r.isSameDate||w6,this.startAndEndAreDateOnly=r.startAndEndAreDateOnly||S6,this.segmentOffset=r.browserTZOffset?r.browserTZOffset():0});function E6(t,r,a,o){var s=Fe(Fe({},t.formats),a);return Fe(Fe({},t),{},{messages:o,startOfWeek:function(){return t.startOfWeek(r)},format:function(c,d){return t.format(c,s[d]||d,r)}})}var D6=function(t){function r(){var a;rt(this,r);for(var o=arguments.length,s=new Array(o),u=0;u<o;u++)s[u]=arguments[u];return a=Et(this,r,[].concat(s)),a.navigate=function(c){a.props.onNavigate(c)},a.view=function(c){a.props.onView(c)},a}return Dt(r,t),at(r,[{key:"render",value:function(){var o=this.props,s=o.localizer.messages,u=o.label;return R.createElement("div",{className:"rbc-toolbar"},R.createElement("span",{className:"rbc-btn-group"},R.createElement("button",{type:"button",onClick:this.navigate.bind(null,Lt.TODAY)},s.today),R.createElement("button",{type:"button",onClick:this.navigate.bind(null,Lt.PREVIOUS)},s.previous),R.createElement("button",{type:"button",onClick:this.navigate.bind(null,Lt.NEXT)},s.next)),R.createElement("span",{className:"rbc-toolbar-label"},u),R.createElement("span",{className:"rbc-btn-group"},this.viewNamesGroup(s)))}},{key:"viewNamesGroup",value:function(o){var s=this,u=this.props.views,c=this.props.view;if(u.length>1)return u.map(function(d){return R.createElement("button",{type:"button",key:d,className:He({"rbc-active":c===d}),onClick:s.view.bind(null,d)},o[d])})}}])}(R.Component);function Ye(t,r){t&&t.apply(null,[].concat(r))}var x6={date:"Date",time:"Time",event:"Event",allDay:"All Day",week:"Week",work_week:"Work Week",day:"Day",month:"Month",previous:"Back",next:"Next",yesterday:"Yesterday",tomorrow:"Tomorrow",today:"Today",agenda:"Agenda",noEventsInRange:"There are no events in this range.",showMore:function(r){return"+".concat(r," more")}};function T6(t){return Fe(Fe({},x6),t)}function _6(t){var r=t.ref,a=t.callback;te.useEffect(function(){var o=function(u){r.current&&!r.current.contains(u.target)&&a()};return document.addEventListener("mousedown",o),function(){document.removeEventListener("mousedown",o)}},[r,a])}var O6=["style","className","event","selected","isAllDay","onSelect","onDoubleClick","onKeyPress","localizer","continuesPrior","continuesAfter","accessors","getters","children","components","slotStart","slotEnd"],iS=function(t){function r(){return rt(this,r),Et(this,r,arguments)}return Dt(r,t),at(r,[{key:"render",value:function(){var o=this.props,s=o.style,u=o.className,c=o.event,d=o.selected,v=o.isAllDay,h=o.onSelect,m=o.onDoubleClick,g=o.onKeyPress,y=o.localizer,w=o.continuesPrior,k=o.continuesAfter,S=o.accessors,E=o.getters,x=o.children,_=o.components,F=_.event,C=_.eventWrapper,A=o.slotStart,B=o.slotEnd,q=Fn(o,O6);delete q.resizable;var Q=S.title(c),I=S.tooltip(c),V=S.end(c),J=S.start(c),le=S.allDay(c),ae=v||le||y.diff(J,y.ceil(V,"day"),"day")>1,fe=E.eventProp(c,J,V,d),me=R.createElement("div",{className:"rbc-event-content",title:I||void 0},F?R.createElement(F,{event:c,continuesPrior:w,continuesAfter:k,title:Q,isAllDay:le,localizer:y,slotStart:A,slotEnd:B}):Q);return R.createElement(C,Object.assign({},this.props,{type:"date"}),R.createElement("div",Object.assign({},q,{style:Fe(Fe({},fe.style),s),className:He("rbc-event",u,fe.className,{"rbc-selected":d,"rbc-event-allday":ae,"rbc-event-continues-prior":w,"rbc-event-continues-after":k}),onClick:function(ie){return h&&h(c,ie)},onDoubleClick:function(ie){return m&&m(c,ie)},onKeyDown:function(ie){return g&&g(c,ie)}}),typeof x=="function"?x(me):me))}}])}(R.Component);function bc(t,r){return!t||r==null?!1:KA(t,r)}function lS(t,r){var a=t.right-t.left,o=a/r;return o}function sS(t,r,a,o){var s=lS(t,o);return a?o-1-Math.floor((r-t.left)/s):Math.floor((r-t.left)/s)}function C6(t,r){var a=r.x,o=r.y;return o>=t.top&&o<=t.bottom&&a>=t.left&&a<=t.right}function P6(t,r,a,o,s){var u=-1,c=-1,d=o-1,v=lS(r,o),h=sS(r,a.x,s,o),m=r.top<a.y&&r.bottom>a.y,g=r.top<t.y&&r.bottom>t.y,y=t.y>r.bottom,w=r.top>t.y,k=a.top<r.top&&a.bottom>r.bottom;return k&&(u=0,c=d),m&&(w?(u=0,c=h):y&&(u=h,c=d)),g&&(u=c=s?d-Math.floor((t.x-r.left)/v):Math.floor((t.x-r.left)/v),m?h<u?u=h:c=h:t.y<a.y?c=d:u=0),{startIdx:u,endIdx:c}}function M6(t){var r=t.target,a=t.offset,o=t.container,s=t.box,u=yn(r),c=u.top,d=u.left,v=u.width,h=u.height,m=yn(o),g=m.top,y=m.left,w=m.width,k=m.height,S=yn(s),E=S.width,x=S.height,_=g+k,F=y+w,C=c+x,A=d+E,B=a.x,q=a.y,Q=C>_?c-x-q:c+q+h,I=A>F?d+B-E+v:d+B;return{topOffset:Q,leftOffset:I}}function N6(t){var r=t.containerRef,a=t.accessors,o=t.getters,s=t.selected,u=t.components,c=t.localizer,d=t.position,v=t.show,h=t.events,m=t.slotStart,g=t.slotEnd,y=t.onSelect,w=t.onDoubleClick,k=t.onKeyPress,S=t.handleDragStart,E=t.popperRef,x=t.target,_=t.offset;_6({ref:E,callback:v}),te.useLayoutEffect(function(){var A=M6({target:x,offset:_,container:r.current,box:E.current}),B=A.topOffset,q=A.leftOffset;E.current.style.top="".concat(B,"px"),E.current.style.left="".concat(q,"px")},[_.x,_.y,x]);var F=d.width,C={minWidth:F+F/2};return R.createElement("div",{style:C,className:"rbc-overlay",ref:E},R.createElement("div",{className:"rbc-overlay-header"},c.format(m,"dayHeaderFormat")),h.map(function(A,B){return R.createElement(iS,{key:B,type:"popup",localizer:c,event:A,getters:o,onSelect:y,accessors:a,components:u,onDoubleClick:w,onKeyPress:k,continuesPrior:c.lt(a.end(A),m,"day"),continuesAfter:c.gte(a.start(A),g,"day"),slotStart:m,slotEnd:g,selected:bc(A,s),draggable:!0,onDragStart:function(){return S(A)},onDragEnd:function(){return v()}})}))}var uS=R.forwardRef(function(t,r){return R.createElement(N6,Object.assign({},t,{popperRef:r}))});uS.propTypes={accessors:oe.object.isRequired,getters:oe.object.isRequired,selected:oe.object,components:oe.object.isRequired,localizer:oe.object.isRequired,position:oe.object.isRequired,show:oe.func.isRequired,events:oe.array.isRequired,slotStart:oe.instanceOf(Date).isRequired,slotEnd:oe.instanceOf(Date),onSelect:oe.func,onDoubleClick:oe.func,onKeyPress:oe.func,handleDragStart:oe.func,style:oe.object,offset:oe.shape({x:oe.number,y:oe.number})};function A6(t){var r=t.containerRef,a=t.popupOffset,o=a===void 0?5:a,s=t.overlay,u=t.accessors,c=t.localizer,d=t.components,v=t.getters,h=t.selected,m=t.handleSelectEvent,g=t.handleDoubleClickEvent,y=t.handleKeyPressEvent,w=t.handleDragStart,k=t.onHide,S=t.overlayDisplay,E=te.useRef(null);if(!s.position)return null;var x=o;isNaN(o)||(x={x:o,y:o});var _=s.position,F=s.events,C=s.date,A=s.end;return R.createElement(Kv,{rootClose:!0,flip:!0,show:!0,placement:"bottom",onHide:k,target:s.target},function(B){var q=B.props;return R.createElement(uS,Object.assign({},q,{containerRef:r,ref:E,target:s.target,offset:x,accessors:u,getters:v,selected:h,components:d,localizer:c,position:_,show:S,events:F,slotStart:C,slotEnd:A,onSelect:m,onDoubleClick:g,onKeyPress:y,handleDragStart:w}))})}var dp=R.forwardRef(function(t,r){return R.createElement(A6,Object.assign({},t,{containerRef:r}))});dp.propTypes={popupOffset:oe.oneOfType([oe.number,oe.shape({x:oe.number,y:oe.number})]),overlay:oe.shape({position:oe.object,events:oe.array,date:oe.instanceOf(Date),end:oe.instanceOf(Date)}),accessors:oe.object.isRequired,localizer:oe.object.isRequired,components:oe.object.isRequired,getters:oe.object.isRequired,selected:oe.object,handleSelectEvent:oe.func,handleDoubleClickEvent:oe.func,handleKeyPressEvent:oe.func,handleDragStart:oe.func,onHide:oe.func,overlayDisplay:oe.func};function bt(t,r){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:document;return Mn(a,t,r,{passive:!1})}function R6(t,r,a){return!t||lr(t,document.elementFromPoint(r,a))}function F6(t,r){var a=r.clientX,o=r.clientY,s=document.elementFromPoint(a,o);return Xl(s,".rbc-event",t)}function z6(t,r){var a=r.clientX,o=r.clientY,s=document.elementFromPoint(a,o);return Xl(s,".rbc-show-more",t)}function Bu(t,r){return!!F6(t,r)}function L6(t,r){return!!z6(t,r)}function ol(t){var r=t;return t.touches&&t.touches.length&&(r=t.touches[0]),{clientX:r.clientX,clientY:r.clientY,pageX:r.pageX,pageY:r.pageY}}var Wb=5,j6=250,cS=function(){function t(r){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=a.global,s=o===void 0?!1:o,u=a.longPressThreshold,c=u===void 0?250:u,d=a.validContainers,v=d===void 0?[]:d;rt(this,t),this._initialEvent=null,this.selecting=!1,this.isDetached=!1,this.container=r,this.globalMouse=!r||s,this.longPressThreshold=c,this.validContainers=v,this._listeners=Object.create(null),this._handleInitialEvent=this._handleInitialEvent.bind(this),this._handleMoveEvent=this._handleMoveEvent.bind(this),this._handleTerminatingEvent=this._handleTerminatingEvent.bind(this),this._keyListener=this._keyListener.bind(this),this._dropFromOutsideListener=this._dropFromOutsideListener.bind(this),this._dragOverFromOutsideListener=this._dragOverFromOutsideListener.bind(this),this._removeTouchMoveWindowListener=bt("touchmove",function(){},window),this._removeKeyDownListener=bt("keydown",this._keyListener),this._removeKeyUpListener=bt("keyup",this._keyListener),this._removeDropFromOutsideListener=bt("drop",this._dropFromOutsideListener),this._removeDragOverFromOutsideListener=bt("dragover",this._dragOverFromOutsideListener),this._addInitialEventListener()}return at(t,[{key:"on",value:function(a,o){var s=this._listeners[a]||(this._listeners[a]=[]);return s.push(o),{remove:function(){var c=s.indexOf(o);c!==-1&&s.splice(c,1)}}}},{key:"emit",value:function(a){for(var o=arguments.length,s=new Array(o>1?o-1:0),u=1;u<o;u++)s[u-1]=arguments[u];var c,d=this._listeners[a]||[];return d.forEach(function(v){c===void 0&&(c=v.apply(void 0,s))}),c}},{key:"teardown",value:function(){this._initialEvent=null,this._initialEventData=null,this._selectRect=null,this.selecting=!1,this._lastClickData=null,this.isDetached=!0,this._listeners=Object.create(null),this._removeTouchMoveWindowListener&&this._removeTouchMoveWindowListener(),this._removeInitialEventListener&&this._removeInitialEventListener(),this._removeEndListener&&this._removeEndListener(),this._onEscListener&&this._onEscListener(),this._removeMoveListener&&this._removeMoveListener(),this._removeKeyUpListener&&this._removeKeyUpListener(),this._removeKeyDownListener&&this._removeKeyDownListener(),this._removeDropFromOutsideListener&&this._removeDropFromOutsideListener(),this._removeDragOverFromOutsideListener&&this._removeDragOverFromOutsideListener()}},{key:"isSelected",value:function(a){var o=this._selectRect;return!o||!this.selecting?!1:Vb(o,za(a))}},{key:"filter",value:function(a){var o=this._selectRect;return!o||!this.selecting?[]:a.filter(this.isSelected,this)}},{key:"_addLongPressListener",value:function(a,o){var s=this,u=null,c=null,d=null,v=function(y){u=setTimeout(function(){m(),a(y)},s.longPressThreshold),c=bt("touchmove",function(){return m()}),d=bt("touchend",function(){return m()})},h=bt("touchstart",v),m=function(){u&&clearTimeout(u),c&&c(),d&&d(),u=null,c=null,d=null};return o&&v(o),function(){m(),h()}}},{key:"_addInitialEventListener",value:function(){var a=this,o=bt("mousedown",function(u){a._removeInitialEventListener(),a._handleInitialEvent(u),a._removeInitialEventListener=bt("mousedown",a._handleInitialEvent)}),s=bt("touchstart",function(u){a._removeInitialEventListener(),a._removeInitialEventListener=a._addLongPressListener(a._handleInitialEvent,u)});this._removeInitialEventListener=function(){o(),s()}}},{key:"_dropFromOutsideListener",value:function(a){var o=ol(a),s=o.pageX,u=o.pageY,c=o.clientX,d=o.clientY;this.emit("dropFromOutside",{x:s,y:u,clientX:c,clientY:d}),a.preventDefault()}},{key:"_dragOverFromOutsideListener",value:function(a){var o=ol(a),s=o.pageX,u=o.pageY,c=o.clientX,d=o.clientY;this.emit("dragOverFromOutside",{x:s,y:u,clientX:c,clientY:d}),a.preventDefault()}},{key:"_handleInitialEvent",value:function(a){if(this._initialEvent=a,!this.isDetached){var o=ol(a),s=o.clientX,u=o.clientY,c=o.pageX,d=o.pageY,v=this.container(),h,m;if(!(a.which===3||a.button===2||!R6(v,s,u))){if(!this.globalMouse&&v&&!lr(v,a.target)){var g=I6(0),y=g.top,w=g.left,k=g.bottom,S=g.right;if(m=za(v),h=Vb({top:m.top-y,left:m.left-w,bottom:m.bottom+k,right:m.right+S},{top:d,left:c}),!h)return}var E=this.emit("beforeSelect",this._initialEventData={isTouch:/^touch/.test(a.type),x:c,y:d,clientX:s,clientY:u});if(E!==!1)switch(a.type){case"mousedown":this._removeEndListener=bt("mouseup",this._handleTerminatingEvent),this._onEscListener=bt("keydown",this._handleTerminatingEvent),this._removeMoveListener=bt("mousemove",this._handleMoveEvent);break;case"touchstart":this._handleMoveEvent(a),this._removeEndListener=bt("touchend",this._handleTerminatingEvent),this._removeMoveListener=bt("touchmove",this._handleMoveEvent);break}}}}},{key:"_isWithinValidContainer",value:function(a){var o=a.target,s=this.validContainers;return!s||!s.length||!o?!0:s.some(function(u){return!!o.closest(u)})}},{key:"_handleTerminatingEvent",value:function(a){var o=this.selecting,s=this._selectRect;if(!o&&a.type.includes("key")&&(a=this._initialEvent),this.selecting=!1,this._removeEndListener&&this._removeEndListener(),this._removeMoveListener&&this._removeMoveListener(),this._selectRect=null,this._initialEvent=null,this._initialEventData=null,!!a){var u=!this.container||lr(this.container(),a.target),c=this._isWithinValidContainer(a);return a.key==="Escape"||!c?this.emit("reset"):!o&&u?this._handleClickEvent(a):o?this.emit("select",s):this.emit("reset")}}},{key:"_handleClickEvent",value:function(a){var o=ol(a),s=o.pageX,u=o.pageY,c=o.clientX,d=o.clientY,v=new Date().getTime();return this._lastClickData&&v-this._lastClickData.timestamp<j6?(this._lastClickData=null,this.emit("doubleClick",{x:s,y:u,clientX:c,clientY:d})):(this._lastClickData={timestamp:v},this.emit("click",{x:s,y:u,clientX:c,clientY:d}))}},{key:"_handleMoveEvent",value:function(a){if(!(this._initialEventData===null||this.isDetached)){var o=this._initialEventData,s=o.x,u=o.y,c=ol(a),d=c.pageX,v=c.pageY,h=Math.abs(s-d),m=Math.abs(u-v),g=Math.min(d,s),y=Math.min(v,u),w=this.selecting,k=this.isClick(d,v);k&&!w&&!(h||m)||(!w&&!k&&this.emit("selectStart",this._initialEventData),k||(this.selecting=!0,this._selectRect={top:y,left:g,x:d,y:v,right:g+h,bottom:y+m},this.emit("selecting",this._selectRect)),a.preventDefault())}}},{key:"_keyListener",value:function(a){this.ctrl=a.metaKey||a.ctrlKey}},{key:"isClick",value:function(a,o){var s=this._initialEventData,u=s.x,c=s.y,d=s.isTouch;return!d&&Math.abs(a-u)<=Wb&&Math.abs(o-c)<=Wb}}])}();function I6(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0;return ir(t)!=="object"&&(t={top:t,left:t,right:t,bottom:t}),t}function Vb(t,r){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,o=za(t),s=o.top,u=o.left,c=o.right,d=c===void 0?u:c,v=o.bottom,h=v===void 0?s:v,m=za(r),g=m.top,y=m.left,w=m.right,k=w===void 0?y:w,S=m.bottom,E=S===void 0?g:S;return!(h-a<g||s+a>E||d-a<y||u+a>k)}function za(t){if(!t.getBoundingClientRect)return t;var r=t.getBoundingClientRect(),a=r.left+Ub("left"),o=r.top+Ub("top");return{top:o,left:a,right:(t.offsetWidth||0)+a,bottom:(t.offsetHeight||0)+o}}function Ub(t){if(t==="left")return window.pageXOffset||document.body.scrollLeft||0;if(t==="top")return window.pageYOffset||document.body.scrollTop||0}var Y6=function(t){function r(a,o){var s;return rt(this,r),s=Et(this,r,[a,o]),s.state={selecting:!1},s.containerRef=te.createRef(),s}return Dt(r,t),at(r,[{key:"componentDidMount",value:function(){this.props.selectable&&this._selectable()}},{key:"componentWillUnmount",value:function(){this._teardownSelectable()}},{key:"componentDidUpdate",value:function(o){!o.selectable&&this.props.selectable&&this._selectable(),o.selectable&&!this.props.selectable&&this._teardownSelectable()}},{key:"render",value:function(){var o=this.props,s=o.range,u=o.getNow,c=o.getters,d=o.date,v=o.components.dateCellWrapper,h=o.localizer,m=this.state,g=m.selecting,y=m.startIdx,w=m.endIdx,k=u();return R.createElement("div",{className:"rbc-row-bg",ref:this.containerRef},s.map(function(S,E){var x=g&&E>=y&&E<=w,_=c.dayProp(S),F=_.className,C=_.style;return R.createElement(v,{key:E,value:S,range:s},R.createElement("div",{style:C,className:He("rbc-day-bg",F,x&&"rbc-selected-cell",h.isSameDate(S,k)&&"rbc-today",d&&h.neq(d,S,"month")&&"rbc-off-range-bg")}))}))}},{key:"_selectable",value:function(){var o=this,s=this.containerRef.current,u=this._selector=new cS(this.props.container,{longPressThreshold:this.props.longPressThreshold}),c=function(v,h){if(!Bu(s,v)&&!L6(s,v)){var m=za(s),g=o.props,y=g.range,w=g.rtl;if(C6(m,v)){var k=sS(m,v.x,w,y.length);o._selectSlot({startIdx:k,endIdx:k,action:h,box:v})}}o._initial={},o.setState({selecting:!1})};u.on("selecting",function(d){var v=o.props,h=v.range,m=v.rtl,g=-1,y=-1;if(o.state.selecting||(Ye(o.props.onSelectStart,[d]),o._initial={x:d.x,y:d.y}),u.isSelected(s)){var w=za(s),k=P6(o._initial,w,d,h.length,m);g=k.startIdx,y=k.endIdx}o.setState({selecting:!0,startIdx:g,endIdx:y})}),u.on("beforeSelect",function(d){if(o.props.selectable==="ignoreEvents")return!Bu(o.containerRef.current,d)}),u.on("click",function(d){return c(d,"click")}),u.on("doubleClick",function(d){return c(d,"doubleClick")}),u.on("select",function(d){o._selectSlot(Fe(Fe({},o.state),{},{action:"select",bounds:d})),o._initial={},o.setState({selecting:!1}),Ye(o.props.onSelectEnd,[o.state])})}},{key:"_teardownSelectable",value:function(){this._selector&&(this._selector.teardown(),this._selector=null)}},{key:"_selectSlot",value:function(o){var s=o.endIdx,u=o.startIdx,c=o.action,d=o.bounds,v=o.box;s!==-1&&u!==-1&&this.props.onSelectSlot&&this.props.onSelectSlot({start:u,end:s,action:c,bounds:d,box:v,resourceId:this.props.resourceId})}}])}(R.Component),Cn={propTypes:{slotMetrics:oe.object.isRequired,selected:oe.object,isAllDay:oe.bool,accessors:oe.object.isRequired,localizer:oe.object.isRequired,components:oe.object.isRequired,getters:oe.object.isRequired,onSelect:oe.func,onDoubleClick:oe.func,onKeyPress:oe.func},defaultProps:{segments:[],selected:{}},renderEvent:function(r,a){var o=r.selected;r.isAllDay;var s=r.accessors,u=r.getters,c=r.onSelect,d=r.onDoubleClick,v=r.onKeyPress,h=r.localizer,m=r.slotMetrics,g=r.components,y=r.resizable,w=m.continuesPrior(a),k=m.continuesAfter(a);return R.createElement(iS,{event:a,getters:u,localizer:h,accessors:s,components:g,onSelect:c,onDoubleClick:d,onKeyPress:v,continuesPrior:w,continuesAfter:k,slotStart:m.first,slotEnd:m.last,selected:bc(a,o),resizable:y})},renderSpan:function(r,a,o){var s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:" ",u=Math.abs(a)/r*100+"%";return R.createElement("div",{key:o,className:"rbc-row-segment",style:{WebkitFlexBasis:u,flexBasis:u,maxWidth:u}},s)}},fS=function(t){function r(){return rt(this,r),Et(this,r,arguments)}return Dt(r,t),at(r,[{key:"render",value:function(){var o=this,s=this.props,u=s.segments,c=s.slotMetrics.slots,d=s.className,v=1;return R.createElement("div",{className:He(d,"rbc-row")},u.reduce(function(h,m,g){var y=m.event,w=m.left,k=m.right,S=m.span,E="_lvl_"+g,x=w-v,_=Cn.renderEvent(o.props,y);return x&&h.push(Cn.renderSpan(c,x,"".concat(E,"_gap"))),h.push(Cn.renderSpan(c,S,E,_)),v=k+1,h},[]))}}])}(R.Component);fS.defaultProps=Fe({},Cn.defaultProps);function dS(t){var r=t.dateRange,a=t.unit,o=a===void 0?"day":a,s=t.localizer;return{first:r[0],last:s.add(r[r.length-1],1,o)}}function H6(t,r,a,o){var s=dS({dateRange:r,localizer:o}),u=s.first,c=s.last,d=o.diff(u,c,"day"),v=o.max(o.startOf(a.start(t),"day"),u),h=o.min(o.ceil(a.end(t),"day"),c),m=$F(r,function(y){return o.isSameDate(y,v)}),g=o.diff(v,h,"day");return g=Math.min(g,d),g=Math.max(g-o.segmentOffset,1),{event:t,span:g,left:m+1,right:Math.max(m+g,1)}}function vS(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1/0,a,o,s,u=[],c=[];for(a=0;a<t.length;a++){for(s=t[a],o=0;o<u.length&&B6(s,u[o]);o++);o>=r?c.push(s):(u[o]||(u[o]=[])).push(s)}for(a=0;a<u.length;a++)u[a].sort(function(d,v){return d.left-v.left});return{levels:u,extra:c}}function Tl(t,r,a,o,s){var u={start:o.start(t),end:o.end(t)},c={start:r,end:a};return s.inEventRange({event:u,range:c})}function B6(t,r){return r.some(function(a){return a.left<=t.right&&a.right>=t.left})}function W6(t,r,a){var o=_u(t),s=[],u=[];o.forEach(function(v){var h=r.start(v),m=r.end(v);a.daySpan(h,m)>1?s.push(v):u.push(v)});var c=s.sort(function(v,h){return bv(v,h,r,a)}),d=u.sort(function(v,h){return bv(v,h,r,a)});return[].concat(_u(c),_u(d))}function bv(t,r,a,o){var s={start:a.start(t),end:a.end(t),allDay:a.allDay(t)},u={start:a.start(r),end:a.end(r),allDay:a.allDay(r)};return o.sortEvents({evtA:s,evtB:u})}var pS=function(r,a){return r.left<=a&&r.right>=a},$b=function(r,a){return r.filter(function(o){return pS(o,a)}).map(function(o){return o.event})},hS=function(t){function r(){return rt(this,r),Et(this,r,arguments)}return Dt(r,t),at(r,[{key:"render",value:function(){for(var o=this.props,s=o.segments,u=o.slotMetrics.slots,c=vS(s).levels[0],d=1,v=1,h=[];d<=u;){var m="_lvl_"+d,g=c.filter(function(_){return pS(_,d)})[0]||{},y=g.event,w=g.left,k=g.right,S=g.span;if(!y){d++;continue}var E=Math.max(0,w-v);if(this.canRenderSlotEvent(w,S)){var x=Cn.renderEvent(this.props,y);E&&h.push(Cn.renderSpan(u,E,m+"_gap")),h.push(Cn.renderSpan(u,S,m,x)),v=d=k+1}else E&&h.push(Cn.renderSpan(u,E,m+"_gap")),h.push(Cn.renderSpan(u,1,m,this.renderShowMore(s,d))),v=d+=1}return R.createElement("div",{className:"rbc-row"},h)}},{key:"canRenderSlotEvent",value:function(o,s){var u=this.props.segments;return az(o,o+s).every(function(c){var d=$b(u,c).length;return d===1})}},{key:"renderShowMore",value:function(o,s){var u=this,c=this.props,d=c.localizer,v=c.slotMetrics,h=c.components,m=v.getEventsForSlot(s),g=$b(o,s),y=g.length;if(h!=null&&h.showMore){var w=h.showMore,k=v.getDateForSlot(s-1);return y?R.createElement(w,{localizer:d,slotDate:k,slot:s,count:y,events:m,remainingEvents:g}):!1}return y?R.createElement("button",{type:"button",key:"sm_"+s,className:He("rbc-button-link","rbc-show-more"),onClick:function(E){return u.showMore(s,E)}},d.messages.showMore(y,g,m)):!1}},{key:"showMore",value:function(o,s){s.preventDefault(),s.stopPropagation(),this.props.onShowMore(o,s.target)}}])}(R.Component);hS.defaultProps=Fe({},Cn.defaultProps);var V6=function(r){var a=r.children;return R.createElement("div",{className:"rbc-row-content-scroll-container"},a)},U6=function(r,a){return r.left<=a&&r.right>=a},$6=function(r,a){return r[0].range===a[0].range&&r[0].events===a[0].events};function mS(){return Y0(function(t){for(var r=t.range,a=t.events,o=t.maxRows,s=t.minRows,u=t.accessors,c=t.localizer,d=dS({dateRange:r,localizer:c}),v=d.first,h=d.last,m=a.map(function(S){return H6(S,r,u,c)}),g=vS(m,Math.max(o-1,1)),y=g.levels,w=g.extra,k=w.length>0?s-1:s;y.length<k;)y.push([]);return{first:v,last:h,levels:y,extra:w,range:r,slots:r.length,clone:function(E){var x=mS();return x(Fe(Fe({},t),E))},getDateForSlot:function(E){return r[E]},getSlotForDate:function(E){return r.find(function(x){return c.isSameDate(x,E)})},getEventsForSlot:function(E){return m.filter(function(x){return U6(x,E)}).map(function(x){return x.event})},continuesPrior:function(E){return c.continuesPrior(u.start(E),v)},continuesAfter:function(E){var x=u.start(E),_=u.end(E);return c.continuesAfter(x,_,h)}}},$6)}var _l=function(t){function r(){var a;rt(this,r);for(var o=arguments.length,s=new Array(o),u=0;u<o;u++)s[u]=arguments[u];return a=Et(this,r,[].concat(s)),a.handleSelectSlot=function(c){var d=a.props,v=d.range,h=d.onSelectSlot;h(v.slice(c.start,c.end+1),c)},a.handleShowMore=function(c,d){var v=a.props,h=v.range,m=v.onShowMore,g=a.slotMetrics(a.props),y=tc(a.containerRef.current,".rbc-row-bg")[0],w;y&&(w=y.children[c-1]);var k=g.getEventsForSlot(c);m(k,h[c-1],w,c,d)},a.getContainer=function(){var c=a.props.container;return c?c():a.containerRef.current},a.renderHeadingCell=function(c,d){var v=a.props,h=v.renderHeader,m=v.getNow,g=v.localizer;return h({date:c,key:"header_".concat(d),className:He("rbc-date-cell",g.isSameDate(c,m())&&"rbc-now")})},a.renderDummy=function(){var c=a.props,d=c.className,v=c.range,h=c.renderHeader,m=c.showAllEvents;return R.createElement("div",{className:d,ref:a.containerRef},R.createElement("div",{className:He("rbc-row-content",m&&"rbc-row-content-scrollable")},h&&R.createElement("div",{className:"rbc-row",ref:a.headingRowRef},v.map(a.renderHeadingCell)),R.createElement("div",{className:"rbc-row",ref:a.eventRowRef},R.createElement("div",{className:"rbc-row-segment"},R.createElement("div",{className:"rbc-event"},R.createElement("div",{className:"rbc-event-content"}," "))))))},a.containerRef=te.createRef(),a.headingRowRef=te.createRef(),a.eventRowRef=te.createRef(),a.slotMetrics=mS(),a}return Dt(r,t),at(r,[{key:"getRowLimit",value:function(){var o,s=Ca(this.eventRowRef.current),u=(o=this.headingRowRef)!==null&&o!==void 0&&o.current?Ca(this.headingRowRef.current):0,c=Ca(this.containerRef.current)-u;return Math.max(Math.floor(c/s),1)}},{key:"render",value:function(){var o=this.props,s=o.date,u=o.rtl,c=o.range,d=o.className,v=o.selected,h=o.selectable,m=o.renderForMeasure,g=o.accessors,y=o.getters,w=o.components,k=o.getNow,S=o.renderHeader,E=o.onSelect,x=o.localizer,_=o.onSelectStart,F=o.onSelectEnd,C=o.onDoubleClick,A=o.onKeyPress,B=o.resourceId,q=o.longPressThreshold,Q=o.isAllDay,I=o.resizable,V=o.showAllEvents;if(m)return this.renderDummy();var J=this.slotMetrics(this.props),le=J.levels,ae=J.extra,fe=V?V6:ar,me=w.weekWrapper,$={selected:v,accessors:g,getters:y,localizer:x,components:w,onSelect:E,onDoubleClick:C,onKeyPress:A,resourceId:B,slotMetrics:J,resizable:I};return R.createElement("div",{className:d,role:"rowgroup",ref:this.containerRef},R.createElement(Y6,{localizer:x,date:s,getNow:k,rtl:u,range:c,selectable:h,container:this.getContainer,getters:y,onSelectStart:_,onSelectEnd:F,onSelectSlot:this.handleSelectSlot,components:w,longPressThreshold:q,resourceId:B}),R.createElement("div",{className:He("rbc-row-content",V&&"rbc-row-content-scrollable"),role:"row"},S&&R.createElement("div",{className:"rbc-row ",ref:this.headingRowRef},c.map(this.renderHeadingCell)),R.createElement(fe,null,R.createElement(me,Object.assign({isAllDay:Q},$,{rtl:this.props.rtl}),le.map(function(ie,se){return R.createElement(fS,Object.assign({key:se,segments:ie},$))}),!!ae.length&&R.createElement(hS,Object.assign({segments:ae,onShowMore:this.handleShowMore},$))))))}}])}(R.Component);_l.defaultProps={minRows:0,maxRows:1/0};var vp=function(r){var a=r.label;return R.createElement("span",{role:"columnheader","aria-sort":"none"},a)},K6=function(r){var a=r.label,o=r.drilldownView,s=r.onDrillDown;return o?R.createElement("button",{type:"button",className:"rbc-button-link",onClick:s},a):R.createElement("span",null,a)},q6=["date","className"],Q6=function(r,a,o,s,u){return r.filter(function(c){return Tl(c,a,o,s,u)})},wc=function(t){function r(){var a;rt(this,r);for(var o=arguments.length,s=new Array(o),u=0;u<o;u++)s[u]=arguments[u];return a=Et(this,r,[].concat(s)),a.getContainer=function(){return a.containerRef.current},a.renderWeek=function(c,d){var v=a.props,h=v.events,m=v.components,g=v.selectable,y=v.getNow,w=v.selected,k=v.date,S=v.localizer,E=v.longPressThreshold,x=v.accessors,_=v.getters,F=v.showAllEvents,C=a.state,A=C.needLimitMeasure,B=C.rowLimit,q=Q6(_u(h),c[0],c[c.length-1],x,S),Q=W6(q,x,S);return R.createElement(_l,{key:d,ref:d===0?a.slotRowRef:void 0,container:a.getContainer,className:"rbc-month-row",getNow:y,date:k,range:c,events:Q,maxRows:F?1/0:B,selected:w,selectable:g,components:m,accessors:x,getters:_,localizer:S,renderHeader:a.readerDateHeading,renderForMeasure:A,onShowMore:a.handleShowMore,onSelect:a.handleSelectEvent,onDoubleClick:a.handleDoubleClickEvent,onKeyPress:a.handleKeyPressEvent,onSelectSlot:a.handleSelectSlot,longPressThreshold:E,rtl:a.props.rtl,resizable:a.props.resizable,showAllEvents:F})},a.readerDateHeading=function(c){var d=c.date,v=c.className,h=Fn(c,q6),m=a.props,g=m.date,y=m.getDrilldownView,w=m.localizer,k=w.neq(d,g,"month"),S=w.isSameDate(d,g),E=y(d),x=w.format(d,"dateFormat"),_=a.props.components.dateHeader||K6;return R.createElement("div",Object.assign({},h,{className:He(v,k&&"rbc-off-range",S&&"rbc-current"),role:"cell"}),R.createElement(_,{label:x,date:d,drilldownView:E,isOffRange:k,onDrillDown:function(C){return a.handleHeadingClick(d,E,C)}}))},a.handleSelectSlot=function(c,d){a._pendingSelection=a._pendingSelection.concat(c),clearTimeout(a._selectTimer),a._selectTimer=setTimeout(function(){return a.selectDates(d)})},a.handleHeadingClick=function(c,d,v){v.preventDefault(),a.clearSelection(),Ye(a.props.onDrillDown,[c,d])},a.handleSelectEvent=function(){a.clearSelection();for(var c=arguments.length,d=new Array(c),v=0;v<c;v++)d[v]=arguments[v];Ye(a.props.onSelectEvent,d)},a.handleDoubleClickEvent=function(){a.clearSelection();for(var c=arguments.length,d=new Array(c),v=0;v<c;v++)d[v]=arguments[v];Ye(a.props.onDoubleClickEvent,d)},a.handleKeyPressEvent=function(){a.clearSelection();for(var c=arguments.length,d=new Array(c),v=0;v<c;v++)d[v]=arguments[v];Ye(a.props.onKeyPressEvent,d)},a.handleShowMore=function(c,d,v,h,m){var g=a.props,y=g.popup,w=g.onDrillDown,k=g.onShowMore,S=g.getDrilldownView,E=g.doShowMoreDrillDown;if(a.clearSelection(),y){var x=Zu(v,a.containerRef.current);a.setState({overlay:{date:d,events:c,position:x,target:m}})}else E&&Ye(w,[d,S(d)||Ot.DAY]);Ye(k,[c,d,h])},a.overlayDisplay=function(){a.setState({overlay:null})},a.state={rowLimit:5,needLimitMeasure:!0,date:null},a.containerRef=te.createRef(),a.slotRowRef=te.createRef(),a._bgRows=[],a._pendingSelection=[],a}return Dt(r,t),at(r,[{key:"componentDidMount",value:function(){var o=this,s;this.state.needLimitMeasure&&this.measureRowLimit(this.props),window.addEventListener("resize",this._resizeListener=function(){s||Ul(function(){s=!1,o.setState({needLimitMeasure:!0})})},!1)}},{key:"componentDidUpdate",value:function(){this.state.needLimitMeasure&&this.measureRowLimit(this.props)}},{key:"componentWillUnmount",value:function(){window.removeEventListener("resize",this._resizeListener,!1)}},{key:"render",value:function(){var o=this.props,s=o.date,u=o.localizer,c=o.className,d=u.visibleDays(s,u),v=H_(d,7);return this._weekCount=v.length,R.createElement("div",{className:He("rbc-month-view",c),role:"table","aria-label":"Month View",ref:this.containerRef},R.createElement("div",{className:"rbc-row rbc-month-header",role:"row"},this.renderHeaders(v[0])),v.map(this.renderWeek),this.props.popup&&this.renderOverlay())}},{key:"renderHeaders",value:function(o){var s=this.props,u=s.localizer,c=s.components,d=o[0],v=o[o.length-1],h=c.header||vp;return u.range(d,v,"day").map(function(m,g){return R.createElement("div",{key:"header_"+g,className:"rbc-header"},R.createElement(h,{date:m,localizer:u,label:u.format(m,"weekdayFormat")}))})}},{key:"renderOverlay",value:function(){var o,s,u=this,c=(o=(s=this.state)===null||s===void 0?void 0:s.overlay)!==null&&o!==void 0?o:{},d=this.props,v=d.accessors,h=d.localizer,m=d.components,g=d.getters,y=d.selected,w=d.popupOffset,k=d.handleDragStart,S=function(){return u.setState({overlay:null})};return R.createElement(dp,{overlay:c,accessors:v,localizer:h,components:m,getters:g,selected:y,popupOffset:w,ref:this.containerRef,handleKeyPressEvent:this.handleKeyPressEvent,handleSelectEvent:this.handleSelectEvent,handleDoubleClickEvent:this.handleDoubleClickEvent,handleDragStart:k,show:!!c.position,overlayDisplay:this.overlayDisplay,onHide:S})}},{key:"measureRowLimit",value:function(){this.setState({needLimitMeasure:!1,rowLimit:this.slotRowRef.current.getRowLimit()})}},{key:"selectDates",value:function(o){var s=this._pendingSelection.slice();this._pendingSelection=[],s.sort(function(d,v){return+d-+v});var u=new Date(s[0]),c=new Date(s[s.length-1]);c.setDate(s[s.length-1].getDate()+1),Ye(this.props.onSelectSlot,{slots:s,start:u,end:c,action:o.action,bounds:o.bounds,box:o.box})}},{key:"clearSelection",value:function(){clearTimeout(this._selectTimer),this._pendingSelection=[]}}],[{key:"getDerivedStateFromProps",value:function(o,s){var u=o.date,c=o.localizer;return{date:u,needLimitMeasure:c.neq(u,s.date,"month")}}}])}(R.Component);wc.range=function(t,r){var a=r.localizer,o=a.firstVisibleDay(t,a),s=a.lastVisibleDay(t,a);return{start:o,end:s}};wc.navigate=function(t,r,a){var o=a.localizer;switch(r){case Lt.PREVIOUS:return o.add(t,-1,"month");case Lt.NEXT:return o.add(t,1,"month");default:return t}};wc.title=function(t,r){var a=r.localizer;return a.format(t,"monthHeaderFormat")};var Kb=function(r){var a=r.min,o=r.max,s=r.step,u=r.slots,c=r.localizer;return"".concat(+c.startOf(a,"minutes"))+"".concat(+c.startOf(o,"minutes"))+"".concat(s,"-").concat(u)};function pp(t){for(var r=t.min,a=t.max,o=t.step,s=t.timeslots,u=t.localizer,c=Kb({step:o,localizer:u}),d=1+u.getTotalMin(r,a),v=u.getMinutesFromMidnight(r),h=Math.ceil((d-1)/(o*s)),m=h*s,g=new Array(h),y=new Array(m),w=0;w<h;w++){g[w]=new Array(s);for(var k=0;k<s;k++){var S=w*s+k,E=S*o;y[S]=g[w][k]=u.getSlotDate(r,v,E)}}var x=y.length*o;y.push(u.getSlotDate(r,v,x));function _(F){var C=u.diff(r,F,"minutes")+u.getDstOffset(r,F);return Math.min(C,d)}return{groups:g,update:function(C){return Kb(C)!==c?pp(C):this},dateIsInGroup:function(C,A){var B=g[A+1];return u.inRange(C,g[A][0],B?B[0]:a,"minutes")},nextSlot:function(C){var A=y[Math.min(y.findIndex(function(B){return B===C||u.eq(B,C)})+1,y.length-1)];return u.eq(A,C)&&(A=u.add(C,o,"minutes")),A},closestSlotToPosition:function(C){var A=Math.min(y.length-1,Math.max(0,Math.floor(C*m)));return y[A]},closestSlotFromPoint:function(C,A){var B=Math.abs(A.top-A.bottom);return this.closestSlotToPosition((C.y-A.top)/B)},closestSlotFromDate:function(C){var A=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;if(u.lt(C,r,"minutes"))return y[0];if(u.gt(C,a,"minutes"))return y[y.length-1];var B=u.diff(r,C,"minutes");return y[(B-B%o)/o+A]},startsBeforeDay:function(C){return u.lt(C,r,"day")},startsAfterDay:function(C){return u.gt(C,a,"day")},startsBefore:function(C){return u.lt(u.merge(r,C),r,"minutes")},startsAfter:function(C){return u.gt(u.merge(a,C),a,"minutes")},getRange:function(C,A,B,q){B||(C=u.min(a,u.max(r,C))),q||(A=u.min(a,u.max(r,A)));var Q=_(C),I=_(A),V=I>o*m&&!u.eq(a,A)?(Q-o)/(o*m)*100:Q/(o*m)*100;return{top:V,height:I/(o*m)*100-V,start:_(C),startDate:C,end:_(A),endDate:A}},getCurrentTimePosition:function(C){var A=_(C),B=A/(o*m)*100;return B}}}var X6=function(){function t(r,a){var o=a.accessors,s=a.slotMetrics;rt(this,t);var u=s.getRange(o.start(r),o.end(r)),c=u.start,d=u.startDate,v=u.end,h=u.endDate,m=u.top,g=u.height;this.start=c,this.end=v,this.startMs=+d,this.endMs=+h,this.top=m,this.height=g,this.data=r}return at(t,[{key:"_width",get:function(){if(this.rows){var a=this.rows.reduce(function(s,u){return Math.max(s,u.leaves.length+1)},0)+1;return 100/a}if(this.leaves){var o=100-this.container._width;return o/(this.leaves.length+1)}return this.row._width}},{key:"width",get:function(){var a=this._width,o=Math.min(100,this._width*1.7);if(this.rows)return o;if(this.leaves)return this.leaves.length>0?o:a;var s=this.row.leaves,u=s.indexOf(this);return u===s.length-1?a:o}},{key:"xOffset",get:function(){if(this.rows)return 0;if(this.leaves)return this.container._width;var a=this.row,o=a.leaves,s=a.xOffset,u=a._width,c=o.indexOf(this)+1;return s+c*u}}])}();function G6(t,r,a){return Math.abs(r.start-t.start)<a||r.start>t.start&&r.start<t.end}function J6(t){for(var r=DL(t,["startMs",function(d){return-d.endMs}]),a=[];r.length>0;){var o=r.shift();a.push(o);for(var s=0;s<r.length;s++){var u=r[s];if(!(o.endMs>u.startMs)){if(s>0){var c=r.splice(s,1)[0];a.push(c)}break}}}return a}function gS(t){for(var r=t.events,a=t.minimumStartDifference,o=t.slotMetrics,s=t.accessors,u=r.map(function(m){return new X6(m,{slotMetrics:o,accessors:s})}),c=J6(u),d=[],v=function(){var g=c[h],y=d.find(function(S){return S.end>g.start||Math.abs(g.start-S.start)<a});if(!y)return g.rows=[],d.push(g),1;g.container=y;for(var w=null,k=y.rows.length-1;!w&&k>=0;k--)G6(y.rows[k],g,a)&&(w=y.rows[k]);w?(w.leaves.push(g),g.row=w):(g.leaves=[],y.rows.push(g))},h=0;h<c.length;h++)v();return c.map(function(m){return{event:m.data,style:{top:m.top,height:m.height,width:m.width,xOffset:Math.max(0,m.xOffset)}}})}function yS(t,r,a){for(var o=0;o<t.friends.length;++o)if(!(a.indexOf(t.friends[o])>-1)){r=r>t.friends[o].idx?r:t.friends[o].idx,a.push(t.friends[o]);var s=yS(t.friends[o],r,a);r=r>s?r:s}return r}function Z6(t){var r=t.events,a=t.minimumStartDifference,o=t.slotMetrics,s=t.accessors,u=gS({events:r,minimumStartDifference:a,slotMetrics:o,accessors:s});u.sort(function(me,$){return me=me.style,$=$.style,me.top!==$.top?me.top>$.top?1:-1:me.height!==$.height?me.top+me.height<$.top+$.height?1:-1:0});for(var c=0;c<u.length;++c)u[c].friends=[],delete u[c].style.left,delete u[c].style.left,delete u[c].idx,delete u[c].size;for(var d=0;d<u.length-1;++d)for(var v=u[d],h=v.style.top,m=v.style.top+v.style.height,g=d+1;g<u.length;++g){var y=u[g],w=y.style.top,k=y.style.top+y.style.height;(w>=h&&k<=m||k>h&&k<=m||w>=h&&w<m)&&(v.friends.push(y),y.friends.push(v))}for(var S=0;S<u.length;++S){for(var E=u[S],x=[],_=0;_<100;++_)x.push(1);for(var F=0;F<E.friends.length;++F)E.friends[F].idx!==void 0&&(x[E.friends[F].idx]=0);E.idx=x.indexOf(1)}for(var C=0;C<u.length;++C){var A=0;if(!u[C].size){var B=[],q=yS(u[C],0,B);A=100/(q+1),u[C].size=A;for(var Q=0;Q<B.length;++Q)B[Q].size=A}}for(var I=0;I<u.length;++I){var V=u[I];V.style.left=V.idx*V.size;for(var J=0,le=0;le<V.friends.length;++le){var ae=V.friends[le].idx;J=J>ae?J:ae}J<=V.idx&&(V.size=100-V.idx*V.size);var fe=V.idx===0?0:3;V.style.width="calc(".concat(V.size,"% - ").concat(fe,"px)"),V.style.height="calc(".concat(V.style.height,"% - 2px)"),V.style.xOffset="calc(".concat(V.style.left,"% + ").concat(fe,"px)")}return u}var qb={overlap:gS,"no-overlap":Z6};function eY(t){return!!(t&&t.constructor&&t.call&&t.apply)}function tY(t){t.events,t.minimumStartDifference,t.slotMetrics,t.accessors;var r=t.dayLayoutAlgorithm,a=r;return r in qb&&(a=qb[r]),eY(a)?a.apply(this,arguments):[]}var bS=function(t){function r(){return rt(this,r),Et(this,r,arguments)}return Dt(r,t),at(r,[{key:"render",value:function(){var o=this.props,s=o.renderSlot,u=o.resource,c=o.group,d=o.getters,v=o.components,h=v===void 0?{}:v,m=h.timeSlotWrapper,g=m===void 0?ar:m,y=d?d.slotGroupProp(c):{};return R.createElement("div",Object.assign({className:"rbc-timeslot-group"},y),c.map(function(w,k){var S=d?d.slotProp(w,u):{};return R.createElement(g,{key:k,value:w,resource:u},R.createElement("div",Object.assign({},S,{className:He("rbc-time-slot",S.className)}),s&&s(w,k)))}))}}])}(te.Component);function yu(t){return typeof t=="string"?t:t+"%"}function nY(t){var r=t.style,a=t.className,o=t.event,s=t.accessors,u=t.rtl,c=t.selected,d=t.label,v=t.continuesPrior,h=t.continuesAfter,m=t.getters,g=t.onClick,y=t.onDoubleClick,w=t.isBackgroundEvent,k=t.onKeyPress,S=t.components,E=S.event,x=S.eventWrapper,_=s.title(o),F=s.tooltip(o),C=s.end(o),A=s.start(o),B=m.eventProp(o,A,C,c),q=[R.createElement("div",{key:"1",className:"rbc-event-label"},d),R.createElement("div",{key:"2",className:"rbc-event-content"},E?R.createElement(E,{event:o,title:_}):_)],Q=r.height,I=r.top,V=r.width,J=r.xOffset,le=Fe(Fe({},B.style),{},_a({top:yu(I),height:yu(Q),width:yu(V)},u?"right":"left",yu(J)));return R.createElement(x,Object.assign({type:"time"},t),R.createElement("div",{role:"button",tabIndex:0,onClick:g,onDoubleClick:y,style:le,onKeyDown:k,title:F?(typeof d=="string"?d+": ":"")+F:void 0,className:He(w?"rbc-background-event":"rbc-event",a,B.className,{"rbc-selected":c,"rbc-event-continues-earlier":v,"rbc-event-continues-later":h})},q))}var rY=function(r){var a=r.children,o=r.className,s=r.style,u=r.innerRef;return R.createElement("div",{className:o,style:s,ref:u},a)},aY=R.forwardRef(function(t,r){return R.createElement(rY,Object.assign({},t,{innerRef:r}))}),oY=["dayProp"],iY=["eventContainerWrapper"],wS=function(t){function r(){var a;rt(this,r);for(var o=arguments.length,s=new Array(o),u=0;u<o;u++)s[u]=arguments[u];return a=Et(this,r,[].concat(s)),a.state={selecting:!1,timeIndicatorPosition:null},a.intervalTriggered=!1,a.renderEvents=function(c){var d=c.events,v=c.isBackgroundEvent,h=a.props,m=h.rtl,g=h.selected,y=h.accessors,w=h.localizer,k=h.getters,S=h.components,E=h.step,x=h.timeslots,_=h.dayLayoutAlgorithm,F=h.resizable,C=a,A=C.slotMetrics,B=w.messages,q=tY({events:d,accessors:y,slotMetrics:A,minimumStartDifference:Math.ceil(E*x/2),dayLayoutAlgorithm:_});return q.map(function(Q,I){var V,J=Q.event,le=Q.style,ae=y.end(J),fe=y.start(J),me=(V=y.eventId(J))!==null&&V!==void 0?V:"evt_"+I,$="eventTimeRangeFormat",ie,se=A.startsBeforeDay(fe),ge=A.startsAfterDay(ae);se?$="eventTimeRangeEndFormat":ge&&($="eventTimeRangeStartFormat"),se&&ge?ie=B.allDay:ie=w.format({start:fe,end:ae},$);var O=se||A.startsBefore(fe),P=ge||A.startsAfter(ae);return R.createElement(nY,{style:le,event:J,label:ie,key:me,getters:k,rtl:m,components:S,continuesPrior:O,continuesAfter:P,accessors:y,resource:a.props.resource,selected:bc(J,g),onClick:function(W){return a._select(Fe(Fe(Fe({},J),a.props.resource&&{sourceResource:a.props.resource}),v&&{isBackgroundEvent:!0}),W)},onDoubleClick:function(W){return a._doubleClick(J,W)},isBackgroundEvent:v,onKeyPress:function(W){return a._keyPress(J,W)},resizable:F})})},a._selectable=function(){var c=a.containerRef.current,d=a.props,v=d.longPressThreshold,h=d.localizer,m=a._selector=new cS(function(){return c},{longPressThreshold:v}),g=function(S){var E=a.props.onSelecting,x=a.state||{},_=y(S),F=_.startDate,C=_.endDate;E&&(h.eq(x.startDate,F,"minutes")&&h.eq(x.endDate,C,"minutes")||E({start:F,end:C,resourceId:a.props.resource})===!1)||(a.state.start!==_.start||a.state.end!==_.end||a.state.selecting!==_.selecting)&&a.setState(_)},y=function(S){var E=a.slotMetrics.closestSlotFromPoint(S,za(c));a.state.selecting||(a._initialSlot=E);var x=a._initialSlot;h.lte(x,E)?E=a.slotMetrics.nextSlot(E):h.gt(x,E)&&(x=a.slotMetrics.nextSlot(x));var _=a.slotMetrics.getRange(h.min(x,E),h.max(x,E));return Fe(Fe({},_),{},{selecting:!0,top:"".concat(_.top,"%"),height:"".concat(_.height,"%")})},w=function(S,E){if(!Bu(a.containerRef.current,S)){var x=y(S),_=x.startDate,F=x.endDate;a._selectSlot({startDate:_,endDate:F,action:E,box:S})}a.setState({selecting:!1})};m.on("selecting",g),m.on("selectStart",g),m.on("beforeSelect",function(k){if(a.props.selectable==="ignoreEvents")return!Bu(a.containerRef.current,k)}),m.on("click",function(k){return w(k,"click")}),m.on("doubleClick",function(k){return w(k,"doubleClick")}),m.on("select",function(k){a.state.selecting&&(a._selectSlot(Fe(Fe({},a.state),{},{action:"select",bounds:k})),a.setState({selecting:!1}))}),m.on("reset",function(){a.state.selecting&&a.setState({selecting:!1})})},a._teardownSelectable=function(){a._selector&&(a._selector.teardown(),a._selector=null)},a._selectSlot=function(c){for(var d=c.startDate,v=c.endDate,h=c.action,m=c.bounds,g=c.box,y=d,w=[];a.props.localizer.lte(y,v);)w.push(y),y=new Date(+y+a.props.step*60*1e3);Ye(a.props.onSelectSlot,{slots:w,start:d,end:v,resourceId:a.props.resource,action:h,bounds:m,box:g})},a._select=function(){for(var c=arguments.length,d=new Array(c),v=0;v<c;v++)d[v]=arguments[v];Ye(a.props.onSelectEvent,d)},a._doubleClick=function(){for(var c=arguments.length,d=new Array(c),v=0;v<c;v++)d[v]=arguments[v];Ye(a.props.onDoubleClickEvent,d)},a._keyPress=function(){for(var c=arguments.length,d=new Array(c),v=0;v<c;v++)d[v]=arguments[v];Ye(a.props.onKeyPressEvent,d)},a.slotMetrics=pp(a.props),a.containerRef=te.createRef(),a}return Dt(r,t),at(r,[{key:"componentDidMount",value:function(){this.props.selectable&&this._selectable(),this.props.isNow&&this.setTimeIndicatorPositionUpdateInterval()}},{key:"componentWillUnmount",value:function(){this._teardownSelectable(),this.clearTimeIndicatorInterval()}},{key:"componentDidUpdate",value:function(o,s){this.props.selectable&&!o.selectable&&this._selectable(),!this.props.selectable&&o.selectable&&this._teardownSelectable();var u=this.props,c=u.getNow,d=u.isNow,v=u.localizer,h=u.date,m=u.min,g=u.max,y=v.neq(o.getNow(),c(),"minutes");if(o.isNow!==d||y){if(this.clearTimeIndicatorInterval(),d){var w=!y&&v.eq(o.date,h,"minutes")&&s.timeIndicatorPosition===this.state.timeIndicatorPosition;this.setTimeIndicatorPositionUpdateInterval(w)}}else d&&(v.neq(o.min,m,"minutes")||v.neq(o.max,g,"minutes"))&&this.positionTimeIndicator()}},{key:"setTimeIndicatorPositionUpdateInterval",value:function(){var o=this,s=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;!this.intervalTriggered&&!s&&this.positionTimeIndicator(),this._timeIndicatorTimeout=window.setTimeout(function(){o.intervalTriggered=!0,o.positionTimeIndicator(),o.setTimeIndicatorPositionUpdateInterval()},6e4)}},{key:"clearTimeIndicatorInterval",value:function(){this.intervalTriggered=!1,window.clearTimeout(this._timeIndicatorTimeout)}},{key:"positionTimeIndicator",value:function(){var o=this.props,s=o.min,u=o.max,c=o.getNow,d=c();if(d>=s&&d<=u){var v=this.slotMetrics.getCurrentTimePosition(d);this.intervalTriggered=!0,this.setState({timeIndicatorPosition:v})}else this.clearTimeIndicatorInterval()}},{key:"render",value:function(){var o=this.props,s=o.date,u=o.max,c=o.rtl,d=o.isNow,v=o.resource,h=o.accessors,m=o.localizer,g=o.getters,y=g.dayProp,w=Fn(g,oY),k=o.components,S=k.eventContainerWrapper,E=Fn(k,iY);this.slotMetrics=this.slotMetrics.update(this.props);var x=this.slotMetrics,_=this.state,F=_.selecting,C=_.top,A=_.height,B=_.startDate,q=_.endDate,Q={start:B,end:q},I=y(u,v),V=I.className,J=I.style,le=E.dayColumnWrapper||aY;return R.createElement(le,{ref:this.containerRef,date:s,style:J,className:He(V,"rbc-day-slot","rbc-time-column",d&&"rbc-now",d&&"rbc-today",F&&"rbc-slot-selecting"),slotMetrics:x,resource:v},x.groups.map(function(ae,fe){return R.createElement(bS,{key:fe,group:ae,resource:v,getters:w,components:E})}),R.createElement(S,{localizer:m,resource:v,accessors:h,getters:w,components:E,slotMetrics:x},R.createElement("div",{className:He("rbc-events-container",c&&"rtl")},this.renderEvents({events:this.props.backgroundEvents,isBackgroundEvent:!0}),this.renderEvents({events:this.props.events}))),F&&R.createElement("div",{className:"rbc-slot-selection",style:{top:C,height:A}},R.createElement("span",null,m.format(Q,"selectRangeFormat"))),d&&this.intervalTriggered&&R.createElement("div",{className:"rbc-current-time-indicator",style:{top:"".concat(this.state.timeIndicatorPosition,"%")}}))}}])}(R.Component);wS.defaultProps={dragThroughEvents:!0,timeslots:2};var SS=function(r){var a=r.label;return R.createElement(R.Fragment,null,a)},lY=function(t){function r(){var a;rt(this,r);for(var o=arguments.length,s=new Array(o),u=0;u<o;u++)s[u]=arguments[u];return a=Et(this,r,[].concat(s)),a.handleHeaderClick=function(c,d,v){v.preventDefault(),Ye(a.props.onDrillDown,[c,d])},a.renderRow=function(c){var d=a.props,v=d.events,h=d.rtl,m=d.selectable,g=d.getNow,y=d.range,w=d.getters,k=d.localizer,S=d.accessors,E=d.components,x=d.resizable,_=S.resourceId(c),F=c?v.filter(function(C){return S.resource(C)===_}):v;return R.createElement(_l,{isAllDay:!0,rtl:h,getNow:g,minRows:2,maxRows:a.props.allDayMaxRows+1,range:y,events:F,resourceId:_,className:"rbc-allday-cell",selectable:m,selected:a.props.selected,components:E,accessors:S,getters:w,localizer:k,onSelect:a.props.onSelectEvent,onShowMore:a.props.onShowMore,onDoubleClick:a.props.onDoubleClickEvent,onKeyPress:a.props.onKeyPressEvent,onSelectSlot:a.props.onSelectSlot,longPressThreshold:a.props.longPressThreshold,resizable:x})},a}return Dt(r,t),at(r,[{key:"renderHeaderCells",value:function(o){var s=this,u=this.props,c=u.localizer,d=u.getDrilldownView,v=u.getNow,h=u.getters.dayProp,m=u.components.header,g=m===void 0?vp:m,y=v();return o.map(function(w,k){var S=d(w),E=c.format(w,"dayFormat"),x=h(w),_=x.className,F=x.style,C=R.createElement(g,{date:w,label:E,localizer:c});return R.createElement("div",{key:k,style:F,className:He("rbc-header",_,c.isSameDate(w,y)&&"rbc-today")},S?R.createElement("button",{type:"button",className:"rbc-button-link",onClick:function(B){return s.handleHeaderClick(w,S,B)}},C):R.createElement("span",null,C))})}},{key:"render",value:function(){var o=this,s=this.props,u=s.width,c=s.rtl,d=s.resources,v=s.range,h=s.events,m=s.getNow,g=s.accessors,y=s.selectable,w=s.components,k=s.getters,S=s.scrollRef,E=s.localizer,x=s.isOverflowing,_=s.components,F=_.timeGutterHeader,C=_.resourceHeader,A=C===void 0?SS:C,B=s.resizable,q={};x&&(q[c?"marginLeft":"marginRight"]="".concat(ql()-1,"px"));var Q=d.groupEvents(h);return R.createElement("div",{style:q,ref:S,className:He("rbc-time-header",x&&"rbc-overflowing")},R.createElement("div",{className:"rbc-label rbc-time-header-gutter",style:{width:u,minWidth:u,maxWidth:u}},F&&R.createElement(F,null)),d.map(function(I,V){var J=on(I,2),le=J[0],ae=J[1];return R.createElement("div",{className:"rbc-time-header-content",key:le||V},ae&&R.createElement("div",{className:"rbc-row rbc-row-resource",key:"resource_".concat(V)},R.createElement("div",{className:"rbc-header"},R.createElement(A,{index:V,label:g.resourceTitle(ae),resource:ae}))),R.createElement("div",{className:"rbc-row rbc-time-header-cell".concat(v.length<=1?" rbc-time-header-cell-single-day":"")},o.renderHeaderCells(v)),R.createElement(_l,{isAllDay:!0,rtl:c,getNow:m,minRows:2,maxRows:o.props.allDayMaxRows+1,range:v,events:Q.get(le)||[],resourceId:ae&&le,className:"rbc-allday-cell",selectable:y,selected:o.props.selected,components:w,accessors:g,getters:k,localizer:E,onSelect:o.props.onSelectEvent,onShowMore:o.props.onShowMore,onDoubleClick:o.props.onDoubleClickEvent,onKeyDown:o.props.onKeyPressEvent,onSelectSlot:o.props.onSelectSlot,longPressThreshold:o.props.longPressThreshold,resizable:B}))}))}}])}(R.Component),sY=function(t){function r(){var a;rt(this,r);for(var o=arguments.length,s=new Array(o),u=0;u<o;u++)s[u]=arguments[u];return a=Et(this,r,[].concat(s)),a.handleHeaderClick=function(c,d,v){v.preventDefault(),Ye(a.props.onDrillDown,[c,d])},a}return Dt(r,t),at(r,[{key:"renderHeaderCells",value:function(o){var s=this,u=this.props,c=u.localizer,d=u.getDrilldownView,v=u.getNow,h=u.getters.dayProp,m=u.components,g=m.header,y=g===void 0?vp:g,w=m.resourceHeader,k=w===void 0?SS:w,S=u.resources,E=u.accessors,x=u.events,_=u.rtl,F=u.selectable,C=u.components,A=u.getters,B=u.resizable,q=v(),Q=S.groupEvents(x);return o.map(function(I,V){var J=d(I),le=c.format(I,"dayFormat"),ae=h(I),fe=ae.className,me=ae.style,$=R.createElement(y,{date:I,label:le,localizer:c});return R.createElement("div",{key:V,className:"rbc-time-header-content rbc-resource-grouping"},R.createElement("div",{className:"rbc-row rbc-time-header-cell".concat(o.length<=1?" rbc-time-header-cell-single-day":"")},R.createElement("div",{style:me,className:He("rbc-header",fe,c.isSameDate(I,q)&&"rbc-today")},J?R.createElement("button",{type:"button",className:"rbc-button-link",onClick:function(se){return s.handleHeaderClick(I,J,se)}},$):R.createElement("span",null,$))),R.createElement("div",{className:"rbc-row"},S.map(function(ie,se){var ge=on(ie,2),O=ge[0],P=ge[1];return R.createElement("div",{key:"resource_".concat(O,"_").concat(se),className:He("rbc-header",fe,c.isSameDate(I,q)&&"rbc-today")},R.createElement(k,{index:se,label:E.resourceTitle(P),resource:P}))})),R.createElement("div",{className:"rbc-row rbc-m-b-negative-3 rbc-h-full"},S.map(function(ie,se){var ge=on(ie,2),O=ge[0],P=ge[1],H=(Q.get(O)||[]).filter(function(W){return c.isSameDate(W.start,I)||c.isSameDate(W.end,I)});return R.createElement(_l,{key:"resource_".concat(O,"_").concat(se),isAllDay:!0,rtl:_,getNow:v,minRows:2,maxRows:s.props.allDayMaxRows+1,range:[I],events:H,resourceId:P&&O,className:"rbc-allday-cell",selectable:F,selected:s.props.selected,components:C,accessors:E,getters:A,localizer:c,onSelect:s.props.onSelectEvent,onShowMore:s.props.onShowMore,onDoubleClick:s.props.onDoubleClickEvent,onKeyDown:s.props.onKeyPressEvent,onSelectSlot:s.props.onSelectSlot,longPressThreshold:s.props.longPressThreshold,resizable:B})})))})}},{key:"render",value:function(){var o=this.props,s=o.width,u=o.rtl,c=o.range,d=o.scrollRef,v=o.isOverflowing,h=o.components.timeGutterHeader,m={};return v&&(m[u?"marginLeft":"marginRight"]="".concat(ql()-1,"px")),R.createElement("div",{style:m,ref:d,className:He("rbc-time-header",v&&"rbc-overflowing")},R.createElement("div",{className:"rbc-label rbc-time-header-gutter",style:{width:s,minWidth:s,maxWidth:s}},h&&R.createElement(h,null)),this.renderHeaderCells(c))}}])}(R.Component);function uY(t){var r=t.min,a=t.max,o=t.localizer;return o.getTimezoneOffset(r)!==o.getTimezoneOffset(a)?{start:o.add(r,-1,"day"),end:o.add(a,-1,"day")}:{start:r,end:a}}var cY=function(r){var a=r.min,o=r.max,s=r.timeslots,u=r.step,c=r.localizer,d=r.getNow,v=r.resource,h=r.components,m=r.getters,g=r.gutterRef,y=h.timeGutterWrapper,w=te.useMemo(function(){return uY({min:a,max:o,localizer:c})},[a==null?void 0:a.toISOString(),o==null?void 0:o.toISOString(),c]),k=w.start,S=w.end,E=te.useState(pp({min:k,max:S,timeslots:s,step:u,localizer:c})),x=on(E,2),_=x[0],F=x[1];te.useEffect(function(){_&&F(_.update({min:k,max:S,timeslots:s,step:u,localizer:c}))},[k==null?void 0:k.toISOString(),S==null?void 0:S.toISOString(),s,u]);var C=te.useCallback(function(A,B){if(B)return null;var q=_.dateIsInGroup(d(),B);return R.createElement("span",{className:He("rbc-label",q&&"rbc-now")},c.format(A,"timeGutterFormat"))},[_,c,d]);return R.createElement(y,{slotMetrics:_},R.createElement("div",{className:"rbc-time-gutter rbc-time-column",ref:g},_.groups.map(function(A,B){return R.createElement(bS,{key:B,group:A,resource:v,components:h,renderSlot:C,getters:m})})))},fY=R.forwardRef(function(t,r){return R.createElement(cY,Object.assign({gutterRef:r},t))}),Gd={};function dY(t,r){return{map:function(o){return t?t.map(function(s,u){return o([r.resourceId(s),s],u)}):[o([Gd,null],0)]},groupEvents:function(o){var s=new Map;return t?(o.forEach(function(u){var c=r.resource(u)||Gd;if(Array.isArray(c))c.forEach(function(v){var h=s.get(v)||[];h.push(u),s.set(v,h)});else{var d=s.get(c)||[];d.push(u),s.set(c,d)}}),s):(s.set(Gd,o),s)}}}var Xo=function(t){function r(a){var o;return rt(this,r),o=Et(this,r,[a]),o.handleScroll=function(s){o.scrollRef.current&&(o.scrollRef.current.scrollLeft=s.target.scrollLeft)},o.handleResize=function(){kl(o.rafHandle),o.rafHandle=Ul(o.checkOverflow)},o.handleKeyPressEvent=function(){o.clearSelection();for(var s=arguments.length,u=new Array(s),c=0;c<s;c++)u[c]=arguments[c];Ye(o.props.onKeyPressEvent,u)},o.handleSelectEvent=function(){o.clearSelection();for(var s=arguments.length,u=new Array(s),c=0;c<s;c++)u[c]=arguments[c];Ye(o.props.onSelectEvent,u)},o.handleDoubleClickEvent=function(){o.clearSelection();for(var s=arguments.length,u=new Array(s),c=0;c<s;c++)u[c]=arguments[c];Ye(o.props.onDoubleClickEvent,u)},o.handleShowMore=function(s,u,c,d,v){var h=o.props,m=h.popup,g=h.onDrillDown,y=h.onShowMore,w=h.getDrilldownView,k=h.doShowMoreDrillDown;if(o.clearSelection(),m){var S=Zu(c,o.containerRef.current);o.setState({overlay:{date:u,events:s,position:Fe(Fe({},S),{},{width:"200px"}),target:v}})}else k&&Ye(g,[u,w(u)||Ot.DAY]);Ye(y,[s,u,d])},o.handleSelectAllDaySlot=function(s,u){var c=o.props.onSelectSlot,d=new Date(s[0]),v=new Date(s[s.length-1]);v.setDate(s[s.length-1].getDate()+1),Ye(c,{slots:s,start:d,end:v,action:u.action,resourceId:u.resourceId})},o.overlayDisplay=function(){o.setState({overlay:null})},o.checkOverflow=function(){if(!o._updatingOverflow){var s=o.contentRef.current;if(s!=null&&s.scrollHeight){var u=s.scrollHeight>s.clientHeight;o.state.isOverflowing!==u&&(o._updatingOverflow=!0,o.setState({isOverflowing:u},function(){o._updatingOverflow=!1}))}}},o.memoizedResources=Y0(function(s,u){return dY(s,u)}),o.state={gutterWidth:void 0,isOverflowing:null},o.scrollRef=R.createRef(),o.contentRef=R.createRef(),o.containerRef=R.createRef(),o._scrollRatio=null,o.gutterRef=te.createRef(),o}return Dt(r,t),at(r,[{key:"getSnapshotBeforeUpdate",value:function(){return this.checkOverflow(),null}},{key:"componentDidMount",value:function(){this.props.width==null&&this.measureGutter(),this.calculateScroll(),this.applyScroll(),window.addEventListener("resize",this.handleResize)}},{key:"componentWillUnmount",value:function(){window.removeEventListener("resize",this.handleResize),kl(this.rafHandle),this.measureGutterAnimationFrameRequest&&window.cancelAnimationFrame(this.measureGutterAnimationFrameRequest)}},{key:"componentDidUpdate",value:function(){this.applyScroll()}},{key:"renderDayColumn",value:function(o,s,u,c,d,v,h,m,g,y){var w=this.props,k=w.min,S=w.max,E=(c.get(s)||[]).filter(function(_){return v.inRange(o,h.start(_),h.end(_),"day")}),x=(d.get(s)||[]).filter(function(_){return v.inRange(o,h.start(_),h.end(_),"day")});return R.createElement(wS,Object.assign({},this.props,{localizer:v,min:v.merge(o,k),max:v.merge(o,S),resource:u&&s,components:m,isNow:v.isSameDate(o,y),key:"".concat(s,"-").concat(o),date:o,events:E,backgroundEvents:x,dayLayoutAlgorithm:g}))}},{key:"renderResourcesFirst",value:function(o,s,u,c,d,v,h,m,g){var y=this;return s.map(function(w){var k=on(w,2),S=k[0],E=k[1];return o.map(function(x){return y.renderDayColumn(x,S,E,u,c,d,v,m,g,h)})})}},{key:"renderRangeFirst",value:function(o,s,u,c,d,v,h,m,g){var y=this;return o.map(function(w){return R.createElement("div",{style:{display:"flex",minHeight:"100%",flex:1},key:w},s.map(function(k){var S=on(k,2),E=S[0],x=S[1];return R.createElement("div",{style:{flex:1},key:v.resourceId(x)},y.renderDayColumn(w,E,x,u,c,d,v,m,g,h))}))})}},{key:"renderEvents",value:function(o,s,u,c){var d=this.props,v=d.accessors,h=d.localizer,m=d.resourceGroupingLayout,g=d.components,y=d.dayLayoutAlgorithm,w=this.memoizedResources(this.props.resources,v),k=w.groupEvents(s),S=w.groupEvents(u);return m?this.renderRangeFirst(o,w,k,S,h,v,c,g,y):this.renderResourcesFirst(o,w,k,S,h,v,c,g,y)}},{key:"render",value:function(){var o,s=this.props,u=s.events,c=s.backgroundEvents,d=s.range,v=s.width,h=s.rtl,m=s.selected,g=s.getNow,y=s.resources,w=s.components,k=s.accessors,S=s.getters,E=s.localizer,x=s.min,_=s.max,F=s.showMultiDayTimes,C=s.longPressThreshold,A=s.resizable,B=s.resourceGroupingLayout;v=v||this.state.gutterWidth;var q=d[0],Q=d[d.length-1];this.slots=d.length;var I=[],V=[],J=[];u.forEach(function(ae){if(Tl(ae,q,Q,k,E)){var fe=k.start(ae),me=k.end(ae);k.allDay(ae)||E.startAndEndAreDateOnly(fe,me)||!F&&!E.isSameDate(fe,me)?I.push(ae):V.push(ae)}}),c.forEach(function(ae){Tl(ae,q,Q,k,E)&&J.push(ae)}),I.sort(function(ae,fe){return bv(ae,fe,k,E)});var le={range:d,events:I,width:v,rtl:h,getNow:g,localizer:E,selected:m,allDayMaxRows:this.props.showAllEvents?1/0:(o=this.props.allDayMaxRows)!==null&&o!==void 0?o:1/0,resources:this.memoizedResources(y,k),selectable:this.props.selectable,accessors:k,getters:S,components:w,scrollRef:this.scrollRef,isOverflowing:this.state.isOverflowing,longPressThreshold:C,onSelectSlot:this.handleSelectAllDaySlot,onSelectEvent:this.handleSelectEvent,onShowMore:this.handleShowMore,onDoubleClickEvent:this.props.onDoubleClickEvent,onKeyPressEvent:this.props.onKeyPressEvent,onDrillDown:this.props.onDrillDown,getDrilldownView:this.props.getDrilldownView,resizable:A};return R.createElement("div",{className:He("rbc-time-view",y&&"rbc-time-view-resources"),ref:this.containerRef},y&&y.length>1&&B?R.createElement(sY,le):R.createElement(lY,le),this.props.popup&&this.renderOverlay(),R.createElement("div",{ref:this.contentRef,className:"rbc-time-content",onScroll:this.handleScroll},R.createElement(fY,{date:q,ref:this.gutterRef,localizer:E,min:E.merge(q,x),max:E.merge(q,_),step:this.props.step,getNow:this.props.getNow,timeslots:this.props.timeslots,components:w,className:"rbc-time-gutter",getters:S}),this.renderEvents(d,V,J,g())))}},{key:"renderOverlay",value:function(){var o,s,u=this,c=(o=(s=this.state)===null||s===void 0?void 0:s.overlay)!==null&&o!==void 0?o:{},d=this.props,v=d.accessors,h=d.localizer,m=d.components,g=d.getters,y=d.selected,w=d.popupOffset,k=d.handleDragStart,S=function(){return u.setState({overlay:null})};return R.createElement(dp,{overlay:c,accessors:v,localizer:h,components:m,getters:g,selected:y,popupOffset:w,ref:this.containerRef,handleKeyPressEvent:this.handleKeyPressEvent,handleSelectEvent:this.handleSelectEvent,handleDoubleClickEvent:this.handleDoubleClickEvent,handleDragStart:k,show:!!c.position,overlayDisplay:this.overlayDisplay,onHide:S})}},{key:"clearSelection",value:function(){clearTimeout(this._selectTimer),this._pendingSelection=[]}},{key:"measureGutter",value:function(){var o=this;this.measureGutterAnimationFrameRequest&&window.cancelAnimationFrame(this.measureGutterAnimationFrameRequest),this.measureGutterAnimationFrameRequest=window.requestAnimationFrame(function(){var s,u=(s=o.gutterRef)!==null&&s!==void 0&&s.current?xl(o.gutterRef.current):void 0;u&&o.state.gutterWidth!==u&&o.setState({gutterWidth:u})})}},{key:"applyScroll",value:function(){if(this._scrollRatio!=null&&this.props.enableAutoScroll===!0){var o=this.contentRef.current;o.scrollTop=o.scrollHeight*this._scrollRatio,this._scrollRatio=null}}},{key:"calculateScroll",value:function(){var o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.props,s=o.min,u=o.max,c=o.scrollToTime,d=o.localizer,v=d.diff(d.merge(c,s),c,"milliseconds"),h=d.diff(s,u,"milliseconds");this._scrollRatio=v/h}}])}(te.Component);Xo.defaultProps={step:30,timeslots:2,resourceGroupingLayout:!1};var vY=["date","localizer","min","max","scrollToTime","enableAutoScroll"],Sc=function(t){function r(){return rt(this,r),Et(this,r,arguments)}return Dt(r,t),at(r,[{key:"render",value:function(){var o=this.props,s=o.date,u=o.localizer,c=o.min,d=c===void 0?u.startOf(new Date,"day"):c,v=o.max,h=v===void 0?u.endOf(new Date,"day"):v,m=o.scrollToTime,g=m===void 0?u.startOf(new Date,"day"):m,y=o.enableAutoScroll,w=y===void 0?!0:y,k=Fn(o,vY),S=r.range(s,{localizer:u});return R.createElement(Xo,Object.assign({},k,{range:S,eventOffset:10,localizer:u,min:d,max:h,scrollToTime:g,enableAutoScroll:w}))}}])}(R.Component);Sc.range=function(t,r){var a=r.localizer;return[a.startOf(t,"day")]};Sc.navigate=function(t,r,a){var o=a.localizer;switch(r){case Lt.PREVIOUS:return o.add(t,-1,"day");case Lt.NEXT:return o.add(t,1,"day");default:return t}};Sc.title=function(t,r){var a=r.localizer;return a.format(t,"dayHeaderFormat")};var pY=["date","localizer","min","max","scrollToTime","enableAutoScroll"],Wr=function(t){function r(){return rt(this,r),Et(this,r,arguments)}return Dt(r,t),at(r,[{key:"render",value:function(){var o=this.props,s=o.date,u=o.localizer,c=o.min,d=c===void 0?u.startOf(new Date,"day"):c,v=o.max,h=v===void 0?u.endOf(new Date,"day"):v,m=o.scrollToTime,g=m===void 0?u.startOf(new Date,"day"):m,y=o.enableAutoScroll,w=y===void 0?!0:y,k=Fn(o,pY),S=r.range(s,this.props);return R.createElement(Xo,Object.assign({},k,{range:S,eventOffset:15,localizer:u,min:d,max:h,scrollToTime:g,enableAutoScroll:w}))}}])}(R.Component);Wr.defaultProps=Xo.defaultProps;Wr.navigate=function(t,r,a){var o=a.localizer;switch(r){case Lt.PREVIOUS:return o.add(t,-1,"week");case Lt.NEXT:return o.add(t,1,"week");default:return t}};Wr.range=function(t,r){var a=r.localizer,o=a.startOfWeek(),s=a.startOf(t,"week",o),u=a.endOf(t,"week",o);return a.range(s,u)};Wr.title=function(t,r){var a=r.localizer,o=Wr.range(t,{localizer:a}),s=K0(o),u=s[0],c=s.slice(1);return a.format({start:u,end:c.pop()},"dayRangeHeaderFormat")};var hY=["date","localizer","min","max","scrollToTime","enableAutoScroll"];function hp(t,r){return Wr.range(t,r).filter(function(a){return[6,0].indexOf(a.getDay())===-1})}var Zl=function(t){function r(){return rt(this,r),Et(this,r,arguments)}return Dt(r,t),at(r,[{key:"render",value:function(){var o=this.props,s=o.date,u=o.localizer,c=o.min,d=c===void 0?u.startOf(new Date,"day"):c,v=o.max,h=v===void 0?u.endOf(new Date,"day"):v,m=o.scrollToTime,g=m===void 0?u.startOf(new Date,"day"):m,y=o.enableAutoScroll,w=y===void 0?!0:y,k=Fn(o,hY),S=hp(s,this.props);return R.createElement(Xo,Object.assign({},k,{range:S,eventOffset:15,localizer:u,min:d,max:h,scrollToTime:g,enableAutoScroll:w}))}}])}(R.Component);Zl.defaultProps=Xo.defaultProps;Zl.range=hp;Zl.navigate=Wr.navigate;Zl.title=function(t,r){var a=r.localizer,o=hp(t,{localizer:a}),s=K0(o),u=s[0],c=s.slice(1);return a.format({start:u,end:c.pop()},"dayRangeHeaderFormat")};var kc=30;function Ec(t){var r=t.accessors,a=t.components,o=t.date,s=t.events,u=t.getters,c=t.length,d=c===void 0?kc:c,v=t.localizer,h=t.onDoubleClickEvent,m=t.onSelectEvent,g=t.selected,y=te.useRef(null),w=te.useRef(null),k=te.useRef(null),S=te.useRef(null),E=te.useRef(null);te.useEffect(function(){F()});var x=function(Q,I,V){var J=a.event,le=a.date;return I=I.filter(function(ae){return Tl(ae,v.startOf(Q,"day"),v.endOf(Q,"day"),r,v)}),I.map(function(ae,fe){var me=r.title(ae),$=r.end(ae),ie=r.start(ae),se=u.eventProp(ae,ie,$,bc(ae,g)),ge=fe===0&&v.format(Q,"agendaDateFormat"),O=fe===0?R.createElement("td",{rowSpan:I.length,className:"rbc-agenda-date-cell"},le?R.createElement(le,{day:Q,label:ge}):ge):!1;return R.createElement("tr",{key:V+"_"+fe,className:se.className,style:se.style},O,R.createElement("td",{className:"rbc-agenda-time-cell"},_(Q,ae)),R.createElement("td",{className:"rbc-agenda-event-cell",onClick:function(H){return m&&m(ae,H)},onDoubleClick:function(H){return h&&h(ae,H)}},J?R.createElement(J,{event:ae,title:me}):me))},[])},_=function(Q,I){var V="",J=a.time,le=v.messages.allDay,ae=r.end(I),fe=r.start(I);return r.allDay(I)||(v.eq(fe,ae)?le=v.format(fe,"agendaTimeFormat"):v.isSameDate(fe,ae)?le=v.format({start:fe,end:ae},"agendaTimeRangeFormat"):v.isSameDate(Q,fe)?le=v.format(fe,"agendaTimeFormat"):v.isSameDate(Q,ae)&&(le=v.format(ae,"agendaTimeFormat"))),v.gt(Q,fe,"day")&&(V="rbc-continues-prior"),v.lt(Q,ae,"day")&&(V+=" rbc-continues-after"),R.createElement("span",{className:V.trim()},J?R.createElement(J,{event:I,day:Q,label:le}):le)},F=function(){if(E.current){var Q=y.current,I=E.current.firstChild;if(I){var V=S.current.scrollHeight>S.current.clientHeight,J=[],le=J;J=[xl(I.children[0]),xl(I.children[1])],(le[0]!==J[0]||le[1]!==J[1])&&(w.current.style.width=J[0]+"px",k.current.style.width=J[1]+"px"),V?(ac(Q,"rbc-header-overflowing"),Q.style.marginRight=ql()+"px"):oc(Q,"rbc-header-overflowing")}}},C=v.messages,A=v.add(o,d,"day"),B=v.range(o,A,"day");return s=s.filter(function(q){return Tl(q,v.startOf(o,"day"),v.endOf(A,"day"),r,v)}),s.sort(function(q,Q){return+r.start(q)-+r.start(Q)}),R.createElement("div",{className:"rbc-agenda-view"},s.length!==0?R.createElement(R.Fragment,null,R.createElement("table",{ref:y,className:"rbc-agenda-table"},R.createElement("thead",null,R.createElement("tr",null,R.createElement("th",{className:"rbc-header",ref:w},C.date),R.createElement("th",{className:"rbc-header",ref:k},C.time),R.createElement("th",{className:"rbc-header"},C.event)))),R.createElement("div",{className:"rbc-agenda-content",ref:S},R.createElement("table",{className:"rbc-agenda-table"},R.createElement("tbody",{ref:E},B.map(function(q,Q){return x(q,s,Q)}))))):R.createElement("span",{className:"rbc-agenda-empty"},C.noEventsInRange))}Ec.range=function(t,r){var a=r.length,o=a===void 0?kc:a,s=r.localizer,u=s.add(t,o,"day");return{start:t,end:u}};Ec.navigate=function(t,r,a){var o=a.length,s=o===void 0?kc:o,u=a.localizer;switch(r){case Lt.PREVIOUS:return u.add(t,-s,"day");case Lt.NEXT:return u.add(t,s,"day");default:return t}};Ec.title=function(t,r){var a=r.length,o=a===void 0?kc:a,s=r.localizer,u=s.add(t,o,"day");return s.format({start:t,end:u},"agendaHeaderFormat")};var Pu=_a(_a(_a(_a(_a({},Ot.MONTH,wc),Ot.WEEK,Wr),Ot.WORK_WEEK,Zl),Ot.DAY,Sc),Ot.AGENDA,Ec),mY=["action","date","today"];function gY(t,r){var a=r.action,o=r.date,s=r.today,u=Fn(r,mY);switch(t=typeof t=="string"?Pu[t]:t,a){case Lt.TODAY:o=s||new Date;break;case Lt.DATE:break;default:vl(t&&typeof t.navigate=="function","Calendar View components must implement a static `.navigate(date, action)` method.s"),o=t.navigate(o,a,u)}return o}function yY(t,r){var a=null;return typeof r=="function"?a=r(t):typeof r=="string"&&ir(t)==="object"&&t!=null&&r in t&&(a=t[r]),a}var nr=function(r){return function(a){return yY(a,r)}},bY=["view","date","getNow","onNavigate"],wY=["view","toolbar","events","backgroundEvents","resourceGroupingLayout","style","className","elementProps","date","getNow","length","showMultiDayTimes","onShowMore","doShowMoreDrillDown","components","formats","messages","culture"];function kS(t){if(Array.isArray(t))return t;for(var r=[],a=0,o=Object.entries(t);a<o.length;a++){var s=on(o[a],2),u=s[0],c=s[1];c&&r.push(u)}return r}function SY(t,r){var a=r.views,o=kS(a);return o.indexOf(t)!==-1}var ES=function(t){function r(){var a;rt(this,r);for(var o=arguments.length,s=new Array(o),u=0;u<o;u++)s[u]=arguments[u];return a=Et(this,r,[].concat(s)),a.getViews=function(){var c=a.props.views;return Array.isArray(c)?J4(c,function(d,v){return d[v]=Pu[v]},{}):ir(c)==="object"?JL(c,function(d,v){return d===!0?Pu[v]:d}):Pu},a.getView=function(){var c=a.getViews();return c[a.props.view]},a.getDrilldownView=function(c){var d=a.props,v=d.view,h=d.drilldownView,m=d.getDrilldownView;return m?m(c,v,Object.keys(a.getViews())):h},a.handleRangeChange=function(c,d,v){var h=a.props,m=h.onRangeChange,g=h.localizer;m&&d.range&&m(d.range(c,{localizer:g}),v)},a.handleNavigate=function(c,d){var v=a.props,h=v.view,m=v.date,g=v.getNow,y=v.onNavigate,w=Fn(v,bY),k=a.getView(),S=g();m=gY(k,Fe(Fe({},w),{},{action:c,date:d||m||S,today:S})),y(m,h,c),a.handleRangeChange(m,k)},a.handleViewChange=function(c){c!==a.props.view&&SY(c,a.props)&&a.props.onView(c);var d=a.getViews();a.handleRangeChange(a.props.date||a.props.getNow(),d[c],c)},a.handleSelectEvent=function(){for(var c=arguments.length,d=new Array(c),v=0;v<c;v++)d[v]=arguments[v];Ye(a.props.onSelectEvent,d)},a.handleDoubleClickEvent=function(){for(var c=arguments.length,d=new Array(c),v=0;v<c;v++)d[v]=arguments[v];Ye(a.props.onDoubleClickEvent,d)},a.handleKeyPressEvent=function(){for(var c=arguments.length,d=new Array(c),v=0;v<c;v++)d[v]=arguments[v];Ye(a.props.onKeyPressEvent,d)},a.handleSelectSlot=function(c){Ye(a.props.onSelectSlot,c)},a.handleDrillDown=function(c,d){var v=a.props.onDrillDown;if(v){v(c,d,a.drilldownView);return}d&&a.handleViewChange(d),a.handleNavigate(Lt.DATE,c)},a.state={context:r.getContext(a.props)},a}return Dt(r,t),at(r,[{key:"render",value:function(){var o=this.props,s=o.view,u=o.toolbar,c=o.events,d=o.backgroundEvents,v=o.resourceGroupingLayout,h=o.style,m=o.className,g=o.elementProps,y=o.date,w=o.getNow,k=o.length,S=o.showMultiDayTimes,E=o.onShowMore,x=o.doShowMoreDrillDown;o.components,o.formats,o.messages,o.culture;var _=Fn(o,wY);y=y||w();var F=this.getView(),C=this.state.context,A=C.accessors,B=C.components,q=C.getters,Q=C.localizer,I=C.viewNames,V=B.toolbar||D6,J=F.title(y,{localizer:Q,length:k});return R.createElement("div",Object.assign({},g,{className:He(m,"rbc-calendar",_.rtl&&"rbc-rtl"),style:h}),u&&R.createElement(V,{date:y,view:s,views:I,label:J,onView:this.handleViewChange,onNavigate:this.handleNavigate,localizer:Q}),R.createElement(F,Object.assign({},_,{events:c,backgroundEvents:d,date:y,getNow:w,length:k,localizer:Q,getters:q,components:B,accessors:A,showMultiDayTimes:S,getDrilldownView:this.getDrilldownView,onNavigate:this.handleNavigate,onDrillDown:this.handleDrillDown,onSelectEvent:this.handleSelectEvent,onDoubleClickEvent:this.handleDoubleClickEvent,onKeyPressEvent:this.handleKeyPressEvent,onSelectSlot:this.handleSelectSlot,onShowMore:E,doShowMoreDrillDown:x,resourceGroupingLayout:v})))}}],[{key:"getDerivedStateFromProps",value:function(o){return{context:r.getContext(o)}}},{key:"getContext",value:function(o){var s=o.startAccessor,u=o.endAccessor,c=o.allDayAccessor,d=o.tooltipAccessor,v=o.titleAccessor,h=o.resourceAccessor,m=o.resourceIdAccessor,g=o.resourceTitleAccessor,y=o.eventIdAccessor,w=o.eventPropGetter,k=o.backgroundEventPropGetter,S=o.slotPropGetter,E=o.slotGroupPropGetter,x=o.dayPropGetter,_=o.view,F=o.views,C=o.localizer,A=o.culture,B=o.messages,q=B===void 0?{}:B,Q=o.components,I=Q===void 0?{}:Q,V=o.formats,J=V===void 0?{}:V,le=kS(F),ae=T6(q);return{viewNames:le,localizer:E6(C,A,J,ae),getters:{eventProp:function(){return w&&w.apply(void 0,arguments)||{}},backgroundEventProp:function(){return k&&k.apply(void 0,arguments)||{}},slotProp:function(){return S&&S.apply(void 0,arguments)||{}},slotGroupProp:function(){return E&&E.apply(void 0,arguments)||{}},dayProp:function(){return x&&x.apply(void 0,arguments)||{}}},components:UL(I[_]||{},I4(I,le),{eventWrapper:ar,backgroundEventWrapper:ar,eventContainerWrapper:ar,dateCellWrapper:ar,weekWrapper:ar,timeSlotWrapper:ar,timeGutterWrapper:ar}),accessors:{start:nr(s),end:nr(u),allDay:nr(c),tooltip:nr(d),title:nr(v),resource:nr(h),resourceId:nr(m),resourceTitle:nr(g),eventId:nr(y)}}}}])}(R.Component);ES.defaultProps={events:[],backgroundEvents:[],elementProps:{},popup:!1,toolbar:!0,view:Ot.MONTH,views:[Ot.MONTH,Ot.WEEK,Ot.DAY,Ot.AGENDA],step:30,length:30,allDayMaxRows:1/0,doShowMoreDrillDown:!0,drilldownView:Ot.DAY,titleAccessor:"title",tooltipAccessor:"title",allDayAccessor:"allDay",startAccessor:"start",endAccessor:"end",resourceAccessor:"resourceId",resourceIdAccessor:"id",resourceTitleAccessor:"title",eventIdAccessor:"id",longPressThreshold:250,getNow:function(){return new Date},dayLayoutAlgorithm:"overlap"};var kY=qw(ES,{view:"onView",date:"onNavigate",selected:"onSelectEvent"}),EY=function(r,a,o){var s=r.start,u=r.end;return o.format(s,"MMMM DD",a)+" – "+o.format(u,o.eq(s,u,"month")?"DD":"MMMM DD",a)},DY=function(r,a,o){var s=r.start,u=r.end;return o.format(s,"L",a)+" – "+o.format(u,"L",a)},Jd=function(r,a,o){var s=r.start,u=r.end;return o.format(s,"LT",a)+" – "+o.format(u,"LT",a)},xY=function(r,a,o){var s=r.start;return o.format(s,"LT",a)+" – "},TY=function(r,a,o){var s=r.end;return" – "+o.format(s,"LT",a)},_Y={dateFormat:"DD",dayFormat:"DD ddd",weekdayFormat:"ddd",selectRangeFormat:Jd,eventTimeRangeFormat:Jd,eventTimeRangeStartFormat:xY,eventTimeRangeEndFormat:TY,timeGutterFormat:"LT",monthHeaderFormat:"MMMM YYYY",dayHeaderFormat:"dddd MMM DD",dayRangeHeaderFormat:EY,agendaHeaderFormat:DY,agendaDateFormat:"ddd MMM DD",agendaTimeFormat:"LT",agendaTimeRangeFormat:Jd};function jr(t){var r=t&&t.toLowerCase();return r==="FullYear"?r="year":r||(r=void 0),r}function OY(t){var r=function(H,W){return W?H.locale(W):H};function a(P){return t(P).toDate().getTimezoneOffset()}function o(P,H){var W,L,G=t(P).local(),ne=t(H).local();if(!t.tz)return G.toDate().getTimezoneOffset()-ne.toDate().getTimezoneOffset();var ve=(W=G==null||(L=G._z)===null||L===void 0?void 0:L.name)!==null&&W!==void 0?W:t.tz.guess(),ce=t.tz.zone(ve).utcOffset(+G),Oe=t.tz.zone(ve).utcOffset(+ne);return ce-Oe}function s(P){var H=t(P).startOf("day");return o(H,P)}function u(P,H,W){var L=jr(W),G=L?t(P).startOf(L):t(P),ne=L?t(H).startOf(L):t(H);return[G,ne,L]}function c(){var P=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null,H=arguments.length>1?arguments[1]:void 0,W=jr(H);return W?t(P).startOf(W).toDate():t(P).toDate()}function d(){var P=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null,H=arguments.length>1?arguments[1]:void 0,W=jr(H);return W?t(P).endOf(W).toDate():t(P).toDate()}function v(P,H,W){var L=u(P,H,W),G=on(L,3),ne=G[0],ve=G[1],ce=G[2];return ne.isSame(ve,ce)}function h(P,H,W){return!v(P,H,W)}function m(P,H,W){var L=u(P,H,W),G=on(L,3),ne=G[0],ve=G[1],ce=G[2];return ne.isAfter(ve,ce)}function g(P,H,W){var L=u(P,H,W),G=on(L,3),ne=G[0],ve=G[1],ce=G[2];return ne.isBefore(ve,ce)}function y(P,H,W){var L=u(P,H,W),G=on(L,3),ne=G[0],ve=G[1],ce=G[2];return ne.isSameOrBefore(ve,ce)}function w(P,H,W){var L=u(P,H,W),G=on(L,3),ne=G[0],ve=G[1],ce=G[2];return ne.isSameOrBefore(ve,ce)}function k(P,H,W){var L=arguments.length>3&&arguments[3]!==void 0?arguments[3]:"day",G=jr(L),ne=t(P),ve=t(H),ce=t(W);return ne.isBetween(ve,ce,G,"[]")}function S(P,H){var W=t(P),L=t(H),G=t.min(W,L);return G.toDate()}function E(P,H){var W=t(P),L=t(H),G=t.max(W,L);return G.toDate()}function x(P,H){if(!P&&!H)return null;var W=t(H).format("HH:mm:ss"),L=t(P).startOf("day").format("MM/DD/YYYY");return t("".concat(L," ").concat(W),"MM/DD/YYYY HH:mm:ss").toDate()}function _(P,H,W){var L=jr(W);return t(P).add(H,L).toDate()}function F(P,H){for(var W=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"day",L=jr(W),G=t(P).toDate(),ne=[];w(G,H);)ne.push(G),G=_(G,1,L);return ne}function C(P,H){var W=jr(H),L=c(P,W);return v(L,P)?L:_(L,1,W)}function A(P,H){var W=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"day",L=jr(W),G=t(P),ne=t(H);return ne.diff(G,L)}function B(P){var H=t(P);return H.minutes()}function q(P){var H=P?t.localeData(P):t.localeData();return H?H.firstDayOfWeek():0}function Q(P){return t(P).startOf("month").startOf("week").toDate()}function I(P){return t(P).endOf("month").endOf("week").toDate()}function V(P){for(var H=Q(P),W=I(P),L=[];w(H,W);)L.push(H),H=_(H,1,"d");return L}function J(P,H,W){return t(P).startOf("day").minute(H+W).toDate()}function le(P,H){return A(P,H,"minutes")}function ae(P){var H=t(P).startOf("day"),W=t(P);return W.diff(H,"minutes")+s(P)}function fe(P,H){var W=t(P),L=t(H);return W.isBefore(L,"day")}function me(P,H,W){var L=t(H),G=t(W);return L.isSameOrAfter(G,"minutes")}function $(P,H){var W=t(P),L=t(H),G=t.duration(L.diff(W));return G.days()}function ie(P){var H=P.evtA,W=H.start,L=H.end,G=H.allDay,ne=P.evtB,ve=ne.start,ce=ne.end,Oe=ne.allDay,Me=+c(W,"day")-+c(ve,"day"),ot=$(W,L),cn=$(ve,ce);return Me||cn-ot||!!Oe-!!G||+W-+ve||+L-+ce}function se(P){var H=P.event,W=H.start,L=H.end,G=P.range,ne=G.start,ve=G.end,ce=t(W).startOf("day"),Oe=t(L),Me=t(ne),ot=t(ve),cn=ce.isSameOrBefore(ot,"day"),jt=!ce.isSame(Oe,"minutes"),dr=jt?Oe.isAfter(Me,"minutes"):Oe.isSameOrAfter(Me,"minutes");return cn&&dr}function ge(P,H){var W=t(P),L=t(H);return W.isSame(L,"day")}function O(){var P=new Date,H=/-/.test(P.toString())?"-":"",W=P.getTimezoneOffset(),L=Number("".concat(H).concat(Math.abs(W))),G=t().utcOffset();return G>L?1:0}return new k6({formats:_Y,firstOfWeek:q,firstVisibleDay:Q,lastVisibleDay:I,visibleDays:V,format:function(H,W,L){return r(t(H),L).format(W)},lt:g,lte:w,gt:m,gte:y,eq:v,neq:h,merge:x,inRange:k,startOf:c,endOf:d,range:F,add:_,diff:A,ceil:C,min:S,max:E,minutes:B,getSlotDate:J,getTimezoneOffset:a,getDstOffset:o,getTotalMin:le,getMinutesFromMidnight:ae,continuesPrior:fe,continuesAfter:me,sortEvents:ie,inEventRange:se,isSameDate:ge,daySpan:$,browserTZOffset:O})}var mp={},DS={exports:{}};(function(t){function r(a){return a&&a.__esModule?a:{default:a}}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports})(DS);var xt=DS.exports,gp={},xS={exports:{}},TS={exports:{}},_S={exports:{}},OS={exports:{}};(function(t){function r(a){"@babel/helpers - typeof";return t.exports=r=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(o){return typeof o}:function(o){return o&&typeof Symbol=="function"&&o.constructor===Symbol&&o!==Symbol.prototype?"symbol":typeof o},t.exports.__esModule=!0,t.exports.default=t.exports,r(a)}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports})(OS);var Go=OS.exports,CS={exports:{}};(function(t){var r=Go.default;function a(o,s){if(r(o)!="object"||!o)return o;var u=o[Symbol.toPrimitive];if(u!==void 0){var c=u.call(o,s||"default");if(r(c)!="object")return c;throw new TypeError("@@toPrimitive must return a primitive value.")}return(s==="string"?String:Number)(o)}t.exports=a,t.exports.__esModule=!0,t.exports.default=t.exports})(CS);var CY=CS.exports;(function(t){var r=Go.default,a=CY;function o(s){var u=a(s,"string");return r(u)=="symbol"?u:u+""}t.exports=o,t.exports.__esModule=!0,t.exports.default=t.exports})(_S);var PS=_S.exports;(function(t){var r=PS;function a(o,s,u){return(s=r(s))in o?Object.defineProperty(o,s,{value:u,enumerable:!0,configurable:!0,writable:!0}):o[s]=u,o}t.exports=a,t.exports.__esModule=!0,t.exports.default=t.exports})(TS);var MS=TS.exports;(function(t){var r=MS;function a(s,u){var c=Object.keys(s);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(s);u&&(d=d.filter(function(v){return Object.getOwnPropertyDescriptor(s,v).enumerable})),c.push.apply(c,d)}return c}function o(s){for(var u=1;u<arguments.length;u++){var c=arguments[u]!=null?arguments[u]:{};u%2?a(Object(c),!0).forEach(function(d){r(s,d,c[d])}):Object.getOwnPropertyDescriptors?Object.defineProperties(s,Object.getOwnPropertyDescriptors(c)):a(Object(c)).forEach(function(d){Object.defineProperty(s,d,Object.getOwnPropertyDescriptor(c,d))})}return s}t.exports=o,t.exports.__esModule=!0,t.exports.default=t.exports})(xS);var Qr=xS.exports,NS={exports:{}},AS={exports:{}};(function(t){function r(a,o){if(a==null)return{};var s={};for(var u in a)if({}.hasOwnProperty.call(a,u)){if(o.indexOf(u)!==-1)continue;s[u]=a[u]}return s}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports})(AS);var PY=AS.exports;(function(t){var r=PY;function a(o,s){if(o==null)return{};var u,c,d=r(o,s);if(Object.getOwnPropertySymbols){var v=Object.getOwnPropertySymbols(o);for(c=0;c<v.length;c++)u=v[c],s.indexOf(u)===-1&&{}.propertyIsEnumerable.call(o,u)&&(d[u]=o[u])}return d}t.exports=a,t.exports.__esModule=!0,t.exports.default=t.exports})(NS);var yp=NS.exports,RS={exports:{}};(function(t){function r(a,o){if(!(a instanceof o))throw new TypeError("Cannot call a class as a function")}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports})(RS);var ja=RS.exports,FS={exports:{}};(function(t){var r=PS;function a(s,u){for(var c=0;c<u.length;c++){var d=u[c];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(s,r(d.key),d)}}function o(s,u,c){return u&&a(s.prototype,u),c&&a(s,c),Object.defineProperty(s,"prototype",{writable:!1}),s}t.exports=o,t.exports.__esModule=!0,t.exports.default=t.exports})(FS);var Ia=FS.exports,zS={exports:{}},LS={exports:{}};(function(t){function r(a){return t.exports=r=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(o){return o.__proto__||Object.getPrototypeOf(o)},t.exports.__esModule=!0,t.exports.default=t.exports,r(a)}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports})(LS);var MY=LS.exports,jS={exports:{}};(function(t){function r(){try{var a=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(t.exports=r=function(){return!!a},t.exports.__esModule=!0,t.exports.default=t.exports)()}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports})(jS);var NY=jS.exports,IS={exports:{}},YS={exports:{}};(function(t){function r(a){if(a===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports})(YS);var AY=YS.exports;(function(t){var r=Go.default,a=AY;function o(s,u){if(u&&(r(u)=="object"||typeof u=="function"))return u;if(u!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return a(s)}t.exports=o,t.exports.__esModule=!0,t.exports.default=t.exports})(IS);var RY=IS.exports;(function(t){var r=MY,a=NY,o=RY;function s(u,c,d){return c=r(c),o(u,a()?Reflect.construct(c,d||[],r(u).constructor):c.apply(u,d))}t.exports=s,t.exports.__esModule=!0,t.exports.default=t.exports})(zS);var Jo=zS.exports,HS={exports:{}},BS={exports:{}};(function(t){function r(a,o){return t.exports=r=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(s,u){return s.__proto__=u,s},t.exports.__esModule=!0,t.exports.default=t.exports,r(a,o)}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports})(BS);var FY=BS.exports;(function(t){var r=FY;function a(o,s){if(typeof s!="function"&&s!==null)throw new TypeError("Super expression must either be null or a function");o.prototype=Object.create(s&&s.prototype,{constructor:{value:o,writable:!0,configurable:!0}}),Object.defineProperty(o,"prototype",{writable:!1}),s&&r(o,s)}t.exports=a,t.exports.__esModule=!0,t.exports.default=t.exports})(HS);var Zo=HS.exports;const es=Cl(Jx);var mn={},zo={};Object.defineProperty(zo,"__esModule",{value:!0});zo.views=zo.navigate=void 0;zo.navigate={PREVIOUS:"PREV",NEXT:"NEXT",TODAY:"TODAY",DATE:"DATE"};zo.views={MONTH:"month",WEEK:"week",WORK_WEEK:"work_week",DAY:"day",AGENDA:"agenda"};var zY=xt.default;Object.defineProperty(mn,"__esModule",{value:!0});mn.views=mn.dateRangeFormat=mn.dateFormat=mn.accessor=mn.DayLayoutAlgorithmPropType=void 0;var Rt=zY(_v),Qb=zo,Xb=Object.keys(Qb.views).map(function(t){return Qb.views[t]});mn.accessor=Rt.default.oneOfType([Rt.default.string,Rt.default.func]);mn.dateFormat=Rt.default.any;mn.dateRangeFormat=Rt.default.func;mn.views=Rt.default.oneOfType([Rt.default.arrayOf(Rt.default.oneOf(Xb)),Rt.default.objectOf(function(t,r){var a=Xb.indexOf(r)!==-1&&typeof t[r]=="boolean";if(a)return null;for(var o=arguments.length,s=new Array(o>2?o-2:0),u=2;u<o;u++)s[u-2]=arguments[u];return Rt.default.elementType.apply(Rt.default,[t,r].concat(s))})]);mn.DayLayoutAlgorithmPropType=Rt.default.oneOfType([Rt.default.oneOf(["overlap","no-overlap"]),Rt.default.func]);var Dc={},ei={},LY=xt.default;Object.defineProperty(ei,"__esModule",{value:!0});ei.accessor=WS;ei.wrapAccessor=void 0;var jY=LY(Go);function WS(t,r){var a=null;return typeof r=="function"?a=r(t):typeof r=="string"&&(0,jY.default)(t)==="object"&&t!=null&&r in t&&(a=t[r]),a}ei.wrapAccessor=function(r){return function(a){return WS(a,r)}};var Ya={},IY=xt.default;Object.defineProperty(Ya,"__esModule",{value:!0});Ya.DnDContext=void 0;var YY=IY(te);Ya.DnDContext=YY.default.createContext();var Ha=xt.default;Object.defineProperty(Dc,"__esModule",{value:!0});Dc.default=void 0;var HY=Ha(Qr),BY=Ha(ja),WY=Ha(Ia),VY=Ha(Jo),UY=Ha(Zo),_o=Ha(te),Gb=Ha(es),Jb=ei,$Y=Ya,VS=function(t){function r(){var a;(0,BY.default)(this,r);for(var o=arguments.length,s=new Array(o),u=0;u<o;u++)s[u]=arguments[u];return a=(0,VY.default)(this,r,[].concat(s)),a.handleResizeUp=function(c){c.button===0&&a.context.draggable.onBeginAction(a.props.event,"resize","UP")},a.handleResizeDown=function(c){c.button===0&&a.context.draggable.onBeginAction(a.props.event,"resize","DOWN")},a.handleResizeLeft=function(c){c.button===0&&a.context.draggable.onBeginAction(a.props.event,"resize","LEFT")},a.handleResizeRight=function(c){c.button===0&&a.context.draggable.onBeginAction(a.props.event,"resize","RIGHT")},a.handleStartDragging=function(c){var d;if(c.button===0){var v=(d=c.target.getAttribute("class"))===null||d===void 0?void 0:d.includes("rbc-addons-dnd-resize");if(!v){var h=(0,HY.default)({},a.props.event);h.sourceResource=a.props.resource,a.context.draggable.onBeginAction(a.props.event,"move")}}},a}return(0,UY.default)(r,t),(0,WY.default)(r,[{key:"renderAnchor",value:function(o){var s=o==="Up"||o==="Down"?"ns":"ew";return _o.default.createElement("div",{className:"rbc-addons-dnd-resize-".concat(s,"-anchor"),onMouseDown:this["handleResize".concat(o)]},_o.default.createElement("div",{className:"rbc-addons-dnd-resize-".concat(s,"-icon")}))}},{key:"render",value:function(){var o=this.props,s=o.event,u=o.type,c=o.continuesPrior,d=o.continuesAfter,v=o.resizable,h=this.props.children;if(s.__isPreview)return _o.default.cloneElement(h,{className:(0,Gb.default)(h.props.className,"rbc-addons-dnd-drag-preview")});var m=this.context.draggable,g=m.draggableAccessor,y=m.resizableAccessor,w=g?!!(0,Jb.accessor)(s,g):!0;if(!w)return h;var k=v&&(y?!!(0,Jb.accessor)(s,y):!0);if(k||w){var S={onMouseDown:this.handleStartDragging,onTouchStart:this.handleStartDragging};if(k){var E=null,x=null;u==="date"?(E=!c&&this.renderAnchor("Left"),x=!d&&this.renderAnchor("Right")):(E=!c&&this.renderAnchor("Up"),x=!d&&this.renderAnchor("Down")),S.children=_o.default.createElement("div",{className:"rbc-addons-dnd-resizable"},E,h.props.children,x)}m.dragAndDropAction.interacting&&m.dragAndDropAction.event===s&&(S.className=(0,Gb.default)(h.props.className,"rbc-addons-dnd-dragged-event")),h=_o.default.cloneElement(h,S)}return h}}])}(_o.default.Component);VS.contextType=$Y.DnDContext;Dc.default=VS;var xc={},US={exports:{}};(function(t){var r=Go.default;function a(s){if(typeof WeakMap!="function")return null;var u=new WeakMap,c=new WeakMap;return(a=function(v){return v?c:u})(s)}function o(s,u){if(!u&&s&&s.__esModule)return s;if(s===null||r(s)!="object"&&typeof s!="function")return{default:s};var c=a(u);if(c&&c.has(s))return c.get(s);var d={__proto__:null},v=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var h in s)if(h!=="default"&&{}.hasOwnProperty.call(s,h)){var m=v?Object.getOwnPropertyDescriptor(s,h):null;m&&(m.get||m.set)?Object.defineProperty(d,h,m):d[h]=s[h]}return d.default=s,c&&c.set(s,d),d}t.exports=o,t.exports.__esModule=!0,t.exports.default=t.exports})(US);var $S=US.exports;function bp(t,r,a,o){if(a===void 0&&(a=!1),o===void 0&&(o=!0),t){var s=document.createEvent("HTMLEvents");s.initEvent(r,a,o),t.dispatchEvent(s)}}function KY(t){var r=pt(t,"transitionDuration")||"",a=r.indexOf("ms")===-1?1e3:1;return parseFloat(r)*a}function qY(t,r,a){a===void 0&&(a=5);var o=!1,s=setTimeout(function(){o||bp(t,"transitionend",!0)},r+a),u=Mn(t,"transitionend",function(){o=!0},{once:!0});return function(){clearTimeout(s),u()}}function wp(t,r,a,o){a==null&&(a=KY(t)||0);var s=qY(t,a,o),u=Mn(t,"transitionend",r);return function(){s(),u()}}var Zb={transition:"","transition-duration":"","transition-delay":"","transition-timing-function":""};function ew(t){var r=t.node,a=t.properties,o=t.duration,s=o===void 0?200:o,u=t.easing,c=t.callback,d=[],v={},h="";Object.keys(a).forEach(function(y){var w=a[y];c0(y)?h+=y+"("+w+") ":(v[y]=w,d.push(u0(y)))}),h&&(v.transform=h,d.push("transform"));function m(y){y.target===y.currentTarget&&(pt(r,Zb),c&&c.call(this,y))}s>0&&(v.transition=d.join(", "),v["transition-duration"]=s/1e3+"s",v["transition-delay"]="0s",v["transition-timing-function"]=u||"linear");var g=wp(r,m,s);return r.clientLeft,pt(r,v),{cancel:function(){g(),pt(r,Zb)}}}function KS(t,r,a,o,s){if(!("nodeType"in t))return ew(t);if(!r)throw new Error("must include properties to animate");return typeof o=="function"&&(s=o,o=""),ew({node:t,properties:r,duration:a,easing:o,callback:s})}function qS(t,r,a){if(t){if(typeof a>"u")return t.getAttribute(r);!a&&a!==""?t.removeAttribute(r):t.setAttribute(r,String(a))}}function QS(t){return t?Array.from(t.children):[]}function XS(t){if(t){for(;t.firstChild;)t.removeChild(t.firstChild);return t}return null}var QY=Function.prototype.bind.call(Function.prototype.call,[].slice);function GS(t){return t?QY(t.childNodes):[]}function JS(t,r){return function(o){var s=o.currentTarget,u=o.target,c=tc(s,t);c.some(function(d){return lr(d,u)})&&r.call(this,o)}}function ZS(t,r){return t&&r&&r.parentNode?(r.nextSibling?r.parentNode.insertBefore(t,r.nextSibling):r.parentNode.appendChild(t),t):null}var XY=/^(?:input|select|textarea|button)$/i;function ek(t){return t?XY.test(t.nodeName):!1}function tk(t){return t?!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length):!1}function nk(t,r,a){r===void 0&&(r=null),a===void 0&&(a=null);for(var o=[];t;t=t.nextElementSibling)if(t!==r){if(a&&ec(t,a))break;o.push(t)}return o}function rk(t,r){return nk(t,t,r)}function GY(t,r){var a=null,o=[];for(a=t?t[r]:null;a&&a.nodeType!==9;)o.push(a),a=a[r]||null;return o}function ak(t){return GY(t,"parentElement")}function ok(t,r){return t&&r?(r.firstElementChild?r.insertBefore(t,r.firstElementChild):r.appendChild(t),t):null}function ik(t){return t&&t.parentNode?(t.parentNode.removeChild(t),t):null}function Sp(t,r){var a=pt(t,"position"),o=a==="absolute",s=t.ownerDocument;if(a==="fixed")return s||document;for(;(t=t.parentNode)&&!f0(t);){var u=o&&pt(t,"position")==="static",c=(pt(t,"overflow")||"")+(pt(t,"overflow-y")||"")+pt(t,"overflow-x");if(!u&&/(auto|scroll)/.test(c)&&(r||Ca(t)<t.scrollHeight))return t}return s||document}function lk(t,r){var a=yn(t),o={top:0,left:0};if(t){var s=r||Sp(t),u=Gu(s),c=Mo(s),d=Ca(s,!0);u||(o=yn(s)),a={top:a.top-o.top,left:a.left-o.left,height:a.height,width:a.width};var v=a.height,h=a.top+(u?0:c),m=h+v;c=c>h?h:m>c+d?m-d:c;var g=Ul(function(){return Mo(s,c)});return function(){return kl(g)}}}function sk(t){return nk(t&&t.parentElement?t.parentElement.firstElementChild:null,t)}var JY=/&nbsp;/gi,ZY=/\xA0/g,eH=/\s+([^\s])/gm;function uk(t,r,a){r===void 0&&(r=!0),a===void 0&&(a=!0);var o="";return t&&(o=(t.textContent||"").replace(JY," ").replace(ZY," "),r&&(o=o.trim()),a&&(o=o.replace(eH," $1"))),o}function ck(t,r){t.classList?t.classList.toggle(r):rc(t,r)?oc(t,r):ac(t,r)}const tH={addEventListener:Lv,removeEventListener:$v,triggerEvent:bp,animate:KS,filter:JS,listen:Mn,style:pt,getComputedStyle:Rv,attribute:qS,activeElement:k0,ownerDocument:Kr,ownerWindow:Av,requestAnimationFrame:Ul,cancelAnimationFrame:kl,matches:ec,height:Ca,width:xl,offset:yn,offsetParent:Fv,position:Zu,contains:lr,scrollbarSize:ql,scrollLeft:Ju,scrollParent:Sp,scrollTo:lk,scrollTop:Mo,querySelectorAll:tc,closest:Xl,addClass:ac,removeClass:oc,hasClass:rc,toggleClass:ck,transitionEnd:wp,childNodes:GS,childElements:QS,nextUntil:rk,parents:ak,siblings:sk,clear:XS,insertAfter:ZS,isInput:ek,isVisible:tk,prepend:ok,remove:ik,text:uk},nH=Object.freeze(Object.defineProperty({__proto__:null,activeElement:k0,addClass:ac,addEventListener:Lv,animate:KS,attribute:qS,cancelAnimationFrame:kl,childElements:QS,childNodes:GS,clear:XS,closest:Xl,contains:lr,default:tH,filter:JS,getComputedStyle:Rv,hasClass:rc,height:Ca,insertAfter:ZS,isInput:ek,isVisible:tk,listen:Mn,matches:ec,nextUntil:rk,offset:yn,offsetParent:Fv,ownerDocument:Kr,ownerWindow:Av,parents:ak,position:Zu,prepend:ok,querySelectorAll:tc,remove:ik,removeClass:oc,removeEventListener:$v,requestAnimationFrame:Ul,scrollLeft:Ju,scrollParent:Sp,scrollTo:lk,scrollTop:Mo,scrollbarSize:ql,siblings:sk,style:pt,text:uk,toggleClass:ck,transitionEnd:wp,triggerEvent:bp,width:xl},Symbol.toStringTag,{value:"Module"})),rH=Cl(nH);var wv={exports:{}};(function(t,r){r.__esModule=!0,r.default=o;var a=Function.prototype.bind.call(Function.prototype.call,[].slice);function o(s,u){return a(s.querySelectorAll(u))}t.exports=r.default})(wv,wv.exports);var aH=wv.exports,Sn={};const oH=Cl(U_),iH=Cl(qA),lH=Cl(iC);var ti=xt.default;Object.defineProperty(Sn,"__esModule",{value:!0});Sn.default=void 0;Sn.getBoundsForNode=Ol;Sn.getEventNodeFromPoint=dk;Sn.getShowMoreNodeFromPoint=vk;Sn.isEvent=vH;Sn.isShowMore=pH;Sn.objectsCollide=kv;var sH=ti(Go),uH=ti(ja),cH=ti(Ia),Sv=ti(oH),fk=ti(iH),fH=ti(lH);function wt(t,r){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:document;return(0,fH.default)(a,t,r,{passive:!1})}function dH(t,r,a){return!t||(0,Sv.default)(t,document.elementFromPoint(r,a))}function dk(t,r){var a=r.clientX,o=r.clientY,s=document.elementFromPoint(a,o);return(0,fk.default)(s,".rbc-event",t)}function vk(t,r){var a=r.clientX,o=r.clientY,s=document.elementFromPoint(a,o);return(0,fk.default)(s,".rbc-show-more",t)}function vH(t,r){return!!dk(t,r)}function pH(t,r){return!!vk(t,r)}function il(t){var r=t;return t.touches&&t.touches.length&&(r=t.touches[0]),{clientX:r.clientX,clientY:r.clientY,pageX:r.pageX,pageY:r.pageY}}var tw=5,hH=250,mH=function(){function t(r){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=a.global,s=o===void 0?!1:o,u=a.longPressThreshold,c=u===void 0?250:u,d=a.validContainers,v=d===void 0?[]:d;(0,uH.default)(this,t),this._initialEvent=null,this.selecting=!1,this.isDetached=!1,this.container=r,this.globalMouse=!r||s,this.longPressThreshold=c,this.validContainers=v,this._listeners=Object.create(null),this._handleInitialEvent=this._handleInitialEvent.bind(this),this._handleMoveEvent=this._handleMoveEvent.bind(this),this._handleTerminatingEvent=this._handleTerminatingEvent.bind(this),this._keyListener=this._keyListener.bind(this),this._dropFromOutsideListener=this._dropFromOutsideListener.bind(this),this._dragOverFromOutsideListener=this._dragOverFromOutsideListener.bind(this),this._removeTouchMoveWindowListener=wt("touchmove",function(){},window),this._removeKeyDownListener=wt("keydown",this._keyListener),this._removeKeyUpListener=wt("keyup",this._keyListener),this._removeDropFromOutsideListener=wt("drop",this._dropFromOutsideListener),this._removeDragOverFromOutsideListener=wt("dragover",this._dragOverFromOutsideListener),this._addInitialEventListener()}return(0,cH.default)(t,[{key:"on",value:function(a,o){var s=this._listeners[a]||(this._listeners[a]=[]);return s.push(o),{remove:function(){var c=s.indexOf(o);c!==-1&&s.splice(c,1)}}}},{key:"emit",value:function(a){for(var o=arguments.length,s=new Array(o>1?o-1:0),u=1;u<o;u++)s[u-1]=arguments[u];var c,d=this._listeners[a]||[];return d.forEach(function(v){c===void 0&&(c=v.apply(void 0,s))}),c}},{key:"teardown",value:function(){this._initialEvent=null,this._initialEventData=null,this._selectRect=null,this.selecting=!1,this._lastClickData=null,this.isDetached=!0,this._listeners=Object.create(null),this._removeTouchMoveWindowListener&&this._removeTouchMoveWindowListener(),this._removeInitialEventListener&&this._removeInitialEventListener(),this._removeEndListener&&this._removeEndListener(),this._onEscListener&&this._onEscListener(),this._removeMoveListener&&this._removeMoveListener(),this._removeKeyUpListener&&this._removeKeyUpListener(),this._removeKeyDownListener&&this._removeKeyDownListener(),this._removeDropFromOutsideListener&&this._removeDropFromOutsideListener(),this._removeDragOverFromOutsideListener&&this._removeDragOverFromOutsideListener()}},{key:"isSelected",value:function(a){var o=this._selectRect;return!o||!this.selecting?!1:kv(o,Ol(a))}},{key:"filter",value:function(a){var o=this._selectRect;return!o||!this.selecting?[]:a.filter(this.isSelected,this)}},{key:"_addLongPressListener",value:function(a,o){var s=this,u=null,c=null,d=null,v=function(y){u=setTimeout(function(){m(),a(y)},s.longPressThreshold),c=wt("touchmove",function(){return m()}),d=wt("touchend",function(){return m()})},h=wt("touchstart",v),m=function(){u&&clearTimeout(u),c&&c(),d&&d(),u=null,c=null,d=null};return o&&v(o),function(){m(),h()}}},{key:"_addInitialEventListener",value:function(){var a=this,o=wt("mousedown",function(u){a._removeInitialEventListener(),a._handleInitialEvent(u),a._removeInitialEventListener=wt("mousedown",a._handleInitialEvent)}),s=wt("touchstart",function(u){a._removeInitialEventListener(),a._removeInitialEventListener=a._addLongPressListener(a._handleInitialEvent,u)});this._removeInitialEventListener=function(){o(),s()}}},{key:"_dropFromOutsideListener",value:function(a){var o=il(a),s=o.pageX,u=o.pageY,c=o.clientX,d=o.clientY;this.emit("dropFromOutside",{x:s,y:u,clientX:c,clientY:d}),a.preventDefault()}},{key:"_dragOverFromOutsideListener",value:function(a){var o=il(a),s=o.pageX,u=o.pageY,c=o.clientX,d=o.clientY;this.emit("dragOverFromOutside",{x:s,y:u,clientX:c,clientY:d}),a.preventDefault()}},{key:"_handleInitialEvent",value:function(a){if(this._initialEvent=a,!this.isDetached){var o=il(a),s=o.clientX,u=o.clientY,c=o.pageX,d=o.pageY,v=this.container(),h,m;if(!(a.which===3||a.button===2||!dH(v,s,u))){if(!this.globalMouse&&v&&!(0,Sv.default)(v,a.target)){var g=gH(0),y=g.top,w=g.left,k=g.bottom,S=g.right;if(m=Ol(v),h=kv({top:m.top-y,left:m.left-w,bottom:m.bottom+k,right:m.right+S},{top:d,left:c}),!h)return}var E=this.emit("beforeSelect",this._initialEventData={isTouch:/^touch/.test(a.type),x:c,y:d,clientX:s,clientY:u});if(E!==!1)switch(a.type){case"mousedown":this._removeEndListener=wt("mouseup",this._handleTerminatingEvent),this._onEscListener=wt("keydown",this._handleTerminatingEvent),this._removeMoveListener=wt("mousemove",this._handleMoveEvent);break;case"touchstart":this._handleMoveEvent(a),this._removeEndListener=wt("touchend",this._handleTerminatingEvent),this._removeMoveListener=wt("touchmove",this._handleMoveEvent);break}}}}},{key:"_isWithinValidContainer",value:function(a){var o=a.target,s=this.validContainers;return!s||!s.length||!o?!0:s.some(function(u){return!!o.closest(u)})}},{key:"_handleTerminatingEvent",value:function(a){var o=this.selecting,s=this._selectRect;if(!o&&a.type.includes("key")&&(a=this._initialEvent),this.selecting=!1,this._removeEndListener&&this._removeEndListener(),this._removeMoveListener&&this._removeMoveListener(),this._selectRect=null,this._initialEvent=null,this._initialEventData=null,!!a){var u=!this.container||(0,Sv.default)(this.container(),a.target),c=this._isWithinValidContainer(a);return a.key==="Escape"||!c?this.emit("reset"):!o&&u?this._handleClickEvent(a):o?this.emit("select",s):this.emit("reset")}}},{key:"_handleClickEvent",value:function(a){var o=il(a),s=o.pageX,u=o.pageY,c=o.clientX,d=o.clientY,v=new Date().getTime();return this._lastClickData&&v-this._lastClickData.timestamp<hH?(this._lastClickData=null,this.emit("doubleClick",{x:s,y:u,clientX:c,clientY:d})):(this._lastClickData={timestamp:v},this.emit("click",{x:s,y:u,clientX:c,clientY:d}))}},{key:"_handleMoveEvent",value:function(a){if(!(this._initialEventData===null||this.isDetached)){var o=this._initialEventData,s=o.x,u=o.y,c=il(a),d=c.pageX,v=c.pageY,h=Math.abs(s-d),m=Math.abs(u-v),g=Math.min(d,s),y=Math.min(v,u),w=this.selecting,k=this.isClick(d,v);k&&!w&&!(h||m)||(!w&&!k&&this.emit("selectStart",this._initialEventData),k||(this.selecting=!0,this._selectRect={top:y,left:g,x:d,y:v,right:g+h,bottom:y+m},this.emit("selecting",this._selectRect)),a.preventDefault())}}},{key:"_keyListener",value:function(a){this.ctrl=a.metaKey||a.ctrlKey}},{key:"isClick",value:function(a,o){var s=this._initialEventData,u=s.x,c=s.y,d=s.isTouch;return!d&&Math.abs(a-u)<=tw&&Math.abs(o-c)<=tw}}])}();function gH(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0;return(0,sH.default)(t)!=="object"&&(t={top:t,left:t,right:t,bottom:t}),t}function kv(t,r){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,o=Ol(t),s=o.top,u=o.left,c=o.right,d=c===void 0?u:c,v=o.bottom,h=v===void 0?s:v,m=Ol(r),g=m.top,y=m.left,w=m.right,k=w===void 0?y:w,S=m.bottom,E=S===void 0?g:S;return!(h-a<g||s+a>E||d-a<y||u+a>k)}function Ol(t){if(!t.getBoundingClientRect)return t;var r=t.getBoundingClientRect(),a=r.left+nw("left"),o=r.top+nw("top");return{top:o,left:a,right:(t.offsetWidth||0)+a,bottom:(t.offsetHeight||0)+o}}function nw(t){if(t==="left")return window.pageXOffset||document.body.scrollLeft||0;if(t==="top")return window.pageYOffset||document.body.scrollTop||0}Sn.default=mH;var Tc={},_c=xt.default;Object.defineProperty(Tc,"__esModule",{value:!0});Tc.default=void 0;var yH=_c(MS),rw=_c(Qr),bH=_c(es),ll=_c(te);function bu(t){return typeof t=="string"?t:t+"%"}function wH(t){var r=t.style,a=t.className,o=t.event,s=t.accessors,u=t.rtl,c=t.selected,d=t.label,v=t.continuesPrior,h=t.continuesAfter,m=t.getters,g=t.onClick,y=t.onDoubleClick,w=t.isBackgroundEvent,k=t.onKeyPress,S=t.components,E=S.event,x=S.eventWrapper,_=s.title(o),F=s.tooltip(o),C=s.end(o),A=s.start(o),B=m.eventProp(o,A,C,c),q=[ll.default.createElement("div",{key:"1",className:"rbc-event-label"},d),ll.default.createElement("div",{key:"2",className:"rbc-event-content"},E?ll.default.createElement(E,{event:o,title:_}):_)],Q=r.height,I=r.top,V=r.width,J=r.xOffset,le=(0,rw.default)((0,rw.default)({},B.style),{},(0,yH.default)({top:bu(I),height:bu(Q),width:bu(V)},u?"right":"left",bu(J)));return ll.default.createElement(x,Object.assign({type:"time"},t),ll.default.createElement("div",{role:"button",tabIndex:0,onClick:g,onDoubleClick:y,style:le,onKeyDown:k,title:F?(typeof d=="string"?d+": ":"")+F:void 0,className:(0,bH.default)(w?"rbc-background-event":"rbc-event",a,B.className,{"rbc-selected":c,"rbc-event-continues-earlier":v,"rbc-event-continues-later":h})},q))}Tc.default=wH;var cr={},pk=xt.default;Object.defineProperty(cr,"__esModule",{value:!0});cr.dragAccessors=void 0;cr.eventTimes=OH;cr.mergeComponents=TH;cr.pointInColumn=_H;var SH=pk(Qr),kH=pk(yp),aw=ei,EH=te,DH=["children"];cr.dragAccessors={start:(0,aw.wrapAccessor)(function(t){return t.start}),end:(0,aw.wrapAccessor)(function(t){return t.end})};function xH(){for(var t=arguments.length,r=new Array(t),a=0;a<t;a++)r[a]=arguments[a];var o=r.filter(Boolean).map(EH.createFactory),s=function(c){var d=c.children,v=(0,kH.default)(c,DH);return o.reduceRight(function(h,m){return m(v,h)},d)};return s}function TH(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0,a=Object.keys(r),o=(0,SH.default)({},t);return a.forEach(function(s){o[s]=t[s]?xH(t[s],r[s]):r[s]}),o}function _H(t,r){var a=t.left,o=t.right,s=t.top,u=r.x,c=r.y;return u<o+10&&u>a&&c>s}function OH(t,r,a){var o=r.start(t),s=r.end(t),u=a.eq(o,s,"minutes")&&a.diff(o,s,"minutes")===0;u&&(s=a.add(s,1,"day"));var c=a.diff(o,s,"milliseconds");return{start:o,end:s,duration:c}}var CH=$S.default,Xr=xt.default;Object.defineProperty(xc,"__esModule",{value:!0});xc.default=void 0;var Ir=Xr(Qr),PH=Xr(ja),MH=Xr(Ia),NH=Xr(Jo),AH=Xr(Zo),xa=Xr(te),RH=Ya,Zd=rH,FH=Xr(aH),Yr=CH(Sn),zH=Xr(Tc),rr=cr,hk=function(t){function r(){var a;(0,PH.default)(this,r);for(var o=arguments.length,s=new Array(o),u=0;u<o;u++)s[u]=arguments[u];return a=(0,NH.default)(this,r,[].concat(s)),a.handleMove=function(c,d){if(!(0,rr.pointInColumn)(d,c))return a.reset();var v=a.context.draggable.dragAndDropAction.event,h=a.props,m=h.accessors,g=h.slotMetrics,y=g.closestSlotFromPoint({y:c.y-a.eventOffsetTop,x:c.x},d),w=(0,rr.eventTimes)(v,m,a.props.localizer),k=w.duration,S=a.props.localizer.add(y,k,"milliseconds");a.update(v,g.getRange(y,S,!1,!0))},a.handleDropFromOutside=function(c,d){var v=a.props,h=v.slotMetrics,m=v.resource,g=h.closestSlotFromPoint({y:c.y,x:c.x},d),y=a._calculateDnDEnd(g);a.context.draggable.onDropFromOutside({start:g,end:y,allDay:!1,resource:m})},a.handleDragOverFromOutside=function(c,d){var v=a.props.slotMetrics,h=v.closestSlotFromPoint({y:c.y,x:c.x},d),m=a._calculateDnDEnd(h),g=a.context.draggable.dragFromOutsideItem();a.update(g,v.getRange(h,m,!1,!0))},a._calculateDnDEnd=function(c){var d=a.props,v=d.accessors,h=d.slotMetrics,m=d.localizer,g=a.context.draggable.dragFromOutsideItem(),y=(0,rr.eventTimes)(g,v,m),w=y.duration,k=h.nextSlot(c),S=!isNaN(w);if(S){var E=m.add(c,w,"milliseconds");k=new Date(Math.max(E,k))}return k},a.updateParentScroll=function(c,d){setTimeout(function(){var v=(0,FH.default)(d,".rbc-addons-dnd-drag-preview")[0];v&&(v.offsetTop<c.scrollTop?(0,Zd.scrollTop)(c,Math.max(v.offsetTop,0)):v.offsetTop+v.offsetHeight>c.scrollTop+c.clientHeight&&(0,Zd.scrollTop)(c,Math.min(v.offsetTop-c.offsetHeight+v.offsetHeight,c.scrollHeight)))})},a._selectable=function(){var c=a.ref.current,d=c.children[0],v=!1,h=a._selector=new Yr.default(function(){return c.closest(".rbc-time-view")}),m=(0,Zd.scrollParent)(c);h.on("beforeSelect",function(g){var y=a.context.draggable.dragAndDropAction;if(!y.action)return!1;if(y.action==="resize")return(0,rr.pointInColumn)((0,Yr.getBoundsForNode)(d),g);var w=(0,Yr.getEventNodeFromPoint)(d,g);if(!w)return!1;a.eventOffsetTop=g.y-(0,Yr.getBoundsForNode)(w).top}),h.on("selecting",function(g){var y=(0,Yr.getBoundsForNode)(d),w=a.context.draggable.dragAndDropAction;w.action==="move"&&(a.updateParentScroll(m,d),a.handleMove(g,y)),w.action==="resize"&&(a.updateParentScroll(m,d),a.handleResize(g,y))}),h.on("dropFromOutside",function(g){if(a.context.draggable.onDropFromOutside){var y=(0,Yr.getBoundsForNode)(d);(0,rr.pointInColumn)(y,g)&&a.handleDropFromOutside(g,y)}}),h.on("dragOverFromOutside",function(g){var y=a.context.draggable.dragFromOutsideItem?a.context.draggable.dragFromOutsideItem():null;if(y){var w=(0,Yr.getBoundsForNode)(d);if(!(0,rr.pointInColumn)(w,g))return a.reset();a.handleDragOverFromOutside(g,w)}}),h.on("selectStart",function(){v=!0,a.context.draggable.onStart()}),h.on("select",function(g){var y=(0,Yr.getBoundsForNode)(d);v=!1;var w=a.context.draggable.dragAndDropAction;if(w.action==="resize")a.handleInteractionEnd();else{if(!a.state.event||!(0,rr.pointInColumn)(y,g))return;a.handleInteractionEnd()}}),h.on("click",function(){v&&a.reset(),a.context.draggable.onEnd(null)}),h.on("reset",function(){a.reset(),a.context.draggable.onEnd(null)})},a.handleInteractionEnd=function(){var c=a.props.resource,d=a.state.event;a.reset(),a.context.draggable.onEnd({start:d.start,end:d.end,resourceId:c})},a._teardownSelectable=function(){a._selector&&(a._selector.teardown(),a._selector=null)},a.state={},a.ref=xa.default.createRef(),a}return(0,AH.default)(r,t),(0,MH.default)(r,[{key:"componentDidMount",value:function(){this._selectable()}},{key:"componentWillUnmount",value:function(){this._teardownSelectable()}},{key:"reset",value:function(){this.state.event&&this.setState({event:null,top:null,height:null})}},{key:"update",value:function(o,s){var u=s.startDate,c=s.endDate,d=s.top,v=s.height,h=this.state.event;h&&u===h.start&&c===h.end||this.setState({top:d,height:v,event:(0,Ir.default)((0,Ir.default)({},o),{},{start:u,end:c})})}},{key:"handleResize",value:function(o,s){var u=this.props,c=u.accessors,d=u.slotMetrics,v=u.localizer,h=this.context.draggable.dragAndDropAction,m=h.event,g=h.direction,y=d.closestSlotFromPoint(o,s),w=(0,rr.eventTimes)(m,c,v),k=w.start,S=w.end,E;if(g==="UP"){var x=v.min(y,d.closestSlotFromDate(S,-1));E=d.getRange(x,S),E=(0,Ir.default)((0,Ir.default)({},E),{},{endDate:S})}else if(g==="DOWN"){var _=v.max(y,d.closestSlotFromDate(k));E=d.getRange(k,_),E=(0,Ir.default)((0,Ir.default)({},E),{},{startDate:k})}this.update(m,E)}},{key:"renderContent",value:function(){var o=this.props,s=o.children,u=o.accessors,c=o.components,d=o.getters,v=o.slotMetrics,h=o.localizer,m=this.state,g=m.event,y=m.top,w=m.height;if(!g)return s;var k=s.props.children,S=g.start,E=g.end,x,_="eventTimeRangeFormat",F=v.startsBeforeDay(S),C=v.startsAfterDay(E);return F?_="eventTimeRangeEndFormat":C&&(_="eventTimeRangeStartFormat"),F&&C?x=h.messages.allDay:x=h.format({start:S,end:E},_),xa.default.cloneElement(s,{children:xa.default.createElement(xa.default.Fragment,null,k,g&&xa.default.createElement(zH.default,{event:g,label:x,className:"rbc-addons-dnd-drag-preview",style:{top:y,height:w,width:100},getters:d,components:c,accessors:(0,Ir.default)((0,Ir.default)({},u),rr.dragAccessors),continuesPrior:F,continuesAfter:C}))})}},{key:"render",value:function(){return xa.default.createElement("div",{ref:this.ref},this.renderContent())}}])}(xa.default.Component);hk.contextType=RH.DnDContext;xc.default=hk;var Oc={},mk={exports:{}},gk={exports:{}},yk={exports:{}};(function(t){function r(a,o){(o==null||o>a.length)&&(o=a.length);for(var s=0,u=Array(o);s<o;s++)u[s]=a[s];return u}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports})(yk);var bk=yk.exports;(function(t){var r=bk;function a(o){if(Array.isArray(o))return r(o)}t.exports=a,t.exports.__esModule=!0,t.exports.default=t.exports})(gk);var LH=gk.exports,wk={exports:{}};(function(t){function r(a){if(typeof Symbol<"u"&&a[Symbol.iterator]!=null||a["@@iterator"]!=null)return Array.from(a)}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports})(wk);var jH=wk.exports,Sk={exports:{}};(function(t){var r=bk;function a(o,s){if(o){if(typeof o=="string")return r(o,s);var u={}.toString.call(o).slice(8,-1);return u==="Object"&&o.constructor&&(u=o.constructor.name),u==="Map"||u==="Set"?Array.from(o):u==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(u)?r(o,s):void 0}}t.exports=a,t.exports.__esModule=!0,t.exports.default=t.exports})(Sk);var IH=Sk.exports,kk={exports:{}};(function(t){function r(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports})(kk);var YH=kk.exports;(function(t){var r=LH,a=jH,o=IH,s=YH;function u(c){return r(c)||a(c)||o(c)||s()}t.exports=u,t.exports.__esModule=!0,t.exports.default=t.exports})(mk);var Ek=mk.exports,Cc={},Pc={},Mc={},Gr=xt.default;Object.defineProperty(Mc,"__esModule",{value:!0});Mc.default=void 0;var ow=Gr(Qr),HH=Gr(yp),BH=Gr(ja),WH=Gr(Ia),VH=Gr(Jo),UH=Gr(Zo),sl=Gr(te),$H=Gr(es),KH=["style","className","event","selected","isAllDay","onSelect","onDoubleClick","onKeyPress","localizer","continuesPrior","continuesAfter","accessors","getters","children","components","slotStart","slotEnd"],qH=function(t){function r(){return(0,BH.default)(this,r),(0,VH.default)(this,r,arguments)}return(0,UH.default)(r,t),(0,WH.default)(r,[{key:"render",value:function(){var o=this.props,s=o.style,u=o.className,c=o.event,d=o.selected,v=o.isAllDay,h=o.onSelect,m=o.onDoubleClick,g=o.onKeyPress,y=o.localizer,w=o.continuesPrior,k=o.continuesAfter,S=o.accessors,E=o.getters,x=o.children,_=o.components,F=_.event,C=_.eventWrapper,A=o.slotStart,B=o.slotEnd,q=(0,HH.default)(o,KH);delete q.resizable;var Q=S.title(c),I=S.tooltip(c),V=S.end(c),J=S.start(c),le=S.allDay(c),ae=v||le||y.diff(J,y.ceil(V,"day"),"day")>1,fe=E.eventProp(c,J,V,d),me=sl.default.createElement("div",{className:"rbc-event-content",title:I||void 0},F?sl.default.createElement(F,{event:c,continuesPrior:w,continuesAfter:k,title:Q,isAllDay:le,localizer:y,slotStart:A,slotEnd:B}):Q);return sl.default.createElement(C,Object.assign({},this.props,{type:"date"}),sl.default.createElement("div",Object.assign({},q,{style:(0,ow.default)((0,ow.default)({},fe.style),s),className:(0,$H.default)("rbc-event",u,fe.className,{"rbc-selected":d,"rbc-event-allday":ae,"rbc-event-continues-prior":w,"rbc-event-continues-after":k}),onClick:function(ie){return h&&h(c,ie)},onDoubleClick:function(ie){return m&&m(c,ie)},onKeyDown:function(ie){return g&&g(c,ie)}}),typeof x=="function"?x(me):me))}}])}(sl.default.Component);Mc.default=qH;var Jr={},QH=xt.default;Object.defineProperty(Jr,"__esModule",{value:!0});Jr.dateCellSelection=ZH;Jr.getSlotAtX=Dk;Jr.isSelected=GH;Jr.pointInBox=JH;Jr.slotWidth=kp;var XH=QH(R0);function GH(t,r){return!t||r==null?!1:(0,XH.default)(t,r)}function kp(t,r){var a=t.right-t.left,o=a/r;return o}function Dk(t,r,a,o){var s=kp(t,o);return a?o-1-Math.floor((r-t.left)/s):Math.floor((r-t.left)/s)}function JH(t,r){var a=r.x,o=r.y;return o>=t.top&&o<=t.bottom&&a>=t.left&&a<=t.right}function ZH(t,r,a,o,s){var u=-1,c=-1,d=o-1,v=kp(r,o),h=Dk(r,a.x,s,o),m=r.top<a.y&&r.bottom>a.y,g=r.top<t.y&&r.bottom>t.y,y=t.y>r.bottom,w=r.top>t.y,k=a.top<r.top&&a.bottom>r.bottom;return k&&(u=0,c=d),m&&(w?(u=0,c=h):y&&(u=h,c=d)),g&&(u=c=s?d-Math.floor((t.x-r.left)/v):Math.floor((t.x-r.left)/v),m?h<u?u=h:c=h:t.y<a.y?c=d:u=0),{startIdx:u,endIdx:c}}var Ep=xt.default;Object.defineProperty(Pc,"__esModule",{value:!0});Pc.default=void 0;var On=Ep(_v),iw=Ep(te),eB=Ep(Mc),tB=Jr;Pc.default={propTypes:{slotMetrics:On.default.object.isRequired,selected:On.default.object,isAllDay:On.default.bool,accessors:On.default.object.isRequired,localizer:On.default.object.isRequired,components:On.default.object.isRequired,getters:On.default.object.isRequired,onSelect:On.default.func,onDoubleClick:On.default.func,onKeyPress:On.default.func},defaultProps:{segments:[],selected:{}},renderEvent:function(r,a){var o=r.selected;r.isAllDay;var s=r.accessors,u=r.getters,c=r.onSelect,d=r.onDoubleClick,v=r.onKeyPress,h=r.localizer,m=r.slotMetrics,g=r.components,y=r.resizable,w=m.continuesPrior(a),k=m.continuesAfter(a);return iw.default.createElement(eB.default,{event:a,getters:u,localizer:h,accessors:s,components:g,onSelect:c,onDoubleClick:d,onKeyPress:v,continuesPrior:w,continuesAfter:k,slotStart:m.first,slotEnd:m.last,selected:(0,tB.isSelected)(a,o),resizable:y})},renderSpan:function(r,a,o){var s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:" ",u=Math.abs(a)/r*100+"%";return iw.default.createElement("div",{key:o,className:"rbc-row-segment",style:{WebkitFlexBasis:u,flexBasis:u,maxWidth:u}},s)}};var Zr=xt.default;Object.defineProperty(Cc,"__esModule",{value:!0});Cc.default=void 0;var nB=Zr(Qr),rB=Zr(ja),aB=Zr(Ia),oB=Zr(Jo),iB=Zr(Zo),lB=Zr(es),lw=Zr(te),Mu=Zr(Pc),xk=function(t){function r(){return(0,rB.default)(this,r),(0,oB.default)(this,r,arguments)}return(0,iB.default)(r,t),(0,aB.default)(r,[{key:"render",value:function(){var o=this,s=this.props,u=s.segments,c=s.slotMetrics.slots,d=s.className,v=1;return lw.default.createElement("div",{className:(0,lB.default)(d,"rbc-row")},u.reduce(function(h,m,g){var y=m.event,w=m.left,k=m.right,S=m.span,E="_lvl_"+g,x=w-v,_=Mu.default.renderEvent(o.props,y);return x&&h.push(Mu.default.renderSpan(c,x,"".concat(E,"_gap"))),h.push(Mu.default.renderSpan(c,S,E,_)),v=k+1,h},[]))}}])}(lw.default.Component);xk.defaultProps=(0,nB.default)({},Mu.default.defaultProps);Cc.default=xk;var fr={},Tk=xt.default;Object.defineProperty(fr,"__esModule",{value:!0});fr.endOfRange=_k;fr.eventLevels=cB;fr.eventSegments=uB;fr.inRange=fB;fr.segsOverlap=Ok;fr.sortEvents=Ev;fr.sortWeekEvents=dB;var ev=Tk(Ek),sB=Tk(I0);function _k(t){var r=t.dateRange,a=t.unit,o=a===void 0?"day":a,s=t.localizer;return{first:r[0],last:s.add(r[r.length-1],1,o)}}function uB(t,r,a,o){var s=_k({dateRange:r,localizer:o}),u=s.first,c=s.last,d=o.diff(u,c,"day"),v=o.max(o.startOf(a.start(t),"day"),u),h=o.min(o.ceil(a.end(t),"day"),c),m=(0,sB.default)(r,function(y){return o.isSameDate(y,v)}),g=o.diff(v,h,"day");return g=Math.min(g,d),g=Math.max(g-o.segmentOffset,1),{event:t,span:g,left:m+1,right:Math.max(m+g,1)}}function cB(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1/0,a,o,s,u=[],c=[];for(a=0;a<t.length;a++){for(s=t[a],o=0;o<u.length&&Ok(s,u[o]);o++);o>=r?c.push(s):(u[o]||(u[o]=[])).push(s)}for(a=0;a<u.length;a++)u[a].sort(function(d,v){return d.left-v.left});return{levels:u,extra:c}}function fB(t,r,a,o,s){var u={start:o.start(t),end:o.end(t)},c={start:r,end:a};return s.inEventRange({event:u,range:c})}function Ok(t,r){return r.some(function(a){return a.left<=t.right&&a.right>=t.left})}function dB(t,r,a){var o=(0,ev.default)(t),s=[],u=[];o.forEach(function(v){var h=r.start(v),m=r.end(v);a.daySpan(h,m)>1?s.push(v):u.push(v)});var c=s.sort(function(v,h){return Ev(v,h,r,a)}),d=u.sort(function(v,h){return Ev(v,h,r,a)});return[].concat((0,ev.default)(c),(0,ev.default)(d))}function Ev(t,r,a,o){var s={start:a.start(t),end:a.end(t),allDay:a.allDay(t)},u={start:a.start(r),end:a.end(r),allDay:a.allDay(r)};return o.sortEvents({evtA:s,evtB:u})}var vB=$S.default,ea=xt.default;Object.defineProperty(Oc,"__esModule",{value:!0});Oc.default=void 0;var wu=ea(Qr),pB=ea(Ek),hB=ea(ja),mB=ea(Ia),gB=ea(Jo),yB=ea(Zo),Su=ea(te),bB=ea(Cc),Oo=vB(Sn),wB=fr,Hr=Jr,ku=cr,SB=Ya,Ck=function(t){function r(){var a;(0,hB.default)(this,r);for(var o=arguments.length,s=new Array(o),u=0;u<o;u++)s[u]=arguments[u];return a=(0,gB.default)(this,r,[].concat(s)),a.handleMove=function(c,d,v){if(!(0,Hr.pointInBox)(d,c))return a.reset();var h=a.context.draggable.dragAndDropAction.event||v,m=a.props,g=m.accessors,y=m.slotMetrics,w=m.rtl,k=m.localizer,S=(0,Hr.getSlotAtX)(d,c.x,w,y.slots),E=y.getDateForSlot(S),x=(0,ku.eventTimes)(h,g,k),_=x.start,F=x.duration;_=k.merge(E,_);var C=k.add(_,F,"milliseconds");a.update(h,_,C)},a.handleDropFromOutside=function(c,d){if(a.context.draggable.onDropFromOutside){var v=a.props,h=v.slotMetrics,m=v.rtl,g=v.localizer,y=(0,Hr.getSlotAtX)(d,c.x,m,h.slots),w=h.getDateForSlot(y);a.context.draggable.onDropFromOutside({start:w,end:g.add(w,1,"day"),allDay:!1})}},a.handleDragOverFromOutside=function(c,d){var v=a.context.draggable.dragFromOutsideItem?a.context.draggable.dragFromOutsideItem():null;v&&a.handleMove(c,d,v)},a._selectable=function(){var c=a.ref.current.closest(".rbc-month-row, .rbc-allday-cell"),d=c.closest(".rbc-month-view, .rbc-time-view"),v=c.classList.contains("rbc-month-row"),h=a._selector=new Oo.default(function(){return d},{validContainers:(0,pB.default)(v?[]:[".rbc-day-slot",".rbc-allday-cell"])});h.on("beforeSelect",function(m){var g=a.props.isAllDay,y=a.context.draggable.dragAndDropAction.action,w=(0,Oo.getBoundsForNode)(c),k=(0,Hr.pointInBox)(w,m);return y==="move"||y==="resize"&&(!g||k)}),h.on("selecting",function(m){var g=(0,Oo.getBoundsForNode)(c),y=a.context.draggable.dragAndDropAction;y.action==="move"&&a.handleMove(m,g),y.action==="resize"&&a.handleResize(m,g)}),h.on("selectStart",function(){return a.context.draggable.onStart()}),h.on("select",function(m){var g=(0,Oo.getBoundsForNode)(c);a.state.segment&&((0,Hr.pointInBox)(g,m)?a.handleInteractionEnd():a.reset())}),h.on("dropFromOutside",function(m){if(a.context.draggable.onDropFromOutside){var g=(0,Oo.getBoundsForNode)(c);(0,Hr.pointInBox)(g,m)&&a.handleDropFromOutside(m,g)}}),h.on("dragOverFromOutside",function(m){if(a.context.draggable.dragFromOutsideItem){var g=(0,Oo.getBoundsForNode)(c);a.handleDragOverFromOutside(m,g)}}),h.on("click",function(){return a.context.draggable.onEnd(null)}),h.on("reset",function(){a.reset(),a.context.draggable.onEnd(null)})},a.handleInteractionEnd=function(){var c=a.props,d=c.resourceId,v=c.isAllDay,h=a.state.segment.event;a.reset(),a.context.draggable.onEnd({start:h.start,end:h.end,resourceId:d,isAllDay:v})},a._teardownSelectable=function(){a._selector&&(a._selector.teardown(),a._selector=null)},a.state={},a.ref=Su.default.createRef(),a}return(0,yB.default)(r,t),(0,mB.default)(r,[{key:"componentDidMount",value:function(){this._selectable()}},{key:"componentWillUnmount",value:function(){this._teardownSelectable()}},{key:"reset",value:function(){this.state.segment&&this.setState({segment:null})}},{key:"update",value:function(o,s,u){var c=(0,wB.eventSegments)((0,wu.default)((0,wu.default)({},o),{},{end:u,start:s,__isPreview:!0}),this.props.slotMetrics.range,ku.dragAccessors,this.props.localizer),d=this.state.segment;d&&c.span===d.span&&c.left===d.left&&c.right===d.right||this.setState({segment:c})}},{key:"handleResize",value:function(o,s){var u=this.context.draggable.dragAndDropAction,c=u.event,d=u.direction,v=this.props,h=v.accessors,m=v.slotMetrics,g=v.rtl,y=v.localizer,w=(0,ku.eventTimes)(c,h,y),k=w.start,S=w.end,E=(0,Hr.getSlotAtX)(s,o.x,g,m.slots),x=m.getDateForSlot(E),_=(0,Hr.pointInBox)(s,o);if(d==="RIGHT"){if(_){if(m.last<k)return this.reset();y.eq(y.startOf(S,"day"),S)?S=y.add(x,1,"day"):S=x}else if(y.inRange(k,m.first,m.last)||s.bottom<o.y&&+m.first>+k)S=y.add(m.last,1,"milliseconds");else{this.setState({segment:null});return}var F=h.end(c);S=y.merge(S,F),y.lt(S,k)&&(S=F)}else if(d==="LEFT"){if(_){if(m.first>S)return this.reset();k=x}else if(y.inRange(S,m.first,m.last)||s.top>o.y&&y.lt(m.last,S))k=y.add(m.first,-1,"milliseconds");else{this.reset();return}var C=h.start(c);k=y.merge(k,C),y.gt(k,S)&&(k=C)}this.update(c,k,S)}},{key:"render",value:function(){var o=this.props,s=o.children,u=o.accessors,c=this.state.segment;return Su.default.createElement("div",{ref:this.ref,className:"rbc-addons-dnd-row-body"},s,c&&Su.default.createElement(bB.default,Object.assign({},this.props,{selected:null,className:"rbc-addons-dnd-drag-row",segments:[c],accessors:(0,wu.default)((0,wu.default)({},u),ku.dragAccessors)})))}}])}(Su.default.Component);Ck.contextType=SB.DnDContext;Oc.default=Ck;var kn=xt.default;Object.defineProperty(gp,"__esModule",{value:!0});gp.default=RB;var Eu=kn(Qr),kB=kn(yp),EB=kn(ja),DB=kn(Ia),xB=kn(Jo),TB=kn(Zo),tv=kn(te),_B=kn(es),OB=kn(Dc),CB=kn(xc),PB=kn(Oc),MB=cr,NB=Ya,AB=["selectable","elementProps","components"];function RB(t){var r=function(a){function o(){var s;(0,EB.default)(this,o);for(var u=arguments.length,c=new Array(u),d=0;d<u;d++)c[d]=arguments[d];return s=(0,xB.default)(this,o,[].concat(c)),s.defaultOnDragOver=function(v){v.preventDefault()},s.handleBeginAction=function(v,h,m){s.setState({event:v,action:h,direction:m});var g=s.props.onDragStart;g&&g({event:v,action:h,direction:m})},s.handleInteractionStart=function(){s.state.interacting===!1&&s.setState({interacting:!0})},s.handleInteractionEnd=function(v){var h=s.state,m=h.action,g=h.event;if(m&&(s.setState({action:null,event:null,interacting:!1,direction:null}),v!=null)){v.event=g;var y=s.props,w=y.onEventDrop,k=y.onEventResize;m==="move"&&w&&w(v),m==="resize"&&k&&k(v)}},s.state={interacting:!1},s}return(0,TB.default)(o,a),(0,DB.default)(o,[{key:"getDnDContextValue",value:function(){return{draggable:{onStart:this.handleInteractionStart,onEnd:this.handleInteractionEnd,onBeginAction:this.handleBeginAction,onDropFromOutside:this.props.onDropFromOutside,dragFromOutsideItem:this.props.dragFromOutsideItem,draggableAccessor:this.props.draggableAccessor,resizableAccessor:this.props.resizableAccessor,dragAndDropAction:this.state}}}},{key:"render",value:function(){var u=this.props,c=u.selectable,d=u.elementProps,v=u.components,h=(0,kB.default)(u,AB),m=this.state.interacting;delete h.onEventDrop,delete h.onEventResize,h.selectable=c?"ignoreEvents":!1,this.components=(0,MB.mergeComponents)(v,{eventWrapper:OB.default,eventContainerWrapper:CB.default,weekWrapper:PB.default});var g=this.props.onDropFromOutside?(0,Eu.default)((0,Eu.default)({},d),{},{onDragOver:this.props.onDragOver||this.defaultOnDragOver}):d;h.className=(0,_B.default)(h.className,"rbc-addons-dnd",!!m&&"rbc-addons-dnd-is-dragging");var y=this.getDnDContextValue();return tv.default.createElement(NB.DnDContext.Provider,{value:y},tv.default.createElement(t,Object.assign({},h,{elementProps:g,components:this.components})))}}])}(tv.default.Component);return r.defaultProps=(0,Eu.default)((0,Eu.default)({},t.defaultProps),{},{draggableAccessor:null,resizableAccessor:null,resizable:!0}),r}var FB=xt.default;Object.defineProperty(mp,"__esModule",{value:!0});var Pk=mp.default=void 0,zB=FB(gp);Pk=mp.default=zB.default;const Mk=Lo((t,r)=>({calendarView:"month",calendarDate:new Date,selectable:!0,resizable:!0,calendarPopup:!0,startDay:"monday",isLoading:!0,setCalendarView:a=>{r().calendarView!==a&&t({calendarView:a})},setCalendarDate:a=>{const o=r().calendarDate;(!o||!a||o.getTime()!==a.getTime())&&t({calendarDate:a})},setSelectable:a=>{r().selectable!==a&&t({selectable:a})},setResizable:a=>{r().resizable!==a&&t({resizable:a})},setCalendarPopup:a=>{r().calendarPopup!==a&&t({calendarPopup:a})},setStartDay:a=>{r().startDay!==a&&(t({startDay:a}),a==="sunday"?ee.moment.updateLocale("en",{week:{dow:0}}):ee.moment.updateLocale("en",{week:{dow:1}}))},setLoading:a=>{r().isLoading!==a&&t({isLoading:a})},saveCalendarView:a=>{const o=r().calendarView;try{a.saveLocalStorage("viewCache",JSON.stringify(o))}catch(s){console.error("Failed to save calendar view",s)}},saveCalendarDate:a=>{const o=r().calendarDate;try{a.saveLocalStorage("currentDate",JSON.stringify(o))}catch(s){console.error("Failed to save calendar date",s)}},loadStoredPreferences:a=>{try{const o=a.loadLocalStorage("viewCache");if(o){const u=JSON.parse(o);u!==r().calendarView&&t({calendarView:u})}const s=a.loadLocalStorage("currentDate");if(s){const u=new Date(JSON.parse(s));if(!isNaN(u.getTime())){const c=r().calendarDate;(!c||c.getTime()!==u.getTime())&&t({calendarDate:u})}}}catch(o){console.error("Failed to load stored preferences",o)}}}));class Wu extends ee.Modal{constructor(a,o,s,u,c,d){super(a);ye(this,"waitForClose");ye(this,"resolvePromise");ye(this,"rejectPromise");ye(this,"didSubmit",!1);ye(this,"contentComponent");ye(this,"content");ye(this,"startDate");ye(this,"endDate");ye(this,"placeholder");ye(this,"submitClickCallback",a=>this.submit());ye(this,"cancelClickCallback",a=>this.cancel());ye(this,"submitEnterCallback",a=>{a.key==="Enter"&&(a.preventDefault(),this.submit())});this.header=o,this.placeholder=s,this.content=u||"";const v=new Date;this.startDate=c||v,d?this.endDate=d:this.endDate=new Date(this.startDate.getTime()+60*60*1e3),this.waitForClose=new Promise((h,m)=>{this.resolvePromise=h,this.rejectPromise=m}),this.display(),this.open()}static Prompt(a,o,s,u,c,d){return new Wu(a,o,s,u,c,d).waitForClose}display(){this.contentEl.empty(),this.titleEl.textContent=this.header;const a=this.contentEl.createDiv();a.style.minWidth="300px",new ee.Setting(a).setName("Event content").setDesc("Enter the content for your event"),this.contentComponent=this.createInputField(a,this.placeholder,this.content),this.createDateTimePicker(a,"Start date and time",this.startDate,o=>{if(this.startDate=o,this.endDate<this.startDate){this.endDate=new Date(this.startDate.getTime()+60*60*1e3);const s=a.querySelector("#end-date-input"),u=a.querySelector("#end-time-input");s&&u&&(s.value=ee.moment(this.endDate).format("YYYY-MM-DD"),u.value=ee.moment(this.endDate).format("HH:mm"))}}),this.createDateTimePicker(a,"End date and time",this.endDate,o=>{this.endDate=o},!0),this.createButtonBar(a)}createDateTimePicker(a,o,s,u,c=!1){const d=a.createDiv();d.style.marginBottom="1rem",new ee.Setting(d).setName(o);const v=d.createDiv();v.style.display="flex",v.style.gap="10px";const h=v.createEl("input");h.type="date",h.id=c?"end-date-input":"start-date-input",h.value=ee.moment(s).format("YYYY-MM-DD"),h.style.flex="1";const m=v.createEl("input");m.type="time",m.id=c?"end-time-input":"start-time-input",m.value=ee.moment(s).format("HH:mm"),m.style.flex="1";const g=()=>{const y=h.value,w=m.value;if(y&&w){const[k,S]=w.split(":").map(Number),E=ee.moment(y).toDate();E.setHours(k,S),u(E)}};h.addEventListener("change",g),m.addEventListener("change",g)}createInputField(a,o,s){const u=new ee.TextComponent(a);return u.inputEl.style.width="100%",u.inputEl.style.marginBottom="1rem",u.setPlaceholder(o??"").setValue(s??"").onChange(c=>this.content=c).inputEl.addEventListener("keydown",this.submitEnterCallback),u}createButton(a,o,s){const u=new ee.ButtonComponent(a);return u.setButtonText(o).onClick(s),u}createButtonBar(a){const o=a.createDiv();this.createButton(o,"Create",this.submitClickCallback).setCta().buttonEl.style.marginRight="0",this.createButton(o,"Cancel",this.cancelClickCallback),o.style.display="flex",o.style.flexDirection="row-reverse",o.style.justifyContent="flex-start",o.style.marginTop="1rem"}submit(){if(!this.content){const a=this.contentEl.createDiv();a.setText("Event content cannot be empty"),a.style.color="red",a.style.marginTop="10px",setTimeout(()=>{a.remove()},2e3);return}this.didSubmit=!0,this.close()}cancel(){this.close()}resolveInput(){this.didSubmit?this.resolvePromise({content:this.content,startDate:this.startDate,endDate:this.endDate}):this.rejectPromise("No input given.")}removeInputListener(){this.contentComponent.inputEl.removeEventListener("keydown",this.submitEnterCallback)}onOpen(){super.onOpen(),this.contentComponent.inputEl.focus(),this.contentComponent.inputEl.select()}onClose(){super.onClose(),this.resolveInput(),this.removeInputListener()}static async createEvent(a,o,s,u,c){try{const d=await Wu.Prompt(a,o,s,"",u,c);return await nt.createEvent(d.content,d.startDate,d.endDate)}catch(d){return console.error("Event creation cancelled or failed:",d),null}}}Lo(t=>({version:"0.3.0",initialized:!1,setInitialized:r=>t({initialized:r})}));const LB=()=>{const t=vt(r=>r.events);return te.useMemo(()=>t,[t])},jB=()=>Pn(t=>t.app),IB=()=>{const t=St(o=>o.hash),r=St(o=>o.query),a=te.useRef({hash:t,query:JSON.stringify(r)});return te.useMemo(()=>{const o=a.current,s=JSON.stringify(r);return(t!==o.hash||s!==o.query)&&(a.current={hash:t,query:s}),{hash:t,query:r}},[t,r])},YB=t=>{const{localizer:{messages:r},label:a,view:o,views:s,onNavigate:u,onView:c,date:d}=t,v=Pn(w=>w.app),h=te.useCallback(w=>{u(w)},[u]),m=te.useCallback(w=>{c(w)},[c]),g=te.useCallback(async()=>{if(!v)return;const w=new HB(v,k=>{u("DATE",k)});w.setInitialDate(d),w.open()},[v,u,d]),y=te.useCallback(()=>{const w=["month","week","day","agenda"];return w.length>1?w.map(k=>ue.jsx("button",{type:"button",className:o===k?"rbc-active":"",onClick:()=>m(k),children:r[k]},k)):null},[s,o,r,m]);return ue.jsxs("div",{className:"rbc-toolbar",children:[ue.jsxs("span",{className:"rbc-btn-group",children:[ue.jsx("button",{type:"button",onClick:()=>h("TODAY"),children:r.today}),ue.jsx("button",{type:"button",onClick:()=>h("PREV"),children:r.previous}),ue.jsx("button",{type:"button",onClick:()=>h("NEXT"),children:r.next})]}),ue.jsxs("span",{"aria-label":re("Select a date"),className:"rbc-toolbar-label",onClick:g,style:{cursor:"pointer"},children:[ue.jsx("span",{ref:w=>{w&&ee.setIcon(w,"calendar-search")}}),a]}),ue.jsx("span",{className:"rbc-btn-group",children:y()})]})};class HB extends ee.Modal{constructor(a,o){super(a);ye(this,"onDateSelected");ye(this,"initialDate");this.onDateSelected=o,this.initialDate=new Date}setInitialDate(a){return this.initialDate=a,this}onOpen(){this.titleEl.setText(re("Select a date"));const a=this.contentEl.createDiv();a.className="date-picker-container";const o=a.createDiv({cls:"current-date-display"}),s=m=>{o.setText(ee.moment(m).format("dddd, MMMM D, YYYY"))};s(this.initialDate);const u=a.createEl("input",{cls:"date-input"});u.type="date",u.value=ee.moment(this.initialDate).format("YYYY-MM-DD"),u.addEventListener("change",()=>{s(ee.moment(u.value).toDate())}),a.createEl("button",{text:re("Today"),cls:"today-button mod-cta"}).addEventListener("click",()=>{const m=new Date;u.value=ee.moment(m).format("YYYY-MM-DD"),s(m)});const d=a.createDiv({cls:"buttons-container"});d.createEl("button",{text:re("Cancel"),cls:"mod-warning"}).addEventListener("click",()=>{this.close()}),d.createEl("button",{text:re("Go to date"),cls:"mod-cta"}).addEventListener("click",()=>{const m=ee.moment(u.value).toDate();this.onDateSelected(m),this.close()}),this.scope.register([],"Enter",m=>{const g=ee.moment(u.value).toDate();return this.onDateSelected(g),this.close(),!1}),setTimeout(()=>{u.focus()},10)}onClose(){this.contentEl.empty()}}class BB extends ee.Modal{constructor(a,o,s){super(a);ye(this,"event");ye(this,"result");ye(this,"onSave");this.event=o,this.onSave=s,this.result={title:o.title,eventType:o.eventType||"default",startDate:new Date(o.start),endDate:new Date(o.end),allDay:o.allDay}}onOpen(){const{contentEl:a,titleEl:o}=this;o.setText("Edit Event");const s=a.createDiv({cls:"event-edit-form"});new ee.Setting(s).setName("Title").setDesc("Event title").addText(v=>{v.setValue(this.result.title).onChange(h=>{this.result.title=h})}),new ee.Setting(s).setName("Event Type").setDesc("Type of event").addDropdown(v=>{[{value:"default",name:"Default"},{value:"TASK-TODO",name:"To-Do"},{value:"TASK-DONE",name:"Done"},{value:"TASK-IN_PROGRESS",name:"In Progress"},{value:"TASK-IMPORTANT",name:"Important"}].forEach(m=>{v.addOption(m.value,m.name)}),v.setValue(this.result.eventType).onChange(m=>{this.result.eventType=m})}),new ee.Setting(s).setName("All Day").setDesc("Is this an all-day event?").addToggle(v=>{v.setValue(this.result.allDay).onChange(h=>{this.result.allDay=h})}),new ee.Setting(s).setName("Start Date").setDesc("When the event starts").addText(v=>{const h=ee.moment(this.result.startDate).format("YYYY-MM-DD HH:mm");v.setValue(h).onChange(m=>{const g=ee.moment(m,"YYYY-MM-DD HH:mm").toDate();isNaN(g.getTime())||(this.result.startDate=g)})}),new ee.Setting(s).setName("End Date").setDesc("When the event ends").addText(v=>{const h=ee.moment(this.result.endDate).format("YYYY-MM-DD HH:mm");v.setValue(h).onChange(m=>{const g=ee.moment(m,"YYYY-MM-DD HH:mm").toDate();isNaN(g.getTime())||(this.result.endDate=g)})});const u=a.createDiv({cls:"event-edit-buttons"});u.createEl("button",{text:"Save",cls:"mod-cta"}).addEventListener("click",()=>{this.onSave(this.result),this.close()}),u.createEl("button",{text:"Cancel"}).addEventListener("click",()=>{this.close()})}onClose(){const{contentEl:a}=this;a.empty()}}const WB=({event:t,title:r,continuesPrior:a,continuesAfter:o,localizer:s,slotStart:u,slotEnd:c})=>{const d=Pn(S=>S.app),v=t,h=!t.eventType||t.eventType==="default",m=Yo(t.eventType),g=S=>{S.preventDefault();const E=new ee.Menu;E.addItem(_=>{_.setTitle("Edit event").setIcon("pencil").onClick(()=>{if(!d){console.error("App instance not available");return}new BB(d,t,C=>{nt.editEvent({...t,eventType:C.eventType,allDay:C.allDay},C.startDate,C.endDate,C.title).then(A=>{A||console.error("Failed to update event")}).catch(A=>{console.error("Error updating event:",A)})}).open()})}),E.addItem(_=>{_.setTitle("Duplicate event").setIcon("copy").onClick(async()=>{try{const F=await nt.createEvent(t.title,t.start,t.end)}catch(F){console.error("Error duplicating event:",F)}})}),E.addSeparator(),[{type:"default",name:"Default"},{type:"TASK-TODO",name:"To-Do"},{type:"TASK-DONE",name:"Done"},{type:"TASK-IN_PROGRESS",name:"In Progress"},{type:"TASK-IMPORTANT",name:"Important"}].forEach(_=>{E.addItem(F=>{const C=t.eventType===_.type;F.setTitle(`Set type: ${_.name}`).setChecked(C).setIcon(_.type==="default"?"pencil":"check").onClick(async()=>{if(!C)try{const A=await nt.updateEvent(t.id,t.title,_.type,t.start,t.end)}catch(A){console.error("Error changing event type:",A)}})})}),E.addSeparator(),E.addItem(_=>{_.setWarning(!0).setTitle("Delete event").setIcon("trash").onClick(async()=>{try{await nt.deleteEventById(t.id)}catch(F){console.error("Error deleting event:",F)}})}),E.showAtMouseEvent(S.nativeEvent)},y=()=>{let S="";if(t.start){const E=new Date(t.start);S+=` 🛫 ${ee.moment(E).format("YYYY-MM-DD")}`}if(t.end){const E=new Date(t.end),x=new Date(t.start);(x.toDateString()!==E.toDateString()||t.allDay&&E.getTime()-x.getTime()>864e5)&&(S+=` 📅 ${ee.moment(E).format("YYYY-MM-DD")}`)}return S},w=()=>{const S=typeof r=="string"?r:"Event",E=y();return`${S}${E}`},k=()=>({display:"flex",flexDirection:"row",alignItems:"center",padding:"var(--size-2-1) var(--size-2-2)",height:"100%",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"});return ue.jsxs("div",{style:k(),className:`rbc-event-content ${t.eventType||"Default"}`,"aria-label":w(),onContextMenu:g,children:[!h&&ue.jsx("span",{className:"event-type-marker",children:ue.jsx("input",{"data-task":m,readOnly:!0,checked:t.eventType!=="TASK-TODO",type:"checkbox",className:"task-list-item-checkbox"})}),ue.jsx("span",{className:"event-title",children:r}),t.allDay===!1&&v.showTime!==!1&&ue.jsx("span",{className:"event-time",children:ee.moment(t.start).format("HH:mm")})]})},Du=R.memo(({iconName:t,size:r=16,className:a="",tooltip:o=""})=>{const s=R.useRef(null);return R.useEffect(()=>{s.current&&(s.current.empty(),ee.setIcon(s.current,t))},[t]),ue.jsx("div",{ref:s,style:{width:r,height:r},"aria-label":o,className:`obsidian-icon ${a}`})}),VB=({onFilterChange:t})=>{const[r,a]=te.useState(""),[o,s]=te.useState(""),[u,c]=te.useState(""),[d,v]=te.useState([]),[h,m]=te.useState(!1);te.useEffect(()=>{const S=Vr.getState().pluginSetting;S!=null&&S.WorkspaceFilters&&v(S.WorkspaceFilters.filter(x=>x.isEnabled));const E=or.getState();a(E.query.filter||""),s(E.query.contentRegex||""),c(E.query.eventType||"")},[]);const g=S=>{a(S);const E=d.find(_=>_.id===S),x=E&&(E.metadataKeys.length>0||Object.keys(E.metadataValues).length>0);or.setEventFilter(S),t(x?"metadata":"client"),m(!0)},y=S=>{const E=S.target.value;s(E),or.setContentRegex(E),t("client"),m(!0)},w=S=>{const E=S.target.value;c(E),or.setEventTypeQuery(E),t("client"),m(!0)},k=()=>{const S=d.find(x=>x.id===r),E=S&&(S.metadataKeys.length>0||Object.keys(S.metadataValues).length>0);a(""),s(""),c(""),or.clearQuery(),t(E?"metadata":"client"),m(!1)};return ue.jsxs("div",{className:"calendar-filter-component",children:[ue.jsxs("div",{className:"filter-section",children:[ue.jsx(Du,{iconName:"telescope",tooltip:re("Workspace Filter")}),ue.jsxs("select",{value:r,onChange:S=>g(S.target.value),className:"filter-select",children:[ue.jsx("option",{value:"",children:re("None")}),d.map(S=>ue.jsx("option",{value:S.id,children:S.name},S.id))]})]}),ue.jsxs("div",{className:"filter-section",children:[ue.jsx(Du,{iconName:"calendar-fold",tooltip:re("Event Type")}),ue.jsxs("select",{value:u?[u]:[],onChange:S=>{const E=S.target.value;w({target:{value:E}})},className:"filter-select",children:[ue.jsx("option",{value:"",children:re("All")}),ue.jsx("option",{value:"default",children:re("Default")}),ue.jsx("option",{value:"TASK-TODO",children:re("Task - Todo")}),ue.jsx("option",{value:"TASK-DONE",children:re("Task - Done")}),ue.jsx("option",{value:"TASK-CANCELLED",children:re("Task - Cancelled")}),ue.jsx("option",{value:"TASK-FORWARDED",children:re("Task - Forwarded")}),ue.jsx("option",{value:"TASK-DEFERRED",children:re("Task - Deferred")}),ue.jsx("option",{value:"TASK-IN_PROGRESS",children:re("Task - In Progress")}),ue.jsx("option",{value:"TASK-QUESTION",children:re("Task - Question")}),ue.jsx("option",{value:"TASK-ADD",children:re("Task - Add")}),ue.jsx("option",{value:"TASK-REVIEWED",children:re("Task - Reviewed")}),ue.jsx("option",{value:"TASK-IMPORTANT",children:re("Task - Important")}),ue.jsx("option",{value:"TASK-INFO",children:re("Task - Info")}),ue.jsx("option",{value:"TASK-BOOKMARK",children:re("Task - Bookmark")}),ue.jsx("option",{value:"TASK-PRO",children:re("Task - Pro")}),ue.jsx("option",{value:"TASK-CON",children:re("Task - Con")}),ue.jsx("option",{value:"TASK-BRAINSTORMING",children:re("Task - Brainstorming")}),ue.jsx("option",{value:"TASK-EXAMPLE",children:re("Task - Example")}),ue.jsx("option",{value:"TASK-QUOTE",children:re("Task - Quote")}),ue.jsx("option",{value:"TASK-NOTE",children:re("Task - Note")}),ue.jsx("option",{value:"TASK-WIN",children:re("Task - Win")}),ue.jsx("option",{value:"TASK-LOSE",children:re("Task - Lose")})]})]}),ue.jsxs("div",{className:"filter-section",children:[ue.jsx(Du,{iconName:"regex",tooltip:re("Content Regex")}),ue.jsx("input",{type:"text",value:o,onChange:y,placeholder:re("Enter regex pattern"),className:"filter-input"})]}),h&&ue.jsxs("button",{className:"filter-button",onClick:k,children:[ue.jsx(Du,{iconName:"filter-x",size:14}),re("Clear Filters")]})]})},UB=te.memo(Pk(kY)),$B=te.forwardRef((t,r)=>{const{selectable:a,resizeable:o,defaultView:s,StartDate:u,popup:c,onEventDoubleClick:d,onEventSelect:v}=t,[h,m]=te.useState(!1),[g,y]=te.useState(!1),w=Pn(L=>L.app),k=LB(),{calendarView:S,calendarDate:E,selectable:x,resizable:_,calendarPopup:F,setCalendarView:C,setCalendarDate:A,setSelectable:B,setResizable:q,setCalendarPopup:Q,setStartDay:I,setLoading:V,loadStoredPreferences:J,saveCalendarView:le,saveCalendarDate:ae}=Mk(),fe=te.useMemo(()=>OY(ee.moment),[]);te.useEffect(()=>{(async()=>{var G;if(w){y(!0);try{await nt.fetchAllEvents(w);const ne=or.getState().query;if(ne){const ve={eventType:ne.eventType||void 0,contentRegex:ne.contentRegex||void 0,folderPaths:((G=ne.folderPaths)==null?void 0:G.length)>0?ne.folderPaths:void 0,contentText:ne.text||""};nt.filterEvents(ve)}}catch(ne){console.error("Error loading events:",ne)}finally{y(!1)}}})()},[w]),te.useEffect(()=>{if(!w)return;const L=setTimeout(()=>{J(w),s&&s!==S&&C(s),a!==x&&B(a),o!==_&&q(o),c!==F&&Q(c),I(u==="sunday"?"sunday":"monday"),V(!1)},0);return()=>clearTimeout(L)},[w,s,S,a,x,o,_,c,F,u,J,C,B,q,Q,I,V]);const me=te.useCallback(async L=>{var G,ne;if(w)if(L==="metadata"){m(!0);try{await nt.fetchAllEvents(w);const ve=or.getState().query,ce={eventType:ve.eventType||void 0,contentRegex:ve.contentRegex||void 0,folderPaths:((G=ve.folderPaths)==null?void 0:G.length)>0?ve.folderPaths:void 0,contentText:ve.text||""};nt.filterEvents(ce)}catch(ve){console.error("Error refreshing events:",ve)}finally{m(!1)}}else{const ve=or.getState().query,ce={eventType:ve.eventType||void 0,contentRegex:ve.contentRegex||void 0,folderPaths:((ne=ve.folderPaths)==null?void 0:ne.length)>0?ve.folderPaths:void 0,contentText:ve.text||""};nt.filterEvents(ce),vt.getState().setForceUpdate()}},[w]),$=te.useCallback(L=>({className:L.eventType}),[]),ie=te.useCallback(L=>{d(L)},[d]),se=te.useCallback(async L=>{const{app:G}=Qe.getState(),ne=await Wu.Prompt(G,"Input Event","","",L.start,L.end);v(ne,L)},[v]),ge=te.useCallback(L=>{S!==L&&(C(L),w&&setTimeout(()=>{le(w)},0))},[S,C,w,le]),O=te.useCallback(L=>{E!==L&&(A(L),w&&setTimeout(()=>{ae(w)},0))},[E,A,w,ae]),P=te.useCallback(L=>{const{event:G,start:ne,end:ve}=L;nt.editEvent(G,ne,ve).then(ce=>{ce||console.error("Failed to resize event")}).catch(ce=>{console.error("Error resizing event:",ce)})},[]),H=te.useCallback(L=>{const{event:G,start:ne,end:ve}=L;nt.editEvent(G,ne,ve).then(ce=>{ce||console.error("Failed to update event")}).catch(ce=>{console.error("Error updating event:",ce)})},[]),W=te.useMemo(()=>({selectable:x,localizer:fe,events:k,resizable:_,defaultView:S,defaultDate:E,date:E,view:S,eventPropGetter:$,popup:F,onEventDrop:H,onEventResize:P,titleAccessor:L=>{const G=L.title;return G.replace(/\d{1,2}:\d{2}(-\d{1,2}:\d{2})?/g,"").replace(/⏲\s?\d{1,2}:\d{2}/g,"").replace(/📅\s?\d{4}-\d{2}-\d{2}/g,"").trim()||G},tooltipAccessor:L=>L.title,onView:ge,onNavigate:O,onDoubleClickEvent:ie,onSelectSlot:se,components:{toolbar:YB,event:WB}}),[x,fe,k,E,_,S,$,F,H,P,ge,O,ie,se]);return ue.jsxs("div",{className:"calendar-container",children:[ue.jsxs("div",{className:"calendar-filters",children:[ue.jsx(VB,{onFilterChange:me}),(h||g)&&ue.jsx("div",{className:"filter-refreshing",children:g?"Loading events...":"Refreshing events..."})]}),ue.jsx(UB,{...W})]})}),KB=te.memo($B),qB=async t=>await zn(async()=>{const{app:r}=Qe.getState();if(!/\d{14,}/.test(t))throw new Error("Invalid event ID format");const a=parseInt(t.slice(14)),o=t.slice(0,14),s=ee.moment(o,"YYYYMMDDHHmmss"),u=await Qe.getDailyNoteByEvent(s);if(!u)throw new Error(`Daily note not found for date: ${s.format("YYYY-MM-DD")}`);if(!ee.Platform.isMobile)await r.workspace.getLeaf(!0).openFile(u,{eState:{line:a}});else{let c=r.workspace.activeLeaf;c===null&&(c=r.workspace.getLeaf(!0)),await c.openFile(u,{eState:{line:a}})}},"Failed to show event in daily notes"),QB=()=>{const t=jB(),[r,a]=te.useState(!0),o=te.useRef(null),{calendarView:s,startDay:u}=Mk();te.useEffect(()=>{let h=!0;return(async()=>{try{await Promise.all([nt.fetchAllEvents(t),Qe.getMyAllDailyNotes()]),h&&a(!1)}catch(g){console.error(g),h&&(new ee.Notice("Failed to fetch data"),a(!1))}})(),()=>{h=!1}},[]);const c=te.useCallback(async h=>{await qB(h.id)},[]),d=te.useCallback(async(h,m)=>{try{const g=await nt.createEvent(h.content,h.startDate,h.endDate);nt.pushEvent(g)}catch(g){console.error(g)}},[]),v=te.useMemo(()=>({selectable:!0,resizeable:!0,StartDate:u,defaultView:s,popup:!0,onEventDoubleClick:c,onEventSelect:d}),[c,d,s,u]);return ue.jsx("div",{className:"big-calendar-wrapper",children:r?ue.jsx("div",{children:"Loading..."}):ue.jsx(KB,{ref:o,...v})})},XB=R.memo(QB),GB={"*":ue.jsx(XB,{})},nv=new Map,JB=t=>r=>{if(nv.has(r))return nv.get(r);let a=null;return t[r]?a=t[r]:a=t["*"],nv.set(r,a),a},ZB=JB(GB);function e8(){const t=IB(),r=te.useMemo(()=>ZB(t.hash),[t.hash]);return ue.jsx(ue.Fragment,{children:ue.jsx("section",{id:"page-wrapper",children:ue.jsx("main",{className:"content-wrapper",children:r})})})}const t8=R.memo(e8);function n8(){return ue.jsx(ue.Fragment,{children:ue.jsx(t8,{})})}const r8=te.createContext(void 0),a8=({children:t,initialView:r=void 0})=>{const[a,o]=te.useState(r),s={view:a,setView:o};return ue.jsx(r8.Provider,{value:s,children:t})};class o8 extends ee.ItemView{constructor(a,o){super(a);ye(this,"plugin");ye(this,"root",null);this.plugin=o}getDisplayText(){return"Big Calendar"}getIcon(){return"calendar-with-checkmark"}getViewType(){return ul}async onFileDeleted(a){xu(a,"day")&&(nt.clearEventsForFile(a.path),await Qe.getAllFiles())}async onFileModified(a){xu(a,"day")&&this.root&&await nt.fetchEventsFromFile(this.app,a)}onFileCreated(a){this.app.workspace.layoutReady&&this.root&&xu(a,"day")&&(Qe.getAllFiles(),nt.fetchEventsFromFile(this.app,a))}async onOpen(){this.onFileCreated=this.onFileCreated.bind(this),this.onFileDeleted=this.onFileDeleted.bind(this),this.onFileModified=this.onFileModified.bind(this),this.registerEvent(this.app.vault.on("create",this.onFileCreated)),this.registerEvent(this.app.vault.on("delete",this.onFileDeleted)),this.registerEvent(this.app.vault.on("modify",this.onFileModified));const a=this.addAction("filter",re("Filter"),()=>{ee.setIcon(a,this.contentEl.hasClass("filter-open")?"filter-x":"filter"),this.contentEl.toggleClass("filter-open",!this.contentEl.hasClass("filter-open"))});this.root=lD.createRoot(this.contentEl),this.root.render(ue.jsx(R.StrictMode,{children:ue.jsx(a8,{initialView:this,children:ue.jsx(n8,{})})}))}async onClose(){this.root&&(this.root.unmount(),this.root=null)}}const sw={changeTaskStatus:'<svg t="1637072255349" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4250" width="100" height="100"><path d="M662.75 440.612c15.863-17.426 42.848-18.693 60.274-2.83 17.426 15.861 18.693 42.847 2.831 60.273L527.617 715.832c-16.55 18.18-45 18.65-62.14 1.026l-112.064-115.23c-16.429-16.894-16.053-43.906 0.84-60.335 16.893-16.428 43.905-16.052 60.334 0.84l80.45 82.724 167.714-184.245zM697.653 256c-23.565 0-42.667-19.103-42.667-42.667s19.102-42.666 42.667-42.666h124.651c40.288 0 73.697 31.956 73.697 72.347v85.783c0 23.564-19.103 42.667-42.667 42.667s-42.666-19.103-42.666-42.667V256H697.652z m113.015 597.333V449.167c0-23.564 19.102-42.667 42.666-42.667 23.564 0 42.667 19.103 42.667 42.667v417.152c0 40.391-33.41 72.348-73.697 72.348H201.697c-40.288 0-73.697-31.957-73.697-72.348V243.014c0-40.39 33.41-72.347 73.697-72.347h124.727c23.564 0 42.667 19.102 42.667 42.666 0 23.564-19.103 42.667-42.667 42.667h-113.09v597.333h597.333z m-384-682.666c-23.564 0-42.667 19.102-42.667 42.666C384 236.897 403.103 256 426.667 256h170.666C620.897 256 640 236.897 640 213.333s-19.103-42.666-42.667-42.666H426.667z m0-85.334h170.666c70.693 0 128 57.308 128 128 0 70.693-57.307 128-128 128H426.667c-70.693 0-128-57.307-128-128 0-70.692 57.307-128 128-128z" p-id="4251" fill="currentColor"></path></svg>'};function i8(){Object.keys(sw).forEach(t=>{ee.addIcon(t,sw[t])})}const Nk={StartDate:"Sunday",InsertAfter:"# Journal",ProcessEntriesBelow:"",DefaultEventComposition:"{TIME} {CONTENT}",WorkspaceFilters:[{id:"default",name:"Default",eventTypes:[],contentRegex:"",folderPaths:[],metadataKeys:[],metadataValues:{},isEnabled:!0}],DefaultFilterId:"default"};class l8 extends ee.PluginSettingTab{constructor(a,o){super(a,o);ye(this,"plugin");ye(this,"applyDebounceTimer",0);this.plugin=o}applySettingsUpdate(){clearTimeout(this.applyDebounceTimer);const a=this.plugin;this.applyDebounceTimer=window.setTimeout(()=>{a.saveSettings()},100)}async hide(){}async display(){await this.plugin.loadSettings();const{containerEl:a}=this;this.containerEl.empty(),new ee.Setting(a).setHeading().setName(re("Regular Options")),new ee.Setting(a).setName(re("First Day of Week")).setDesc(re("Choose the first day of the week. Sunday is the default.")).addDropdown(o=>o.addOption("sunday",re("Sunday")).addOption("monday",re("Monday")).setValue(this.plugin.settings.StartDate).onChange(async s=>{this.plugin.settings.StartDate=s,this.applySettingsUpdate()})),new ee.Setting(a).setName(re("Insert after heading")).setDesc(re("You should set the same heading below if you want to insert and process events below the same heading.")).addText(o=>o.setPlaceholder("# JOURNAL").setValue(this.plugin.settings.InsertAfter).onChange(async s=>{this.plugin.settings.InsertAfter=s,this.applySettingsUpdate()})),new ee.Setting(a).setName(re("Process Events below")).setDesc(re("Only entries below this string/section in your notes will be processed. If it does not exist no notes will be processed for that file.")).addText(o=>o.setPlaceholder(Nk.ProcessEntriesBelow).setValue(this.plugin.settings.ProcessEntriesBelow).onChange(async s=>{this.plugin.settings.ProcessEntriesBelow=s,this.applySettingsUpdate()})),new ee.Setting(a).setHeading().setName(re("Workspace Filters")).addExtraButton(o=>o.setIcon("plus").onClick(()=>{const s={id:`filter-${Date.now()}`,name:`Filter ${this.plugin.settings.WorkspaceFilters.length}`,eventTypes:[],contentRegex:"",folderPaths:[],metadataKeys:[],metadataValues:{},isEnabled:!0};this.plugin.settings.WorkspaceFilters.push(s),this.applySettingsUpdate(),this.display()})),this.plugin.settings.WorkspaceFilters.forEach((o,s)=>{const u=new ee.Setting(a).setName(o.name).setDesc(re("Configure filter settings")).addExtraButton(c=>c.setIcon("pencil").onClick(()=>{this.showFilterEditModal(o,s)})).addToggle(c=>c.setValue(o.isEnabled).onChange(async d=>{this.plugin.settings.WorkspaceFilters[s].isEnabled=d,this.applySettingsUpdate()}));s>0&&u.addButton(c=>c.setButtonText(re("Delete")).onClick(()=>{this.plugin.settings.WorkspaceFilters.splice(s,1),this.applySettingsUpdate(),this.display()}))}),new ee.Setting(a).setName(re("Default Filter")).setDesc(re("Choose the default filter to apply when opening the calendar")).addDropdown(o=>{this.plugin.settings.WorkspaceFilters.forEach(s=>{o.addOption(s.id,s.name)}),o.setValue(this.plugin.settings.DefaultFilterId),o.onChange(async s=>{this.plugin.settings.DefaultFilterId=s,this.applySettingsUpdate()})}),new ee.Setting(a).setHeading().setName(re("Say Thank You")),new ee.Setting(a).setName(re("Donate")).setDesc(re("If you like this plugin, consider donating to support continued development:")).addButton(o=>{o.buttonEl.outerHTML='<a href="https://www.buymeacoffee.com/boninall"><img src="https://img.buymeacoffee.com/button-api/?text=Buy me a coffee&emoji=&slug=boninall&button_colour=6495ED&font_colour=ffffff&font_family=Inter&outline_colour=000000&coffee_colour=FFDD00"></a>'})}showFilterEditModal(a,o){new s8(this.app,a,u=>{this.plugin.settings.WorkspaceFilters[o]=u,this.applySettingsUpdate(),this.display()}).open()}}class s8 extends ee.Modal{constructor(a,o,s){super(a);ye(this,"filter");ye(this,"onSubmit");ye(this,"eventTypesEl");ye(this,"folderPathsEl");ye(this,"metadataKeysEl");ye(this,"metadataValuesEl");this.filter={...o},this.onSubmit=s}onOpen(){const{contentEl:a}=this;a.empty(),a.createEl("h2",{text:re("Edit Filter")}),new ee.Setting(a).setName(re("Filter Name")).addText(o=>{o.setValue(this.filter.name).onChange(s=>{this.filter.name=s})}),new ee.Setting(a).setName(re("Content Regex")).setDesc(re("Regular expression to match against event content")).addText(o=>{o.setValue(this.filter.contentRegex).onChange(s=>{this.filter.contentRegex=s})}),new ee.Setting(a).setName(re("Event Types")).setDesc(re("Types of events to include in this filter")).addButton(o=>{o.setIcon("plus").setTooltip(re("Add Event Type")).onClick(()=>{const s=new ee.Setting(this.eventTypesEl).addText(u=>{u.setPlaceholder(re("Enter event type"))}).addButton(u=>{u.setButtonText(re("Cancel")).onClick(()=>{s.clear(),s.settingEl.detach()})}).addButton(u=>{u.setButtonText(re("Add")).onClick(()=>{const c=s.components[0].inputEl.value;c&&!this.filter.eventTypes.includes(c)&&(this.filter.eventTypes.push(c),this.renderEventTypes())})})})}),this.eventTypesEl=a.createDiv("event-types-container"),this.renderEventTypes(),new ee.Setting(a).setName(re("Folder Paths")).setDesc(re("Folder paths to include in this filter")).addButton(o=>{o.setIcon("plus").setTooltip(re("Add Folder Path")).onClick(()=>{const s=new ee.Setting(this.folderPathsEl).addText(u=>{u.setPlaceholder(re("Enter folder path"))}).addButton(u=>{u.setButtonText(re("Cancel")).onClick(()=>{s.clear(),s.settingEl.detach()})}).addButton(u=>{u.setButtonText(re("Add")).onClick(()=>{const c=s.components[0].inputEl.value;c&&!this.filter.folderPaths.includes(c)&&(this.filter.folderPaths.push(c),this.renderFolderPaths())})})})}),this.folderPathsEl=a.createDiv("folder-paths-container"),this.renderFolderPaths(),new ee.Setting(a).setName(re("Metadata Keys")).setDesc(re("Metadata keys that should exist in the file")).addButton(o=>{o.setIcon("plus").setTooltip(re("Add Metadata Key")).onClick(()=>{const s=new ee.Setting(this.metadataKeysEl).addText(u=>{u.setPlaceholder(re("Enter metadata key"))}).addButton(u=>{u.setButtonText(re("Cancel")).onClick(()=>{s.clear(),s.settingEl.detach()})}).addButton(u=>{u.setButtonText(re("Add")).onClick(()=>{const c=s.components[0].inputEl.value;c&&!this.filter.metadataKeys.includes(c)&&(this.filter.metadataKeys.push(c),this.renderMetadataKeys())})})})}),this.metadataKeysEl=a.createDiv("metadata-keys-container"),this.renderMetadataKeys(),new ee.Setting(a).setName(re("Metadata Values")).setDesc(re("Key-value pairs for matching specific metadata values")).addButton(o=>{o.setIcon("plus").setTooltip(re("Add Metadata Value")).onClick(()=>{const s=this.metadataValuesEl.createDiv("metadata-value-pair"),u=new ee.Setting(s).setName(re("Key")).addText(d=>{d.setPlaceholder(re("Enter metadata key"))}),c=new ee.Setting(s).setName(re("Value")).addText(d=>{d.setPlaceholder(re("Enter metadata value"))});new ee.Setting(s).addButton(d=>{d.setButtonText(re("Cancel")).onClick(()=>{s.remove()})}).addButton(d=>{d.setButtonText(re("Add")).onClick(()=>{const v=u.components[0].inputEl.value,h=c.components[0].inputEl.value;v&&(this.filter.metadataValues[v]=h,this.renderMetadataValues(),s.remove())})})})}),this.metadataValuesEl=a.createDiv("metadata-values-container"),this.renderMetadataValues(),new ee.Setting(a).addButton(o=>{o.setButtonText(re("Save")).setCta().onClick(()=>{this.onSubmit(this.filter),this.close()})})}onClose(){const{contentEl:a}=this;a.empty()}renderEventTypes(){this.eventTypesEl.empty(),this.filter.eventTypes.forEach((a,o)=>{new ee.Setting(this.eventTypesEl).setName(a).addButton(s=>{s.setButtonText(re("Remove")).onClick(()=>{this.filter.eventTypes.splice(o,1),this.renderEventTypes()})}).addButton(s=>{s.setButtonText(re("Remove")).onClick(()=>{this.filter.eventTypes.splice(o,1),this.renderEventTypes()})})})}renderFolderPaths(){this.folderPathsEl.empty(),this.filter.folderPaths.forEach((a,o)=>{new ee.Setting(this.folderPathsEl).setName(a).addButton(s=>{s.setButtonText(re("Remove")).onClick(()=>{this.filter.folderPaths.splice(o,1),this.renderFolderPaths()})})})}renderMetadataKeys(){this.metadataKeysEl.empty(),this.filter.metadataKeys.forEach((a,o)=>{new ee.Setting(this.metadataKeysEl).setName(a).addButton(s=>{s.setButtonText(re("Remove")).onClick(()=>{this.filter.metadataKeys.splice(o,1),this.renderMetadataKeys()})})})}renderMetadataValues(){this.metadataValuesEl.empty(),Object.entries(this.filter.metadataValues).forEach(([a,o])=>{new ee.Setting(this.metadataValuesEl).setName(`${a}: ${o}`).addButton(s=>{s.setButtonText(re("Remove")).onClick(()=>{delete this.filter.metadataValues[a],this.renderMetadataValues()})})})}}class u8 extends ee.Plugin{constructor(){super(...arguments);ye(this,"settings")}async onload(){await this.loadSettings(),Vr.setPluginSetting(this.settings),this.app.workspace.onLayoutReady(()=>{Qe.setApp(this.app),Qe.initAllFiles(),nt.fetchAllEvents(this.app)}),this.registerView(ul,a=>new o8(a,this)),i8(),this.addRibbonIcon("changeTaskStatus","Big Calendar",()=>{new ee.Notice(re("Open big calendar successfully")),this.openCalendar()}),this.addCommand({id:"open-big-calendar",name:re("Open big calendar"),callback:()=>this.openCalendar(),hotkeys:[]}),this.addSettingTab(new l8(this.app,this))}async loadSettings(){this.settings=Object.assign({},Nk,await this.loadData())}async saveSettings(){await this.saveData(this.settings)}onunload(){this.app.workspace.detachLeavesOfType(ul),new ee.Notice(re("Close big calendar successfully"))}async openCalendar(){const a=this.app.workspace;a.detachLeavesOfType(ul);const o=a.getLeaf(!ee.Platform.isMobile&&a.activeLeaf&&a.activeLeaf.view instanceof ee.FileView);await o.setViewState({type:ul}),a.revealLeaf(o)}}module.exports=u8;

/* nosourcemap */