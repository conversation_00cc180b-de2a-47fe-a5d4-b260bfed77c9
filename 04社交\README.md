- The archive is inactive items from the other three categories.
- 存档是来自其他三个类别的非活动条目。

存档是指已经不再积极参与和维护的信息，但可能在将来有用。

例如：

- 旧的项目文件
- 过期的资源
- 不再使用的笔记

存档中的信息具有以下特点：

1. 当前已不再活跃
2. 仍可能在将来有参考价值
3. 不需要频繁查阅和维护
4. 在搜索中仍然可用

将信息存档，并非是要直接丢弃，而是调整其状态，不再积极参与更新和维护。

例如：

项目：写完的书稿

资源：已经看完但未整理的视频

领域：不再关注的兴趣爱好 

等等信息，可以分类存档。

在将来如果需要时，仍可以通过搜索等方式查找。

与被动的丢弃相比，存档可以：

1. 减少搜索难度
2. 避免信息流失
3. 提高可控度

所以存档作为 P.A.R.A 系统的一个模块，也是非常重要的。