{"FirstLoaded": false, "StartDate": "Sunday", "InsertAfter": "", "UserName": "Panda", "ProcessEntriesBelow": "# memo", "Language": "en", "SaveMemoButtonLabel": "NOTE", "SaveMemoButtonIcon": "", "ShareFooterStart": "{ThinoNum} Thino {UsedDay} Day", "ShareFooterEnd": "✍️ by {UserName}", "DefaultPrefix": "List", "DefaultTimePrefix": "HH:mm", "UseDailyOrPeriodic": "Daily", "InsertDateFormat": "Tasks", "DefaultEditorLocation": "Top", "UseButtonToShowEditor": false, "FocusOnEditor": true, "OpenDailyMemosWithMemos": true, "HideDoneTasks": false, "ShowTaskLabel": true, "AppendDateWhenTaskDone": false, "MobileAndDesktop": "All", "OpenMemosAutomatically": false, "ShowScrollbar": false, "ShowTime": true, "ShowDate": true, "AddBlankLineWhenDate": false, "AutoSaveWhenOnMobile": false, "DeleteFileName": "delete", "QueryFileName": "query", "UseVaultTags": true, "SetCustomBackgroundImage": false, "SetCustomMemoFooter": false, "DefaultLightBackgroundImage": "", "DefaultDarkBackgroundImage": "", "DefaultLightBackgroundImageForClean": "", "DefaultDarkBackgroundImageForClean": "", "DefaultMemoComposition": "{TIME} {CONTENT}", "CommentOnMemos": false, "CommentsInOriginalNotes": false, "FetchMemosMark": "#memo", "FetchMemosFromNote": false, "ShowCommentOnMemos": false, "ShowLeftSideBar": false, "MemoListView": "list", "MemoSaveLocation": "DAILY", "MemoOtherSaveLocation": {"MemoDefaultCanvasPath": "basic.thino.canvas", "MemoDefaultMultiFilePath": "<PERSON><PERSON>", "MemoDefaultSingleFilePath": "basic.thino.md"}, "MemoDailyTarget": 5, "HeatmapColorScheme": "default", "EnabledHttpApi": false, "HttpApiIpType": "127.0.0.1", "HttpApiPort": "43999", "AutoDownloadImage": false, "EditorType": "obsidian", "EnabledLocationList": [{"value": "DAILY", "target": "ProcessEntriesBelow", "insert": "InsertAfter"}], "DeleteThinoDirectly": false, "CaptureKey": {"EnableCaptureKey": false, "OpenThinoGlobally": "CommandOrControl+Shift+T", "ShowNearMouse": false}, "startSync": false, "tokenForVerify": {}, "saveThinoType": "FILE", "showDayMark": false, "dayMarkRange": "day", "navigation": false, "password": "", "needVerify": false, "addTagAutomatically": false, "addTagPosition": "End", "addTagWithNewline": false, "chatViewStyle": "default", "MomentsBackgroundImage": "https://images.pexels.com/photos/531767/pexels-photo-531767.jpeg", "MomentsIcon": "https://images.pexels.com/photos/256514/pexels-photo-256514.jpeg", "MomentsQuote": "随心而动", "DefaultThemeForThino": "classic", "LastUpdatedVersion": "2.7.9", "ShareToThinoWithText": false, "ShareToThinoWithTextAppend": "", "ShareToThinoWithTextPrepend": "", "ShareToThinoWithTextNewline": "none", "hidePinnedGroup": false, "DifferentInsertTarget": false, "InsertAfterForTask": "", "ProcessContentTarget": "custom", "InsertType": "end", "ShareAppendType": "preset", "SharePrependType": "preset", "SetFileNameAfterCreate": false, "TagForFileTypeFiles": "thino/file", "TagForMultiTypeFiles": "thino/multi", "MinHeightForShare": "200px", "ThinoMaxHeight": 0, "ThinoCollapsedHeight": 100, "OptimizeForCallout": true, "AppendOrPrependTextViaServer": false, "AppendViaServer": "", "PrependViaServer": "", "WithNewLineViaServer": "none", "SupportSelectOtherView": false, "WaitTemplaterToFinishParse": false, "NavbarButton": false, "AlwaysShowStatusText": false, "FilterByMetadata": false, "ShowSourcePath": false, "ShowUpdateMessage": true, "SyncManually": false, "MemoFixedPrefix": "", "MemoFixedSuffix": "", "UseMemoFixedStrings": false, "enableWordCount": false, "maxWordCount": 0, "enableReferenceLinksGroup": false, "doubleClickBehavior": "edit-thino", "useMobileViewDefaultHeader": false, "IgnoreFolderForMultiType": [], "UseBlockLinkWhenDragging": false, "ShowScrollToTopButton": true, "ZoomImageWhenViewing": false, "ViewArchiveInRandomReview": false, "DraggingBehavior": "block-link"}