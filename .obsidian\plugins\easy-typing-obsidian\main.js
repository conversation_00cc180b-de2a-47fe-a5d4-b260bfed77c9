/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __commonJS = (cb, mod) => function __require() {
  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
};
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target, mod));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// node_modules/sprintf-js/src/sprintf.js
var require_sprintf = __commonJS({
  "node_modules/sprintf-js/src/sprintf.js"(exports) {
    !function() {
      "use strict";
      var re = {
        not_string: /[^s]/,
        not_bool: /[^t]/,
        not_type: /[^T]/,
        not_primitive: /[^v]/,
        number: /[diefg]/,
        numeric_arg: /[bcdiefguxX]/,
        json: /[j]/,
        not_json: /[^j]/,
        text: /^[^\x25]+/,
        modulo: /^\x25{2}/,
        placeholder: /^\x25(?:([1-9]\d*)\$|\(([^)]+)\))?(\+)?(0|'[^$])?(-)?(\d+)?(?:\.(\d+))?([b-gijostTuvxX])/,
        key: /^([a-z_][a-z_\d]*)/i,
        key_access: /^\.([a-z_][a-z_\d]*)/i,
        index_access: /^\[(\d+)\]/,
        sign: /^[+-]/
      };
      function sprintf2(key) {
        return sprintf_format(sprintf_parse(key), arguments);
      }
      function vsprintf(fmt, argv) {
        return sprintf2.apply(null, [fmt].concat(argv || []));
      }
      function sprintf_format(parse_tree, argv) {
        var cursor = 1, tree_length = parse_tree.length, arg, output = "", i, k, ph, pad, pad_character, pad_length, is_positive, sign;
        for (i = 0; i < tree_length; i++) {
          if (typeof parse_tree[i] === "string") {
            output += parse_tree[i];
          } else if (typeof parse_tree[i] === "object") {
            ph = parse_tree[i];
            if (ph.keys) {
              arg = argv[cursor];
              for (k = 0; k < ph.keys.length; k++) {
                if (arg == void 0) {
                  throw new Error(sprintf2('[sprintf] Cannot access property "%s" of undefined value "%s"', ph.keys[k], ph.keys[k - 1]));
                }
                arg = arg[ph.keys[k]];
              }
            } else if (ph.param_no) {
              arg = argv[ph.param_no];
            } else {
              arg = argv[cursor++];
            }
            if (re.not_type.test(ph.type) && re.not_primitive.test(ph.type) && arg instanceof Function) {
              arg = arg();
            }
            if (re.numeric_arg.test(ph.type) && (typeof arg !== "number" && isNaN(arg))) {
              throw new TypeError(sprintf2("[sprintf] expecting number but found %T", arg));
            }
            if (re.number.test(ph.type)) {
              is_positive = arg >= 0;
            }
            switch (ph.type) {
              case "b":
                arg = parseInt(arg, 10).toString(2);
                break;
              case "c":
                arg = String.fromCharCode(parseInt(arg, 10));
                break;
              case "d":
              case "i":
                arg = parseInt(arg, 10);
                break;
              case "j":
                arg = JSON.stringify(arg, null, ph.width ? parseInt(ph.width) : 0);
                break;
              case "e":
                arg = ph.precision ? parseFloat(arg).toExponential(ph.precision) : parseFloat(arg).toExponential();
                break;
              case "f":
                arg = ph.precision ? parseFloat(arg).toFixed(ph.precision) : parseFloat(arg);
                break;
              case "g":
                arg = ph.precision ? String(Number(arg.toPrecision(ph.precision))) : parseFloat(arg);
                break;
              case "o":
                arg = (parseInt(arg, 10) >>> 0).toString(8);
                break;
              case "s":
                arg = String(arg);
                arg = ph.precision ? arg.substring(0, ph.precision) : arg;
                break;
              case "t":
                arg = String(!!arg);
                arg = ph.precision ? arg.substring(0, ph.precision) : arg;
                break;
              case "T":
                arg = Object.prototype.toString.call(arg).slice(8, -1).toLowerCase();
                arg = ph.precision ? arg.substring(0, ph.precision) : arg;
                break;
              case "u":
                arg = parseInt(arg, 10) >>> 0;
                break;
              case "v":
                arg = arg.valueOf();
                arg = ph.precision ? arg.substring(0, ph.precision) : arg;
                break;
              case "x":
                arg = (parseInt(arg, 10) >>> 0).toString(16);
                break;
              case "X":
                arg = (parseInt(arg, 10) >>> 0).toString(16).toUpperCase();
                break;
            }
            if (re.json.test(ph.type)) {
              output += arg;
            } else {
              if (re.number.test(ph.type) && (!is_positive || ph.sign)) {
                sign = is_positive ? "+" : "-";
                arg = arg.toString().replace(re.sign, "");
              } else {
                sign = "";
              }
              pad_character = ph.pad_char ? ph.pad_char === "0" ? "0" : ph.pad_char.charAt(1) : " ";
              pad_length = ph.width - (sign + arg).length;
              pad = ph.width ? pad_length > 0 ? pad_character.repeat(pad_length) : "" : "";
              output += ph.align ? sign + arg + pad : pad_character === "0" ? sign + pad + arg : pad + sign + arg;
            }
          }
        }
        return output;
      }
      var sprintf_cache = /* @__PURE__ */ Object.create(null);
      function sprintf_parse(fmt) {
        if (sprintf_cache[fmt]) {
          return sprintf_cache[fmt];
        }
        var _fmt = fmt, match, parse_tree = [], arg_names = 0;
        while (_fmt) {
          if ((match = re.text.exec(_fmt)) !== null) {
            parse_tree.push(match[0]);
          } else if ((match = re.modulo.exec(_fmt)) !== null) {
            parse_tree.push("%");
          } else if ((match = re.placeholder.exec(_fmt)) !== null) {
            if (match[2]) {
              arg_names |= 1;
              var field_list = [], replacement_field = match[2], field_match = [];
              if ((field_match = re.key.exec(replacement_field)) !== null) {
                field_list.push(field_match[1]);
                while ((replacement_field = replacement_field.substring(field_match[0].length)) !== "") {
                  if ((field_match = re.key_access.exec(replacement_field)) !== null) {
                    field_list.push(field_match[1]);
                  } else if ((field_match = re.index_access.exec(replacement_field)) !== null) {
                    field_list.push(field_match[1]);
                  } else {
                    throw new SyntaxError("[sprintf] failed to parse named argument key");
                  }
                }
              } else {
                throw new SyntaxError("[sprintf] failed to parse named argument key");
              }
              match[2] = field_list;
            } else {
              arg_names |= 2;
            }
            if (arg_names === 3) {
              throw new Error("[sprintf] mixing positional and named placeholders is not (yet) supported");
            }
            parse_tree.push({
              placeholder: match[0],
              param_no: match[1],
              keys: match[2],
              sign: match[3],
              pad_char: match[4],
              align: match[5],
              width: match[6],
              precision: match[7],
              type: match[8]
            });
          } else {
            throw new SyntaxError("[sprintf] unexpected placeholder");
          }
          _fmt = _fmt.substring(match[0].length);
        }
        return sprintf_cache[fmt] = parse_tree;
      }
      if (typeof exports !== "undefined") {
        exports["sprintf"] = sprintf2;
        exports["vsprintf"] = vsprintf;
      }
      if (typeof window !== "undefined") {
        window["sprintf"] = sprintf2;
        window["vsprintf"] = vsprintf;
        if (typeof define === "function" && define["amd"]) {
          define(function() {
            return {
              "sprintf": sprintf2,
              "vsprintf": vsprintf
            };
          });
        }
      }
    }();
  }
});

// src/main.ts
var main_exports = {};
__export(main_exports, {
  default: () => EasyTypingPlugin
});
module.exports = __toCommonJS(main_exports);
var import_obsidian3 = require("obsidian");
var import_state3 = require("@codemirror/state");

// src/core.ts
var import_obsidian = require("obsidian");

// src/utils.ts
var DEBUG = false;
var print = (message, ...optionalParams) => {
  if (DEBUG) {
    console.log(message, ...optionalParams);
  }
};
function setDebug(value) {
  DEBUG = value;
}
function offsetToPos(doc, offset) {
  let line = doc.lineAt(offset);
  return { line: line.number - 1, ch: offset - line.from };
}
function getTypeStrOfTransac(tr) {
  let TransacTypeArray = [
    "EasyTyping.change",
    "EasyTyping.paste",
    "input.type.compose",
    "input.type",
    "input.paste",
    "input.drop",
    "input.complete",
    "input",
    "delete.selection",
    "delete.forward",
    "delete.backward",
    "delete.cut",
    "delete",
    "move.drop",
    "undo",
    "redo",
    "select.pointer"
  ];
  for (let i = 0; i < TransacTypeArray.length; i++) {
    if (tr.isUserEvent(TransacTypeArray[i]))
      return TransacTypeArray[i];
  }
  return "none";
}
function string2pairstring(s) {
  let cursorIdx = findFirstPipeNotPrecededByBackslash(s);
  let left = s.substring(0, cursorIdx);
  let _left = isRegexp(left) ? left : convertEscapeChar(left);
  let right = s.substring(cursorIdx + 1);
  let _right = isRegexp(right) ? right : convertEscapeChar(right);
  return { left: _left, right: _right };
}
function replacePlaceholders(str, replacements) {
  let replace_matches = str.replace(/\[\[(\d+)\]\]/g, function(match, index) {
    return replacements[parseInt(index, 10)] || match;
  });
  return replace_matches;
}
function replacePlaceholdersAndTabstops(str, replacements) {
  let tabstops = [];
  const regex = /\$(\d+)|\$\{(\d+): *([^ {}]*?)\}|\[\[(\d+)\]\]/g;
  let match;
  let replaceStrings = [];
  while ((match = regex.exec(str)) !== null) {
    const isSimpleVar = match[1];
    const isNamedVar = match[2];
    const content = match[3];
    const replaceN = match[4];
    const tabstopN = isSimpleVar || isNamedVar;
    const startIndex = match.index;
    const endIndex = startIndex + match[0].length;
    if (replaceN) {
      let matchedN = parseInt(replaceN, 10);
      if (matchedN < replacements.length) {
        replaceStrings.push({ from: startIndex, to: endIndex, replacement: replacements[matchedN], tabstop: false });
      }
    } else {
      let n = parseInt(tabstopN, 10);
      let contentStr = replacePlaceholders(content ? content : "", replacements);
      replaceStrings.push({ from: startIndex, to: endIndex, replacement: contentStr, tabstop: true, tabstopNumber: n });
    }
  }
  let newString = str;
  let offset = 0;
  for (let i = 0; i < replaceStrings.length; i++) {
    let replaceString = replaceStrings[i];
    newString = newString.substring(0, replaceString.from + offset) + replaceString.replacement + newString.substring(replaceString.to + offset);
    if (replaceString.tabstop) {
      let tabstop = {
        from: replaceString.from + offset,
        to: replaceString.from + offset + replaceString.replacement.length,
        number: replaceString.tabstopNumber
      };
      tabstops.push(tabstop);
    }
    offset += replaceString.replacement.length - (replaceString.to - replaceString.from);
  }
  return [newString, tabstops];
}
function parseTheAfterPattern(pattern, replacements) {
  let single_cursor_pos = findFirstPipeNotPrecededByBackslash(pattern);
  let general_cursor_find = /\$(\d+)|\$\{(\d+): *([^ {}]*?)\}/.test(pattern);
  let single_cursor_find = single_cursor_pos !== -1;
  let final_pattern = pattern;
  if (general_cursor_find) {
    final_pattern = pattern;
  } else if (single_cursor_find) {
    final_pattern = pattern.substring(0, single_cursor_pos) + "$0" + pattern.substring(single_cursor_pos + 1);
  } else {
    final_pattern = pattern + "$0";
  }
  return replacePlaceholdersAndTabstops(convertEscapeChar(final_pattern), replacements);
}
function isRegexp(s) {
  return s.startsWith("r/") && s.endsWith("/");
}
function convertEscapeChar(s) {
  return s.replace(/\\\|/g, "|").replace(/\\n/g, "\n").replace(/\\r/g, "\r").replace(/\\t/g, "	").replace(/\\\n/g, "\\n").replace(/\\\r/g, "\\r").replace(/\\\t/g, "\\t").replace(/\\\\/g, "\\");
}
function ruleStringList2RuleList(list) {
  let res = [];
  for (let i in list) {
    res[i] = { before: string2pairstring(list[i][0]), after: string2pairstring(list[i][1]), after_pattern: list[i][1] };
  }
  return res;
}
function findFirstPipeNotPrecededByBackslash(s) {
  let regex = /^r\/[^]*?\/\|/;
  let regMatch = s.match(regex);
  if (regMatch)
    return regMatch[0].length - 1;
  const match = s.match(/((^|[^\\])(\\\\)*)\|/);
  return match ? s.indexOf(match[0]) + match[1].length : -1;
}
function stringDeleteAt(str, index) {
  return str.substring(0, index) + str.substring(index + 1);
}
function stringInsertAt(str, index, s) {
  return str.substring(0, index) + s + str.substring(index);
}
function isParamDefined(param) {
  return typeof param !== "undefined";
}
function showString(s) {
  return s.replace(/\n/g, "\\n");
}
function taboutCursorInPairedString(input, cursorPosition, symbolPairs) {
  let stack = [];
  let fail = { isSuccess: false, newPosition: 0 };
  for (let i = 0; i < cursorPosition; i++) {
    for (const { left: open, right: close } of symbolPairs) {
      if (input.startsWith(open, i) && (open !== close || stack.lastIndexOf(open) === -1)) {
        stack.push(open);
        i += open.length - 1;
      } else if (input.startsWith(close, i) && stack.length > 0) {
        const lastOpenIndex = stack.lastIndexOf(open);
        if (lastOpenIndex !== -1) {
          stack = stack.slice(0, lastOpenIndex);
        }
        i += close.length - 1;
      }
    }
  }
  if (stack.length === 0) {
    return fail;
  }
  let tempStack = [];
  for (let i = cursorPosition; i < input.length; i++) {
    for (const { left: open, right: close } of symbolPairs) {
      if (input.startsWith(open, i) && (open !== close || stack.lastIndexOf(open) === -1 && tempStack.lastIndexOf(open) === -1)) {
        tempStack.push(open);
        i += open.length - 1;
      } else if (input.startsWith(close, i)) {
        const lastOpenIndex = tempStack.lastIndexOf(open);
        if (lastOpenIndex === -1 && stack.lastIndexOf(open) !== -1) {
          return { isSuccess: true, newPosition: cursorPosition === i ? i + close.length : i };
        } else if (lastOpenIndex !== -1) {
          tempStack = tempStack.slice(0, lastOpenIndex);
        }
        i += close.length - 1;
      }
    }
  }
  return fail;
}

// src/core.ts
var import_language = require("@codemirror/language");
var LineFormater = class {
  constructor() {
  }
  syntaxTreeNodeNameType(name) {
    if (name.contains("code") && !name.contains("link")) {
      return "code" /* code */;
    } else if (name.contains("math")) {
      return "formula" /* formula */;
    } else {
      return "text" /* text */;
    }
  }
  parseLineWithSyntaxTree(state, lineNum, regRegExp) {
    let linePartsOfTxtCodeFormula = [];
    let line = state.doc.line(lineNum);
    const tree = (0, import_language.syntaxTree)(state);
    let pos = line.from;
    let prevNodeType = "none" /* none */;
    let prevBeginIdx = 0;
    while (pos < line.to) {
      let node = tree.resolve(pos, 1);
      let curNodeType = this.syntaxTreeNodeNameType(node.name);
      if (prevNodeType == "none" /* none */) {
        prevNodeType = curNodeType;
        prevBeginIdx = 0;
      } else if (prevNodeType == curNodeType) {
      } else {
        linePartsOfTxtCodeFormula.push({
          content: line.text.substring(prevBeginIdx, pos - line.from),
          type: prevNodeType,
          begin: prevBeginIdx,
          end: pos - line.from,
          leftSpaceRequire: 0 /* none */,
          rightSpaceRequire: 0 /* none */
        });
        prevNodeType = curNodeType;
        prevBeginIdx = pos - line.from;
      }
      if (curNodeType == "text" /* text */) {
        pos++;
      } else {
        pos = node.to;
      }
      if (pos == line.to) {
        linePartsOfTxtCodeFormula.push({
          content: line.text.substring(prevBeginIdx, pos - line.from),
          type: prevNodeType,
          begin: prevBeginIdx,
          end: pos - line.from,
          leftSpaceRequire: 0 /* none */,
          rightSpaceRequire: 0 /* none */
        });
      }
    }
    let retArray = [];
    for (let i = 0; i < linePartsOfTxtCodeFormula.length; i++) {
      if (linePartsOfTxtCodeFormula[i].type != "text" /* text */) {
        retArray.push(linePartsOfTxtCodeFormula[i]);
      } else {
        let tempArray;
        if (isParamDefined(regRegExp))
          tempArray = splitTextWithLinkAndUserDefined(linePartsOfTxtCodeFormula[i].content, regRegExp);
        else
          tempArray = splitTextWithLinkAndUserDefined(linePartsOfTxtCodeFormula[i].content);
        tempArray.forEach((item) => {
          item.begin += linePartsOfTxtCodeFormula[i].begin;
          item.end += linePartsOfTxtCodeFormula[i].begin;
          retArray.push(item);
        });
      }
    }
    return retArray;
  }
  formatLineOfDoc(state, settings, fromB, toB, insertedStr) {
    let doc = state.doc;
    let line = doc.lineAt(fromB).text;
    let res = null;
    if (insertedStr.contains("\n")) {
      res = this.formatLine(state, doc.lineAt(fromB).number, settings, offsetToPos(doc, fromB).ch, offsetToPos(doc, fromB).ch);
    } else {
      res = this.formatLine(state, doc.lineAt(fromB).number, settings, offsetToPos(doc, toB).ch, offsetToPos(doc, fromB).ch);
    }
    if (res === null || res[2].length == 0)
      return null;
    let newline = stringInsertAt(res[0], res[1], "|");
    let changes = [];
    let offset = doc.lineAt(fromB).from;
    for (let changeItem of res[2]) {
      changes.push({
        changes: { from: offset + changeItem.begin, to: offset + changeItem.end, insert: changeItem.text },
        userEvent: "EasyTyping.change"
      });
    }
    if (insertedStr.contains("\n")) {
      console.log("insertStr", insertedStr);
      res[1] += insertedStr.length;
    }
    return [changes, { selection: { anchor: offset + res[1] }, userEvent: "EasyTyping.change" }];
  }
  formatLine(state, lineNum, settings, curCh, prevCh) {
    let line = state.doc.line(lineNum).text;
    let regNull = /^\s*$/g;
    if (regNull.test(line))
      return [line, curCh, []];
    let lineParts = settings.UserDefinedRegSwitch ? this.parseLineWithSyntaxTree(state, lineNum, settings.UserDefinedRegExp) : this.parseLineWithSyntaxTree(state, lineNum);
    if (settings.debug)
      console.log("line parts\n", lineParts);
    let linePartsOrigin = JSON.parse(JSON.stringify(lineParts));
    let inlineChangeList = [];
    let cursorLinePartIndex = -1;
    let cursorRelativeIndex = -1;
    let resultCursorCh = 0;
    for (let i = 0; i < lineParts.length; i++) {
      if (curCh > lineParts[i].begin && curCh <= lineParts[i].end) {
        cursorLinePartIndex = i;
        cursorRelativeIndex = curCh - lineParts[i].begin;
        if (lineParts[i].type === "text" /* text */) {
          lineParts[i].content = stringInsertAt(lineParts[i].content, cursorRelativeIndex, "\0");
        }
        break;
      }
    }
    let resultLine = "";
    let offset = 0;
    let prevPartType = "none" /* none */;
    let prevTextEndSpaceState = 0 /* none */;
    for (let i = 0; i < lineParts.length; i++) {
      if (i === 0 && lineParts[i].type === "text" /* text */ && settings.AutoCapital) {
        if (isParamDefined(prevCh) && cursorLinePartIndex != 0) {
        } else {
          let regFirstSentence = /^\s*(\- (\[[x ]\] )?)?“?[a-z\u0401\u0451\u0410-\u044f]/g;
          let regHeaderSentence = /^(#+ |>+ ?|“)[a-z\u0401\u0451\u0410-\u044f]/g;
          let textcopy = lineParts[0].content;
          let match = regFirstSentence.exec(textcopy);
          let matchHeader = regHeaderSentence.exec(textcopy);
          let dstCharIndex = -1;
          if (match) {
            dstCharIndex = regFirstSentence.lastIndex - 1;
          } else if (matchHeader) {
            dstCharIndex = regHeaderSentence.lastIndex - 1;
          }
          if (settings.AutoCapitalMode == "global" /* Globally */ || isParamDefined(prevCh) && dstCharIndex >= prevCh && dstCharIndex < curCh) {
          } else {
            dstCharIndex = -1;
          }
          if (dstCharIndex != -1) {
            lineParts[0].content = textcopy.substring(0, dstCharIndex) + textcopy.charAt(dstCharIndex).toUpperCase() + textcopy.substring(dstCharIndex + 1);
          }
        }
      }
      switch (lineParts[i].type) {
        case "text" /* text */:
          let insertSpace = function(content2, reg2, prevCh2, curCh2, offset2) {
            while (true) {
              let match = reg2.exec(content2);
              if (!match)
                break;
              let tempIndex = reg2.lastIndex - 1;
              if (isParamDefined(prevCh2) && tempIndex >= prevCh2 - offset2 && tempIndex < curCh2 - offset2) {
                content2 = content2.substring(0, tempIndex) + " " + content2.substring(tempIndex);
                curCh2 += 1;
              }
            }
            return [content2, curCh2];
          };
          let content = lineParts[i].content;
          if (settings.AutoCapital) {
            var reg = /[\.\?\!。！？]([\s]*)[a-z\u0401\u0451\u0410-\u044f]/g;
            while (true) {
              let match = reg.exec(content);
              if (!match)
                break;
              let tempIndex = reg.lastIndex - 1;
              let isSpaceDot = tempIndex - 2 < 0 || content.substring(tempIndex - 2, tempIndex) == " .";
              if (settings.AutoCapitalMode == "global" /* Globally */ && !isSpaceDot) {
                lineParts[i].content = content.substring(0, tempIndex) + content.charAt(tempIndex).toUpperCase() + content.substring(reg.lastIndex);
                content = lineParts[i].content;
              } else if (isParamDefined(prevCh) && tempIndex >= prevCh - offset && tempIndex < curCh - offset && !isSpaceDot) {
                lineParts[i].content = content.substring(0, tempIndex) + content.charAt(tempIndex).toUpperCase() + content.substring(reg.lastIndex);
                content = lineParts[i].content;
              }
            }
          }
          if (settings.ChineseEnglishSpace) {
            let reg1 = /([A-Za-z])([\u4e00-\u9fa5])/gi;
            let reg2 = /([\u4e00-\u9fa5])([A-Za-z])/gi;
            [content, curCh] = insertSpace(content, reg1, prevCh, curCh, offset);
            [content, curCh] = insertSpace(content, reg2, prevCh, curCh, offset);
          }
          if (settings.ChineseNumberSpace) {
            let reg2 = /([0-9])([\u4e00-\u9fa5])/g;
            let reg1 = /([\u4e00-\u9fa5])([0-9])/g;
            [content, curCh] = insertSpace(content, reg2, prevCh, curCh, offset);
            [content, curCh] = insertSpace(content, reg1, prevCh, curCh, offset);
          }
          if (settings.EnglishNumberSpace) {
            let reg2 = /([A-Za-z])(\d)/g;
            let reg1 = /(\d)([A-Za-z])/g;
            [content, curCh] = insertSpace(content, reg2, prevCh, curCh, offset);
            [content, curCh] = insertSpace(content, reg1, prevCh, curCh, offset);
          }
          if (settings.ChineseNoSpace) {
            let reg2 = /([\u4e00-\u9fa5，。、！；‘’《》]+)(\s+)([\u4e00-\u9fa5，。、！；‘’《》]+)/g;
            while (reg2.exec(content)) {
              lineParts[i].content = content.replace(reg2, "$1$3");
              content = lineParts[i].content;
            }
          }
          if (settings.PunctuationSpace) {
            {
              let reg2 = /([,\.;\?\!\)])([0-9A-Za-z\u0401\u0451\u0410-\u044f\u4e00-\u9fa5])|([A-Za-z0-9\u4e00-\u9fa5:,\.\?\!'"]+)(\()|[,\.;\?:!][\u4e00-\u9fa5]/gi;
              while (true) {
                let match = reg2.exec(content);
                if (!match)
                  break;
                let tempIndex = reg2.lastIndex - 1;
                let isSpaceDot = "!.?;,".contains(content.charAt(tempIndex - 1)) && (tempIndex - 2 < 0 && i == 0 || content.charAt(tempIndex - 2) == " ");
                let isNumPuncNum = /[,.]\d/.test(content.substring(tempIndex - 1, tempIndex + 1)) && (tempIndex - 2 < 0 || /\d/.test(content.charAt(tempIndex - 2)));
                if (settings.PunctuationSpaceMode == "global" /* Globally */ && !isSpaceDot && !isNumPuncNum) {
                  content = content.substring(0, tempIndex) + " " + content.substring(tempIndex);
                } else if (isParamDefined(prevCh) && tempIndex >= prevCh - offset && tempIndex < curCh - offset && !isSpaceDot && !isNumPuncNum) {
                  content = content.substring(0, tempIndex) + " " + content.substring(tempIndex);
                  curCh += 1;
                }
              }
              let reg22 = /(:)([A-Za-z0-9_]+[ ,\.\?\\\/;'"，。？；‘“”’、\[\]\-\{\}])/gi;
              lineParts[i].content = content.replace(reg22, "$1 $2");
              content = lineParts[i].content;
              let reg3 = /(:)(["'])/g;
              lineParts[i].content = content.replace(reg3, "$1 $2");
              content = lineParts[i].content;
            }
          }
          let regStrictSpaceStart = /^\0?\s/;
          let regStrictSpaceEnd = /\s\0?$/;
          let regStartWithSpace = /^\0?[\s,\.;\?\!，。；》？：:！~\*、（）"”\[\]\)\{\}]/;
          let regEndWithSpace = /[\s，。、：；？！（）~\*"《“\[\]\(\{\}]\0?$/;
          let txtStartSpaceSate = 0 /* none */;
          let txtEndSpaceState = 0 /* none */;
          if (regStartWithSpace.test(content) || content.startsWith("<br>")) {
            if (regStrictSpaceStart.test(content))
              txtStartSpaceSate = 2 /* strict */;
            else
              txtStartSpaceSate = 1 /* soft */;
          }
          if (regEndWithSpace.test(content) || content.endsWith("<br>")) {
            if (regStrictSpaceEnd.test(content))
              txtEndSpaceState = 2 /* strict */;
            else
              txtEndSpaceState = 1 /* soft */;
          }
          switch (prevPartType) {
            case "none" /* none */:
              break;
            case "code" /* code */:
              if (settings.InlineCodeSpaceMode > txtStartSpaceSate) {
                lineParts[i].content = " " + content;
                content = lineParts[i].content;
              }
              break;
            case "formula" /* formula */:
              if (settings.InlineFormulaSpaceMode > txtStartSpaceSate) {
                lineParts[i].content = " " + content;
                content = lineParts[i].content;
              }
              break;
            case "wikilink" /* wikilink */:
            case "mdlink" /* mdlink */:
              if (!settings.InlineLinkSmartSpace && settings.InlineLinkSpaceMode > txtStartSpaceSate) {
                lineParts[i].content = " " + content;
                content = lineParts[i].content;
              } else if (settings.InlineLinkSmartSpace && txtStartSpaceSate == 0 /* none */) {
                let charAtTextBegin = content.charAt(0);
                let regMdLinkEnd = /\]/;
                let charAtLinkEndIndex = lineParts[i - 1].content.search(regMdLinkEnd) - 1;
                let charAtLinkEnd = lineParts[i - 1].content.charAt(charAtLinkEndIndex);
                if (charAtLinkEnd === "[")
                  break;
                let twoNeighborChars = charAtLinkEnd + charAtTextBegin;
                let regNotNeedSpace = /[\u4e00-\u9fa5，。？：；”“’‘-）}][\u4e00-\u9fa5]/g;
                if (!regNotNeedSpace.test(twoNeighborChars)) {
                  lineParts[i].content = " " + content;
                  content = lineParts[i].content;
                }
              }
              break;
            case "user-defined" /* user */:
              if (lineParts[i - 1].rightSpaceRequire > txtStartSpaceSate) {
                lineParts[i].content = " " + content;
                content = lineParts[i].content;
              }
              break;
          }
          if (i === cursorLinePartIndex) {
            let reg2 = "\0";
            let n = content.search(reg2);
            resultCursorCh = offset + n;
            lineParts[i].content = stringDeleteAt(content, n);
          }
          resultLine += lineParts[i].content;
          offset += lineParts[i].content.length;
          prevPartType = "text" /* text */;
          prevTextEndSpaceState = txtEndSpaceState;
          break;
        case "code" /* code */:
          switch (prevPartType) {
            case "none" /* none */:
              break;
            case "text" /* text */:
              if (settings.InlineCodeSpaceMode > prevTextEndSpaceState) {
                lineParts[i - 1].content += " ";
                resultLine += " ";
                offset += 1;
              }
              break;
            case "code" /* code */:
              if (settings.InlineCodeSpaceMode > 0 /* none */) {
                inlineChangeList.push({
                  text: " ",
                  begin: lineParts[i].begin,
                  end: lineParts[i].begin,
                  origin: ""
                });
                resultLine += " ";
                offset += 1;
              }
              break;
            case "formula" /* formula */:
              if (settings.InlineCodeSpaceMode > 0 /* none */ || settings.InlineFormulaSpaceMode > 0 /* none */) {
                inlineChangeList.push({
                  text: " ",
                  begin: lineParts[i].begin,
                  end: lineParts[i].begin,
                  origin: ""
                });
                resultLine += " ";
                offset += 1;
              }
              break;
            case "mdlink" /* mdlink */:
            case "wikilink" /* wikilink */:
              if (settings.InlineCodeSpaceMode > 0 /* none */ || settings.InlineLinkSpaceMode > 0 /* none */) {
                inlineChangeList.push({
                  text: " ",
                  begin: lineParts[i].begin,
                  end: lineParts[i].begin,
                  origin: ""
                });
                resultLine += " ";
                offset += 1;
              }
              break;
            case "user-defined" /* user */:
              if (settings.InlineCodeSpaceMode > 0 /* none */ && lineParts[i - 1].rightSpaceRequire > 0 /* none */) {
                inlineChangeList.push({
                  text: " ",
                  begin: lineParts[i].begin,
                  end: lineParts[i].begin,
                  origin: ""
                });
                resultLine += " ";
                offset += 1;
              }
              break;
          }
          if (i === cursorLinePartIndex) {
            resultCursorCh = offset + cursorRelativeIndex;
          }
          resultLine += lineParts[i].content;
          offset += lineParts[i].content.length;
          prevPartType = "code" /* code */;
          prevTextEndSpaceState = 0 /* none */;
          break;
        case "formula" /* formula */:
          if (lineParts[i].content == "$\\qquad$") {
            prevPartType = "text" /* text */;
            prevTextEndSpaceState = 2 /* strict */;
            break;
          }
          switch (prevPartType) {
            case "none" /* none */:
              break;
            case "text" /* text */:
              if (settings.InlineFormulaSpaceMode > prevTextEndSpaceState) {
                lineParts[i - 1].content += " ";
                resultLine += " ";
                offset += 1;
              }
              break;
            case "code" /* code */:
              if (settings.InlineFormulaSpaceMode > 0 /* none */ || settings.InlineCodeSpaceMode > 0 /* none */) {
                inlineChangeList.push({
                  text: " ",
                  begin: lineParts[i].begin,
                  end: lineParts[i].begin,
                  origin: ""
                });
                resultLine += " ";
                offset += 1;
              }
              break;
            case "formula" /* formula */:
              if (settings.InlineCodeSpaceMode > 0 /* none */) {
                inlineChangeList.push({
                  text: " ",
                  begin: lineParts[i].begin,
                  end: lineParts[i].begin,
                  origin: ""
                });
                resultLine += " ";
                offset += 1;
              }
              break;
            case "mdlink" /* mdlink */:
            case "wikilink" /* wikilink */:
              if (settings.InlineFormulaSpaceMode > 0 /* none */ || settings.InlineLinkSpaceMode > 0 /* none */) {
                inlineChangeList.push({
                  text: " ",
                  begin: lineParts[i].begin,
                  end: lineParts[i].begin,
                  origin: ""
                });
                resultLine += " ";
                offset += 1;
              }
              break;
            case "user-defined" /* user */:
              if (settings.InlineFormulaSpaceMode > 0 /* none */ && lineParts[i - 1].rightSpaceRequire > 0 /* none */) {
                inlineChangeList.push({
                  text: " ",
                  begin: lineParts[i].begin,
                  end: lineParts[i].begin,
                  origin: ""
                });
                resultLine += " ";
                offset += 1;
              }
              break;
          }
          if (i === cursorLinePartIndex) {
            resultCursorCh = offset + cursorRelativeIndex;
          }
          resultLine += lineParts[i].content;
          offset += lineParts[i].content.length;
          prevPartType = "formula" /* formula */;
          prevTextEndSpaceState = 0 /* none */;
          break;
        case "mdlink" /* mdlink */:
        case "wikilink" /* wikilink */:
          switch (prevPartType) {
            case "none" /* none */:
              break;
            case "text" /* text */:
              if (prevTextEndSpaceState >= settings.InlineLinkSpaceMode && !settings.InlineLinkSmartSpace)
                break;
              if (prevTextEndSpaceState == 2 /* strict */ && settings.InlineLinkSpaceMode == 2 /* strict */)
                break;
              let charAtTextEnd = lineParts[i - 1].content.charAt(lineParts[i - 1].content.length - 1);
              let charAtLinkBegin = "";
              if (lineParts[i].type == "wikilink" /* wikilink */) {
                let regAlias = /\|/;
                let charOfAliasBegin = lineParts[i].content.search(regAlias);
                let beginIndex = 2;
                if (lineParts[i].content.charAt(0) === "!")
                  beginIndex = 3;
                if (charOfAliasBegin != -1) {
                  beginIndex = charOfAliasBegin + 1;
                } else if (lineParts[i].content.charAt(beginIndex) == "#") {
                  beginIndex += 1;
                }
                charAtLinkBegin = lineParts[i].content.charAt(beginIndex);
                if (charAtLinkBegin == "]")
                  break;
              } else {
                let regMdLinkBegin = /\[/;
                let charAtLinkBeginIndex = lineParts[i].content.search(regMdLinkBegin) + 1;
                charAtLinkBegin = lineParts[i].content.charAt(charAtLinkBeginIndex);
                if (charAtLinkBegin === "]")
                  break;
              }
              if (settings.InlineLinkSpaceMode == 2 /* strict */ && prevTextEndSpaceState < 2 /* strict */) {
                lineParts[i - 1].content += " ";
                resultLine += " ";
                offset += 1;
              } else if (settings.InlineLinkSmartSpace && lineParts[i - 1].content.endsWith(" ")) {
                let tempContent = lineParts[i - 1].content + charAtLinkBegin;
                let regRevertSpace = /[\u4e00-\u9fa5] [\u4e00-\u9fa5]$/;
                if (regRevertSpace.test(tempContent)) {
                  lineParts[i - 1].content = lineParts[i - 1].content.substring(0, lineParts[i - 1].content.length - 1);
                  resultLine = resultLine.substring(0, resultLine.length - 1);
                  offset -= 1;
                }
              } else if (settings.InlineLinkSmartSpace && prevTextEndSpaceState == 0 /* none */) {
                let regNoNeedSpace = /[\u4e00-\u9fa5][\u4e00-\u9fa5]/g;
                let twoNeighborChars = charAtTextEnd + charAtLinkBegin;
                if (!regNoNeedSpace.test(twoNeighborChars)) {
                  lineParts[i - 1].content += " ";
                  resultLine += " ";
                  offset += 1;
                }
              } else if (!settings.InlineLinkSmartSpace && settings.InlineLinkSpaceMode > prevTextEndSpaceState) {
                lineParts[i - 1].content += " ";
                resultLine += " ";
                offset += 1;
              }
              break;
            case "code" /* code */:
              if (settings.InlineLinkSpaceMode > 0 /* none */ || settings.InlineCodeSpaceMode > 0 /* none */) {
                inlineChangeList.push({
                  text: " ",
                  begin: lineParts[i].begin,
                  end: lineParts[i].begin,
                  origin: ""
                });
                resultLine += " ";
                offset += 1;
              }
              break;
            case "formula" /* formula */:
              if (settings.InlineLinkSpaceMode > 0 /* none */ || settings.InlineFormulaSpaceMode > 0 /* none */) {
                inlineChangeList.push({
                  text: " ",
                  begin: lineParts[i].begin,
                  end: lineParts[i].begin,
                  origin: ""
                });
                resultLine += " ";
                offset += 1;
              }
              break;
            case "mdlink" /* mdlink */:
            case "wikilink" /* wikilink */:
              if (settings.InlineLinkSpaceMode > 0 /* none */) {
                inlineChangeList.push({
                  text: " ",
                  begin: lineParts[i].begin,
                  end: lineParts[i].begin,
                  origin: ""
                });
                resultLine += " ";
                offset += 1;
              }
              break;
            case "user-defined" /* user */:
              if (lineParts[i - 1].rightSpaceRequire > 0 /* none */ && settings.InlineLinkSpaceMode > 0 /* none */) {
                inlineChangeList.push({
                  text: " ",
                  begin: lineParts[i].begin,
                  end: lineParts[i].begin,
                  origin: ""
                });
                resultLine += " ";
                offset += 1;
              }
          }
          if (i === cursorLinePartIndex) {
            resultCursorCh = offset + cursorRelativeIndex;
          }
          resultLine += lineParts[i].content;
          offset += lineParts[i].content.length;
          prevPartType = lineParts[i].type;
          prevTextEndSpaceState = 0 /* none */;
          break;
        case "user-defined" /* user */:
          switch (prevPartType) {
            case "none" /* none */:
              break;
            case "text" /* text */:
              if (lineParts[i].leftSpaceRequire > prevTextEndSpaceState) {
                lineParts[i - 1].content += " ";
                resultLine += " ";
                offset += 1;
              }
              break;
            case "code" /* code */:
              if (lineParts[i].leftSpaceRequire > 0 /* none */ && settings.InlineCodeSpaceMode > 0 /* none */) {
                inlineChangeList.push({
                  text: " ",
                  begin: lineParts[i].begin,
                  end: lineParts[i].begin,
                  origin: ""
                });
                resultLine += " ";
                offset += 1;
              }
              break;
            case "formula" /* formula */:
              if (lineParts[i].leftSpaceRequire > 0 /* none */ && settings.InlineFormulaSpaceMode > 0 /* none */) {
                inlineChangeList.push({
                  text: " ",
                  begin: lineParts[i].begin,
                  end: lineParts[i].begin,
                  origin: ""
                });
                resultLine += " ";
                offset += 1;
              }
              break;
            case "mdlink" /* mdlink */:
            case "wikilink" /* wikilink */:
              if (lineParts[i].leftSpaceRequire > 0 /* none */ && settings.InlineLinkSpaceMode > 0 /* none */) {
                inlineChangeList.push({
                  text: " ",
                  begin: lineParts[i].begin,
                  end: lineParts[i].begin,
                  origin: ""
                });
                resultLine += " ";
                offset += 1;
              }
              break;
            case "user-defined" /* user */:
              if (lineParts[i].leftSpaceRequire > 0 /* none */ && lineParts[i - 1].rightSpaceRequire > 0 /* none */) {
                inlineChangeList.push({
                  text: " ",
                  begin: lineParts[i].begin,
                  end: lineParts[i].begin,
                  origin: ""
                });
                resultLine += " ";
                offset += 1;
              }
              break;
          }
          if (i === cursorLinePartIndex) {
            resultCursorCh = offset + cursorRelativeIndex;
          }
          resultLine += lineParts[i].content;
          offset += lineParts[i].content.length;
          prevPartType = "user-defined" /* user */;
          prevTextEndSpaceState = 0 /* none */;
          break;
      }
    }
    for (let i = 0; i < lineParts.length; i++) {
      if (lineParts[i].type === "text" /* text */ && lineParts[i].content != linePartsOrigin[i].content) {
        inlineChangeList.push({
          text: lineParts[i].content,
          begin: linePartsOrigin[i].begin,
          end: linePartsOrigin[i].end,
          origin: linePartsOrigin[i].content
        });
      }
    }
    inlineChangeList = inlineChangeList.sort((a, b) => a.begin - b.begin);
    return [resultLine, resultCursorCh, inlineChangeList];
  }
};
function matchWithReg(text, regExp, type, inlineTypeArray, checkArray = false, leftSpaceRe = 0 /* none */, rightSpaceRe = 0 /* none */) {
  let retArray = inlineTypeArray;
  let matchArray = [];
  retArray = retArray.sort((a, b) => a.begin - b.begin);
  while (true) {
    let match = regExp.exec(text);
    if (!match)
      break;
    let valid = true;
    if (checkArray) {
      for (let i = 0; i < retArray.length; i++) {
        if (regExp.lastIndex > retArray[i].begin && retArray[i].end > match.index) {
          valid = false;
          break;
        }
      }
    }
    if (!valid)
      continue;
    matchArray.push({
      content: match[0],
      type,
      begin: match.index,
      end: regExp.lastIndex,
      leftSpaceRequire: leftSpaceRe,
      rightSpaceRequire: rightSpaceRe
    });
  }
  retArray = retArray.concat(matchArray);
  return retArray;
}
function matchWithAbbr(text, type, inlineTypeArray, checkArray = false) {
  let retArray = inlineTypeArray;
  let matchArray = [];
  retArray = retArray.sort((a, b) => a.begin - b.begin);
  let regAbbr = /([a-zA-Z]\.)+/g;
  while (true) {
    let match = regAbbr.exec(text);
    if (!match)
      break;
    let valid = true;
    let isInBlockBegin = match.index == 0;
    if (checkArray) {
      for (let i = 0; i < retArray.length; i++) {
        if (match.index == retArray[i].end) {
          isInBlockBegin = true;
        }
        if (regAbbr.lastIndex > retArray[i].begin && retArray[i].end > match.index) {
          valid = false;
          break;
        }
      }
    }
    if (!isInBlockBegin && valid) {
      let regChar = /[a-zA-Z0-9]/;
      if (regChar.test(text.charAt(match.index - 1))) {
        valid = false;
      }
    }
    if (!valid)
      continue;
    matchArray.push({
      content: match[0],
      type,
      begin: match.index,
      end: regAbbr.lastIndex,
      leftSpaceRequire: 0 /* none */,
      rightSpaceRequire: 0 /* none */
    });
  }
  retArray = retArray.concat(matchArray);
  return retArray;
}
function splitTextWithLinkAndUserDefined(text, regExps) {
  let retArray = [];
  let regWikiLink = /\!{0,2}\[\[[^\[\]]*?\]\]/g;
  let regMdLink = /\!{0,2}\[[^\[\]]*?\]\([^\s]*\)/g;
  retArray = matchWithReg(text, regWikiLink, "wikilink" /* wikilink */, retArray);
  retArray = matchWithReg(text, regMdLink, "mdlink" /* mdlink */, retArray);
  let regExpList = [];
  let leftSRequireList = [];
  let rightSRequireList = [];
  let regNull = /^\s*$|^\/\//g;
  let regSRequire = /\|[\-=\+][\-=\+]$/;
  if (regExps) {
    let regs = regExps.split("\n");
    for (let i = 0; i < regs.length; i++) {
      if (regNull.test(regs[i]))
        continue;
      if (!regSRequire.test(regs[i]) || regs[i].length <= 3) {
        new import_obsidian.Notice("EasyTyping: \u7B2C" + String(i) + "\u884C\u81EA\u5B9A\u4E49\u6B63\u5219\u4E0D\u7B26\u5408\u89C4\u8303\n" + regs[i]);
        continue;
      }
      let regItem = regs[i].substring(0, regs[i].length - 3);
      let spaceReqString = regs[i].substring(regs[i].length - 3);
      let isValidReg = true;
      try {
        let regTemp = new RegExp(regItem, "g");
      } catch (error) {
        isValidReg = false;
        if (this.settings.debug) {
          new import_obsidian.Notice("EasuTyping: Bad RegExp:\n" + regItem);
        }
      }
      if (isValidReg) {
        regExpList.push(new RegExp(regItem, "g"));
        leftSRequireList.push(str2SpaceState(spaceReqString.charAt(1)));
        rightSRequireList.push(str2SpaceState(spaceReqString.charAt(2)));
      }
    }
    let regLen = regExpList.length;
    for (let i = 0; i < regLen; i++) {
      retArray = matchWithReg(text, regExpList[i], "user-defined" /* user */, retArray, true, leftSRequireList[i], rightSRequireList[i]);
    }
  }
  retArray = matchWithReg(text, /\d{1,2}:\d{1,2}(:\d{0,2}){0,1}/g, "user-defined" /* user */, retArray, true, 0 /* none */, 0 /* none */);
  retArray = matchWithAbbr(text, "user-defined" /* user */, retArray, true);
  retArray = retArray.sort((a, b) => a.begin - b.begin);
  let textArray = [];
  let textBegin = 0;
  let textEnd = 0;
  for (let i = 0; i < retArray.length; i++) {
    if (textBegin < retArray[i].begin) {
      textEnd = retArray[i].begin;
      textArray.push({
        content: text.substring(textBegin, textEnd),
        type: "text" /* text */,
        begin: textBegin,
        end: textEnd,
        leftSpaceRequire: 0 /* none */,
        rightSpaceRequire: 0 /* none */
      });
    }
    textBegin = retArray[i].end;
  }
  if (textBegin != text.length) {
    textArray.push({
      content: text.substring(textBegin, text.length),
      type: "text" /* text */,
      begin: textBegin,
      end: text.length,
      leftSpaceRequire: 0 /* none */,
      rightSpaceRequire: 0 /* none */
    });
  }
  retArray = retArray.concat(textArray);
  retArray = retArray.sort((a, b) => a.begin - b.begin);
  return retArray;
}
function str2SpaceState(s) {
  switch (s) {
    case "+":
      return 2 /* strict */;
    case "=":
      return 1 /* soft */;
    case "-":
    default:
      return 0 /* none */;
  }
}
function string2SpaceState(s) {
  if (Number(s) == 0 /* none */)
    return 0 /* none */;
  if (Number(s) == 1 /* soft */)
    return 1 /* soft */;
  if (Number(s) == 2 /* strict */)
    return 2 /* strict */;
  return 0 /* none */;
}
function getPosLineType(state, pos) {
  const line = state.doc.lineAt(pos);
  let line_number = line.number;
  const tree = (0, import_language.ensureSyntaxTree)(state, line.to);
  const token = tree.resolve(line.from, 1).name;
  if (token.contains("table")) {
    return "table" /* table */;
  }
  if (token.contains("hmd-frontmatter")) {
    return "frontmatter" /* frontmatter */;
  }
  if (token.contains("math")) {
    for (let p = line.from + 1; p < line.to; p += 1) {
      if (!tree.resolve(p, 1).name.contains("math")) {
        return "text" /* text */;
      }
    }
    return "formula" /* formula */;
  } else if (token.contains("code") && token.contains("block")) {
    for (let p = line.from + 1; p < line.to; p += 1) {
      let t = tree.resolve(p, 1).name;
      if (!(t.contains("code") && t.contains("block"))) {
        return "text" /* text */;
      }
    }
    return "codeblock" /* codeblock */;
  } else if (token.contains("quote") && !token.contains("callout")) {
    let callout_start_line = -1;
    for (let l = line_number - 1; l >= 1; l -= 1) {
      let l_line = state.doc.line(l);
      let l_token = tree.resolve(l_line.from, 1).name;
      if (!l_token.contains("quote")) {
        break;
      }
      if (l_token.contains("callout")) {
        callout_start_line = l;
        break;
      }
    }
    if (callout_start_line == -1)
      return "text" /* text */;
    let is_code_block = false;
    let reset = false;
    let reg_code_begin = /^>+ ```/;
    let reg_code_end = /^>+ ```$/;
    for (let l = callout_start_line + 1; l <= line_number; l += 1) {
      let l_line = state.doc.line(l);
      if (reset) {
        is_code_block = false;
        reset = false;
      }
      if (is_code_block && reg_code_end.test(l_line.text)) {
        is_code_block = true;
        reset = true;
      } else if (!is_code_block && reg_code_begin.test(l_line.text)) {
        is_code_block = true;
      }
    }
    if (is_code_block) {
      return "codeblock" /* codeblock */;
    } else
      return "text" /* text */;
  } else if (token.contains("list")) {
    for (let p = line.from + 1; p < line.to; p += 1) {
      let t = tree.resolve(p, 1).name;
      if (t.contains("code") && t.contains("block")) {
        return "codeblock" /* codeblock */;
      }
    }
  }
  return "text" /* text */;
}
function getPosLineType2(state, pos) {
  const line = state.doc.lineAt(pos);
  const tree = (0, import_language.syntaxTree)(state);
  const token = tree.resolve(line.from, 1).name;
  if (token.contains("hmd-frontmatter")) {
    return "frontmatter" /* frontmatter */;
  }
  if (token.contains("math")) {
    for (let p = line.from + 1; p < line.to; p += 1) {
      if (!tree.resolve(p, 1).name.contains("math")) {
        return "text" /* text */;
      }
    }
    return "formula" /* formula */;
  } else if (token.contains("code") && token.contains("block")) {
    for (let p = line.from + 1; p < line.to; p += 1) {
      let t = tree.resolve(p, 1).name;
      if (!(t.contains("code") && t.contains("block"))) {
        return "text" /* text */;
      }
    }
    return "codeblock" /* codeblock */;
  }
  for (let p = line.from; p < line.to; p += 1) {
    if (tree.resolve(p, 1).name.contains("list")) {
      return "list" /* list */;
    } else if (tree.resolve(p, 1).name.contains("callout")) {
      return "callout_title" /* callout_title */;
    }
  }
  if (token.contains("quote")) {
    return "quote" /* quote */;
  }
  return "text" /* text */;
}

// src/settings.ts
var import_obsidian2 = require("obsidian");

// src/lang/locale/en-US.ts
var locale = {
  settings: {
    symbolAutoPair: {
      name: "Symbol auto pair and delete with pair",
      desc: "Add auto-pairing and auto-deletion for various symbols such as \u300A\u300B, \u201C\u201D, \u300C\u300D, \u300E\u300F, \u3010\u3011, etc."
    },
    selectionReplace: {
      name: "Selection Replace Enhancement",
      desc: "Enhanced editing for selected text, e.g., pressing \uFFE5 \u2192 $selected text$, pressing \xB7 \u2192 `selected text`, \u300A \u2192 \u300Aselected text\u300B, etc."
    },
    fullWidthToHalfWidth: {
      name: "Convert successive full width symbol to half width symbol",
      desc: "Convert consecutive full-width symbols to half-width, e.g., \u3002\u3002\u2192 ., \uFF01\uFF01\u2192 !, \u300B\u300B\u2192 >"
    },
    basicInputEnhance: {
      name: "Basic symbol input enhance for Obsidian",
      desc: "Basic input enhancement for Obsidian, e.g., \u3010\u3010| \u2192 [[|]], starting with \u3001\u2192 /, starting with \u300B\u2192 >, \xB7\xB7| \u2192 `|`, `\xB7|` becomes code block, \uFFE5\uFFE5| \u2192 $|$"
    },
    codeblockEdit: {
      name: "Enhance codeblock edit",
      desc: "Improve editing in codeblocks (Tab, delete, paste, Cmd/Ctrl+A select)."
    },
    backspaceEdit: {
      name: "Enhance backspace edit",
      desc: "Improve backspace featurefor empty list item or empty quote line."
    },
    tabOut: {
      name: "Tabout",
      desc: "Tab out of inline code or paired symbols."
    },
    autoFormatting: {
      name: "Auto formatting when typing",
      desc: "Toggle auto-formatting of text while editing the document."
    },
    spaceBetweenChineseEnglish: {
      name: "Space between Chinese and English",
      desc: "Insert space between Chinese and English characters."
    },
    spaceBetweenChineseNumber: {
      name: "Space between Chinese and Number",
      desc: "Insert space between Chinese characters and numbers."
    },
    spaceBetweenEnglishNumber: {
      name: "Space between English and Number",
      desc: "Insert space between English characters and numbers."
    },
    quoteSpace: {
      name: "Space between quote character > and text",
      desc: "Insert space between quote character > and text."
    },
    deleteSpaceBetweenChinese: {
      name: "Delete the Space between Chinese characters",
      desc: "Remove spaces between Chinese characters."
    },
    capitalizeFirstLetter: {
      name: "Capitalize the first letter of every sentence",
      desc: "Capitalize the first letter of each sentence in English."
    },
    smartInsertSpace: {
      name: "Smartly insert space between text and punctuation",
      desc: "Insert space between text and punctuation intelligently."
    },
    spaceStrategyInlineCode: {
      name: "Space strategy between inline code and text",
      desc: "No requirement: No space requirement between this category block and the surrounding text. Soft space: Only requires a soft space between this category block and the surrounding blocks. Soft space example: If the adjacent text on the left side of the current block is full-width punctuation like . , ; ? etc., and the adjacent text on the right side of the current block is all full-width or half-width punctuation. Strict space: Strictly add spaces between the current block and the adjacent text."
    },
    spaceStrategyInlineFormula: {
      name: "Space strategy between inline formula and text",
      desc: "Define the spacing strategy between inline formulas and text."
    },
    spaceStrategyLinkText: {
      name: "Space strategy between link and text",
      desc: "Define the spacing strategy between [[wikilink]] [mdlink](...) and text."
    },
    userDefinedRegexpSwitch: {
      name: "User Defined RegExp Switch",
      desc: "Toggle custom regular expressions, preventing formatting and setting space strategy between matched content and other text."
    },
    userDefinedRegexp: {
      name: "User-defined Regular Expression, one expression per line",
      desc: "User-defined regular expression, matched to the content is not formatted, one expression per line, do not feel free to add spaces at the end of the line.The end of each line of three characters fixed as | and two space strategy symbols, space strategy symbols for - = +, respectively, on behalf of not requiring spaces (-), soft spaces (=), strict spaces (+).These two space strategy symbols are the space strategy for the left and right sides of the matching block respectively"
    },
    excludeFoldersFiles: {
      name: "Exclude Folders/Files",
      desc: "This plugin will parse each line as an exclude folder or file. For example: DailyNote/, DailyNote/WeekNotes/, DailyNote/test.md"
    },
    fixMacOSContextMenu: {
      name: "Fix MacOS context-menu cursor position (Need to restart Obsidian)",
      desc: "Fix the issue where the cursor jumps to the next line when the context menu is invoked on MacOS (requires restarting Obsidian)."
    },
    fixMicrosoftIME: {
      name: "Fix Microsoft Input Method Issue",
      desc: "Adapt for older versions of Microsoft Input Method."
    },
    strictLineBreaks: {
      name: "Strict Line breaks Mode",
      desc: "In strict line breaks mode, pressing Enter once in normal text lines will produce two line breaks or two spaces and Enter."
    },
    enhanceModA: {
      name: "Enhance Mod+A selection in text",
      desc: "First select the current line, second select the current text block, third select the entire text."
    },
    puncRectify: {
      name: "Punc rectify",
      desc: "Automatically convert English punctuation (, . ? !) between Chinese characters to full-width punctuation during typing (reversible)."
    },
    printDebugInfo: {
      name: "Print debug info in console",
      desc: "Print debug information in the console."
    },
    selectionReplaceRule: {
      name: "Selection Replace Rule",
      desc: "User defined Selection Replace Rule"
    },
    deleteRule: {
      name: "Delete Rule",
      desc: "Rule: Use | to indicate the cursor position. Tips: Using | to indicate the cursor position."
    },
    convertRule: {
      name: "Convert Rule",
      desc: "Rule: Use | to indicate the cursor position. Tips: Using | to indicate the cursor position."
    },
    trigger: {
      name: "Trigger"
    },
    left: {
      name: "Left"
    },
    right: {
      name: "Right"
    },
    oldPattern: {
      name: "Old Pattern"
    },
    newPattern: {
      name: "New Pattern"
    }
  },
  headers: {
    main: "Obsidian EasyTyping Plugin",
    githubDetail: "More detail is in Github: ",
    enhancedEditing: "Enhanced Editing Setting",
    customizeEditRule: "Customize Edit Convertion Rule",
    autoformatSetting: "Autoformat Setting",
    detailedSetting: "Detailed Setting Below",
    customRegexpBlock: "Custom regular expressions block",
    excludeFoldersFiles: "Exclude Folders/Files",
    experimentalFeatures: "Experimental Features",
    aboutRegexp: {
      header: "For knowledge about regular expressions, see ",
      text: "Yifeng Nguyen: A Concise Tutorial on Regular Expressions"
    },
    instructionsRegexp: {
      header: "Instructions and examples for using regular expression rules: ",
      text: "Customizing Regular Expression Rules"
    },
    customizeSelectionRule: "Customize Selection Replace Rule",
    customizeDeleteRule: "Customize Delete Rule",
    customizeConvertRule: "Customize Convert Rule",
    editSelectionReplaceRule: "Edit Selection Replace Rule"
  },
  dropdownOptions: {
    enterTwice: "Enter Twice",
    twoSpace: "Two Space",
    mixMode: "Mix Mode",
    onlyWhenTyping: "Only When Typing",
    globally: "Work Globally",
    noRequire: "No Require",
    softSpace: "Soft Space",
    strictSpace: "Strict Space",
    dummy: "Dummy",
    smart: "Smart"
  },
  toolTip: {
    switch: "Switch",
    editRule: "Edit rule",
    removeRule: "Remove rule",
    addRule: "Add Rule"
  },
  placeHolder: {
    triggerSymbol: "Trigger Symbol",
    newLeftSideString: "New Left Side String",
    newRightSideString: "New Right Side String",
    addRule: "Add Rule",
    noticeInvaidTrigger: "Inlvalid trigger, trigger must be a symbol of length 1 or symbol \u2014\u2014, \u2026\u2026",
    noticeWarnTriggerExists: "warning! Trigger %s is already exist!",
    noticeMissingInput: "missing input",
    beforeDelete: "Before Delete",
    newPattern: "New Pattern",
    noticeInvaidTriggerPatternContainSymbol: "Inlvalid trigger, pattern must contain symbol | which indicate cursor position",
    beforeConvert: "Before Convert",
    noticeInvalidPatternString: "Invalid pattern string!"
  },
  button: {
    update: "Update"
  }
};
var en_US_default = locale;

// src/lang/locale/zh-CN.ts
var locale2 = {
  settings: {
    symbolAutoPair: {
      name: "\u7B26\u53F7\u81EA\u52A8\u914D\u5BF9\u53CA\u5220\u9664\u914D\u5BF9",
      desc: "\u589E\u52A0\u591A\u79CD\u7B26\u53F7\u914D\u5BF9\u8F93\u5165\uFF0C\u914D\u5BF9\u5220\u9664\uFF0C\u5982\u300A\u300B, \u201C\u201D, \u300C\u300D, \u300E\u300F, \u3010\u3011\u7B49"
    },
    selectionReplace: {
      name: "\u9009\u4E2D\u6587\u672C\u66FF\u6362\u589E\u5F3A",
      desc: "\u9009\u4E2D\u6587\u672C\u60C5\u51B5\u4E0B\u7684\u7F16\u8F91\u589E\u5F3A\uFF0C\u6309\uFFE5\u2192$\u9009\u4E2D\u7684\u6587\u672C$, \u6309\xB7\u2192`\u9009\u4E2D\u7684\u6587\u672C`\uFF0C\u300A \u2192 \u300A\u9009\u4E2D\u7684\u6587\u672C\u300B\u7B49\u7B49"
    },
    fullWidthToHalfWidth: {
      name: "\u8FDE\u7EED\u8F93\u5165\u5168\u89D2\u7B26\u53F7\u8F6C\u534A\u89D2\u7B26\u53F7",
      desc: "\u8FDE\u7EED\u8F93\u5165\u5168\u89D2\u7B26\u53F7\u8F6C\u534A\u89D2\uFF0C\u3002\u3002\u2192 .\uFF0C\uFF01\uFF01\u2192 !\uFF0C \u300B\u300B\u2192 >"
    },
    basicInputEnhance: {
      name: "Obsidian \u7684\u57FA\u7840\u7B26\u53F7\u8F93\u5165\u589E\u5F3A",
      desc: "Obsidian \u7684\u57FA\u7840\u8F93\u5165\u589E\u5F3A\uFF0C\u5982\u3010\u3010| \u2192 [[|]]\uFF0C\u53E5\u9996\u7684\u3001\u2192 /\uFF0C\u53E5\u9996\u7684\u300B\u2192 >\uFF0C\xB7\xB7| \u2192 `|`\uFF0C `\xB7|` \u53D8\u6210\u4EE3\u7801\u5757\uFF0C\uFFE5\uFFE5| \u2192 $|$"
    },
    codeblockEdit: {
      name: "\u589E\u5F3A\u4EE3\u7801\u5757\u7F16\u8F91",
      desc: "\u589E\u5F3A\u4EE3\u7801\u5757\u5185\u7684\u7F16\u8F91\uFF08Cmd/Ctrl+A \u9009\u4E2D\u3001Tab\u3001\u5220\u9664\u3001\u7C98\u8D34\uFF09"
    },
    backspaceEdit: {
      name: "\u589E\u5F3A\u5220\u9664\u529F\u80FD",
      desc: "\u589E\u5F3A\u5220\u9664\u7A7A\u5217\u8868\u9879\u6216\u7A7A\u5F15\u7528\u884C\u7684\u529F\u80FD"
    },
    tabOut: {
      name: "Tab \u952E\u5149\u6807\u8DF3\u51FA",
      desc: "Tab \u952E\u8DF3\u51FA\u884C\u5185\u4EE3\u7801\u5757\u6216\u914D\u5BF9\u7B26\u53F7\u5757"
    },
    autoFormatting: {
      name: "\u8F93\u5165\u65F6\u81EA\u52A8\u683C\u5F0F\u5316",
      desc: "\u662F\u5426\u5728\u7F16\u8F91\u6587\u6863\u65F6\u81EA\u52A8\u683C\u5F0F\u5316\u6587\u672C\uFF0C\u81EA\u52A8\u683C\u5F0F\u5316\u7684\u603B\u5F00\u5173"
    },
    spaceBetweenChineseEnglish: {
      name: "\u4E2D\u6587\u4E0E\u82F1\u6587\u4E4B\u95F4\u7684\u7A7A\u683C",
      desc: "\u5728\u4E2D\u6587\u548C\u82F1\u6587\u4E4B\u95F4\u63D2\u5165\u7A7A\u683C\uFF0C\u53EF\u64A4\u9500"
    },
    spaceBetweenChineseNumber: {
      name: "\u4E2D\u6587\u4E0E\u6570\u5B57\u4E4B\u95F4\u7684\u7A7A\u683C",
      desc: "\u5728\u4E2D\u6587\u548C\u6570\u5B57\u4E4B\u95F4\u63D2\u5165\u7A7A\u683C\uFF0C\u53EF\u64A4\u9500"
    },
    spaceBetweenEnglishNumber: {
      name: "\u82F1\u6587\u4E0E\u6570\u5B57\u4E4B\u95F4\u7684\u7A7A\u683C",
      desc: "\u5728\u82F1\u6587\u548C\u6570\u5B57\u4E4B\u95F4\u63D2\u5165\u7A7A\u683C\uFF0C\u53EF\u64A4\u9500"
    },
    quoteSpace: {
      name: "\u5F15\u7528\u7B26\u53F7 > \u4E0E\u6587\u672C\u4E4B\u95F4\u81EA\u52A8\u7A7A\u683C",
      desc: "\u5728\u5F15\u7528\u7B26\u53F7 > \u4E0E\u6587\u672C\u4E4B\u95F4\u81EA\u52A8\u63D2\u5165\u7A7A\u683C\uFF0C\u4E0D\u53EF\u64A4\u9500"
    },
    deleteSpaceBetweenChinese: {
      name: "\u5220\u9664\u4E2D\u6587\u5B57\u7B26\u95F4\u7684\u7A7A\u683C",
      desc: "\u53BB\u9664\u4E2D\u6587\u5B57\u7B26\u4E4B\u95F4\u7684\u7A7A\u683C\uFF0C\u4E0D\u53EF\u64A4\u9500"
    },
    capitalizeFirstLetter: {
      name: "\u53E5\u9996\u5B57\u6BCD\u5927\u5199",
      desc: "\u82F1\u6587\u6BCF\u4E2A\u53E5\u9996\u5B57\u6BCD\u5927\u5199\uFF0C\u53EF\u64A4\u9500"
    },
    smartInsertSpace: {
      name: "\u667A\u80FD\u63D2\u5165\u7A7A\u683C",
      desc: "\u5728\u6587\u672C\u548C\u6807\u70B9\u4E4B\u95F4\u667A\u80FD\u63D2\u5165\u7A7A\u683C"
    },
    spaceStrategyInlineCode: {
      name: "\u884C\u5185\u4EE3\u7801\u548C\u6587\u672C\u4E4B\u95F4\u7684\u7A7A\u683C\u7B56\u7565",
      desc: "\u65E0\u8981\u6C42\uFF1A\u5BF9\u672C\u7C7B\u522B\u5757\u4E0E\u5DE6\u53F3\u6587\u672C\u6CA1\u6709\u7A7A\u683C\u7684\u8981\u6C42\uFF0C\u8F6F\u7A7A\u683C\uFF1A\u5BF9\u672C\u7C7B\u522B\u5757\u4E0E\u5468\u56F4\u533A\u5757\u53EA\u8981\u6C42\u6709\u8F6F\u7A7A\u683C\uFF0C\u8F6F\u7A7A\u683C\u5982\u5F53\u524D\u5757\u5DE6\u8FB9\u7684\u4E34\u8FD1\u6587\u672C\u4E3A\u3002\uFF0C\uFF1B\uFF1F\u7B49\u5168\u89D2\u6807\u70B9\uFF0C\u5F53\u524D\u5757\u53F3\u8FB9\u7684\u4E34\u8FD1\u6587\u672C\u4E3A\u6240\u6709\u5168\u534A\u89D2\u6807\u70B9\uFF0C\u4E25\u683C\u7A7A\u683C\uFF1A\u5F53\u524D\u5757\u4E0E\u4E34\u8FD1\u6587\u672C\u4E4B\u95F4\u4E25\u683C\u6DFB\u52A0\u7A7A\u683C\u3002"
    },
    spaceStrategyInlineFormula: {
      name: "\u884C\u5185\u516C\u5F0F\u548C\u6587\u672C\u4E4B\u95F4\u7684\u7A7A\u683C\u7B56\u7565",
      desc: "\u5B9A\u4E49\u884C\u5185\u516C\u5F0F\u548C\u6587\u672C\u4E4B\u95F4\u7684\u7A7A\u683C\u7B56\u7565"
    },
    spaceStrategyLinkText: {
      name: "\u94FE\u63A5\u548C\u6587\u672C\u4E4B\u95F4\u7684\u7A7A\u683C\u7B56\u7565",
      desc: "\u5B9A\u4E49 [[wikilink]] [mdlink](...) \u548C\u6587\u672C\u4E4B\u95F4\u7684\u7A7A\u683C\u7B56\u7565"
    },
    userDefinedRegexpSwitch: {
      name: "\u7528\u6237\u5B9A\u4E49\u7684\u6B63\u5219\u8868\u8FBE\u5F0F\u5F00\u5173",
      desc: "\u81EA\u5B9A\u4E49\u6B63\u5219\u8868\u8FBE\u5F0F\u5F00\u5173\uFF0C\u5339\u914D\u5230\u7684\u5185\u5BB9\u4E0D\u8FDB\u884C\u683C\u5F0F\u5316\uFF0C\u4E14\u53EF\u4EE5\u8BBE\u7F6E\u5339\u914D\u5230\u7684\u5185\u5BB9\u5757\u4E0E\u5176\u4ED6\u5185\u5BB9\u4E4B\u95F4\u7684\u7A7A\u683C\u7B56\u7565"
    },
    userDefinedRegexp: {
      name: "\u7528\u6237\u5B9A\u4E49\u7684\u6B63\u5219\u8868\u8FBE\u5F0F",
      desc: "\u7528\u6237\u81EA\u5B9A\u4E49\u6B63\u5219\u8868\u8FBE\u5F0F\uFF0C\u5339\u914D\u5230\u7684\u5185\u5BB9\u4E0D\u8FDB\u884C\u683C\u5F0F\u5316\uFF0C\u6BCF\u884C\u4E00\u4E2A\u8868\u8FBE\u5F0F\uFF0C\u884C\u5C3E\u4E0D\u8981\u968F\u610F\u52A0\u7A7A\u683C\u3002\u6BCF\u884C\u672B\u5C3E3\u4E2A\u5B57\u7B26\u7684\u56FA\u5B9A\u4E3A|\u548C\u4E24\u4E2A\u7A7A\u683C\u7B56\u7565\u7B26\u53F7\uFF0C\u7A7A\u683C\u7B56\u7565\u7B26\u53F7\u4E3A-=+\uFF0C\u5206\u522B\u4EE3\u8868\u4E0D\u8981\u6C42\u7A7A\u683C(-)\uFF0C\u8F6F\u7A7A\u683C(=)\uFF0C\u4E25\u683C\u7A7A\u683C(+)\u3002\u8FD9\u4E24\u4E2A\u7A7A\u683C\u7B56\u7565\u7B26\u53F7\u5206\u522B\u4E3A\u5339\u914D\u533A\u5757\u7684\u5DE6\u53F3\u4E24\u8FB9\u7684\u7A7A\u683C\u7B56\u7565"
    },
    excludeFoldersFiles: {
      name: "\u6392\u9664\u6587\u4EF6\u5939/\u6587\u4EF6",
      desc: "\u8BE5\u63D2\u4EF6\u5C06\u6BCF\u884C\u89E3\u6790\u4E3A\u4E00\u4E2A\u6392\u9664\u6587\u4EF6\u5939\u6216\u6587\u4EF6\u3002\u4F8B\u5982\uFF1ADailyNote/, DailyNote/WeekNotes/, DailyNote/test.md"
    },
    fixMacOSContextMenu: {
      name: "\u4FEE\u590D MacOS \u53F3\u952E\u83DC\u5355\u5149\u6807\u4F4D\u7F6E",
      desc: "\u4FEE\u590D MacOS \u9F20\u6807\u53F3\u952E\u547C\u51FA\u83DC\u5355\u65F6\u5149\u6807\u8DF3\u5230\u4E0B\u4E00\u884C\u7684\u95EE\u9898 (\u9700\u8981\u91CD\u542F Obsidian \u751F\u6548)"
    },
    fixMicrosoftIME: {
      name: "\u4FEE\u590D\u5FAE\u8F6F\u8F93\u5165\u6CD5\u95EE\u9898",
      desc: "\u9002\u914D\u65E7\u7248\u5FAE\u8F6F\u8F93\u5165\u6CD5"
    },
    strictLineBreaks: {
      name: "\u4E25\u683C\u6362\u884C\u6A21\u5F0F\u56DE\u8F66\u589E\u5F3A",
      desc: "\u4E25\u683C\u6362\u884C\u7684\u8BBE\u7F6E\u4E0B\uFF0C\u5728\u666E\u901A\u6587\u672C\u884C\u8FDB\u884C\u4E00\u6B21\u56DE\u8F66\u4F1A\u6839\u636E\u6A21\u5F0F\u4EA7\u751F\u4E24\u4E2A\u6362\u884C\u7B26\u6216\u8005\u4E24\u4E2A\u7A7A\u683C\u548C\u56DE\u8F66"
    },
    enhanceModA: {
      name: "\u589E\u5F3A Ctrl/Cmd+A \u529F\u80FD",
      desc: "\u7B2C\u4E00\u6B21\u9009\u4E2D\u5F53\u524D\u884C\uFF0C\u7B2C\u4E8C\u6B21\u9009\u4E2D\u5F53\u524D\u6587\u672C\u5757\uFF0C\u7B2C\u4E09\u6B21\u9009\u4E2D\u5168\u6587\u3002"
    },
    puncRectify: {
      name: "\u6807\u70B9\u77EB\u6B63",
      desc: "\u4EC5\u5728\u8F93\u5165\u8FC7\u7A0B\u4E2D\uFF0C\u4E2D\u6587\u95F4\u7684\u82F1\u6587\u6807\u70B9\uFF08,.?!\uFF09\u81EA\u52A8\u8F6C\u6362\u4E3A\u5168\u89D2\uFF08\u53EF\u64A4\u9500\uFF09"
    },
    printDebugInfo: {
      name: "\u5728\u63A7\u5236\u53F0\u8F93\u51FA\u8C03\u8BD5\u4FE1\u606F",
      desc: "\u5728\u63A7\u5236\u53F0\u8F93\u51FA\u8C03\u8BD5\u4FE1\u606F"
    },
    selectionReplaceRule: {
      name: "\u9009\u4E2D\u66FF\u6362\u89C4\u5219",
      desc: "\u7528\u6237\u5B9A\u4E49\u7684\u9009\u62E9\u66FF\u6362\u89C4\u5219"
    },
    deleteRule: {
      name: "\u5220\u9664\u89C4\u5219",
      desc: "\u89C4\u5219\uFF1A\u7528 | \u4EE3\u8868\u5149\u6807\u4F4D\u7F6E\uFF0C\u5FC5\u987B\u5305\u542B\u5149\u6807\u3002\u63D0\u793A\uFF1A\u4F7F\u7528 | \u8868\u793A\u5149\u6807\u4F4D\u7F6E\u3002"
    },
    convertRule: {
      name: "\u8F6C\u6362\u89C4\u5219",
      desc: "\u89C4\u5219\uFF1A\u7528 | \u4EE3\u8868\u5149\u6807\u4F4D\u7F6E\uFF0C\u5FC5\u987B\u5305\u542B\u5149\u6807\u3002\u63D0\u793A\uFF1A\u4F7F\u7528 | \u8868\u793A\u5149\u6807\u4F4D\u7F6E\u3002"
    },
    trigger: {
      name: "\u89E6\u53D1\u5668"
    },
    left: {
      name: "\u5DE6"
    },
    right: {
      name: "\u53F3"
    },
    oldPattern: {
      name: "\u65E7\u6A21\u5F0F"
    },
    newPattern: {
      name: "\u65B0\u6A21\u5F0F"
    }
  },
  headers: {
    main: "Obsidian EasyTyping \u63D2\u4EF6",
    githubDetail: "\u8BE6\u60C5\u89C1 Github\uFF1A",
    enhancedEditing: "\u589E\u5F3A\u7F16\u8F91\u8BBE\u7F6E",
    customizeEditRule: "\u81EA\u5B9A\u4E49\u7F16\u8F91\u8F6C\u6362\u89C4\u5219",
    autoformatSetting: "\u81EA\u52A8\u683C\u5F0F\u5316\u8BBE\u7F6E",
    detailedSetting: "\u8BE6\u7EC6\u8BBE\u7F6E\u5982\u4E0B",
    customRegexpBlock: "\u81EA\u5B9A\u4E49\u6B63\u5219\u533A\u5757",
    excludeFoldersFiles: "\u6307\u5B9A\u6587\u4EF6\u4E0D\u81EA\u52A8\u683C\u5F0F\u5316",
    experimentalFeatures: "\u5B9E\u9A8C\u529F\u80FD",
    aboutRegexp: {
      header: "\u6B63\u5219\u8868\u8FBE\u5F0F\u76F8\u5173\u77E5\u8BC6\uFF0C\u89C1 ",
      text: "\u300A\u962E\u4E00\u5CF0\uFF1A\u6B63\u5219\u8868\u8FBE\u5F0F\u7B80\u660E\u6559\u7A0B\u300B"
    },
    instructionsRegexp: {
      header: "\u6B63\u5219\u8868\u8FBE\u5F0F\u89C4\u5219\u4F7F\u7528\u8BF4\u660E\u4E0E\u793A\u4F8B\uFF1A ",
      text: "\u81EA\u5B9A\u4E49\u6B63\u5219\u8868\u8FBE\u5F0F\u89C4\u5219"
    },
    customizeSelectionRule: "\u81EA\u5B9A\u4E49\u9009\u4E2D\u6587\u672C\u7F16\u8F91\u589E\u5F3A\u89C4\u5219",
    customizeDeleteRule: "\u81EA\u5B9A\u4E49\u5220\u9664\u7F16\u8F91\u589E\u5F3A\u89C4\u5219",
    customizeConvertRule: "\u81EA\u5B9A\u4E49\u7F16\u8F91\u8F6C\u6362\u89C4\u5219",
    editSelectionReplaceRule: "\u7F16\u8F91\u9009\u4E2D\u66FF\u6362\u89C4\u5219"
  },
  dropdownOptions: {
    enterTwice: "\u4E24\u6B21\u56DE\u8F66",
    twoSpace: "\u52A0\u4E24\u4E2A\u7A7A\u683C",
    mixMode: "\u6DF7\u5408\u6A21\u5F0F",
    onlyWhenTyping: "\u8F93\u5165\u65F6\u751F\u6548",
    globally: "\u5168\u5C40\u751F\u6548",
    noRequire: "\u65E0\u8981\u6C42",
    softSpace: "\u8F6F\u7A7A\u683C",
    strictSpace: "\u4E25\u683C\u7A7A\u683C",
    dummy: "\u5446\u7A7A\u683C",
    smart: "\u667A\u80FD\u7A7A\u683C"
  },
  toolTip: {
    switch: "\u529F\u80FD\u5F00\u5173",
    editRule: "\u7F16\u8F91\u89C4\u5219",
    removeRule: "\u5220\u9664\u89C4\u5219",
    addRule: "\u6DFB\u52A0\u89C4\u5219"
  },
  placeHolder: {
    triggerSymbol: "\u89E6\u53D1\u7B26",
    newLeftSideString: "\u5DE6\u8FB9\u7B26\u53F7",
    newRightSideString: "\u53F3\u8FB9\u7B26\u53F7",
    addRule: "\u6DFB\u52A0\u89C4\u5219",
    noticeInvaidTrigger: "\u65E0\u6548\u7684\u89E6\u53D1\u7B26, \u89E6\u53D1\u7B26\u5FC5\u987B\u662F\u5355\u5B57\u7B26\u6216\u8005\u662F \u2014\u2014\u3001\u2026\u2026",
    noticeWarnTriggerExists: "\u65E0\u6548\u89C4\u5219! \u89E6\u53D1\u7B26 %s \u5DF2\u5B58\u5728",
    noticeMissingInput: "missing input",
    beforeDelete: "\u5220\u9664\u524D|",
    newPattern: "\u89E6\u53D1\u89C4\u5219\u540E\u5B57\u7B26\u4E32\u6A21\u5F0F",
    noticeInvaidTriggerPatternContainSymbol: "\u65E0\u6548\u89C4\u5219, \u8F6C\u6362\u524D\u6A21\u5F0F\u5FC5\u987B\u5305\u542B\u4EE3\u8868\u5149\u6807\u4F4D\u7F6E\u7684\u7B26\u53F7 |",
    beforeConvert: "\u8F6C\u6362\u524D|",
    noticeInvalidPatternString: "Invalid pattern string!"
  },
  button: {
    update: "\u66F4\u65B0"
  }
};
var zh_CN_default = locale2;

// src/lang/locale/ru-RU.ts
var locale3 = {
  settings: {
    symbolAutoPair: {
      name: "\u0410\u0432\u0442\u043E\u043C\u0430\u0442\u0438\u0447\u0435\u0441\u043A\u043E\u0435 \u0434\u043E\u0431\u0430\u0432\u043B\u0435\u043D\u0438\u0435 \u0438 \u0443\u0434\u0430\u043B\u0435\u043D\u0438\u0435 \u0441\u0438\u043C\u0432\u043E\u043B\u043E\u0432 \u043F\u0430\u0440\u0430",
      desc: "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0430\u0432\u0442\u043E\u0437\u0430\u043A\u0440\u044B\u0442\u0438\u0435 \u0438 \u0430\u0432\u0442\u043E\u0437\u0430\u043A\u0440\u044B\u0442\u0438\u0435 \u0434\u043B\u044F \u0440\u0430\u0437\u043B\u0438\u0447\u043D\u044B\u0445 \u0441\u0438\u043C\u0432\u043E\u043B\u043E\u0432, \u0442\u0430\u043A\u0438\u0445 \u043A\u0430\u043A \u300A\u300B, \u201C\u201D, \u300C\u300D, \u300E\u300F, \u3010\u3011 \u0438 \u0442.\u0434."
    },
    selectionReplace: {
      name: "\u0423\u043B\u0443\u0447\u0448\u0435\u043D\u0438\u0435 \u0437\u0430\u043C\u0435\u043D\u044B \u0432\u044B\u0434\u0435\u043B\u0435\u043D\u043D\u043E\u0433\u043E \u0442\u0435\u043A\u0441\u0442\u0430",
      desc: "\u0423\u043B\u0443\u0447\u0448\u0435\u043D\u043D\u043E\u0435 \u0440\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u0435 \u0432\u044B\u0434\u0435\u043B\u0435\u043D\u043D\u043E\u0433\u043E \u0442\u0435\u043A\u0441\u0442\u0430, \u043D\u0430\u043F\u0440\u0438\u043C\u0435\u0440, \u043D\u0430\u0436\u0430\u0442\u0438\u0435 \uFFE5 \u2192 $\u0432\u044B\u0434\u0435\u043B\u0435\u043D\u043D\u044B\u0439 \u0442\u0435\u043A\u0441\u0442$, \u043D\u0430\u0436\u0430\u0442\u0438\u0435 \xB7 \u2192 `\u0432\u044B\u0434\u0435\u043B\u0435\u043D\u043D\u044B\u0439 \u0442\u0435\u043A\u0441\u0442`, \u300A \u2192 \u300A\u0432\u044B\u0434\u0435\u043B\u0435\u043D\u043D\u044B\u0439 \u0442\u0435\u043A\u0441\u0442\u300B \u0438 \u0442.\u0434."
    },
    fullWidthToHalfWidth: {
      name: "\u041A\u043E\u043D\u0432\u0435\u0440\u0442\u0430\u0446\u0438\u044F \u043F\u043E\u0441\u043B\u0435\u0434\u043E\u0432\u0430\u0442\u0435\u043B\u044C\u043D\u044B\u0445 \u043F\u043E\u043B\u043D\u043E\u0448\u0438\u0440\u0438\u043D\u043D\u044B\u0445 \u0441\u0438\u043C\u0432\u043E\u043B\u043E\u0432 \u0432 \u043F\u043E\u043B\u0443\u0448\u0438\u0440\u0438\u043D\u043D\u044B\u0435",
      desc: "\u041A\u043E\u043D\u0432\u0435\u0440\u0442\u0430\u0446\u0438\u044F \u043F\u043E\u0441\u043B\u0435\u0434\u043E\u0432\u0430\u0442\u0435\u043B\u044C\u043D\u044B\u0445 \u043F\u043E\u043B\u043D\u043E\u0448\u0438\u0440\u0438\u043D\u043D\u044B\u0445 \u0441\u0438\u043C\u0432\u043E\u043B\u043E\u0432 \u0432 \u043F\u043E\u043B\u0443\u0448\u0438\u0440\u0438\u043D\u043D\u044B\u0435, \u043D\u0430\u043F\u0440\u0438\u043C\u0435\u0440, \u3002\u3002\u2192 ., \uFF01\uFF01\u2192 !, \u300B\u300B\u2192 >"
    },
    basicInputEnhance: {
      name: "\u0423\u043B\u0443\u0447\u0448\u0435\u043D\u0438\u0435 \u0431\u0430\u0437\u043E\u0432\u043E\u0433\u043E \u0432\u0432\u043E\u0434\u0430 \u0441\u0438\u043C\u0432\u043E\u043B\u043E\u0432 \u0434\u043B\u044F Obsidian",
      desc: "\u0423\u043B\u0443\u0447\u0448\u0435\u043D\u0438\u0435 \u0431\u0430\u0437\u043E\u0432\u043E\u0433\u043E \u0432\u0432\u043E\u0434\u0430 \u0434\u043B\u044F Obsidian, \u043D\u0430\u043F\u0440\u0438\u043C\u0435\u0440, \u3010\u3010| \u2192 [[|]], \u043D\u0430\u0447\u0430\u043B\u043E \u0441 \u3001\u2192 /, \u043D\u0430\u0447\u0430\u043B\u043E \u0441 \u300B\u2192 >, \xB7\xB7| \u2192 `|`, `\xB7|` \u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u0441\u044F \u043A\u043E\u0434\u043E\u0432\u044B\u043C \u0431\u043B\u043E\u043A\u043E\u043C, \uFFE5\uFFE5| \u2192 $|$"
    },
    codeblockEdit: {
      name: "\u0423\u043B\u0443\u0447\u0448\u0435\u043D\u0438\u0435 \u0440\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u044F \u043A\u043E\u0434\u043E\u0432\u044B\u0445 \u0431\u043B\u043E\u043A\u043E\u0432",
      desc: "\u0423\u043B\u0443\u0447\u0448\u0435\u043D\u0438\u0435 \u0440\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u044F \u0432 \u043A\u043E\u0434\u043E\u0432\u044B\u0445 \u0431\u043B\u043E\u043A\u0430\u0445 (Tab, \u0443\u0434\u0430\u043B\u0435\u043D\u0438\u0435, \u0432\u0441\u0442\u0430\u0432\u043A\u0430, Cmd/Ctrl+A \u0432\u044B\u0434\u0435\u043B\u0435\u043D\u0438\u0435)."
    },
    backspaceEdit: {
      name: "\u0423\u043B\u0443\u0447\u0448\u0435\u043D\u0438\u0435 \u0443\u0434\u0430\u043B\u0435\u043D\u0438\u044F",
      desc: "\u0423\u043B\u0443\u0447\u0448\u0435\u043D\u0438\u0435 \u0443\u0434\u0430\u043B\u0435\u043D\u0438\u044F \u043F\u0443\u0441\u0442\u044B\u0445 \u044D\u043B\u0435\u043C\u0435\u043D\u0442\u043E\u0432 \u0441\u043F\u0438\u0441\u043A\u0430 \u0438\u043B\u0438 \u043F\u0443\u0441\u0442\u044B\u0445 \u0441\u0442\u0440\u043E\u043A \u0441\u0441\u044B\u043B\u043E\u043A."
    },
    tabOut: {
      name: "Tabout",
      desc: "\u0412\u044B\u0439\u0442\u0438 \u0438\u0437 \u0432\u0441\u0442\u0440\u043E\u0435\u043D\u043D\u043E\u0433\u043E \u043A\u043E\u0434\u0430 \u0438\u043B\u0438 \u043F\u0430\u0440\u043D\u044B\u0445 \u0441\u0438\u043C\u0432\u043E\u043B\u043E\u0432."
    },
    autoFormatting: {
      name: "\u0410\u0432\u0442\u043E\u0444\u043E\u0440\u043C\u0430\u0442\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u0435 \u043F\u0440\u0438 \u043D\u0430\u0431\u043E\u0440\u0435 \u0442\u0435\u043A\u0441\u0442\u0430",
      desc: "\u0412\u043A\u043B\u044E\u0447\u0435\u043D\u0438\u0435/\u0432\u044B\u043A\u043B\u044E\u0447\u0435\u043D\u0438\u0435 \u0430\u0432\u0442\u043E\u0444\u043E\u0440\u043C\u0430\u0442\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u044F \u0442\u0435\u043A\u0441\u0442\u0430 \u0432\u043E \u0432\u0440\u0435\u043C\u044F \u0440\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u044F \u0434\u043E\u043A\u0443\u043C\u0435\u043D\u0442\u0430."
    },
    spaceBetweenChineseEnglish: {
      name: "\u041F\u0440\u043E\u0431\u0435\u043B \u043C\u0435\u0436\u0434\u0443 \u043A\u0438\u0442\u0430\u0439\u0441\u043A\u0438\u043C\u0438 \u0438 \u0430\u043D\u0433\u043B\u0438\u0439\u0441\u043A\u0438\u043C\u0438 \u0441\u0438\u043C\u0432\u043E\u043B\u0430\u043C\u0438",
      desc: "\u0412\u0441\u0442\u0430\u0432\u043A\u0430 \u043F\u0440\u043E\u0431\u0435\u043B\u0430 \u043C\u0435\u0436\u0434\u0443 \u043A\u0438\u0442\u0430\u0439\u0441\u043A\u0438\u043C\u0438 \u0438 \u0430\u043D\u0433\u043B\u0438\u0439\u0441\u043A\u0438\u043C\u0438 \u0441\u0438\u043C\u0432\u043E\u043B\u0430\u043C\u0438."
    },
    spaceBetweenChineseNumber: {
      name: "\u041F\u0440\u043E\u0431\u0435\u043B \u043C\u0435\u0436\u0434\u0443 \u043A\u0438\u0442\u0430\u0439\u0441\u043A\u0438\u043C\u0438 \u0441\u0438\u043C\u0432\u043E\u043B\u0430\u043C\u0438 \u0438 \u0447\u0438\u0441\u043B\u0430\u043C\u0438",
      desc: "\u0412\u0441\u0442\u0430\u0432\u043A\u0430 \u043F\u0440\u043E\u0431\u0435\u043B\u0430 \u043C\u0435\u0436\u0434\u0443 \u043A\u0438\u0442\u0430\u0439\u0441\u043A\u0438\u043C\u0438 \u0441\u0438\u043C\u0432\u043E\u043B\u0430\u043C\u0438 \u0438 \u0447\u0438\u0441\u043B\u0430\u043C\u0438."
    },
    spaceBetweenEnglishNumber: {
      name: "\u041F\u0440\u043E\u0431\u0435\u043B \u043C\u0435\u0436\u0434\u0443 \u0430\u043D\u0433\u043B\u0438\u0439\u0441\u043A\u0438\u043C\u0438 \u0441\u0438\u043C\u0432\u043E\u043B\u0430\u043C\u0438 \u0438 \u0447\u0438\u0441\u043B\u0430\u043C\u0438",
      desc: "\u0412\u0441\u0442\u0430\u0432\u043A\u0430 \u043F\u0440\u043E\u0431\u0435\u043B\u0430 \u043C\u0435\u0436\u0434\u0443 \u0430\u043D\u0433\u043B\u0438\u0439\u0441\u043A\u0438\u043C\u0438 \u0441\u0438\u043C\u0432\u043E\u043B\u0430\u043C\u0438 \u0438 \u0447\u0438\u0441\u043B\u0430\u043C\u0438."
    },
    quoteSpace: {
      name: "\u041F\u0440\u043E\u0431\u0435\u043B \u043C\u0435\u0436\u0434\u0443 \u0441\u0438\u043C\u0432\u043E\u043B\u043E\u043C > \u0438 \u0442\u0435\u043A\u0441\u0442\u043E\u043C",
      desc: "\u0412\u0441\u0442\u0430\u0432\u043A\u0430 \u043F\u0440\u043E\u0431\u0435\u043B\u0430 \u043C\u0435\u0436\u0434\u0443 \u0441\u0438\u043C\u0432\u043E\u043B\u043E\u043C > \u0438 \u0442\u0435\u043A\u0441\u0442\u043E\u043C."
    },
    deleteSpaceBetweenChinese: {
      name: "\u0423\u0434\u0430\u043B\u0435\u043D\u0438\u0435 \u043F\u0440\u043E\u0431\u0435\u043B\u0430 \u043C\u0435\u0436\u0434\u0443 \u043A\u0438\u0442\u0430\u0439\u0441\u043A\u0438\u043C\u0438 \u0441\u0438\u043C\u0432\u043E\u043B\u0430\u043C\u0438",
      desc: "\u0423\u0434\u0430\u043B\u0435\u043D\u0438\u0435 \u043F\u0440\u043E\u0431\u0435\u043B\u043E\u0432 \u043C\u0435\u0436\u0434\u0443 \u043A\u0438\u0442\u0430\u0439\u0441\u043A\u0438\u043C\u0438 \u0441\u0438\u043C\u0432\u043E\u043B\u0430\u043C\u0438."
    },
    capitalizeFirstLetter: {
      name: "\u0417\u0430\u0433\u043B\u0430\u0432\u043D\u0430\u044F \u0431\u0443\u043A\u0432\u0430 \u0432 \u043D\u0430\u0447\u0430\u043B\u0435 \u043A\u0430\u0436\u0434\u043E\u0433\u043E \u043F\u0440\u0435\u0434\u043B\u043E\u0436\u0435\u043D\u0438\u044F",
      desc: "\u041F\u0440\u0435\u043E\u0431\u0440\u0430\u0437\u043E\u0432\u0430\u043D\u0438\u0435 \u043F\u0435\u0440\u0432\u043E\u0439 \u0431\u0443\u043A\u0432\u044B \u043A\u0430\u0436\u0434\u043E\u0433\u043E \u043F\u0440\u0435\u0434\u043B\u043E\u0436\u0435\u043D\u0438\u044F \u0432 \u0430\u043D\u0433\u043B\u0438\u0439\u0441\u043A\u043E\u043C \u0432 \u0437\u0430\u0433\u043B\u0430\u0432\u043D\u0443\u044E."
    },
    smartInsertSpace: {
      name: "\u0418\u043D\u0442\u0435\u043B\u043B\u0435\u043A\u0442\u0443\u0430\u043B\u044C\u043D\u0430\u044F \u0432\u0441\u0442\u0430\u0432\u043A\u0430 \u043F\u0440\u043E\u0431\u0435\u043B\u0430 \u043C\u0435\u0436\u0434\u0443 \u0442\u0435\u043A\u0441\u0442\u043E\u043C \u0438 \u043F\u0443\u043D\u043A\u0442\u0443\u0430\u0446\u0438\u0435\u0439",
      desc: "\u0418\u043D\u0442\u0435\u043B\u043B\u0435\u043A\u0442\u0443\u0430\u043B\u044C\u043D\u0430\u044F \u0432\u0441\u0442\u0430\u0432\u043A\u0430 \u043F\u0440\u043E\u0431\u0435\u043B\u0430 \u043C\u0435\u0436\u0434\u0443 \u0442\u0435\u043A\u0441\u0442\u043E\u043C \u0438 \u043F\u0443\u043D\u043A\u0442\u0443\u0430\u0446\u0438\u0435\u0439."
    },
    spaceStrategyInlineCode: {
      name: "\u0421\u0442\u0440\u0430\u0442\u0435\u0433\u0438\u044F \u043F\u0440\u043E\u0431\u0435\u043B\u043E\u0432 \u043C\u0435\u0436\u0434\u0443 \u0432\u0441\u0442\u0440\u043E\u0435\u043D\u043D\u044B\u043C \u043A\u043E\u0434\u043E\u043C \u0438 \u0442\u0435\u043A\u0441\u0442\u043E\u043C",
      desc: "\u041D\u0435\u0442 \u0442\u0440\u0435\u0431\u043E\u0432\u0430\u043D\u0438\u0439: \u041D\u0435\u0442 \u0442\u0440\u0435\u0431\u043E\u0432\u0430\u043D\u0438\u0439 \u043A \u043F\u0440\u043E\u0431\u0435\u043B\u0430\u043C \u043C\u0435\u0436\u0434\u0443 \u044D\u0442\u0438\u043C \u0431\u043B\u043E\u043A\u043E\u043C \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438 \u0438 \u043E\u043A\u0440\u0443\u0436\u0430\u044E\u0449\u0438\u043C \u0442\u0435\u043A\u0441\u0442\u043E\u043C. \u041C\u044F\u0433\u043A\u0438\u0439 \u043F\u0440\u043E\u0431\u0435\u043B: \u0422\u0440\u0435\u0431\u0443\u0435\u0442\u0441\u044F \u0442\u043E\u043B\u044C\u043A\u043E \u043C\u044F\u0433\u043A\u0438\u0439 \u043F\u0440\u043E\u0431\u0435\u043B \u043C\u0435\u0436\u0434\u0443 \u044D\u0442\u0438\u043C \u0431\u043B\u043E\u043A\u043E\u043C \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438 \u0438 \u043E\u043A\u0440\u0443\u0436\u0430\u044E\u0449\u0438\u043C\u0438 \u0431\u043B\u043E\u043A\u0430\u043C\u0438. \u041F\u0440\u0438\u043C\u0435\u0440 \u043C\u044F\u0433\u043A\u043E\u0433\u043E \u043F\u0440\u043E\u0431\u0435\u043B\u0430: \u0415\u0441\u043B\u0438 \u043F\u0440\u0438\u043B\u0435\u0433\u0430\u044E\u0449\u0438\u0439 \u0442\u0435\u043A\u0441\u0442 \u0441\u043B\u0435\u0432\u0430 \u043E\u0442 \u0442\u0435\u043A\u0443\u0449\u0435\u0433\u043E \u0431\u043B\u043E\u043A\u0430 - \u044D\u0442\u043E \u043F\u043E\u043B\u043D\u043E\u0448\u0438\u0440\u0438\u043D\u043D\u0430\u044F \u043F\u0443\u043D\u043A\u0442\u0443\u0430\u0446\u0438\u044F, \u0442\u0430\u043A\u0430\u044F \u043A\u0430\u043A . , ; ? \u0438 \u0442.\u0434., \u0430 \u043F\u0440\u0438\u043B\u0435\u0433\u0430\u044E\u0449\u0438\u0439 \u0442\u0435\u043A\u0441\u0442 \u0441\u043F\u0440\u0430\u0432\u0430 \u043E\u0442 \u0442\u0435\u043A\u0443\u0449\u0435\u0433\u043E \u0431\u043B\u043E\u043A\u0430 - \u044D\u0442\u043E \u0432\u0441\u044F \u043F\u043E\u043B\u043D\u043E\u0448\u0438\u0440\u0438\u043D\u043D\u0430\u044F \u0438\u043B\u0438 \u043F\u043E\u043B\u0443\u0448\u0438\u0440\u0438\u043D\u043D\u0430\u044F \u043F\u0443\u043D\u043A\u0442\u0443\u0430\u0446\u0438\u044F. \u0421\u0442\u0440\u043E\u0433\u0438\u0439 \u043F\u0440\u043E\u0431\u0435\u043B: \u0421\u0442\u0440\u043E\u0433\u043E\u0435 \u0434\u043E\u0431\u0430\u0432\u043B\u0435\u043D\u0438\u0435 \u043F\u0440\u043E\u0431\u0435\u043B\u043E\u0432 \u043C\u0435\u0436\u0434\u0443 \u0442\u0435\u043A\u0443\u0449\u0438\u043C \u0431\u043B\u043E\u043A\u043E\u043C \u0438 \u043F\u0440\u0438\u043B\u0435\u0433\u0430\u044E\u0449\u0438\u043C \u0442\u0435\u043A\u0441\u0442\u043E\u043C."
    },
    spaceStrategyInlineFormula: {
      name: "\u0421\u0442\u0440\u0430\u0442\u0435\u0433\u0438\u044F \u043F\u0440\u043E\u0431\u0435\u043B\u043E\u0432 \u043C\u0435\u0436\u0434\u0443 \u0432\u0441\u0442\u0440\u043E\u0435\u043D\u043D\u043E\u0439 \u0444\u043E\u0440\u043C\u0443\u043B\u043E\u0439 \u0438 \u0442\u0435\u043A\u0441\u0442\u043E\u043C",
      desc: "\u041E\u043F\u0440\u0435\u0434\u0435\u043B\u0435\u043D\u0438\u0435 \u0441\u0442\u0440\u0430\u0442\u0435\u0433\u0438\u0438 \u043F\u0440\u043E\u0431\u0435\u043B\u043E\u0432 \u043C\u0435\u0436\u0434\u0443 \u0432\u0441\u0442\u0440\u043E\u0435\u043D\u043D\u044B\u043C\u0438 \u0444\u043E\u0440\u043C\u0443\u043B\u0430\u043C\u0438 \u0438 \u0442\u0435\u043A\u0441\u0442\u043E\u043C."
    },
    spaceStrategyLinkText: {
      name: "\u0421\u0442\u0440\u0430\u0442\u0435\u0433\u0438\u044F \u043F\u0440\u043E\u0431\u0435\u043B\u043E\u0432 \u043C\u0435\u0436\u0434\u0443 \u0441\u0441\u044B\u043B\u043A\u043E\u0439 \u0438 \u0442\u0435\u043A\u0441\u0442\u043E\u043C",
      desc: "\u041E\u043F\u0440\u0435\u0434\u0435\u043B\u0435\u043D\u0438\u0435 \u0441\u0442\u0440\u0430\u0442\u0435\u0433\u0438\u0438 \u043F\u0440\u043E\u0431\u0435\u043B\u043E\u0432 \u043C\u0435\u0436\u0434\u0443 [[\u0432\u0438\u043A\u0438\u0441\u0441\u044B\u043B\u043A\u0430\u043C\u0438]] [markdown-\u0441\u0441\u044B\u043B\u043A\u0430\u043C\u0438](...) \u0438 \u0442\u0435\u043A\u0441\u0442\u043E\u043C."
    },
    userDefinedRegexpSwitch: {
      name: "\u041F\u0435\u0440\u0435\u043A\u043B\u044E\u0447\u0435\u043D\u0438\u0435 \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C\u0441\u043A\u0438\u0445 \u0440\u0435\u0433\u0443\u043B\u044F\u0440\u043D\u044B\u0445 \u0432\u044B\u0440\u0430\u0436\u0435\u043D\u0438\u0439",
      desc: "\u0412\u043A\u043B\u044E\u0447\u0435\u043D\u0438\u0435/\u0432\u044B\u043A\u043B\u044E\u0447\u0435\u043D\u0438\u0435 \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C\u0441\u043A\u0438\u0445 \u0440\u0435\u0433\u0443\u043B\u044F\u0440\u043D\u044B\u0445 \u0432\u044B\u0440\u0430\u0436\u0435\u043D\u0438\u0439, \u043F\u0440\u0435\u0434\u043E\u0442\u0432\u0440\u0430\u0449\u0435\u043D\u0438\u0435 \u0444\u043E\u0440\u043C\u0430\u0442\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u044F \u0438 \u0443\u0441\u0442\u0430\u043D\u043E\u0432\u043A\u0430 \u0441\u0442\u0440\u0430\u0442\u0435\u0433\u0438\u0438 \u043F\u0440\u043E\u0431\u0435\u043B\u043E\u0432 \u043C\u0435\u0436\u0434\u0443 \u0441\u043E\u0432\u043F\u0430\u0434\u0430\u044E\u0449\u0438\u043C \u0441\u043E\u0434\u0435\u0440\u0436\u0438\u043C\u044B\u043C \u0438 \u0434\u0440\u0443\u0433\u0438\u043C \u0442\u0435\u043A\u0441\u0442\u043E\u043C."
    },
    userDefinedRegexp: {
      name: "\u041F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C\u0441\u043A\u043E\u0435 \u0440\u0435\u0433\u0443\u043B\u044F\u0440\u043D\u043E\u0435 \u0432\u044B\u0440\u0430\u0436\u0435\u043D\u0438\u0435, \u043E\u0434\u043D\u043E \u0432\u044B\u0440\u0430\u0436\u0435\u043D\u0438\u0435 \u043D\u0430 \u0441\u0442\u0440\u043E\u043A\u0443",
      desc: "\u041F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C\u0441\u043A\u043E\u0435 \u0440\u0435\u0433\u0443\u043B\u044F\u0440\u043D\u043E\u0435 \u0432\u044B\u0440\u0430\u0436\u0435\u043D\u0438\u0435, \u0441\u043E\u0432\u043F\u0430\u0434\u0430\u044E\u0449\u0435\u0435 \u0441 \u0441\u043E\u0434\u0435\u0440\u0436\u0438\u043C\u044B\u043C, \u043D\u0435 \u0444\u043E\u0440\u043C\u0430\u0442\u0438\u0440\u0443\u0435\u0442\u0441\u044F, \u043E\u0434\u043D\u043E \u0432\u044B\u0440\u0430\u0436\u0435\u043D\u0438\u0435 \u043D\u0430 \u0441\u0442\u0440\u043E\u043A\u0443, \u043D\u0435 \u0434\u043E\u0431\u0430\u0432\u043B\u044F\u0439\u0442\u0435 \u043F\u0440\u043E\u0431\u0435\u043B\u044B \u0432 \u043A\u043E\u043D\u0446\u0435 \u0441\u0442\u0440\u043E\u043A\u0438.\u041A\u043E\u043D\u0435\u0446 \u043A\u0430\u0436\u0434\u043E\u0439 \u0441\u0442\u0440\u043E\u043A\u0438 \u0444\u0438\u043A\u0441\u0438\u0440\u043E\u0432\u0430\u043D \u0442\u0440\u0435\u043C\u044F \u0441\u0438\u043C\u0432\u043E\u043B\u0430\u043C\u0438: | \u0438 \u0434\u0432\u0443\u043C\u044F \u0441\u0438\u043C\u0432\u043E\u043B\u0430\u043C\u0438 \u0441\u0442\u0440\u0430\u0442\u0435\u0433\u0438\u0438 \u043F\u0440\u043E\u0431\u0435\u043B\u043E\u0432, \u0441\u0438\u043C\u0432\u043E\u043B\u044B \u0441\u0442\u0440\u0430\u0442\u0435\u0433\u0438\u0438 \u043F\u0440\u043E\u0431\u0435\u043B\u043E\u0432 - \u044D\u0442\u043E - = +, \u043A\u043E\u0442\u043E\u0440\u044B\u0435 \u0441\u043E\u043E\u0442\u0432\u0435\u0442\u0441\u0442\u0432\u0435\u043D\u043D\u043E \u043E\u0431\u043E\u0437\u043D\u0430\u0447\u0430\u044E\u0442 \u043E\u0442\u0441\u0443\u0442\u0441\u0442\u0432\u0438\u0435 \u0442\u0440\u0435\u0431\u043E\u0432\u0430\u043D\u0438\u044F \u043F\u0440\u043E\u0431\u0435\u043B\u043E\u0432 (-), \u043C\u044F\u0433\u043A\u0438\u0435 \u043F\u0440\u043E\u0431\u0435\u043B\u044B (=), \u0441\u0442\u0440\u043E\u0433\u0438\u0435 \u043F\u0440\u043E\u0431\u0435\u043B\u044B (+).\u042D\u0442\u0438 \u0434\u0432\u0430 \u0441\u0438\u043C\u0432\u043E\u043B\u0430 \u0441\u0442\u0440\u0430\u0442\u0435\u0433\u0438\u0438 \u043F\u0440\u043E\u0431\u0435\u043B\u043E\u0432 \u044F\u0432\u043B\u044F\u044E\u0442\u0441\u044F \u0441\u0442\u0440\u0430\u0442\u0435\u0433\u0438\u0435\u0439 \u043F\u0440\u043E\u0431\u0435\u043B\u043E\u0432 \u0434\u043B\u044F \u043B\u0435\u0432\u043E\u0439 \u0438 \u043F\u0440\u0430\u0432\u043E\u0439 \u0441\u0442\u043E\u0440\u043E\u043D \u0441\u043E\u0432\u043F\u0430\u0434\u0430\u044E\u0449\u0435\u0433\u043E \u0431\u043B\u043E\u043A\u0430 \u0441\u043E\u043E\u0442\u0432\u0435\u0442\u0441\u0442\u0432\u0435\u043D\u043D\u043E"
    },
    excludeFoldersFiles: {
      name: "\u0418\u0441\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u043F\u0430\u043F\u043A\u0438/\u0444\u0430\u0439\u043B\u044B",
      desc: "\u042D\u0442\u043E\u0442 \u043F\u043B\u0430\u0433\u0438\u043D \u0431\u0443\u0434\u0435\u0442 \u043E\u0431\u0440\u0430\u0431\u0430\u0442\u044B\u0432\u0430\u0442\u044C \u043A\u0430\u0436\u0434\u0443\u044E \u0441\u0442\u0440\u043E\u043A\u0443 \u043A\u0430\u043A \u0438\u0441\u043A\u043B\u044E\u0447\u0430\u0435\u043C\u0443\u044E \u043F\u0430\u043F\u043A\u0443 \u0438\u043B\u0438 \u0444\u0430\u0439\u043B. \u041D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: DailyNote/, DailyNote/WeekNotes/, DailyNote/test.md"
    },
    fixMacOSContextMenu: {
      name: "\u0418\u0441\u043F\u0440\u0430\u0432\u043B\u0435\u043D\u0438\u0435 \u043F\u043E\u043B\u043E\u0436\u0435\u043D\u0438\u044F \u043A\u0443\u0440\u0441\u043E\u0440\u0430 \u043A\u043E\u043D\u0442\u0435\u043A\u0441\u0442\u043D\u043E\u0433\u043E \u043C\u0435\u043D\u044E MacOS (\u0442\u0440\u0435\u0431\u0443\u0435\u0442\u0441\u044F \u043F\u0435\u0440\u0435\u0437\u0430\u043F\u0443\u0441\u043A Obsidian)",
      desc: "\u0418\u0441\u043F\u0440\u0430\u0432\u043B\u0435\u043D\u0438\u0435 \u043F\u0440\u043E\u0431\u043B\u0435\u043C\u044B, \u043A\u043E\u0433\u0434\u0430 \u043A\u0443\u0440\u0441\u043E\u0440 \u043F\u0435\u0440\u0435\u0441\u043A\u0430\u043A\u0438\u0432\u0430\u0435\u0442 \u043D\u0430 \u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0443\u044E \u0441\u0442\u0440\u043E\u043A\u0443 \u043F\u0440\u0438 \u0432\u044B\u0437\u043E\u0432\u0435 \u043A\u043E\u043D\u0442\u0435\u043A\u0441\u0442\u043D\u043E\u0433\u043E \u043C\u0435\u043D\u044E \u043D\u0430 MacOS (\u0442\u0440\u0435\u0431\u0443\u0435\u0442\u0441\u044F \u043F\u0435\u0440\u0435\u0437\u0430\u043F\u0443\u0441\u043A Obsidian)."
    },
    fixMicrosoftIME: {
      name: "\u0418\u0441\u043F\u0440\u0430\u0432\u043B\u0435\u043D\u0438\u0435 \u043F\u0440\u043E\u0431\u043B\u0435\u043C\u044B \u0441 Microsoft Input Method",
      desc: "\u0410\u0434\u0430\u043F\u0442\u0430\u0446\u0438\u044F \u0434\u043B\u044F \u0441\u0442\u0430\u0440\u044B\u0445 \u0432\u0435\u0440\u0441\u0438\u0439 Microsoft Input Method."
    },
    strictLineBreaks: {
      name: "\u0420\u0435\u0436\u0438\u043C \u0441\u0442\u0440\u043E\u0433\u0438\u0445 \u0440\u0430\u0437\u0440\u044B\u0432\u043E\u0432 \u0441\u0442\u0440\u043E\u043A",
      desc: "\u0412 \u0440\u0435\u0436\u0438\u043C\u0435 \u0441\u0442\u0440\u043E\u0433\u0438\u0445 \u0440\u0430\u0437\u0440\u044B\u0432\u043E\u0432 \u0441\u0442\u0440\u043E\u043A, \u043E\u0434\u043D\u043E\u043A\u0440\u0430\u0442\u043D\u043E\u0435 \u043D\u0430\u0436\u0430\u0442\u0438\u0435 Enter \u0432 \u043E\u0431\u044B\u0447\u043D\u044B\u0445 \u0442\u0435\u043A\u0441\u0442\u043E\u0432\u044B\u0445 \u0441\u0442\u0440\u043E\u043A\u0430\u0445 \u0441\u043E\u0437\u0434\u0430\u0441\u0442 \u0434\u0432\u0430 \u0440\u0430\u0437\u0440\u044B\u0432\u0430 \u0441\u0442\u0440\u043E\u043A\u0438 \u0438\u043B\u0438 \u0434\u0432\u0430 \u043F\u0440\u043E\u0431\u0435\u043B\u0430 \u0438 Enter."
    },
    enhanceModA: {
      name: "\u0423\u043B\u0443\u0447\u0448\u0438\u0442\u044C \u0432\u044B\u0434\u0435\u043B\u0435\u043D\u0438\u0435 Mod+A \u0432 \u0442\u0435\u043A\u0441\u0442\u0435",
      desc: "\u0421\u043D\u0430\u0447\u0430\u043B\u0430 \u0432\u044B\u0434\u0435\u043B\u0438\u0442\u0435 \u0442\u0435\u043A\u0443\u0449\u0443\u044E \u0441\u0442\u0440\u043E\u043A\u0443, \u0437\u0430\u0442\u0435\u043C \u0432\u044B\u0434\u0435\u043B\u0438\u0442\u0435 \u0442\u0435\u043A\u0443\u0449\u0438\u0439 \u0442\u0435\u043A\u0441\u0442\u043E\u0432\u044B\u0439 \u0431\u043B\u043E\u043A, \u0437\u0430\u0442\u0435\u043C \u0432\u044B\u0434\u0435\u043B\u0438\u0442\u0435 \u0432\u0435\u0441\u044C \u0442\u0435\u043A\u0441\u0442."
    },
    puncRectify: {
      name: "\u041A\u043E\u0440\u0440\u0435\u043A\u0446\u0438\u044F \u043F\u0443\u043D\u043A\u0442\u0443\u0430\u0446\u0438\u0438",
      desc: "\u0410\u0432\u0442\u043E\u043C\u0430\u0442\u0438\u0447\u0435\u0441\u043A\u0430\u044F \u043A\u043E\u043D\u0432\u0435\u0440\u0442\u0430\u0446\u0438\u044F \u0430\u043D\u0433\u043B\u0438\u0439\u0441\u043A\u043E\u0439 \u043F\u0443\u043D\u043A\u0442\u0443\u0430\u0446\u0438\u0438 (, . ? !) \u043C\u0435\u0436\u0434\u0443 \u043A\u0438\u0442\u0430\u0439\u0441\u043A\u0438\u043C\u0438 \u0441\u0438\u043C\u0432\u043E\u043B\u0430\u043C\u0438 \u0432 \u043F\u043E\u043B\u043D\u043E\u0448\u0438\u0440\u0438\u043D\u043D\u0443\u044E \u043F\u0443\u043D\u043A\u0442\u0443\u0430\u0446\u0438\u044E \u043F\u0440\u0438 \u043D\u0430\u0431\u043E\u0440\u0435 \u0442\u0435\u043A\u0441\u0442\u0430 (\u043E\u0431\u0440\u0430\u0442\u0438\u043C\u043E)."
    },
    printDebugInfo: {
      name: "\u0412\u044B\u0432\u043E\u0434 \u043E\u0442\u043B\u0430\u0434\u043E\u0447\u043D\u043E\u0439 \u0438\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u0438 \u0432 \u043A\u043E\u043D\u0441\u043E\u043B\u044C",
      desc: "\u0412\u044B\u0432\u043E\u0434 \u043E\u0442\u043B\u0430\u0434\u043E\u0447\u043D\u043E\u0439 \u0438\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u0438 \u0432 \u043A\u043E\u043D\u0441\u043E\u043B\u044C."
    },
    selectionReplaceRule: {
      name: "\u041F\u0440\u0430\u0432\u0438\u043B\u043E \u0437\u0430\u043C\u0435\u043D\u044B \u0432\u044B\u0434\u0435\u043B\u0435\u043D\u043D\u043E\u0433\u043E \u0442\u0435\u043A\u0441\u0442\u0430",
      desc: "\u041F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C\u0441\u043A\u043E\u0435 \u043F\u0440\u0430\u0432\u0438\u043B\u043E \u0437\u0430\u043C\u0435\u043D\u044B \u0432\u044B\u0434\u0435\u043B\u0435\u043D\u043D\u043E\u0433\u043E \u0442\u0435\u043A\u0441\u0442\u0430"
    },
    deleteRule: {
      name: "\u041F\u0440\u0430\u0432\u0438\u043B\u043E \u0443\u0434\u0430\u043B\u0435\u043D\u0438\u044F",
      desc: "\u041F\u0440\u0430\u0432\u0438\u043B\u043E: \u0418\u0441\u043F\u043E\u043B\u044C\u0437\u0443\u0439\u0442\u0435 | \u0434\u043B\u044F \u0443\u043A\u0430\u0437\u0430\u043D\u0438\u044F \u043F\u043E\u0437\u0438\u0446\u0438\u0438 \u043A\u0443\u0440\u0441\u043E\u0440\u0430. \u041F\u043E\u0434\u0441\u043A\u0430\u0437\u043A\u0430: \u0418\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D\u0438\u0435 | \u0434\u043B\u044F \u0443\u043A\u0430\u0437\u0430\u043D\u0438\u044F \u043F\u043E\u0437\u0438\u0446\u0438\u0438 \u043A\u0443\u0440\u0441\u043E\u0440\u0430."
    },
    convertRule: {
      name: "\u041F\u0440\u0430\u0432\u0438\u043B\u043E \u043F\u0440\u0435\u043E\u0431\u0440\u0430\u0437\u043E\u0432\u0430\u043D\u0438\u044F",
      desc: "\u041F\u0440\u0430\u0432\u0438\u043B\u043E: \u0418\u0441\u043F\u043E\u043B\u044C\u0437\u0443\u0439\u0442\u0435 | \u0434\u043B\u044F \u0443\u043A\u0430\u0437\u0430\u043D\u0438\u044F \u043F\u043E\u0437\u0438\u0446\u0438\u0438 \u043A\u0443\u0440\u0441\u043E\u0440\u0430. \u041F\u043E\u0434\u0441\u043A\u0430\u0437\u043A\u0430: \u0418\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D\u0438\u0435 | \u0434\u043B\u044F \u0443\u043A\u0430\u0437\u0430\u043D\u0438\u044F \u043F\u043E\u0437\u0438\u0446\u0438\u0438 \u043A\u0443\u0440\u0441\u043E\u0440\u0430."
    },
    trigger: {
      name: "\u0422\u0440\u0438\u0433\u0433\u0435\u0440"
    },
    left: {
      name: "\u041B\u0435\u0432\u044B\u0439"
    },
    right: {
      name: "\u041F\u0440\u0430\u0432\u044B\u0439"
    },
    oldPattern: {
      name: "\u0421\u0442\u0430\u0440\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D"
    },
    newPattern: {
      name: "\u041D\u043E\u0432\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D"
    }
  },
  headers: {
    main: "\u041F\u043B\u0430\u0433\u0438\u043D Obsidian EasyTyping",
    githubDetail: "\u041F\u043E\u0434\u0440\u043E\u0431\u043D\u0435\u0435 \u043D\u0430 Github: ",
    enhancedEditing: "\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0430 \u0443\u043B\u0443\u0447\u0448\u0435\u043D\u043D\u043E\u0433\u043E \u0440\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u044F",
    customizeEditRule: "\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0430 \u043F\u0440\u0430\u0432\u0438\u043B\u0430 \u043F\u0440\u0435\u043E\u0431\u0440\u0430\u0437\u043E\u0432\u0430\u043D\u0438\u044F \u0440\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u044F",
    autoformatSetting: "\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0430 \u0430\u0432\u0442\u043E\u0444\u043E\u0440\u043C\u0430\u0442\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u044F",
    detailedSetting: "\u041F\u043E\u0434\u0440\u043E\u0431\u043D\u0430\u044F \u043D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0430 \u043D\u0438\u0436\u0435",
    customRegexpBlock: "\u0411\u043B\u043E\u043A \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C\u0441\u043A\u0438\u0445 \u0440\u0435\u0433\u0443\u043B\u044F\u0440\u043D\u044B\u0445 \u0432\u044B\u0440\u0430\u0436\u0435\u043D\u0438\u0439",
    excludeFoldersFiles: "\u0418\u0441\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u043F\u0430\u043F\u043A\u0438/\u0444\u0430\u0439\u043B\u044B",
    experimentalFeatures: "\u042D\u043A\u0441\u043F\u0435\u0440\u0438\u043C\u0435\u043D\u0442\u0430\u043B\u044C\u043D\u044B\u0435 \u0444\u0443\u043D\u043A\u0446\u0438\u0438",
    aboutRegexp: {
      header: "\u0414\u043B\u044F \u0438\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u0438 \u043E \u0440\u0435\u0433\u0443\u043B\u044F\u0440\u043D\u044B\u0445 \u0432\u044B\u0440\u0430\u0436\u0435\u043D\u0438\u044F\u0445 \u0441\u043C. ",
      text: "Yifeng Nguyen: \u041A\u0440\u0430\u0442\u043A\u043E\u0435 \u0440\u0443\u043A\u043E\u0432\u043E\u0434\u0441\u0442\u0432\u043E \u043F\u043E \u0440\u0435\u0433\u0443\u043B\u044F\u0440\u043D\u044B\u043C \u0432\u044B\u0440\u0430\u0436\u0435\u043D\u0438\u044F\u043C"
    },
    instructionsRegexp: {
      header: "\u0418\u043D\u0441\u0442\u0440\u0443\u043A\u0446\u0438\u0438 \u0438 \u043F\u0440\u0438\u043C\u0435\u0440\u044B \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D\u0438\u044F \u043F\u0440\u0430\u0432\u0438\u043B \u0440\u0435\u0433\u0443\u043B\u044F\u0440\u043D\u044B\u0445 \u0432\u044B\u0440\u0430\u0436\u0435\u043D\u0438\u0439: ",
      text: "\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0430 \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C\u0441\u043A\u0438\u0445 \u043F\u0440\u0430\u0432\u0438\u043B \u0440\u0435\u0433\u0443\u043B\u044F\u0440\u043D\u044B\u0445 \u0432\u044B\u0440\u0430\u0436\u0435\u043D\u0438\u0439"
    },
    customizeSelectionRule: "\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0430 \u043F\u0440\u0430\u0432\u0438\u043B\u0430 \u0437\u0430\u043C\u0435\u043D\u044B \u0432\u044B\u0434\u0435\u043B\u0435\u043D\u043D\u043E\u0433\u043E \u0442\u0435\u043A\u0441\u0442\u0430",
    customizeDeleteRule: "\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0430 \u043F\u0440\u0430\u0432\u0438\u043B\u0430 \u0443\u0434\u0430\u043B\u0435\u043D\u0438\u044F",
    customizeConvertRule: "\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0430 \u043F\u0440\u0430\u0432\u0438\u043B\u0430 \u043F\u0440\u0435\u043E\u0431\u0440\u0430\u0437\u043E\u0432\u0430\u043D\u0438\u044F",
    editSelectionReplaceRule: "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u0435 \u043F\u0440\u0430\u0432\u0438\u043B\u0430 \u0437\u0430\u043C\u0435\u043D\u044B \u0432\u044B\u0434\u0435\u043B\u0435\u043D\u043D\u043E\u0433\u043E \u0442\u0435\u043A\u0441\u0442\u0430"
  },
  dropdownOptions: {
    enterTwice: "\u0414\u0432\u0430\u0436\u0434\u044B \u043D\u0430\u0436\u043C\u0438\u0442\u0435 Enter",
    twoSpace: "\u0414\u0432\u0430 \u043F\u0440\u043E\u0431\u0435\u043B\u0430",
    mixMode: "\u0421\u043C\u0435\u0448\u0430\u043D\u043D\u044B\u0439 \u0440\u0435\u0436\u0438\u043C",
    onlyWhenTyping: "\u0422\u043E\u043B\u044C\u043A\u043E \u043F\u0440\u0438 \u043D\u0430\u0431\u043E\u0440\u0435 \u0442\u0435\u043A\u0441\u0442\u0430",
    globally: "\u0420\u0430\u0431\u043E\u0442\u0430\u0442\u044C \u0433\u043B\u043E\u0431\u0430\u043B\u044C\u043D\u043E",
    noRequire: "\u041D\u0435\u0442 \u0442\u0440\u0435\u0431\u043E\u0432\u0430\u043D\u0438\u0439",
    softSpace: "\u041C\u044F\u0433\u043A\u0438\u0439 \u043F\u0440\u043E\u0431\u0435\u043B",
    strictSpace: "\u0421\u0442\u0440\u043E\u0433\u0438\u0439 \u043F\u0440\u043E\u0431\u0435\u043B",
    dummy: "\u0424\u0438\u043A\u0442\u0438\u0432\u043D\u044B\u0439",
    smart: "\u0423\u043C\u043D\u044B\u0439"
  },
  toolTip: {
    switch: "\u041F\u0435\u0440\u0435\u043A\u043B\u044E\u0447\u0438\u0442\u044C",
    editRule: "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u043F\u0440\u0430\u0432\u0438\u043B\u043E",
    removeRule: "\u0423\u0434\u0430\u043B\u0438\u0442\u044C \u043F\u0440\u0430\u0432\u0438\u043B\u043E",
    addRule: "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043F\u0440\u0430\u0432\u0438\u043B\u043E"
  },
  placeHolder: {
    triggerSymbol: "\u0421\u0438\u043C\u0432\u043E\u043B \u0442\u0440\u0438\u0433\u0433\u0435\u0440\u0430",
    newLeftSideString: "\u041D\u043E\u0432\u0430\u044F \u0441\u0442\u0440\u043E\u043A\u0430 \u0441 \u043B\u0435\u0432\u043E\u0439 \u0441\u0442\u043E\u0440\u043E\u043D\u044B",
    newRightSideString: "\u041D\u043E\u0432\u0430\u044F \u0441\u0442\u0440\u043E\u043A\u0430 \u0441 \u043F\u0440\u0430\u0432\u043E\u0439 \u0441\u0442\u043E\u0440\u043E\u043D\u044B",
    addRule: "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043F\u0440\u0430\u0432\u0438\u043B\u043E",
    noticeInvaidTrigger: "\u041D\u0435\u0434\u0435\u0439\u0441\u0442\u0432\u0438\u0442\u0435\u043B\u044C\u043D\u044B\u0439 \u0442\u0440\u0438\u0433\u0433\u0435\u0440, \u0442\u0440\u0438\u0433\u0433\u0435\u0440 \u0434\u043E\u043B\u0436\u0435\u043D \u0431\u044B\u0442\u044C \u0441\u0438\u043C\u0432\u043E\u043B\u043E\u043C \u0434\u043B\u0438\u043D\u043E\u0439 1 \u0438\u043B\u0438 \u0441\u0438\u043C\u0432\u043E\u043B\u043E\u043C \u2014\u2014, \u2026\u2026",
    noticeWarnTriggerExists: "\u0412\u043D\u0438\u043C\u0430\u043D\u0438\u0435! \u0422\u0440\u0438\u0433\u0433\u0435\u0440 %s \u0443\u0436\u0435 \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u0435\u0442!",
    noticeMissingInput: "\u041E\u0442\u0441\u0443\u0442\u0441\u0442\u0432\u0443\u0435\u0442 \u0432\u0432\u043E\u0434",
    beforeDelete: "\u0414\u043E \u0443\u0434\u0430\u043B\u0435\u043D\u0438\u044F",
    newPattern: "\u041D\u043E\u0432\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D",
    noticeInvaidTriggerPatternContainSymbol: "\u041D\u0435\u0434\u0435\u0439\u0441\u0442\u0432\u0438\u0442\u0435\u043B\u044C\u043D\u044B\u0439 \u0442\u0440\u0438\u0433\u0433\u0435\u0440, \u0448\u0430\u0431\u043B\u043E\u043D \u0434\u043E\u043B\u0436\u0435\u043D \u0441\u043E\u0434\u0435\u0440\u0436\u0430\u0442\u044C \u0441\u0438\u043C\u0432\u043E\u043B |, \u0443\u043A\u0430\u0437\u044B\u0432\u0430\u044E\u0449\u0438\u0439 \u043D\u0430 \u043F\u043E\u0437\u0438\u0446\u0438\u044E \u043A\u0443\u0440\u0441\u043E\u0440\u0430",
    beforeConvert: "\u0414\u043E \u043F\u0440\u0435\u043E\u0431\u0440\u0430\u0437\u043E\u0432\u0430\u043D\u0438\u044F",
    noticeInvalidPatternString: "\u041D\u0435\u0434\u0435\u0439\u0441\u0442\u0432\u0438\u0442\u0435\u043B\u044C\u043D\u0430\u044F \u0441\u0442\u0440\u043E\u043A\u0430 \u0448\u0430\u0431\u043B\u043E\u043D\u0430!"
  },
  button: {
    update: "\u041E\u0431\u043D\u043E\u0432\u0438\u0442\u044C"
  }
};
var ru_RU_default = locale3;

// src/lang/locale/zh-TW.ts
var locale4 = {
  settings: {
    symbolAutoPair: {
      name: "\u7B26\u865F\u81EA\u52D5\u914D\u5C0D\u53CA\u522A\u9664\u914D\u5C0D",
      desc: "\u589E\u52A0\u591A\u7A2E\u7B26\u865F\u914D\u5C0D\u8F38\u5165\uFF0C\u914D\u5C0D\u522A\u9664\uFF0C\u5982\u300A\u300B, \u201C\u201D, \u300C\u300D, \u300E\u300F, \u3010\u3011\u7B49"
    },
    selectionReplace: {
      name: "\u9078\u4E2D\u6587\u672C\u66FF\u63DB\u589E\u5F3A",
      desc: "\u9078\u4E2D\u6587\u672C\u60C5\u6CC1\u4E0B\u7684\u7DE8\u8F2F\u589E\u5F3A\uFF0C\u6309\uFFE5\u2192$\u9078\u4E2D\u7684\u6587\u672C$, \u6309\xB7\u2192`\u9078\u4E2D\u7684\u6587\u672C`\uFF0C\u300A \u2192 \u300A\u9078\u4E2D\u7684\u6587\u672C\u300B\u7B49\u7B49"
    },
    fullWidthToHalfWidth: {
      name: "\u9023\u7E8C\u8F38\u5165\u5168\u89D2\u7B26\u865F\u8F49\u534A\u89D2\u7B26\u865F",
      desc: "\u9023\u7E8C\u8F38\u5165\u5168\u89D2\u7B26\u865F\u8F49\u534A\u89D2\uFF0C\u3002\u3002\u2192 .\uFF0C\uFF01\uFF01\u2192 !\uFF0C \u300B\u300B\u2192 >"
    },
    basicInputEnhance: {
      name: "Obsidian \u7684\u57FA\u790E\u7B26\u865F\u8F38\u5165\u589E\u5F3A",
      desc: "Obsidian \u7684\u57FA\u790E\u8F38\u5165\u589E\u5F3A\uFF0C\u5982\u3010\u3010| \u2192 [[|]]\uFF0C\u53E5\u9996\u7684\u3001\u2192 /\uFF0C\u53E5\u9996\u7684\u300B\u2192 >\uFF0C\xB7\xB7| \u2192 `|`\uFF0C `\xB7|` \u8B8A\u6210\u4EE3\u78BC\u584A\uFF0C\uFFE5\uFFE5| \u2192 $|$"
    },
    codeblockEdit: {
      name: "\u589E\u5F3A\u4EE3\u78BC\u584A\u7DE8\u8F2F",
      desc: "\u589E\u5F3A\u4EE3\u78BC\u584A\u5167\u7684\u7DE8\u8F2F\uFF08Cmd/Ctrl+A \u9078\u4E2D\u3001Tab\u3001\u522A\u9664\u3001\u7C98\u8CBC\uFF09"
    },
    backspaceEdit: {
      name: "\u589E\u5F37\u522A\u9664\u529F\u80FD",
      desc: "\u589E\u5F37\u522A\u9664\u7A7A\u5217\u8868\u9805\u6216\u7A7A\u5F15\u7528\u884C\u7684\u529F\u80FD"
    },
    tabOut: {
      name: "Tab \u952E\u5149\u6807\u8DF3\u51FA",
      desc: "Tab \u952E\u8DF3\u51FA\u884C\u5167\u4EE3\u78BC\u584A\u6216\u914D\u5C0D\u7B26\u865F\u584A"
    },
    autoFormatting: {
      name: "\u8F38\u5165\u6642\u81EA\u52D5\u683C\u5F0F\u5316",
      desc: "\u662F\u5426\u5728\u7DE8\u8F2F\u6587\u6A94\u6642\u81EA\u52D5\u683C\u5F0F\u5316\u6587\u672C\uFF0C\u81EA\u52D5\u683C\u5F0F\u5316\u7684\u7E3D\u958B\u95DC"
    },
    spaceBetweenChineseEnglish: {
      name: "\u4E2D\u6587\u8207\u82F1\u6587\u4E4B\u9593\u7684\u7A7A\u683C",
      desc: "\u5728\u4E2D\u6587\u548C\u82F1\u6587\u4E4B\u9593\u63D2\u5165\u7A7A\u683C\uFF0C\u53EF\u53D6\u6D88"
    },
    spaceBetweenChineseNumber: {
      name: "\u4E2D\u6587\u8207\u6578\u5B57\u4E4B\u9593\u7684\u7A7A\u683C",
      desc: "\u5728\u4E2D\u6587\u548C\u6578\u5B57\u4E4B\u9593\u63D2\u5165\u7A7A\u683C\uFF0C\u53EF\u53D6\u6D88"
    },
    spaceBetweenEnglishNumber: {
      name: "\u82F1\u6587\u8207\u6578\u5B57\u4E4B\u9593\u7684\u7A7A\u683C",
      desc: "\u5728\u82F1\u6587\u548C\u6578\u5B57\u4E4B\u9593\u63D2\u5165\u7A7A\u683C\uFF0C\u53EF\u53D6\u6D88"
    },
    quoteSpace: {
      name: "\u5F15\u7528\u7B26\u865F > \u8207\u6587\u672C\u4E4B\u9593\u81EA\u52D5\u7A7A\u683C",
      desc: "\u5728\u5F15\u7528\u7B26\u865F > \u8207\u6587\u672C\u4E4B\u9593\u81EA\u52D5\u63D2\u5165\u7A7A\u683C\uFF0C\u4E0D\u53EF\u53D6\u6D88"
    },
    deleteSpaceBetweenChinese: {
      name: "\u522A\u9664\u4E2D\u6587\u5B57\u7B26\u9593\u7684\u7A7A\u683C",
      desc: "\u53BB\u9664\u4E2D\u6587\u5B57\u7B26\u4E4B\u9593\u7684\u7A7A\u683C\uFF0C\u4E0D\u53EF\u53D6\u6D88"
    },
    capitalizeFirstLetter: {
      name: "\u53E5\u9996\u5B57\u6BCD\u5927\u5BEB",
      desc: "\u82F1\u6587\u6BCF\u500B\u53E5\u9996\u5B57\u6BCD\u5927\u5BEB\uFF0C\u53EF\u53D6\u6D88"
    },
    smartInsertSpace: {
      name: "\u667A\u80FD\u63D2\u5165\u7A7A\u683C",
      desc: "\u5728\u6587\u672C\u548C\u6A19\u9EDE\u4E4B\u9593\u667A\u80FD\u63D2\u5165\u7A7A\u683C"
    },
    spaceStrategyInlineCode: {
      name: "\u884C\u5167\u4EE3\u78BC\u548C\u6587\u672C\u4E4B\u9593\u7684\u7A7A\u683C\u7B56\u7565",
      desc: "\u7121\u8981\u6C42\uFF1A\u5C0D\u672C\u985E\u5225\u584A\u8207\u5DE6\u53F3\u6587\u672C\u6C92\u6709\u7A7A\u683C\u7684\u8981\u6C42\uFF0C\u8EDF\u7A7A\u683C\uFF1A\u5C0D\u672C\u985E\u5225\u584A\u8207\u5468\u570D\u5340\u584A\u53EA\u8981\u6C42\u6709\u8EDF\u7A7A\u683C\uFF0C\u8EDF\u7A7A\u683C\u5982\u7576\u524D\u584A\u5DE6\u908A\u7684\u81E8\u8FD1\u6587\u672C\u70BA\u3002\uFF0C\uFF1B\uFF1F\u7B49\u5168\u89D2\u6A19\u9EDE\uFF0C\u7576\u524D\u584A\u53F3\u908A\u7684\u81E8\u8FD1\u6587\u672C\u70BA\u6240\u6709\u5168\u534A\u89D2\u6A19\u9EDE\uFF0C\u56B4\u683C\u7A7A\u683C\uFF1A\u7576\u524D\u584A\u8207\u81E8\u8FD1\u6587\u672C\u4E4B\u9593\u56B4\u683C\u6DFB\u52A0\u7A7A\u683C\u3002"
    },
    spaceStrategyInlineFormula: {
      name: "\u884C\u5167\u516C\u5F0F\u548C\u6587\u672C\u4E4B\u9593\u7684\u7A7A\u683C\u7B56\u7565",
      desc: "\u5B9A\u7FA9\u884C\u5167\u516C\u5F0F\u548C\u6587\u672C\u4E4B\u9593\u7684\u7A7A\u683C\u7B56\u7565"
    },
    spaceStrategyLinkText: {
      name: "\u9023\u7D50\u548C\u6587\u672C\u4E4B\u9593\u7684\u7A7A\u683C\u7B56\u7565",
      desc: "\u5B9A\u7FA9 [[wikilink]] [mdlink](...) \u548C\u6587\u672C\u4E4B\u9593\u7684\u7A7A\u683C\u7B56\u7565"
    },
    userDefinedRegexpSwitch: {
      name: "\u7528\u6236\u5B9A\u7FA9\u7684\u6B63\u5247\u8868\u9054\u5F0F\u958B\u95DC",
      desc: "\u81EA\u5B9A\u7FA9\u6B63\u5247\u8868\u9054\u5F0F\u958B\u95DC\uFF0C\u5339\u914D\u5230\u7684\u5167\u5BB9\u4E0D\u9032\u884C\u683C\u5F0F\u5316\uFF0C\u4E14\u53EF\u4EE5\u8A2D\u7F6E\u5339\u914D\u5230\u7684\u5167\u5BB9\u584A\u8207\u5176\u4ED6\u5167\u5BB9\u4E4B\u9593\u7684\u7A7A\u683C\u7B56\u7565"
    },
    userDefinedRegexp: {
      name: "\u7528\u6236\u5B9A\u7FA9\u7684\u6B63\u5247\u8868\u9054\u5F0F",
      desc: "\u7528\u6236\u81EA\u5B9A\u7FA9\u6B63\u5247\u8868\u9054\u5F0F\uFF0C\u5339\u914D\u5230\u7684\u5167\u5BB9\u4E0D\u9032\u884C\u683C\u5F0F\u5316\uFF0C\u6BCF\u884C\u4E00\u500B\u8868\u9054\u5F0F\uFF0C\u884C\u5C3E\u4E0D\u8981\u96A8\u610F\u52A0\u7A7A\u683C\u3002\u6BCF\u884C\u672B\u5C3E3\u500B\u5B57\u7B26\u7684\u56FA\u5B9A\u70BA|\u548C\u5169\u500B\u7A7A\u683C\u7B56\u7565\u7B26\u865F\uFF0C\u7A7A\u683C\u7B56\u7565\u7B26\u865F\u70BA-=+\uFF0C\u5206\u5225\u4EE3\u8868\u4E0D\u8981\u6C42\u7A7A\u683C(-)\uFF0C\u8EDF\u7A7A\u683C(=)\uFF0C\u56B4\u683C\u7A7A\u683C(+)\u3002\u9019\u5169\u500B\u7A7A\u683C\u7B56\u7565\u7B26\u865F\u5206\u5225\u70BA\u5339\u914D\u5340\u584A\u7684\u5DE6\u53F3\u5169\u908A\u7684\u7A7A\u683C\u7B56\u7565"
    },
    excludeFoldersFiles: {
      name: "\u6392\u9664\u6587\u4EF6\u593E/\u6587\u4EF6",
      desc: "\u8A72\u63D2\u4EF6\u5C07\u6BCF\u884C\u89E3\u6790\u70BA\u4E00\u500B\u6392\u9664\u6587\u4EF6\u593E\u6216\u6587\u4EF6\u3002\u4F8B\u5982\uFF1ADailyNote/, DailyNote/WeekNotes/, DailyNote/test.md"
    },
    fixMacOSContextMenu: {
      name: "\u4FEE\u5FA9 MacOS \u53F3\u9375\u83DC\u55AE\u5149\u6A19\u4F4D\u7F6E",
      desc: "\u4FEE\u5FA9 MacOS \u9F20\u6A19\u53F3\u9375\u547C\u51FA\u83DC\u55AE\u6642\u5149\u6A19\u8DF3\u5230\u4E0B\u4E00\u884C\u7684\u554F\u984C (\u9700\u8981\u91CD\u555F Obsidian \u751F\u6548)"
    },
    fixMicrosoftIME: {
      name: "\u4FEE\u5FA9\u5FAE\u8EDF\u8F38\u5165\u6CD5\u554F\u984C",
      desc: "\u9069\u914D\u820A\u7248\u5FAE\u8EDF\u8F38\u5165\u6CD5"
    },
    strictLineBreaks: {
      name: "\u56B4\u683C\u63DB\u884C\u6A21\u5F0F\u56DE\u8ECA\u589E\u5F37",
      desc: "\u56B4\u683C\u63DB\u884C\u7684\u8A2D\u7F6E\u4E0B\uFF0C\u5728\u666E\u901A\u6587\u672C\u884C\u9032\u884C\u4E00\u6B21\u56DE\u8ECA\u6703\u6839\u64DA\u6A21\u5F0F\u7522\u751F\u5169\u500B\u63DB\u884C\u7B26\u6216\u8005\u5169\u500B\u7A7A\u683C\u548C\u56DE\u8ECA"
    },
    enhanceModA: {
      name: "\u589E\u5F37 Mod+A \u529F\u80FD",
      desc: "\u7B2C\u4E00\u6B21\u9078\u4E2D\u7576\u524D\u884C\uFF0C\u7B2C\u4E8C\u6B21\u9078\u4E2D\u7576\u524D\u6587\u672C\u584A\uFF0C\u7B2C\u4E09\u6B21\u9078\u4E2D\u5168\u6587\u3002"
    },
    puncRectify: {
      name: "\u6A19\u9EDE\u77EB\u6B63",
      desc: "\u50C5\u5728\u8F38\u5165\u904E\u7A0B\u4E2D\uFF0C\u4E2D\u6587\u9593\u7684\u82F1\u6587\u6A19\u9EDE\uFF08,.?!\uFF09\u81EA\u52D5\u8F49\u63DB\u70BA\u5168\u89D2\uFF08\u53EF\u53D6\u6D88\uFF09"
    },
    printDebugInfo: {
      name: "\u5728\u63A7\u5236\u53F0\u8F38\u51FA\u8ABF\u8A66\u8CC7\u8A0A",
      desc: "\u5728\u63A7\u5236\u53F0\u8F38\u51FA\u8ABF\u8A66\u8CC7\u8A0A"
    },
    selectionReplaceRule: {
      name: "\u9078\u4E2D\u66FF\u63DB\u898F\u5247",
      desc: "\u7528\u6236\u5B9A\u7FA9\u7684\u9078\u4E2D\u66FF\u63DB\u898F\u5247"
    },
    deleteRule: {
      name: "\u522A\u9664\u898F\u5247",
      desc: "\u898F\u5247\uFF1A\u7528 | \u4EE3\u8868\u5149\u6A19\u4F4D\u7F6E\uFF0C\u5FC5\u9808\u5305\u542B\u5149\u6A19\u3002\u63D0\u793A\uFF1A\u4F7F\u7528 | \u8868\u793A\u5149\u6A19\u4F4D\u7F6E\u3002"
    },
    convertRule: {
      name: "\u8F49\u63DB\u898F\u5247",
      desc: "\u898F\u5247\uFF1A\u7528 | \u4EE3\u8868\u5149\u6A19\u4F4D\u7F6E\uFF0C\u5FC5\u9808\u5305\u542B\u5149\u6A19\u3002\u63D0\u793A\uFF1A\u4F7F\u7528 | \u8868\u793A\u5149\u6A19\u4F4D\u7F6E\u3002"
    },
    trigger: {
      name: "\u89F8\u767C\u5668"
    },
    left: {
      name: "\u5DE6"
    },
    right: {
      name: "\u53F3"
    },
    oldPattern: {
      name: "\u820A\u6A21\u5F0F"
    },
    newPattern: {
      name: "\u65B0\u6A21\u5F0F"
    }
  },
  headers: {
    main: "Obsidian EasyTyping \u63D2\u4EF6",
    githubDetail: "\u8A73\u60C5\u898B Github\uFF1A",
    enhancedEditing: "\u589E\u5F37\u7DE8\u8F2F\u8A2D\u7F6E",
    customizeEditRule: "\u81EA\u5B9A\u7FA9\u7DE8\u8F2F\u8F49\u63DB\u898F\u5247",
    autoformatSetting: "\u81EA\u52D5\u683C\u5F0F\u5316\u8A2D\u7F6E",
    detailedSetting: "\u8A73\u7D30\u8A2D\u7F6E\u5982\u4E0B",
    customRegexpBlock: "\u81EA\u5B9A\u7FA9\u6B63\u5247\u5340\u584A",
    excludeFoldersFiles: "\u6307\u5B9A\u6587\u4EF6\u4E0D\u81EA\u52D5\u683C\u5F0F\u5316",
    experimentalFeatures: "\u5BE6\u9A57\u529F\u80FD",
    aboutRegexp: {
      header: "\u6B63\u5247\u8868\u9054\u5F0F\u76F8\u95DC\u77E5\u8B58\uFF0C\u898B ",
      text: "\u300A\u962E\u4E00\u5CF0\uFF1A\u6B63\u5247\u8868\u9054\u5F0F\u7C21\u660E\u6559\u7A0B\u300B"
    },
    instructionsRegexp: {
      header: "\u6B63\u5247\u8868\u9054\u5F0F\u898F\u5247\u4F7F\u7528\u8AAA\u660E\u8207\u793A\u4F8B\uFF1A ",
      text: "\u81EA\u5B9A\u7FA9\u6B63\u5247\u8868\u9054\u5F0F\u898F\u5247"
    },
    customizeSelectionRule: "\u81EA\u5B9A\u7FA9\u9078\u4E2D\u6587\u672C\u7DE8\u8F2F\u589E\u5F3A\u898F\u5247",
    customizeDeleteRule: "\u81EA\u5B9A\u7FA9\u522A\u9664\u7DE8\u8F2F\u589E\u5F3A\u898F\u5247",
    customizeConvertRule: "\u81EA\u5B9A\u7FA9\u7DE8\u8F2F\u8F49\u63DB\u898F\u5247",
    editSelectionReplaceRule: "\u7DE8\u8F2F\u9078\u4E2D\u66FF\u63DB\u898F\u5247"
  },
  dropdownOptions: {
    enterTwice: "\u5169\u6B21\u56DE\u8ECA",
    twoSpace: "\u52A0\u5169\u500B\u7A7A\u683C",
    mixMode: "\u6DF7\u5408\u6A21\u5F0F",
    onlyWhenTyping: "\u8F38\u5165\u6642\u751F\u6548",
    globally: "\u5168\u5C40\u751F\u6548",
    noRequire: "\u7121\u8981\u6C42",
    softSpace: "\u8EDF\u7A7A\u683C",
    strictSpace: "\u56B4\u683C\u7A7A\u683C",
    dummy: "\u5446\u7A7A\u683C",
    smart: "\u667A\u80FD\u7A7A\u683C"
  },
  toolTip: {
    switch: "\u529F\u80FD\u958B\u95DC",
    editRule: "\u7DE8\u8F2F\u898F\u5247",
    removeRule: "\u522A\u9664\u898F\u5247",
    addRule: "\u6DFB\u52A0\u898F\u5247"
  },
  placeHolder: {
    triggerSymbol: "\u89F8\u767C\u7B26",
    newLeftSideString: "\u5DE6\u908A\u7B26\u865F",
    newRightSideString: "\u53F3\u908A\u7B26\u865F",
    addRule: "\u6DFB\u52A0\u898F\u5247",
    noticeInvaidTrigger: "\u7121\u6548\u7684\u89F8\u767C\u7B26, \u89F8\u767C\u7B26\u5FC5\u9808\u662F\u55AE\u5B57\u7B26\u6216\u8005\u662F \u2014\u2014\u3001\u2026\u2026",
    noticeWarnTriggerExists: "\u7121\u6548\u898F\u5247! \u89F8\u767C\u7B26 %s \u5DF2\u5B58\u5728",
    noticeMissingInput: "missing input",
    beforeDelete: "\u522A\u9664\u524D|",
    newPattern: "\u89F8\u767C\u898F\u5247\u5F8C\u5B57\u4E32\u6A21\u5F0F",
    noticeInvaidTriggerPatternContainSymbol: "\u7121\u6548\u898F\u5247, \u8F49\u63DB\u524D\u6A21\u5F0F\u5FC5\u9808\u5305\u542B\u4EE3\u8868\u5149\u6A19\u4F4D\u7F6E\u7684\u7B26\u865F |",
    beforeConvert: "\u8F49\u63DB\u524D|",
    noticeInvalidPatternString: "Invalid pattern string!"
  },
  button: {
    update: "\u66F4\u65B0"
  }
};
var zh_TW_default = locale4;

// src/settings.ts
var import_sprintf_js = __toESM(require_sprintf());
var DEFAULT_SETTINGS = {
  Tabout: true,
  SelectionEnhance: true,
  IntrinsicSymbolPairs: true,
  BaseObEditEnhance: true,
  FW2HWEnhance: true,
  BetterCodeEdit: true,
  BetterBackspace: true,
  AutoFormat: true,
  ExcludeFiles: "",
  ChineseEnglishSpace: true,
  ChineseNumberSpace: true,
  EnglishNumberSpace: true,
  ChineseNoSpace: true,
  QuoteSpace: true,
  PunctuationSpace: true,
  AutoCapital: true,
  AutoCapitalMode: "typing" /* OnlyWhenTyping */,
  PunctuationSpaceMode: "typing" /* OnlyWhenTyping */,
  InlineCodeSpaceMode: 1 /* soft */,
  InlineFormulaSpaceMode: 1 /* soft */,
  InlineLinkSpaceMode: 1 /* soft */,
  InlineLinkSmartSpace: true,
  UserDefinedRegSwitch: true,
  UserDefinedRegExp: "{{.*?}}|++\n<.*?>|--\n\\[\\!.*?\\][-+]{0,1}|-+\n(file:///|https?://|ftp://|obsidian://|zotero://|www.)[^\\s\uFF08\uFF09\u300A\u300B\u3002,\uFF0C\uFF01\uFF1F;\uFF1B\uFF1A\u201C\u201D\u2018\u2019\\)\\(\\[\\]\\{\\}']+|--\n\n[a-zA-Z0-9_\\-.]+@[a-zA-Z0-9_\\-.]+|++\n(?<!#)#[\\u4e00-\\u9fa5\\w-\\/]+|++",
  debug: false,
  userSelRepRuleTrigger: ["-", "#"],
  userSelRepRuleValue: [{ left: "~~", right: "~~" }, { left: "#", right: " " }],
  userDeleteRulesStrList: [["demo|", "|"]],
  userConvertRulesStrList: [[":)|", "\u{1F600}|"]],
  userSelRuleSettingsOpen: true,
  userDelRuleSettingsOpen: true,
  userCvtRuleSettingsOpen: true,
  StrictModeEnter: false,
  StrictLineMode: "enter_twice" /* EnterTwice */,
  EnhanceModA: false,
  TryFixChineseIM: true,
  PuncRectify: false,
  FixMacOSContextMenu: false,
  TryFixMSIME: false
};
var locale5 = en_US_default;
var EasyTypingSettingTab = class extends import_obsidian2.PluginSettingTab {
  constructor(app, plugin) {
    super(app, plugin);
    this.plugin = plugin;
  }
  display() {
    const { containerEl } = this;
    if (import_obsidian2.moment.locale() == "zh" || import_obsidian2.moment.locale() == "zh-cn") {
      locale5 = zh_CN_default;
    } else if (import_obsidian2.moment.locale().toLowerCase() == "zh-tw") {
      locale5 = zh_TW_default;
    } else if (import_obsidian2.moment.locale() == "ru") {
      locale5 = ru_RU_default;
    }
    containerEl.empty();
    containerEl.createEl("h1", { text: locale5.headers.main });
    containerEl.createEl("p", { text: locale5.headers.githubDetail }).createEl("a", {
      text: "easy-typing-obsidian",
      href: "https://github.com/Yaozhuwa/easy-typing-obsidian"
    });
    containerEl.createEl("h2", { text: locale5.headers.enhancedEditing });
    new import_obsidian2.Setting(containerEl).setName(locale5.settings.symbolAutoPair.name).setDesc(locale5.settings.symbolAutoPair.desc).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.IntrinsicSymbolPairs).onChange(async (value) => {
        this.plugin.settings.IntrinsicSymbolPairs = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian2.Setting(containerEl).setName(locale5.settings.selectionReplace.name).setDesc(locale5.settings.selectionReplace.desc).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.SelectionEnhance).onChange(async (value) => {
        this.plugin.settings.SelectionEnhance = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian2.Setting(containerEl).setName(locale5.settings.fullWidthToHalfWidth.name).setDesc(locale5.settings.fullWidthToHalfWidth.desc).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.FW2HWEnhance).onChange(async (value) => {
        this.plugin.settings.FW2HWEnhance = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian2.Setting(containerEl).setName(locale5.settings.basicInputEnhance.name).setDesc(locale5.settings.basicInputEnhance.desc).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.BaseObEditEnhance).onChange(async (value) => {
        this.plugin.settings.BaseObEditEnhance = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian2.Setting(containerEl).setName(locale5.settings.codeblockEdit.name).setDesc(locale5.settings.codeblockEdit.desc).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.BetterCodeEdit).onChange(async (value) => {
        this.plugin.settings.BetterCodeEdit = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian2.Setting(containerEl).setName(locale5.settings.backspaceEdit.name).setDesc(locale5.settings.backspaceEdit.desc).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.BetterBackspace).onChange(async (value) => {
        this.plugin.settings.BetterBackspace = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian2.Setting(containerEl).setName(locale5.settings.tabOut.name).setDesc(locale5.settings.tabOut.desc).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.Tabout).onChange(async (value) => {
        this.plugin.settings.Tabout = value;
        await this.plugin.saveSettings();
      });
    });
    containerEl.createEl("h2", { text: locale5.headers.customizeEditRule });
    this.buildUserSelRepRuleSetting(this.containerEl.createEl("details", {
      cls: "easytyping-nested-settings",
      attr: {
        ...this.plugin.settings.userSelRuleSettingsOpen ? { open: true } : {}
      }
    }));
    this.buildUserDeleteRuleSetting(this.containerEl.createEl("details", {
      cls: "easytyping-nested-settings",
      attr: {
        ...this.plugin.settings.userDelRuleSettingsOpen ? { open: true } : {}
      }
    }));
    this.buildUserConvertRuleSetting(this.containerEl.createEl("details", {
      cls: "easytyping-nested-settings",
      attr: {
        ...this.plugin.settings.userCvtRuleSettingsOpen ? { open: true } : {}
      }
    }));
    containerEl.createEl("h2", { text: locale5.headers.autoformatSetting });
    new import_obsidian2.Setting(containerEl).setName(locale5.settings.autoFormatting.name).setDesc(locale5.settings.autoFormatting.desc).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.AutoFormat).onChange(async (value) => {
        this.plugin.settings.AutoFormat = value;
        await this.plugin.saveSettings();
      });
    });
    containerEl.createEl("p", { text: locale5.headers.detailedSetting });
    new import_obsidian2.Setting(containerEl).setName(locale5.settings.spaceBetweenChineseEnglish.name).setDesc(locale5.settings.spaceBetweenChineseEnglish.desc).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.ChineseEnglishSpace).onChange(async (value) => {
        this.plugin.settings.ChineseEnglishSpace = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian2.Setting(containerEl).setName(locale5.settings.spaceBetweenChineseNumber.name).setDesc(locale5.settings.spaceBetweenChineseNumber.desc).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.ChineseNumberSpace).onChange(async (value) => {
        this.plugin.settings.ChineseNumberSpace = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian2.Setting(containerEl).setName(locale5.settings.spaceBetweenEnglishNumber.name).setDesc(locale5.settings.spaceBetweenEnglishNumber.desc).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.EnglishNumberSpace).onChange(async (value) => {
        this.plugin.settings.EnglishNumberSpace = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian2.Setting(containerEl).setName(locale5.settings.deleteSpaceBetweenChinese.name).setDesc(locale5.settings.deleteSpaceBetweenChinese.desc).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.ChineseNoSpace).onChange(async (value) => {
        this.plugin.settings.ChineseNoSpace = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian2.Setting(containerEl).setName(locale5.settings.quoteSpace.name).setDesc(locale5.settings.quoteSpace.desc).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.QuoteSpace).onChange(async (value) => {
        this.plugin.settings.QuoteSpace = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian2.Setting(containerEl).setName(locale5.settings.capitalizeFirstLetter.name).setDesc(locale5.settings.capitalizeFirstLetter.desc).addDropdown((dropdown) => {
      dropdown.addOption("typing" /* OnlyWhenTyping */, locale5.dropdownOptions.onlyWhenTyping);
      dropdown.addOption("global" /* Globally */, locale5.dropdownOptions.globally);
      dropdown.setValue(this.plugin.settings.AutoCapitalMode);
      dropdown.onChange(async (v) => {
        this.plugin.settings.AutoCapitalMode = v;
        await this.plugin.saveSettings();
      });
    }).addToggle((toggle) => {
      toggle.setTooltip(locale5.toolTip.switch);
      toggle.setValue(this.plugin.settings.AutoCapital).onChange(async (value) => {
        this.plugin.settings.AutoCapital = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian2.Setting(containerEl).setName(locale5.settings.smartInsertSpace.name).setDesc(locale5.settings.smartInsertSpace.desc).addDropdown((dropdown) => {
      dropdown.addOption("typing" /* OnlyWhenTyping */, locale5.dropdownOptions.onlyWhenTyping);
      dropdown.addOption("global" /* Globally */, locale5.dropdownOptions.globally);
      dropdown.setValue(this.plugin.settings.PunctuationSpaceMode);
      dropdown.onChange(async (v) => {
        this.plugin.settings.PunctuationSpaceMode = v;
        await this.plugin.saveSettings();
      });
    }).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.PunctuationSpace).onChange(async (value) => {
        this.plugin.settings.PunctuationSpace = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian2.Setting(containerEl).setName(locale5.settings.spaceStrategyInlineCode.name).setDesc(locale5.settings.spaceStrategyInlineCode.desc).addDropdown((dropdown) => {
      dropdown.addOption(String(0 /* none */), locale5.dropdownOptions.noRequire);
      dropdown.addOption(String(1 /* soft */), locale5.dropdownOptions.softSpace);
      dropdown.addOption(String(2 /* strict */), locale5.dropdownOptions.strictSpace);
      dropdown.setValue(String(this.plugin.settings.InlineCodeSpaceMode));
      dropdown.onChange(async (v) => {
        this.plugin.settings.InlineCodeSpaceMode = string2SpaceState(v);
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian2.Setting(containerEl).setName(locale5.settings.spaceStrategyInlineFormula.name).setDesc(locale5.settings.spaceStrategyInlineFormula.desc).addDropdown((dropdown) => {
      dropdown.addOption(String(0 /* none */), locale5.dropdownOptions.noRequire);
      dropdown.addOption(String(1 /* soft */), locale5.dropdownOptions.softSpace);
      dropdown.addOption(String(2 /* strict */), locale5.dropdownOptions.strictSpace);
      dropdown.setValue(String(this.plugin.settings.InlineFormulaSpaceMode));
      dropdown.onChange(async (v) => {
        this.plugin.settings.InlineFormulaSpaceMode = string2SpaceState(v);
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian2.Setting(containerEl).setName(locale5.settings.spaceStrategyLinkText.name).setDesc(locale5.settings.spaceStrategyLinkText.desc).addDropdown((dropdown) => {
      dropdown.addOption("dummy", locale5.dropdownOptions.dummy);
      dropdown.addOption("smart", locale5.dropdownOptions.smart);
      dropdown.setValue(this.plugin.settings.InlineLinkSmartSpace ? "smart" : "dummy");
      dropdown.onChange(async (v) => {
        this.plugin.settings.InlineLinkSmartSpace = v == "smart" ? true : false;
        await this.plugin.saveSettings();
      });
    }).addDropdown((dropdown) => {
      dropdown.addOption(String(0 /* none */), locale5.dropdownOptions.noRequire);
      dropdown.addOption(String(1 /* soft */), locale5.dropdownOptions.softSpace);
      dropdown.addOption(String(2 /* strict */), locale5.dropdownOptions.strictSpace);
      dropdown.setValue(String(this.plugin.settings.InlineLinkSpaceMode));
      dropdown.onChange(async (v) => {
        this.plugin.settings.InlineLinkSpaceMode = string2SpaceState(v);
        await this.plugin.saveSettings();
      });
    });
    containerEl.createEl("h2", { text: locale5.headers.customRegexpBlock });
    new import_obsidian2.Setting(containerEl).setName(locale5.settings.userDefinedRegexpSwitch.name).setDesc(locale5.settings.userDefinedRegexpSwitch.desc).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.UserDefinedRegSwitch).onChange(async (value) => {
        this.plugin.settings.UserDefinedRegSwitch = value;
        await this.plugin.saveSettings();
      });
    });
    containerEl.createEl("p", { text: locale5.headers.aboutRegexp.header }).createEl("a", {
      text: locale5.headers.aboutRegexp.text,
      href: "https://javascript.ruanyifeng.com/stdlib/regexp.html#"
    });
    containerEl.createEl("p", { text: locale5.headers.instructionsRegexp.header }).createEl("a", {
      text: locale5.headers.instructionsRegexp.text,
      href: "https://github.com/Yaozhuwa/easy-typing-obsidian/blob/master/UserDefinedRegExp.md"
    });
    const regContentAreaSetting = new import_obsidian2.Setting(containerEl);
    regContentAreaSetting.settingEl.setAttribute("style", "display: grid; grid-template-columns: 1fr;");
    regContentAreaSetting.setName(locale5.settings.userDefinedRegexp.name).setDesc(locale5.settings.userDefinedRegexp.desc);
    const regContentArea = new import_obsidian2.TextAreaComponent(regContentAreaSetting.controlEl);
    setAttributes(regContentArea.inputEl, {
      style: "margin-top: 12px; width: 100%;  height: 30vh;"
    });
    regContentArea.setValue(this.plugin.settings.UserDefinedRegExp).onChange(async (value) => {
      this.plugin.settings.UserDefinedRegExp = value;
      this.plugin.saveSettings();
    });
    containerEl.createEl("h2", { text: locale5.headers.excludeFoldersFiles });
    new import_obsidian2.Setting(containerEl).setName(locale5.settings.excludeFoldersFiles.name).setDesc(locale5.settings.excludeFoldersFiles.desc).addTextArea((text) => text.setValue(this.plugin.settings.ExcludeFiles).onChange(async (value) => {
      this.plugin.settings.ExcludeFiles = value;
      this.plugin.saveSettings();
    }));
    containerEl.createEl("h2", { text: locale5.headers.experimentalFeatures });
    new import_obsidian2.Setting(containerEl).setName(locale5.settings.strictLineBreaks.name).setDesc(locale5.settings.strictLineBreaks.desc).addDropdown((dropdown) => {
      dropdown.addOption("enter_twice" /* EnterTwice */, locale5.dropdownOptions.enterTwice);
      dropdown.addOption("two_space" /* TwoSpace */, locale5.dropdownOptions.twoSpace);
      dropdown.addOption("mix_mode" /* Mix */, locale5.dropdownOptions.mixMode);
      dropdown.setValue(this.plugin.settings.StrictLineMode);
      dropdown.onChange(async (v) => {
        this.plugin.settings.StrictLineMode = v;
        await this.plugin.saveSettings();
      });
    }).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.StrictModeEnter).onChange(async (value) => {
        this.plugin.settings.StrictModeEnter = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian2.Setting(containerEl).setName(locale5.settings.enhanceModA.name).setDesc(locale5.settings.enhanceModA.desc).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.EnhanceModA).onChange(async (value) => {
        this.plugin.settings.EnhanceModA = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian2.Setting(containerEl).setName(locale5.settings.fixMicrosoftIME.name).setDesc(locale5.settings.fixMicrosoftIME.desc).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.TryFixMSIME).onChange(async (value) => {
        this.plugin.settings.TryFixMSIME = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian2.Setting(containerEl).setName(locale5.settings.fixMacOSContextMenu.name).setDesc(locale5.settings.fixMacOSContextMenu.desc).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.FixMacOSContextMenu).onChange(async (value) => {
        this.plugin.settings.FixMacOSContextMenu = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian2.Setting(containerEl).setName(locale5.settings.puncRectify.name).setDesc(locale5.settings.puncRectify.desc).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.PuncRectify).onChange(async (value) => {
        this.plugin.settings.PuncRectify = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian2.Setting(containerEl).setName(locale5.settings.printDebugInfo.name).setDesc(locale5.settings.printDebugInfo.desc).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.debug).onChange(async (value) => {
        this.plugin.settings.debug = value;
        setDebug(value);
        await this.plugin.saveSettings();
      });
    });
  }
  buildUserSelRepRuleSetting(containerEl) {
    containerEl.empty();
    containerEl.ontoggle = async () => {
      this.plugin.settings.userSelRuleSettingsOpen = containerEl.open;
      await this.plugin.saveSettings();
    };
    const summary = containerEl.createEl("summary", { cls: "easytyping-nested-settings" });
    summary.setText(locale5.headers.customizeSelectionRule);
    const selectionRuleSetting = new import_obsidian2.Setting(containerEl);
    selectionRuleSetting.setName(locale5.settings.selectionReplaceRule.name);
    const replaceRuleTrigger = new import_obsidian2.TextComponent(selectionRuleSetting.controlEl);
    replaceRuleTrigger.setPlaceholder(locale5.placeHolder.triggerSymbol);
    const replaceLeftString = new import_obsidian2.TextComponent(selectionRuleSetting.controlEl);
    replaceLeftString.setPlaceholder(locale5.placeHolder.newLeftSideString);
    const replaceRightString = new import_obsidian2.TextComponent(selectionRuleSetting.controlEl);
    replaceRightString.setPlaceholder(locale5.placeHolder.newRightSideString);
    selectionRuleSetting.addButton((button) => {
      button.setButtonText("+").setTooltip(locale5.placeHolder.addRule).onClick(async (buttonEl) => {
        let trigger = replaceRuleTrigger.inputEl.value;
        let left = replaceLeftString.inputEl.value;
        let right = replaceRightString.inputEl.value;
        if (trigger && (left || right)) {
          if (trigger.length != 1 && trigger != "\u2014\u2014" && trigger != "\u2026\u2026") {
            new import_obsidian2.Notice(locale5.placeHolder.noticeInvaidTrigger);
            return;
          }
          if (this.plugin.addUserSelectionRepRule(trigger, left, right)) {
            await this.plugin.saveSettings();
            this.display();
          } else {
            new import_obsidian2.Notice((0, import_sprintf_js.sprintf)(locale5.placeHolder.noticeWarnTriggerExists, trigger));
          }
        } else {
          new import_obsidian2.Notice(locale5.placeHolder.noticeMissingInput);
        }
      });
    });
    for (let i = 0; i < this.plugin.settings.userSelRepRuleTrigger.length; i++) {
      let trigger = this.plugin.settings.userSelRepRuleTrigger[i];
      let left_s = this.plugin.settings.userSelRepRuleValue[i].left;
      let right_s = this.plugin.settings.userSelRepRuleValue[i].right;
      let showStr = "Trigger: " + trigger + " \u2192 " + showString(left_s) + "selected" + showString(right_s);
      new import_obsidian2.Setting(containerEl).setName(showStr).addExtraButton((button) => {
        button.setIcon("gear").setTooltip(locale5.toolTip.editRule).onClick(() => {
          new SelectRuleEditModal(this.app, trigger, left_s, right_s, async (new_left, new_right) => {
            this.plugin.updateUserSelectionRepRule(i, new_left, new_right);
            await this.plugin.saveSettings();
            this.display();
          }).open();
        });
      }).addExtraButton((button) => {
        button.setIcon("trash").setTooltip(locale5.toolTip.removeRule).onClick(async () => {
          this.plugin.deleteUserSelectionRepRule(i);
          await this.plugin.saveSettings();
          this.display();
        });
      });
    }
  }
  buildUserDeleteRuleSetting(containerEl) {
    containerEl.empty();
    containerEl.ontoggle = async () => {
      this.plugin.settings.userDelRuleSettingsOpen = containerEl.open;
      await this.plugin.saveSettings();
    };
    const summary = containerEl.createEl("summary", { cls: "easytyping-nested-settings" });
    summary.setText(locale5.headers.customizeDeleteRule);
    const deleteRuleSetting = new import_obsidian2.Setting(containerEl);
    deleteRuleSetting.setName(locale5.settings.deleteRule.name).setDesc(locale5.settings.deleteRule.desc);
    const patternBefore = new import_obsidian2.TextAreaComponent(deleteRuleSetting.controlEl);
    patternBefore.setPlaceholder(locale5.placeHolder.beforeDelete);
    const patternAfter = new import_obsidian2.TextAreaComponent(deleteRuleSetting.controlEl);
    patternAfter.setPlaceholder(locale5.placeHolder.newPattern);
    deleteRuleSetting.addButton((button) => {
      button.setButtonText("+").setTooltip(locale5.toolTip.addRule).onClick(async (buttonEl) => {
        let before = patternBefore.inputEl.value;
        let after = patternAfter.inputEl.value;
        if (before && after) {
          if (findFirstPipeNotPrecededByBackslash(before) == -1) {
            new import_obsidian2.Notice(locale5.placeHolder.noticeInvaidTriggerPatternContainSymbol);
            return;
          } else {
            this.plugin.addUserDeleteRule(before, after);
            await this.plugin.saveSettings();
            this.display();
          }
        } else {
          new import_obsidian2.Notice(locale5.placeHolder.noticeMissingInput);
        }
      });
    });
    for (let i = 0; i < this.plugin.settings.userDeleteRulesStrList.length; i++) {
      let before = this.plugin.settings.userDeleteRulesStrList[i][0];
      let after = this.plugin.settings.userDeleteRulesStrList[i][1];
      let showStr = '"' + showString(before) + '"  delete.backwards  \u2192 "' + showString(after) + '"';
      new import_obsidian2.Setting(containerEl).setName(showStr).addExtraButton((button) => {
        button.setIcon("gear").setTooltip(locale5.toolTip.editRule).onClick(() => {
          new EditConvertRuleModal(this.app, "Delete Rule" /* delete */, before, after, async (new_before, new_after) => {
            this.plugin.updateUserDeleteRule(i, new_before, new_after);
            await this.plugin.saveSettings();
            this.display();
          }).open();
        });
      }).addExtraButton((button) => {
        button.setIcon("trash").setTooltip(locale5.toolTip.removeRule).onClick(async () => {
          this.plugin.deleteUserDeleteRule(i);
          await this.plugin.saveSettings();
          this.display();
        });
      });
    }
  }
  buildUserConvertRuleSetting(containerEl) {
    containerEl.empty();
    containerEl.ontoggle = async () => {
      this.plugin.settings.userCvtRuleSettingsOpen = containerEl.open;
      await this.plugin.saveSettings();
    };
    const summary = containerEl.createEl("summary", { cls: "easytyping-nested-settings" });
    summary.setText(locale5.headers.customizeConvertRule);
    const convertRuleSetting = new import_obsidian2.Setting(containerEl);
    convertRuleSetting.setName(locale5.settings.convertRule.name).setDesc(locale5.settings.convertRule.desc);
    const patternBefore = new import_obsidian2.TextAreaComponent(convertRuleSetting.controlEl);
    patternBefore.setPlaceholder(locale5.placeHolder.beforeConvert);
    const patternAfter = new import_obsidian2.TextAreaComponent(convertRuleSetting.controlEl);
    patternAfter.setPlaceholder(locale5.placeHolder.newPattern);
    convertRuleSetting.addButton((button) => {
      button.setButtonText("+").setTooltip(locale5.toolTip.addRule).onClick(async (buttonEl) => {
        let before = patternBefore.inputEl.value;
        let after = patternAfter.inputEl.value;
        if (before && after) {
          if (findFirstPipeNotPrecededByBackslash(before) == -1) {
            new import_obsidian2.Notice(locale5.placeHolder.noticeInvaidTriggerPatternContainSymbol);
            return;
          } else {
            this.plugin.addUserConvertRule(before, after);
            await this.plugin.saveSettings();
            this.display();
          }
        } else {
          new import_obsidian2.Notice(locale5.placeHolder.noticeMissingInput);
        }
      });
    });
    for (let i = 0; i < this.plugin.settings.userConvertRulesStrList.length; i++) {
      let before = this.plugin.settings.userConvertRulesStrList[i][0];
      let after = this.plugin.settings.userConvertRulesStrList[i][1];
      let showStr = '"' + showString(before) + '"  auto convert to "' + showString(after) + '"';
      new import_obsidian2.Setting(containerEl).setName(showStr).addExtraButton((button) => {
        button.setIcon("gear").setTooltip(locale5.toolTip.editRule).onClick(() => {
          new EditConvertRuleModal(this.app, "Convert Rule" /* convert */, before, after, async (new_before, new_after) => {
            this.plugin.updateUserConvertRule(i, new_before, new_after);
            await this.plugin.saveSettings();
            this.display();
          }).open();
        });
      }).addExtraButton((button) => {
        button.setIcon("trash").setTooltip(locale5.toolTip.removeRule).onClick(async () => {
          this.plugin.deleteUserConvertRule(i);
          await this.plugin.saveSettings();
          this.display();
        });
      });
    }
  }
};
function setAttributes(element, attributes) {
  for (let key in attributes) {
    element.setAttribute(key, attributes[key]);
  }
}
var SelectRuleEditModal = class extends import_obsidian2.Modal {
  constructor(app, trigger, left, right, onSubmit) {
    super(app);
    this.trigger = trigger;
    this.old_left = left;
    this.old_right = right;
    this.new_left = left;
    this.new_right = right;
    this.onSubmit = onSubmit;
  }
  onOpen() {
    const { contentEl } = this;
    contentEl.createEl("h1", { text: locale5.headers.editSelectionReplaceRule });
    new import_obsidian2.Setting(contentEl).setName(locale5.settings.trigger.name).addText((text) => {
      text.setValue(this.trigger);
      text.setDisabled(true);
    });
    new import_obsidian2.Setting(contentEl).setName(locale5.settings.left.name).addTextArea((text) => {
      text.setValue(this.old_left);
      text.onChange((value) => {
        this.new_left = value;
      });
    });
    new import_obsidian2.Setting(contentEl).setName(locale5.settings.right.name).addTextArea((text) => {
      text.setValue(this.old_right);
      text.onChange((value) => {
        this.new_right = value;
      });
    });
    new import_obsidian2.Setting(contentEl).addButton((btn) => btn.setButtonText(locale5.button.update).setCta().onClick(() => {
      this.close();
      this.onSubmit(this.new_left, this.new_right);
    }));
  }
  onClose() {
    let { contentEl } = this;
    contentEl.empty();
  }
};
var EditConvertRuleModal = class extends import_obsidian2.Modal {
  constructor(app, type, before, after, onSubmit) {
    super(app);
    this.type = type;
    this.old_before = before;
    this.old_after = after;
    this.new_before = before;
    this.new_after = after;
    this.onSubmit = onSubmit;
  }
  onOpen() {
    const { contentEl } = this;
    contentEl.createEl("h1", { text: "Edit " + this.type });
    new import_obsidian2.Setting(contentEl).setName(locale5.settings.oldPattern.name).addTextArea((text) => {
      text.setValue(this.old_before);
      text.onChange((value) => {
        this.new_before = value;
      });
    });
    new import_obsidian2.Setting(contentEl).setName(locale5.settings.newPattern.name).addTextArea((text) => {
      text.setValue(this.old_after);
      text.onChange((value) => {
        this.new_after = value;
      });
    });
    new import_obsidian2.Setting(contentEl).addButton((btn) => btn.setButtonText(locale5.button.update).setCta().onClick(() => {
      if (this.checkConvertPatternString(this.new_before, this.new_after)) {
        this.close();
        this.onSubmit(this.new_before, this.new_after);
      } else {
        new import_obsidian2.Notice(locale5.placeHolder.noticeInvalidPatternString);
      }
    }));
  }
  checkConvertPatternString(before, after) {
    if (findFirstPipeNotPrecededByBackslash(before) == -1)
      return false;
    return true;
  }
  onClose() {
    let { contentEl } = this;
    contentEl.empty();
  }
};

// src/main.ts
var import_view3 = require("@codemirror/view");
var import_language3 = require("@codemirror/language");

// src/syntax.ts
var import_language2 = require("@codemirror/language");
function isCodeBlockInPos(state, pos) {
  let codeBlockInfos = getCodeBlocksInfos(state);
  for (let i = 0; i < codeBlockInfos.length; i++) {
    if (pos >= codeBlockInfos[i].start_pos && pos <= codeBlockInfos[i].end_pos) {
      return true;
    }
  }
  return false;
}
function getCodeBlockInfoInPos(state, pos) {
  let codeBlockInfos = getCodeBlocksInfos(state);
  for (let i = 0; i < codeBlockInfos.length; i++) {
    if (pos >= codeBlockInfos[i].start_pos && pos <= codeBlockInfos[i].end_pos) {
      return codeBlockInfos[i];
    }
  }
  return null;
}
function selectCodeBlockInPos(view, selection) {
  let pos = selection.anchor;
  let codeBlockInfos = getCodeBlocksInfos(view.state);
  for (let i = 0; i < codeBlockInfos.length; i++) {
    if (pos >= codeBlockInfos[i].start_pos && pos <= codeBlockInfos[i].end_pos) {
      if (codeBlockInfos[i].code_start_pos == codeBlockInfos[i].code_end_pos) {
        view.dispatch({
          selection: {
            anchor: codeBlockInfos[i].start_pos,
            head: codeBlockInfos[i].end_pos
          }
        });
        return true;
      }
      let code_line_start = view.state.doc.lineAt(codeBlockInfos[i].code_start_pos);
      let isCodeSelected = selection.anchor == code_line_start.from && selection.head == codeBlockInfos[i].code_end_pos;
      let isCodeBlockSelected = selection.anchor == codeBlockInfos[i].start_pos && selection.head == codeBlockInfos[i].end_pos;
      if (isCodeSelected) {
        view.dispatch({
          selection: {
            anchor: codeBlockInfos[i].start_pos,
            head: codeBlockInfos[i].end_pos
          }
        });
        return true;
      }
      if (isCodeBlockSelected)
        return false;
      view.dispatch({
        selection: {
          anchor: code_line_start.from,
          head: codeBlockInfos[i].code_end_pos
        }
      });
      return true;
    }
  }
  return false;
}
function getCodeBlocksInfos(state) {
  let isCodeBlockBegin = false;
  let codeBlockInfos = [];
  let curCodeBlockInfo = null;
  const doc = state.doc;
  (0, import_language2.syntaxTree)(state).iterate({
    enter(node) {
      const nodeName = node.name;
      const nodeFrom = node.from;
      const nodeTo = node.to;
      const nodeText = state.sliceDoc(nodeFrom, nodeTo);
      if (nodeName.includes("codeblock-begin")) {
        isCodeBlockBegin = true;
        let start_pos = nodeFrom + nodeText.indexOf("`");
        let indent = start_pos - state.doc.lineAt(start_pos).from;
        let language = nodeText.trim().substring(3);
        curCodeBlockInfo = {
          start_pos,
          end_pos: -1,
          code_start_pos: -1,
          code_end_pos: -1,
          language: language.toLowerCase(),
          indent
        };
      } else if (nodeName.includes("codeblock-end")) {
        isCodeBlockBegin = false;
        if (curCodeBlockInfo != null) {
          curCodeBlockInfo.end_pos = nodeTo;
          if (doc.lineAt(curCodeBlockInfo.start_pos).number == doc.lineAt(curCodeBlockInfo.end_pos).number - 1) {
            curCodeBlockInfo.code_start_pos = doc.lineAt(curCodeBlockInfo.start_pos).to;
            curCodeBlockInfo.code_end_pos = doc.lineAt(curCodeBlockInfo.start_pos).to;
          } else {
            let code_start_line = doc.lineAt(curCodeBlockInfo.start_pos).number + 1;
            let code_end_line = doc.lineAt(curCodeBlockInfo.end_pos).number - 1;
            curCodeBlockInfo.code_start_pos = doc.line(code_start_line).from + curCodeBlockInfo.indent;
            curCodeBlockInfo.code_end_pos = doc.line(code_end_line).to;
          }
          codeBlockInfos.push(curCodeBlockInfo);
          curCodeBlockInfo = null;
        }
      }
    }
  });
  if (isCodeBlockBegin && curCodeBlockInfo) {
    curCodeBlockInfo.end_pos = doc.length;
    curCodeBlockInfo.code_end_pos = doc.length;
    if (doc.lines > doc.lineAt(curCodeBlockInfo.start_pos).number) {
      let start_line = doc.lineAt(curCodeBlockInfo.start_pos).number + 1;
      let code_start_pos = doc.line(start_line).from + curCodeBlockInfo.indent;
      curCodeBlockInfo.code_start_pos = code_start_pos < doc.length ? code_start_pos : doc.lineAt(curCodeBlockInfo.start_pos + 1).from;
    } else {
      curCodeBlockInfo.code_start_pos = doc.lineAt(curCodeBlockInfo.start_pos).to;
    }
    codeBlockInfos.push(curCodeBlockInfo);
    curCodeBlockInfo = null;
  }
  return codeBlockInfos;
}
function getQuoteInfoInPos(state, pos) {
  let quote_regex = /^(\s*)(>+) ?/;
  let callout_regex = /^(\s*)(>)+ \[![^\s]+\][+-]? ?/;
  let cur_line = state.doc.lineAt(pos);
  let match = cur_line.text.match(quote_regex);
  let is_callout = false;
  let cur_start_pos = -1;
  let cur_end_pos = -1;
  if (match) {
    let match_callout = cur_line.text.match(callout_regex);
    cur_start_pos = cur_line.from + (match_callout ? match_callout[0].length : match[0].length);
    cur_end_pos = cur_line.to;
    let quote_start_line = cur_line.number;
    let quote_end_line = quote_start_line;
    for (let i = quote_start_line + 1; i <= state.doc.lines; i += 1) {
      let line = state.doc.line(i);
      if (line.text.match(quote_regex)) {
        quote_end_line = i;
      } else
        break;
    }
    for (let i = quote_start_line; i >= 1; i -= 1) {
      let line = state.doc.line(i);
      let match_callout2 = line.text.match(callout_regex);
      let match_quote = line.text.match(quote_regex);
      if (match_callout2) {
        is_callout = true;
        quote_start_line = i;
      } else if (match_quote) {
        quote_start_line = i;
      } else
        break;
    }
    return {
      start_pos: state.doc.line(quote_start_line).from,
      end_pos: state.doc.line(quote_end_line).to,
      is_callout,
      cur_start_pos,
      cur_end_pos
    };
  } else {
    return null;
  }
}

// src/tabstops_state_field.ts
var import_view = require("@codemirror/view");
var import_state = require("@codemirror/state");
var addTabstopsEffect = import_state.StateEffect.define();
var removeTabstopEffect = import_state.StateEffect.define();
var removeAllTabstopsEffect = import_state.StateEffect.define();
var tabstopsStateField = import_state.StateField.define({
  create() {
    return [];
  },
  update(value, transaction) {
    let tabstopGroups = value;
    tabstopGroups.forEach((grp) => grp.map(transaction.changes));
    for (const effect of transaction.effects) {
      if (effect.is(addTabstopsEffect)) {
        tabstopGroups = [];
        tabstopGroups.unshift(...effect.value);
      } else if (effect.is(removeTabstopEffect)) {
        tabstopGroups.shift();
      } else if (effect.is(removeAllTabstopsEffect)) {
        tabstopGroups = [];
      }
    }
    return tabstopGroups;
  },
  provide: (field) => {
    return import_view.EditorView.decorations.of((view) => {
      const tabstopGroups = view.state.field(field);
      const decos = [];
      if (tabstopGroups.length >= 2) {
        decos.push(...tabstopGroups[1].getDecoRanges());
      }
      return import_view.Decoration.set(decos, true);
    });
  }
});
function getTabstopGroupsFromView(view) {
  const currentTabstopGroups = view.state.field(tabstopsStateField);
  return currentTabstopGroups;
}
function addTabstops(view, tabstopGroups) {
  view.dispatch({
    effects: [addTabstopsEffect.of(tabstopGroups)]
  });
}
function removeTabstop(view) {
  view.dispatch({
    effects: [removeTabstopEffect.of(null)]
  });
}
function removeAllTabstops(view) {
  view.dispatch({
    effects: [removeAllTabstopsEffect.of(null)]
  });
}
function addTabstopsAndSelect(view, tabstopGroups) {
  addTabstops(view, tabstopGroups);
  tabstopGroups[0].select(view, false);
}
function tidyTabstops(view) {
  const currentTabstopGroups = getTabstopGroupsFromView(view);
  if (currentTabstopGroups.length === 1) {
    removeAllTabstops(view);
  }
}
function isInsideCurTabstop(view) {
  const currentTabstopGroups = getTabstopGroupsFromView(view);
  if (currentTabstopGroups.length > 1 && currentTabstopGroups[0].containsSelection(view.state.selection)) {
    return true;
  }
  return false;
}
function consumeAndGotoNextTabstop(view) {
  if (getTabstopGroupsFromView(view).length === 0)
    return false;
  removeTabstop(view);
  const oldSel = view.state.selection;
  const nextGrp = getTabstopGroupsFromView(view)[0];
  if (!nextGrp)
    return false;
  const shouldMoveToEndpoints = nextGrp.containsSelection(oldSel);
  nextGrp.select(view, shouldMoveToEndpoints);
  const newSel = view.state.selection;
  if (oldSel.eq(newSel))
    return consumeAndGotoNextTabstop(view);
  tidyTabstops(view);
  return true;
}

// src/tabstop.ts
var import_state2 = require("@codemirror/state");
var import_view2 = require("@codemirror/view");
var TABSTOP_DECO_CLASS = "easy-typing-tabstops";
var CURSOR_WIDGET_CLASS = "easy-typing-cursor-widget";
function getMarkerDecoration(from, to) {
  const className = `${TABSTOP_DECO_CLASS}`;
  if (from == to) {
    return import_view2.Decoration.widget({
      widget: new CursorWidget(),
      side: 1
    }).range(from);
  }
  return import_view2.Decoration.mark({
    inclusive: true,
    class: className
  }).range(from, to);
}
var TabstopGroup = class {
  constructor(tabstopSpecs) {
    const decos = tabstopSpecs.map((spec) => getMarkerDecoration(spec.from, spec.to));
    this.selections = tabstopSpecs.map((spec) => import_state2.EditorSelection.range(spec.from, spec.to));
    this.decos = import_view2.Decoration.set(decos, true);
  }
  select(view, selectEndpoints) {
    const sel = this.toEditorSelection();
    const toSelect = selectEndpoints ? getEditorSelectionEndpoints(sel) : sel;
    view.dispatch({
      selection: toSelect
    });
  }
  toSelectionRanges() {
    return this.selections;
  }
  toEditorSelection() {
    return import_state2.EditorSelection.create(this.toSelectionRanges());
  }
  containsSelection(selection) {
    function rangeLiesWithinSelection(range, sel) {
      for (const selRange of sel) {
        if (selRange.from <= range.from && selRange.to >= range.to) {
          return true;
        }
      }
      return false;
    }
    const tabstopRanges = this.toSelectionRanges();
    let result = true;
    for (const range of selection.ranges) {
      if (!rangeLiesWithinSelection(range, tabstopRanges)) {
        result = false;
        break;
      }
    }
    return result;
  }
  map(changes) {
    this.decos = this.decos.map(changes);
    this.selections = this.selections.map((range) => {
      let rangeFrom = changes.mapPos(range.from, -1);
      let rangeTo = changes.mapPos(range.to, 1);
      return import_state2.EditorSelection.range(rangeFrom, rangeTo);
    });
  }
  getDecoRanges() {
    const ranges = [];
    const cur = this.decos.iter();
    while (cur.value != null) {
      if (cur.from != cur.to) {
        ranges.push(cur.value.range(cur.from, cur.to));
      } else {
        ranges.push(cur.value.range(cur.from));
      }
      cur.next();
    }
    return ranges;
  }
};
function tabstopSpecsToTabstopGroups(tabstops) {
  const tabstopsByNumber = {};
  for (const tabstop of tabstops) {
    const n = String(tabstop.number);
    if (tabstopsByNumber[n]) {
      tabstopsByNumber[n].push(tabstop);
    } else {
      tabstopsByNumber[n] = [tabstop];
    }
  }
  const result = [];
  const numbers = Object.keys(tabstopsByNumber);
  numbers.sort((a, b) => parseInt(a) - parseInt(b));
  for (const number of numbers) {
    const grp = new TabstopGroup(tabstopsByNumber[number]);
    result.push(grp);
  }
  return result;
}
function getEditorSelectionEndpoints(sel) {
  const endpoints = sel.ranges.map((range) => import_state2.EditorSelection.range(range.to, range.to));
  return import_state2.EditorSelection.create(endpoints);
}
var CursorWidget = class extends import_view2.WidgetType {
  eq(widget) {
    return true;
  }
  toDOM(view) {
    const cursorEl = document.createElement("span");
    cursorEl.className = `${CURSOR_WIDGET_CLASS}`;
    cursorEl.textContent = "|";
    return cursorEl;
  }
};

// src/main.ts
var EasyTypingPlugin = class extends import_obsidian3.Plugin {
  constructor() {
    super(...arguments);
    this.getDefaultIndentChar = () => {
      let useTab = this.app.vault.config.useTab === void 0 ? true : false;
      let tabSize = this.app.vault.config.tabSize == void 0 ? 4 : this.app.vault.config.tabSize;
      let default_indent = useTab ? "	" : " ".repeat(tabSize);
      return default_indent;
    };
    this.transactionFilterPlugin = (tr) => {
      const changes = [];
      if (!tr.docChanged)
        return tr;
      let selected = tr.startState.selection.asSingle().main.anchor != tr.startState.selection.asSingle().main.head;
      let changeTypeStr = getTypeStrOfTransac(tr);
      tr.changes.iterChanges((fromA, toA, fromB, toB, inserted) => {
        var _a, _b, _c;
        let changedStr = tr.startState.sliceDoc(fromA, toA);
        let changestr_ = changedStr.replace(/\s/g, "0");
        let insertedStr = inserted.sliceString(0);
        if (this.settings.debug) {
          console.log("[TransactionFilter] type, fromA, toA, changed, fromB, toB, inserted");
          console.log(changeTypeStr, fromA, toA, changedStr, fromB, toB, insertedStr);
        }
        if (getPosLineType(tr.startState, fromA) == "table" /* table */)
          return tr;
        if (this.settings.SelectionEnhance) {
          if ((changeTypeStr == "input.type" || changeTypeStr == "input.type.compose") && fromA != toA && (fromB + 1 === toB || insertedStr == "\u2014\u2014" || insertedStr == "\u2026\u2026")) {
            if (this.SelectionReplaceMap.has(insertedStr)) {
              changes.push({ changes: { from: fromA, insert: (_a = this.SelectionReplaceMap.get(insertedStr)) == null ? void 0 : _a.left }, userEvent: "EasyTyping.change" });
              changes.push({ changes: { from: toA, insert: (_b = this.SelectionReplaceMap.get(insertedStr)) == null ? void 0 : _b.right }, userEvent: "EasyTyping.change" });
              tr = tr.startState.update(...changes);
              return tr;
            }
          }
        }
        if (this.settings.BetterCodeEdit && changeTypeStr.contains("paste") && fromA == fromB && isCodeBlockInPos(tr.startState, fromA)) {
          print("\u68C0\u6D4B\u5230\u5728\u4EE3\u7801\u5757\u4E2D\u7C98\u8D34");
          let line = tr.startState.doc.lineAt(fromB).text;
          let base_indent_num = (_c = getCodeBlockInfoInPos(tr.startState, fromA)) == null ? void 0 : _c.indent;
          let base_indent = base_indent_num == 0 ? "" : " ".repeat(base_indent_num);
          let inserted_lines = insertedStr.split("\n");
          if (inserted_lines.length > 1) {
            let min_indent_space = Infinity;
            for (let line2 of inserted_lines) {
              if (!/^\s*$/.test(line2)) {
                let indent = line2.match(/^\s*/)[0].length;
                min_indent_space = Math.min(min_indent_space, indent);
              }
            }
            let adjusted_lines = inserted_lines.map((line2, index) => {
              let trimmed_line = line2.substring(min_indent_space);
              trimmed_line = trimmed_line.replace(/[\t]/g, this.getDefaultIndentChar());
              if (index === 0) {
                return trimmed_line;
              } else {
                return base_indent + trimmed_line;
              }
            });
            let new_insertedStr = adjusted_lines.join("\n");
            changes.push({
              changes: { from: fromA, to: toA, insert: new_insertedStr },
              selection: { anchor: fromA + new_insertedStr.length },
              userEvent: "EasyTyping.change"
            });
            tr = tr.startState.update(...changes);
            return tr;
          }
        }
        if (this.settings.BaseObEditEnhance && changeTypeStr.contains("paste") && fromA == fromB && fromA == tr.startState.doc.lineAt(toA).to) {
          const lineContent = tr.startState.doc.lineAt(toA).text;
          const listMatch = lineContent.match(/^(\s*)([-*+] \[.\]|[-*+]|\d+\.)\s/);
          const quoteMatch = lineContent.match(/^(\s*)(>+)(\s)?/);
          if (listMatch || quoteMatch) {
            let prefix = listMatch ? listMatch[1] + listMatch[2] + " " : quoteMatch[1] + quoteMatch[2] + " ";
            let indent_num = listMatch ? listMatch[1].length : quoteMatch[1].length;
            let indent_str = indent_num == 0 ? "" : " ".repeat(indent_num);
            let inserted_lines = insertedStr.split("\n");
            let min_indent_space = Infinity;
            for (let line of inserted_lines) {
              if (!/^\s*$/.test(line)) {
                let indent = line.match(/^\s*/)[0].length;
                min_indent_space = Math.min(min_indent_space, indent);
              }
            }
            let paste_list = true;
            for (let line of inserted_lines) {
              if (line.match(/^(\s*)([-*+] \[.\]|[-*+]|\d+\.)\s/) || /^\s*$/.test(line)) {
                continue;
              } else {
                let indent = line.match(/^\s*/)[0].length;
                if (indent < min_indent_space + 2) {
                  paste_list = false;
                  break;
                }
              }
            }
            let adjusted_lines = [];
            if (paste_list && listMatch) {
              adjusted_lines = inserted_lines.map((line, index) => {
                let trimmed_line = line.substring(min_indent_space);
                trimmed_line = trimmed_line.replace(/[\t]/g, this.getDefaultIndentChar());
                if (index === 0) {
                  trimmed_line = trimmed_line.replace(/^([-*+] \[.\]|[-*+]|\d+\.)\s/, "");
                  return trimmed_line;
                } else {
                  return indent_str + trimmed_line;
                }
              });
            } else {
              adjusted_lines = inserted_lines.map((line, index) => {
                let trimmed_line = line.substring(min_indent_space);
                trimmed_line = trimmed_line.replace(/[\t]/g, this.getDefaultIndentChar());
                if (index === 0) {
                  return trimmed_line;
                } else {
                  return prefix + trimmed_line;
                }
              });
            }
            let new_insertedStr = adjusted_lines.join("\n");
            changes.push({
              changes: { from: fromA, to: toA, insert: new_insertedStr },
              selection: { anchor: fromA + new_insertedStr.length },
              userEvent: "EasyTyping.change"
            });
            tr = tr.startState.update(...changes);
            return tr;
          }
        }
        if (selected)
          return tr;
        if (this.settings.TryFixMSIME && changeTypeStr == "input.type.compose" && changedStr == "" && /^[\u4e00-\u9fa5]+$/.test(insertedStr)) {
          print("MS-IME Compose detected:", insertedStr);
          tr = tr.startState.update(...changes);
          return tr;
        }
        let codeblockinfo = getCodeBlockInfoInPos(tr.startState, toA);
        if (this.settings.BetterCodeEdit && changeTypeStr == "delete.backward" && !selected && codeblockinfo && toA > tr.startState.doc.lineAt(codeblockinfo.start_pos).to) {
          let line_number = tr.startState.doc.lineAt(toA).number;
          let cur_line = tr.startState.doc.lineAt(toA);
          let list_code_indent = codeblockinfo.indent;
          if (list_code_indent !== 0) {
            print("list_code, indent: ", list_code_indent);
            if (toA == cur_line.from + list_code_indent) {
              changes.push({ changes: { from: tr.startState.doc.line(line_number - 1).to, to: toA, insert: "" }, userEvent: "EasyTyping.change" });
              tr = tr.startState.update(...changes);
              return tr;
            }
            if (fromA >= cur_line.from && fromA < cur_line.from + list_code_indent && toA > cur_line.from + list_code_indent) {
              changes.push({ changes: { from: cur_line.from + list_code_indent, to: toA, insert: "" }, userEvent: "EasyTyping.change" });
              tr = tr.startState.update(...changes);
              return tr;
            }
          }
        }
        if (changeTypeStr === "delete.backward" && this.settings.IntrinsicSymbolPairs) {
          if (this.SymbolPairsMap.has(changedStr) && this.SymbolPairsMap.get(changedStr) === tr.startState.sliceDoc(toA, toA + 1)) {
            changes.push({ changes: { from: fromA, to: toA + 1 }, userEvent: "EasyTyping.change" });
            tr = tr.startState.update(...changes);
            return tr;
          }
          let line_content = tr.startState.doc.lineAt(toA).text;
          let next_line_content = tr.startState.doc.sliceString(toA, toA + line_content.length + 1);
          if (/^\s*```$/.test(line_content) && "\n" + line_content == next_line_content) {
            changes.push({
              changes: {
                from: toA - 3,
                to: toA + line_content.length + 1,
                insert: ""
              },
              selection: { anchor: toA - 3 },
              userEvent: "EasyTyping.change"
            });
            tr = tr.startState.update(...changes);
            return tr;
          }
          for (let rule of this.IntrinsicDeleteRules) {
            let left = tr.startState.doc.sliceString(toA - rule.before.left.length, toA);
            let right = tr.startState.doc.sliceString(toA, toA + rule.before.right.length);
            if (left === rule.before.left && right === rule.before.right) {
              changes.push({
                changes: {
                  from: toA - rule.before.left.length,
                  to: toA + rule.before.right.length,
                  insert: rule.after.left + rule.after.right
                },
                selection: { anchor: toA - rule.before.left.length + rule.after.left.length },
                userEvent: "EasyTyping.change"
              });
              tr = tr.startState.update(...changes);
              return tr;
            }
          }
        }
        if (changeTypeStr == "delete.backward") {
          for (let rule of this.UserDeleteRules) {
            let leftDocStr = tr.startState.doc.sliceString(0, toA);
            let rightDocStr = tr.startState.doc.sliceString(toA);
            let leftRegexpStr = rule.before.left;
            if (isRegexp(rule.before.left)) {
              leftRegexpStr = leftRegexpStr.slice(2, -1);
            } else {
              leftRegexpStr = leftRegexpStr.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
            }
            let leftRegexp = new RegExp(leftRegexpStr + "$");
            let leftMatch = leftDocStr.match(leftRegexp);
            if (leftMatch) {
              let leftMatchStr = leftMatch[0];
              let matchList = leftMatch.slice(1);
              let matchPosBegin = toA - leftMatchStr.length;
              let rightRegexpStr = rule.before.right;
              if (isRegexp(rule.before.right)) {
                rightRegexpStr = rightRegexpStr.slice(2, -1);
              } else {
                rightRegexpStr = rightRegexpStr.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
              }
              let rightRegexp = new RegExp("^" + rightRegexpStr);
              let rightMatch = rightDocStr.match(rightRegexp);
              if (rightMatch) {
                let rightMatchStr = rightMatch[0];
                let matchPosEnd = toA + rightMatchStr.length;
                matchList.push(...rightMatch.slice(1));
                let [new_string, tabstops] = parseTheAfterPattern(rule.after_pattern, matchList);
                const updatedTabstops = tabstops.map((tabstop) => ({
                  ...tabstop,
                  from: tabstop.from + matchPosBegin,
                  to: tabstop.to + matchPosBegin
                }));
                let tabstopGroups = tabstopSpecsToTabstopGroups(updatedTabstops);
                changes.push({
                  changes: {
                    from: matchPosBegin,
                    to: matchPosEnd,
                    insert: new_string
                  },
                  selection: tabstopGroups[0].toEditorSelection(),
                  effects: [addTabstopsEffect.of(tabstopGroups)],
                  userEvent: "EasyTyping.change"
                });
                tr = tr.startState.update(...changes);
                return tr;
              }
            }
          }
        }
        if (changeTypeStr == "input.type" && insertedStr == "`\n```" && this.settings.BaseObEditEnhance) {
          const line_content = tr.startState.doc.lineAt(fromA).text;
          if (/^\s*``$/.test(line_content)) {
            changes.push({
              changes: { from: fromA, to: toA, insert: "`\n" + line_content + "`" },
              selection: { anchor: fromA + 1 },
              userEvent: "EasyTyping.change"
            });
            tr = tr.startState.update(...changes);
            return tr;
          }
        }
        if ((changeTypeStr == "input.type" || changeTypeStr == "input.type.compose") && fromA === toA && fromB + 1 === toB) {
          if (this.settings.BaseObEditEnhance) {
            if (insertedStr === "`" && toA - tr.startState.doc.lineAt(toA).from > 2 && tr.startState.sliceDoc(toA - 1, toA) === "`" && tr.startState.sliceDoc(toA - 2, toA - 1) != "`") {
              changes.push({
                changes: { from: toA, insert: "`" },
                selection: { anchor: toA },
                userEvent: "EasyTyping.change"
              });
              tr = tr.startState.update(...changes);
              return tr;
            }
            if (insertedStr == "\xB7") {
              let line_content = tr.startState.doc.lineAt(fromA).text;
              let ch_pos = fromA - tr.startState.doc.lineAt(fromA).from;
              if (/^\s*``$/.test(line_content) && ch_pos == line_content.length - 1) {
                changes.push({
                  changes: { from: fromA + 1, to: toA + 1, insert: "`\n" + line_content + "`" },
                  selection: { anchor: fromA + 2 },
                  userEvent: "EasyTyping.change"
                });
                tr = tr.startState.update(...changes);
                return tr;
              }
            }
            for (let rule of this.BasicConvRules) {
              if (insertedStr != rule.before.left.charAt(rule.before.left.length - 1))
                continue;
              if (rule.before.left.charAt(0) === "\n" && offsetToPos(tr.state.doc, fromA).line === 0 && toB - rule.before.left.length + 1 === 0) {
                let left = tr.state.doc.sliceString(toB - rule.before.left.length + 1, toB);
                let right = tr.state.doc.sliceString(toB, toB + rule.before.right.length);
                if (left === rule.before.left.substring(1) && right === rule.before.right) {
                  changes.push({
                    changes: {
                      from: toA - rule.before.left.length + 2,
                      to: toA + rule.before.right.length,
                      insert: rule.after.left.substring(1) + rule.after.right
                    },
                    selection: { anchor: toA - rule.before.left.length + rule.after.left.length + 1 },
                    userEvent: "EasyTyping.change"
                  });
                  tr = tr.startState.update(...changes);
                  return tr;
                }
              } else {
                let left = tr.state.doc.sliceString(toB - rule.before.left.length, toB);
                let right = tr.state.doc.sliceString(toB, toB + rule.before.right.length);
                if (left === rule.before.left && right === rule.before.right) {
                  changes.push({
                    changes: {
                      from: toA - rule.before.left.length + 1,
                      to: toA + rule.before.right.length,
                      insert: rule.after.left + rule.after.right
                    },
                    selection: { anchor: toA - rule.before.left.length + rule.after.left.length + 1 },
                    userEvent: "EasyTyping.change"
                  });
                  tr = tr.startState.update(...changes);
                  return tr;
                }
              }
            }
          }
          if (this.settings.FW2HWEnhance) {
            for (let rule of this.FW2HWSymbolRules) {
              if (insertedStr != rule.before.left.charAt(rule.before.left.length - 1))
                continue;
              let left = tr.state.doc.sliceString(toB - rule.before.left.length, toB);
              let right = tr.state.doc.sliceString(toB, toB + rule.before.right.length);
              if (left === rule.before.left && right === rule.before.right) {
                changes.push({
                  changes: {
                    from: toA - rule.before.left.length + 1,
                    to: toA + rule.before.right.length,
                    insert: rule.after.left + rule.after.right
                  },
                  selection: { anchor: toA - rule.before.left.length + rule.after.left.length + 1 },
                  userEvent: "EasyTyping.change"
                });
                tr = tr.startState.update(...changes);
                return tr;
              }
            }
          }
          if (this.settings.IntrinsicSymbolPairs) {
            for (let rule of this.IntrinsicAutoPairRulesPatch) {
              if (insertedStr != rule.before.left.charAt(rule.before.left.length - 1))
                continue;
              let left = tr.state.doc.sliceString(toB - rule.before.left.length, toB);
              let right = tr.state.doc.sliceString(toB, toB + rule.before.right.length);
              if (left === rule.before.left && right === rule.before.right) {
                changes.push({
                  changes: {
                    from: toA - rule.before.left.length + 1,
                    to: toA + rule.before.right.length,
                    insert: rule.after.left + rule.after.right
                  },
                  selection: { anchor: toA - rule.before.left.length + rule.after.left.length + 1 },
                  userEvent: "EasyTyping.change"
                });
                tr = tr.startState.update(...changes);
                return tr;
              }
            }
            if (this.SymbolPairsMap.has(insertedStr) && insertedStr != "'") {
              changes.push({
                changes: { from: fromA, to: toA, insert: insertedStr + this.SymbolPairsMap.get(insertedStr) },
                selection: { anchor: fromA + 1 },
                userEvent: "EasyTyping.change"
              });
              tr = tr.startState.update(...changes);
              return tr;
            } else if (insertedStr === "'") {
              let charBeforeCursor = tr.startState.sliceDoc(fromA - 1, fromA);
              if (["", " ", "\n"].includes(charBeforeCursor)) {
                changes.push({
                  changes: { from: fromA, to: toA, insert: "''" },
                  selection: { anchor: fromA + 1 },
                  userEvent: "EasyTyping.change"
                });
                tr = tr.startState.update(...changes);
                return tr;
              }
            }
            if (insertedStr === "\u201D" || insertedStr === "\u2019") {
              let tempStr = insertedStr === "\u201D" ? "\u201C\u201D" : "\u2018\u2019";
              changes.push({
                changes: { from: fromA, to: toA, insert: tempStr },
                selection: { anchor: fromA + 1 },
                userEvent: "EasyTyping.change"
              });
              tr = tr.startState.update(...changes);
              return tr;
            }
          }
        }
      });
      return tr;
    };
    this.viewUpdatePlugin = (update) => {
      if (this.onFormatArticle === true)
        return;
      let cursor_changed = update.transactions.find((tr2) => tr2.selection) != null;
      if ((update.docChanged || cursor_changed) && !update.view.composing && !isInsideCurTabstop(update.view)) {
        removeAllTabstops(update.view);
      }
      if (update.transactions.find((tr2) => tr2.isUserEvent("undo"))) {
        removeAllTabstops(update.view);
      }
      let notSelected = true;
      let mainSelection = update.view.state.selection.asSingle().main;
      if (mainSelection.anchor != mainSelection.head)
        notSelected = false;
      if (!update.docChanged)
        return;
      let isExcludeFile = this.isCurrentFileExclude();
      let tr = update.transactions[0];
      let changeType = getTypeStrOfTransac(tr);
      tr.changes.iterChanges((fromA, toA, fromB, toB, inserted) => {
        let insertedStr = inserted.sliceString(0);
        let changedStr = tr.startState.doc.sliceString(fromA, toA);
        if (this.settings.debug) {
          console.log("[ViewUpdate] type, fromA, toA, changed, fromB, toB, inserted");
          console.log(changeType, fromA, toA, changedStr, fromB, toB, insertedStr);
          console.log("==>[Composing]", update.view.composing);
        }
        if (getPosLineType(update.view.state, fromB) == "table" /* table */) {
          return;
        }
        let cursor = update.view.state.selection.asSingle().main;
        if (update.view.composing) {
          if (this.compose_need_handle) {
            this.compose_end_pos = cursor.anchor;
          } else {
            this.compose_need_handle = true;
            this.compose_begin_pos = fromA;
            this.compose_end_pos = cursor.anchor;
          }
          return;
        }
        let change_from = fromB;
        let change_to = toB;
        let composeEnd = false;
        if (this.compose_need_handle) {
          composeEnd = true;
          this.compose_need_handle = false;
          change_from = this.compose_begin_pos;
          change_to = this.compose_end_pos;
        }
        if (changeType.contains("EasyTyping") || changeType == "undo" || changeType == "redo")
          return;
        if (changeType != "none" && notSelected && !changeType.includes("delete")) {
          if (this.triggerCvtRule(update.view, mainSelection.anchor))
            return;
          if (composeEnd && this.triggerPuncRectify(update.view, change_from))
            return;
          if (this.settings.AutoFormat && notSelected && !isExcludeFile && (changeType != "none" || insertedStr == "\n")) {
            if (getPosLineType(update.view.state, change_from) == "text" /* text */ || getPosLineType(update.view.state, change_from) == "table" /* table */) {
              let changes = this.Formater.formatLineOfDoc(update.state, this.settings, change_from, cursor.anchor, insertedStr);
              if (changes != null) {
                update.view.dispatch(...changes[0]);
                update.view.dispatch(changes[1]);
                return;
              }
            }
          }
        }
        if (this.settings.AutoFormat && !isExcludeFile && changeType == "input.paste" && !import_obsidian3.Platform.isIosApp) {
          let updateLineStart = update.state.doc.lineAt(fromB).number;
          let updateLineEnd = update.state.doc.lineAt(toB).number;
          if (updateLineStart == updateLineEnd && getPosLineType(update.view.state, toB) == "text" /* text */) {
            let changes = this.Formater.formatLineOfDoc(update.state, this.settings, fromB, toB, insertedStr);
            if (changes != null) {
              update.view.dispatch(...changes[0]);
              return;
            }
          } else {
            let all_changes = [];
            let inserted_array = insertedStr.split("\n");
            let update_start = fromB;
            for (let i = updateLineStart; i <= updateLineEnd; i++) {
              let real_inserted = inserted_array[i - updateLineStart];
              let changes = this.Formater.formatLineOfDoc(update.state, this.settings, update_start, update_start + real_inserted.length, real_inserted);
              if (changes != null) {
                all_changes.push(...changes[0]);
              }
              update_start += real_inserted.length + 1;
            }
            if (all_changes.length > 0) {
              update.view.dispatch(...all_changes);
              return;
            }
          }
        }
      });
    };
    this.handleTabDown = (view) => {
      if (consumeAndGotoNextTabstop(view)) {
        return true;
      }
      if (!this.settings.Tabout)
        return false;
      let state = view.state;
      let doc = state.doc;
      const tree = (0, import_language3.syntaxTree)(state);
      const s = view.state.selection;
      if (s.ranges.length > 1)
        return false;
      const pos = s.main.to;
      let line = doc.lineAt(pos);
      if (s.main.from == s.main.to && isCodeBlockInPos(state, pos)) {
        const default_indent = this.getDefaultIndentChar();
        view.dispatch({
          changes: {
            from: s.main.from,
            insert: default_indent
          },
          selection: {
            anchor: s.main.from + default_indent.length
          }
        });
        return true;
      }
      if (this.settings.BetterCodeEdit && pos - line.from != 0 && tree.resolve(pos - 1, 1).name.contains("inline-code")) {
        if (tree.resolve(pos, 1).name.contains("formatting-code_inline-code")) {
          view.dispatch({
            selection: { anchor: pos + 1, head: pos + 1 }
          });
          return true;
        }
        for (let p = pos + 1; p < line.to && tree.resolve(p, 1).name.contains("inline-code"); p += 1) {
          if (tree.resolve(p, 1).name.contains("formatting-code_inline-code")) {
            view.dispatch({
              selection: { anchor: p, head: p }
            });
            return true;
          }
          if (p == line.to - 1 && tree.resolve(p, 1).name.contains("inline-code")) {
            view.dispatch({
              selection: { anchor: p + 1, head: p + 1 }
            });
            return true;
          }
        }
      }
      let selection = view.state.selection.asSingle().main;
      let selected = selection.anchor != selection.head;
      if (selected) {
        let new_anchor = selection.anchor < selection.head ? selection.anchor : selection.head;
        let new_head = selection.anchor > selection.head ? selection.anchor : selection.head;
        for (let pstr of this.TaboutPairStrs) {
          if (doc.sliceString(new_anchor - pstr.left.length, new_anchor) == pstr.left && doc.sliceString(new_head, new_head + pstr.right.length) == pstr.right) {
            view.dispatch({
              selection: { anchor: new_head + pstr.right.length, head: new_head + pstr.right.length }
            });
            return true;
          }
        }
      } else {
        let taboutRes = taboutCursorInPairedString(line.text, pos - line.from, this.TaboutPairStrs);
        if (taboutRes.isSuccess) {
          view.dispatch({
            selection: { anchor: taboutRes.newPosition + line.from }
          });
          return true;
        }
      }
      return false;
    };
    this.handleEnter = (view) => {
      var _a;
      let state = view.state;
      let doc = state.doc;
      const tree = (0, import_language3.syntaxTree)(state);
      const s = view.state.selection;
      if (s.ranges.length > 1)
        return false;
      const pos = s.main.to;
      let line = doc.lineAt(pos);
      let codeBlockInfo = getCodeBlockInfoInPos(state, pos);
      if (this.settings.BetterCodeEdit && codeBlockInfo && codeBlockInfo.code_start_pos !== doc.lineAt(codeBlockInfo.start_pos).to && pos >= codeBlockInfo.code_start_pos && pos <= codeBlockInfo.code_end_pos) {
        let line_indent_str = ((_a = line.text.match(/^\s*/)) == null ? void 0 : _a[0]) || "";
        view.dispatch({
          changes: { from: pos, to: pos, insert: "\n" + line_indent_str },
          selection: { anchor: pos + line_indent_str.length + 1, head: pos + line_indent_str.length + 1 },
          userEvent: "EasyTyping.handleEnter"
        });
        return true;
      }
      if (!this.settings.StrictModeEnter)
        return false;
      let strictLineBreaks = this.app.vault.config.strictLineBreaks || false;
      if (!strictLineBreaks)
        return false;
      if (/^\s*$/.test(line.text))
        return false;
      if (pos == line.from)
        return false;
      if (getPosLineType2(state, pos) == "quote" /* quote */) {
        let reg_quote = /^(\s*)(>+)/;
        let quote_match = line.text.match(reg_quote);
        if (!quote_match)
          return false;
        let quote_indent_str = (quote_match == null ? void 0 : quote_match[1]) || "";
        let quote_level = (quote_match == null ? void 0 : quote_match[2].length) || 0;
        let quote_content = line.text.slice(quote_match[0].length);
        if (quote_content.trim() == "")
          return false;
        else {
          let space_str2 = "  ";
          if (quote_content.endsWith("  "))
            space_str2 = "";
          let inserted_str = space_str2 + "\n" + quote_match[0] + " ";
          if (this.settings.StrictLineMode == "enter_twice" /* EnterTwice */) {
            inserted_str = "\n" + quote_match[0] + " \n" + quote_match[0] + " ";
          }
          view.dispatch({
            changes: { from: pos, to: pos, insert: inserted_str },
            selection: { anchor: pos + inserted_str.length },
            userEvent: "EasyTyping.handleEnter"
          });
          return true;
        }
      }
      let space_str = "  ";
      if (line.text.endsWith("  "))
        space_str = "";
      if (line.number < doc.lines && !/^\s*$/.test(doc.line(line.number + 1).text)) {
        if (this.settings.StrictLineMode != "two_space" /* TwoSpace */)
          return false;
      }
      if (this.settings.StrictLineMode == "two_space" /* TwoSpace */ && getPosLineType2(state, pos) == "text" /* text */) {
        let inserted_str = space_str + "\n";
        view.dispatch({
          changes: { from: pos, to: pos, insert: inserted_str },
          selection: { anchor: pos + inserted_str.length, head: pos + inserted_str.length },
          userEvent: "EasyTyping.handleEnter"
        });
        return true;
      }
      if (getPosLineType2(state, pos) == "text" /* text */ || codeBlockInfo && pos == codeBlockInfo.end_pos && codeBlockInfo.indent == 0) {
        view.dispatch({
          changes: {
            from: pos,
            to: pos,
            insert: "\n\n"
          },
          selection: { anchor: pos + 2 },
          userEvent: "EasyTyping.handleEnter"
        });
        return true;
      }
      return false;
    };
    this.handleModA = (view) => {
      let selection = view.state.selection.main;
      let line = view.state.doc.lineAt(selection.head);
      let line_type = getPosLineType2(view.state, selection.head);
      let is_in_code_block = isCodeBlockInPos(view.state, selection.head);
      if (this.settings.EnhanceModA && line_type == "text" /* text */ && !is_in_code_block) {
        let [block_start, block_end] = this.getBlockLinesInPos(view.state, selection.head);
        if (selection.anchor <= view.state.doc.line(block_start).from && selection.head >= view.state.doc.line(block_end).to) {
          return false;
        }
        if (selection.anchor == line.from && selection.head == line.to) {
          if (block_start != block_end) {
            view.dispatch({
              selection: {
                anchor: view.state.doc.line(block_start).from,
                head: view.state.doc.line(block_end).to
              },
              userEvent: "EasyTyping.handleModA"
            });
            return true;
          }
          return false;
        }
        view.dispatch({
          selection: { anchor: line.from, head: line.to },
          userEvent: "EasyTyping.handleModA"
        });
        return true;
      }
      let quote_info = getQuoteInfoInPos(view.state, selection.head);
      if (this.settings.EnhanceModA && quote_info) {
        if (selection.anchor == quote_info.start_pos && selection.head == quote_info.end_pos) {
          return false;
        } else if (selection.anchor == quote_info.cur_start_pos && selection.head == quote_info.cur_end_pos) {
          view.dispatch({
            selection: { anchor: quote_info.start_pos, head: quote_info.end_pos },
            userEvent: "EasyTyping.handleModA"
          });
          return true;
        } else {
          view.dispatch({
            selection: { anchor: quote_info.cur_start_pos, head: quote_info.cur_end_pos },
            userEvent: "EasyTyping.handleModA"
          });
          return true;
        }
      }
      if (this.settings.EnhanceModA && line_type == "list" /* list */) {
      }
      if (!this.settings.BetterCodeEdit)
        return false;
      let mainSelection = view.state.selection.asSingle().main;
      return selectCodeBlockInPos(view, mainSelection);
    };
    this.onKeyup = (event, view) => {
      if (this.settings.debug) {
        console.log("Keyup:", event.key);
      }
      this.handleEndComposeTypeKey(event, view);
    };
    this.triggerCvtRule = (view, cursor_pos) => {
      let rules = [];
      if (this.settings.BaseObEditEnhance)
        rules = rules.concat(this.ExtraBasicConvRules);
      if (this.settings.QuoteSpace)
        rules = rules.concat(this.QuoteSpaceRules);
      rules = rules.concat(this.UserConvertRules);
      for (let rule of rules) {
        let leftDocStr = view.state.doc.sliceString(0, cursor_pos);
        let rightDocStr = view.state.doc.sliceString(cursor_pos);
        let leftRegexpStr = rule.before.left;
        if (isRegexp(rule.before.left)) {
          leftRegexpStr = leftRegexpStr.slice(2, -1);
        } else {
          leftRegexpStr = leftRegexpStr.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
        }
        let leftRegexp = new RegExp(leftRegexpStr + "$");
        let leftMatch = leftDocStr.match(leftRegexp);
        if (leftMatch) {
          let leftMatchStr = leftMatch[0];
          let matchList = leftMatch.slice(1);
          let matchPosBegin = cursor_pos - leftMatchStr.length;
          let rightRegexpStr = rule.before.right;
          if (isRegexp(rule.before.right)) {
            rightRegexpStr = rightRegexpStr.slice(2, -1);
          } else {
            rightRegexpStr = rightRegexpStr.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
          }
          let rightRegexp = new RegExp("^" + rightRegexpStr);
          let rightMatch = rightDocStr.match(rightRegexp);
          if (rightMatch) {
            let rightMatchStr = rightMatch[0];
            let matchPosEnd = cursor_pos + rightMatchStr.length;
            matchList.push(...rightMatch.slice(1));
            let [new_string, tabstops] = parseTheAfterPattern(rule.after_pattern, matchList);
            const updatedTabstops = tabstops.map((tabstop) => ({
              ...tabstop,
              from: tabstop.from + matchPosBegin,
              to: tabstop.to + matchPosBegin
            }));
            view.dispatch({
              changes: {
                from: matchPosBegin,
                to: matchPosEnd,
                insert: new_string
              },
              userEvent: "EasyTyping.change"
            });
            addTabstopsAndSelect(view, tabstopSpecsToTabstopGroups(updatedTabstops));
            return true;
          }
        }
      }
      return false;
    };
    this.triggerPuncRectify = (view, change_from_pos) => {
      if (this.settings.PuncRectify && /[,.?!]/.test(view.state.doc.sliceString(change_from_pos - 1, change_from_pos))) {
        let punc = view.state.doc.sliceString(change_from_pos - 1, change_from_pos);
        if (change_from_pos > 2 && /[^\u4e00-\u9fa5]/.test(view.state.doc.sliceString(change_from_pos - 2, change_from_pos - 1))) {
        } else {
          view.dispatch({
            changes: {
              from: change_from_pos - 1,
              to: change_from_pos,
              insert: this.halfToFullSymbolMap.get(punc)
            },
            userEvent: "EasyTyping.change"
          });
          return true;
        }
      }
      return false;
    };
    this.handleEndComposeTypeKey = (event, view) => {
      if ((["Enter", "Process", " ", "Shift"].contains(event.key) || /\d/.test(event.key)) && this.compose_need_handle) {
        let cursor = view.state.selection.asSingle().main;
        if (cursor.head != cursor.anchor)
          return;
        let insertedStr = view.state.doc.sliceString(this.compose_begin_pos, cursor.anchor);
        this.compose_need_handle = false;
        if (this.triggerCvtRule(view, cursor.anchor))
          return;
        if (this.triggerPuncRectify(view, this.compose_begin_pos))
          return;
        if (this.settings.AutoFormat && !this.isCurrentFileExclude()) {
          if (getPosLineType(view.state, cursor.anchor) != "text" /* text */)
            return;
          let changes = this.Formater.formatLineOfDoc(view.state, this.settings, this.compose_begin_pos, cursor.anchor, insertedStr);
          if (changes != null) {
            view.dispatch(...changes[0]);
            view.dispatch(changes[1]);
            return;
          }
        }
      }
    };
    this.formatArticle = (editor, view) => {
      const editorView = editor.cm;
      const tree = (0, import_language3.ensureSyntaxTree)(editorView.state, editorView.state.doc.length);
      if (!tree) {
        new import_obsidian3.Notice("EasyTyping: Syntax tree is not ready yet, please wait a moment and try again later!", 5e3);
        return;
      }
      this.onFormatArticle = true;
      let lineCount = editor.lineCount();
      let new_article = "";
      let cs = editor.getCursor();
      let ch = 0;
      for (let i = 0; i < lineCount; i++) {
        if (i != 0)
          new_article += "\n";
        if (i != cs.line) {
          new_article += this.preFormatOneLine(editor, i + 1)[0];
        } else {
          let newData = this.preFormatOneLine(editor, i + 1, cs.ch);
          new_article += newData[0];
          ch = newData[1];
        }
      }
      editor.setValue(new_article);
      editor.setCursor({ line: cs.line, ch });
      this.onFormatArticle = false;
      new import_obsidian3.Notice("EasyTyping: Format Article Done!");
    };
    this.formatSelectionOrCurLine = (editor, view) => {
      if (!editor.somethingSelected() || editor.getSelection() === "") {
        let lineNumber = editor.getCursor().line;
        let newLineData = this.preFormatOneLine(editor, lineNumber + 1, editor.getCursor().ch);
        editor.replaceRange(newLineData[0], { line: lineNumber, ch: 0 }, { line: lineNumber, ch: editor.getLine(lineNumber).length });
        editor.setSelection({ line: lineNumber, ch: newLineData[1] });
        return;
      }
      let selection = editor.listSelections()[0];
      let begin = selection.anchor.line;
      let end = selection.head.line;
      if (begin > end) {
        let temp = begin;
        begin = end;
        end = temp;
      }
      let new_lines = "";
      for (let i = begin; i <= end; i++) {
        if (i != begin)
          new_lines += "\n";
        new_lines += this.preFormatOneLine(editor, i + 1)[0];
      }
      editor.replaceRange(new_lines, { line: begin, ch: 0 }, { line: end, ch: editor.getLine(end).length });
      if (selection.anchor.line < selection.head.line) {
        editor.setSelection({ line: selection.anchor.line, ch: 0 }, { line: selection.head.line, ch: editor.getLine(selection.head.line).length });
      } else {
        editor.setSelection({ line: selection.anchor.line, ch: editor.getLine(selection.anchor.line).length }, { line: selection.head.line, ch: 0 });
      }
    };
    this.formatOneLine = (editor, lineNumber) => {
      const editorView = editor.cm;
      let state = editorView.state;
      let line = state.doc.line(lineNumber);
      if (getPosLineType(state, line.from) == "text" /* text */ || getPosLineType(state, line.from) == "table" /* table */) {
        let oldLine = line.text;
        let newLine = this.Formater.formatLine(state, lineNumber, this.settings, oldLine.length, 0)[0];
        if (oldLine != newLine) {
          editor.replaceRange(newLine, { line: lineNumber - 1, ch: 0 }, { line: lineNumber - 1, ch: oldLine.length });
          editor.setCursor({ line: lineNumber - 1, ch: editor.getLine(lineNumber - 1).length });
        }
      }
      return;
    };
    this.preFormatOneLine = (editor, lineNumber, ch = -1) => {
      const editorView = editor.cm;
      let state = editorView.state;
      let line = state.doc.line(lineNumber);
      let newLine = line.text;
      let newCh = 0;
      let curCh = line.text.length;
      if (ch != -1) {
        curCh = ch;
      }
      if (getPosLineType(state, line.from) == "text" /* text */ || getPosLineType(state, line.from) == "table" /* table */) {
        let newLineData = this.Formater.formatLine(state, lineNumber, this.settings, curCh, 0);
        newLine = newLineData[0];
        newCh = newLineData[1];
      }
      return [newLine, newCh];
    };
    this.deleteBlankLines = (editor) => {
      if (this.settings.debug) {
        console.log("config.strictLineBreaks", this.app.vault.getConfig("strictLineBreaks"));
      }
      let strictLineBreaks = this.app.vault.config.strictLineBreaks || false;
      const editorView = editor.cm;
      let state = editorView.state;
      let doc = state.doc;
      const tree = (0, import_language3.ensureSyntaxTree)(state, doc.length);
      if (!tree) {
        new import_obsidian3.Notice("EasyTyping: Syntax tree is not ready yet, please wait a moment and try again later!", 5e3);
        return;
      }
      let start_line = 1;
      let end_line = doc.lines;
      let line_num = doc.lines;
      const selected = editor.somethingSelected() && editor.getSelection() != "";
      if (selected) {
        let selection = editor.listSelections()[0];
        let begin = selection.anchor.line + 1;
        let end = selection.head.line + 1;
        if (begin > end) {
          let temp = begin;
          begin = end;
          end = temp;
        }
        start_line = begin;
        end_line = end;
      }
      let delete_index = [];
      let blank_reg = /^\s*$/;
      let remain_next_blank = false;
      if (start_line != 1) {
        let node = tree.resolve(doc.line(start_line - 1).from, 1);
        if (node.name.contains("list") || node.name.contains("quote") || node.name.contains("blockid")) {
          remain_next_blank = true;
        }
      }
      if (end_line != line_num && !blank_reg.test(doc.line(end_line + 1).text)) {
        end_line += 1;
      }
      for (let i = start_line; i <= end_line; i++) {
        let line = doc.line(i);
        let pos = line.from;
        let node = tree.resolve(pos, 1);
        if (blank_reg.test(line.text) && !remain_next_blank) {
          delete_index.push(i);
          continue;
        } else if (blank_reg.test(line.text) && remain_next_blank) {
          remain_next_blank = false;
          continue;
        }
        if (node.name.contains("hr") && delete_index[delete_index.length - 1] == i - 1) {
          delete_index.pop();
        } else if (node.name.contains("list") || node.name.contains("quote") || node.name.contains("blockid")) {
          remain_next_blank = true;
        } else {
          remain_next_blank = false;
        }
      }
      let newContent = "";
      for (let i = 1; i < line_num; i++) {
        if (!delete_index.contains(i)) {
          newContent += doc.line(i).text + "\n";
        }
      }
      if (!delete_index.contains(line_num)) {
        newContent += doc.line(line_num).text;
      }
      editor.setValue(newContent);
    };
    this.getEditor = () => {
      let editor = null;
      let markdownView = this.app.workspace.getActiveViewOfType(import_obsidian3.MarkdownView);
      if (markdownView) {
        editor = markdownView.editor;
      }
      if (editor === null)
        console.log("can't get editor");
      return editor;
    };
  }
  async onload() {
    await this.loadSettings();
    this.selectionReplaceMapInitalData = [
      ["\u3010", { left: "[", right: "]" }],
      ["\uFFE5", { left: "$", right: "$" }],
      ["\xB7", { left: "`", right: "`" }],
      ["\xA5", { left: "$", right: "$" }],
      ["\u300A", { left: "\u300A", right: "\u300B" }],
      ["\u201C", { left: "\u201C", right: "\u201D" }],
      ["\u201D", { left: "\u201C", right: "\u201D" }],
      ["\uFF08", { left: "\uFF08", right: "\uFF09" }],
      ["<", { left: "<", right: ">" }],
      ['"', { left: '"', right: '"' }],
      ["'", { left: "'", right: "'" }],
      ["\u300C", { left: "\u300C", right: "\u300D" }],
      ["\u300E", { left: "\u300E", right: "\u300F" }]
    ];
    this.refreshSelectionReplaceRule();
    this.SymbolPairsMap = /* @__PURE__ */ new Map();
    let SymbolPairs = ["\u3010\u3011", "\uFF08\uFF09", "\u300A\u300B", "\u201C\u201D", "\u2018\u2019", "\u300C\u300D", "\u300E\u300F", "[]", "()", "{}", '""', "''"];
    for (let pairStr of SymbolPairs)
      this.SymbolPairsMap.set(pairStr.charAt(0), pairStr.charAt(1));
    this.halfToFullSymbolMap = /* @__PURE__ */ new Map([
      [".", "\u3002"],
      [",", "\uFF0C"],
      ["?", "\uFF1F"],
      ["!", "\uFF01"]
    ]);
    let BasicConvRuleStringList = [
      ["\xB7\xB7|", "`|`"],
      ["\uFF01\u3010\u3010|\u3011", "![[|]]"],
      ["\uFF01\u3010\u3010|", "![[|]]"],
      ["\u3010\u3010|\u3011", "[[|]]"],
      ["\u3010\u3010|", "[[|]]"],
      ["\uFFE5\uFFE5|", "$|$"],
      ["$\uFFE5|$", "$$\n|\n$$"],
      ["\xA5\xA5|", "$|$"],
      ["$\xA5|$", "$$\n|\n$$"],
      ["$$|$", "$$\n|\n$$"],
      ["$$|", "$|$"],
      ["\n\u300B|", "\n> |"],
      ["\n\u3001|", "\n/|"]
    ];
    let ExtraBasicConvRuleStringList = [["r/(?<=^|\\n)(\\s*>*) ?[>\u300B]/|", "[[0]]> |"]];
    let QuoteSpaceRuleStringList = [["r/(?<=^|\\n)(\\s*>+)([^ >\u300B]+)/|", "[[0]] [[1]]|"]];
    this.ExtraBasicConvRules = ruleStringList2RuleList(ExtraBasicConvRuleStringList);
    this.QuoteSpaceRules = ruleStringList2RuleList(QuoteSpaceRuleStringList);
    this.BasicConvRules = ruleStringList2RuleList(BasicConvRuleStringList);
    let FW2HWSymbolRulesStrList = [
      ["\u3002\u3002|", ".|"],
      ["\uFF01\uFF01|", "!|"],
      ["\uFF1B\uFF1B|", ";|"],
      ["\uFF0C\uFF0C|", ",|"],
      ["\uFF1A\uFF1A|", ":|"],
      ["\uFF1F\uFF1F|", "?|"],
      ["\uFF08\uFF08|\uFF09", "(|)"],
      ["\uFF08\uFF08|", "(|)"],
      ["\u201C\u201C|\u201D", '"|"'],
      ["\u201C\u201D|\u201D", '"|"'],
      ["\u2018\u2018|\u2019", "'|'"],
      ["\u2018\u2019|\u2019", "'|'"],
      ["\u300B\u300B|", ">|"],
      ["\u300A\u300A|\u300B", "<|"],
      ["\u300A\u300A|", "<|"]
    ];
    this.FW2HWSymbolRules = ruleStringList2RuleList(FW2HWSymbolRulesStrList);
    let fw2hw_rule_0 = { before: { left: "\uFF5C\uFF5C", right: "" }, after: { left: "|", right: "" } };
    this.FW2HWSymbolRules.push(fw2hw_rule_0);
    let DeleteRulesStrList = [["$|$", "|"], ["==|==", "|"], ["$$\n|\n$$", "|"]];
    this.IntrinsicDeleteRules = ruleStringList2RuleList(DeleteRulesStrList);
    let autoPairRulesPatchStrList = [
      ["\u3010\u3011|\u3011", "\u3010\u3011|"],
      ["\uFF08\uFF09|\uFF09", "\uFF08\uFF09|"],
      ["<>|>", "<>|"],
      ["\u300A\u300B|\u300B", "\u300A\u300B|"],
      ["\u300C\u300D|\u300D", "\u300C\u300D|"],
      ["\u300E\u300F|\u300F", "\u300E\u300F|"],
      ["()|)", "()|"],
      ["[]|]", "[]|"],
      ["{}|}", "{}|"],
      ["''|'", "''|"],
      ['""|"', '""|']
    ];
    this.IntrinsicAutoPairRulesPatch = ruleStringList2RuleList(autoPairRulesPatchStrList);
    let TaboutPairStrs = [
      "\u3010|\u3011",
      "\uFF08|\uFF09",
      "\u300A|\u300B",
      "\u201C|\u201D",
      "\u2018|\u2019",
      "\u300C|\u300D",
      "\u300E|\u300F",
      "'|'",
      '"|"',
      "$$|$$",
      "$|$",
      "__|__",
      "_|_",
      "==|==",
      "~~|~~",
      "**|**",
      "*|*",
      "[[|]]",
      "[|]",
      "{|}",
      "(|)",
      "<|>"
    ];
    this.TaboutPairStrs = TaboutPairStrs.map((s) => string2pairstring(s));
    this.refreshUserDeleteRule();
    this.refreshUserConvertRule();
    this.CurActiveMarkdown = "";
    this.compose_need_handle = false;
    this.Formater = new LineFormater();
    this.onFormatArticle = false;
    setDebug(this.settings.debug);
    this.registerEditorExtension([
      import_state3.EditorState.transactionFilter.of(this.transactionFilterPlugin),
      import_view3.EditorView.updateListener.of(this.viewUpdatePlugin),
      import_state3.Prec.highest(import_view3.EditorView.domEventHandlers({
        "keyup": this.onKeyup
      })),
      tabstopsStateField.extension
    ]);
    this.registerEditorExtension(import_state3.Prec.highest(import_view3.keymap.of([
      {
        key: "Tab",
        run: (view) => {
          const success = this.handleTabDown(view);
          return success;
        }
      },
      {
        key: "Enter",
        run: (view) => {
          const success = this.handleEnter(view);
          return success;
        }
      },
      {
        key: "Mod-a",
        run: (view) => {
          const success = this.handleModA(view);
          return success;
        }
      },
      {
        key: "Backspace",
        run: (view) => {
          if (!this.settings.BetterBackspace)
            return false;
          return this.handleBackspace(view);
        }
      },
      {
        key: "Shift-Enter",
        run: (view) => {
          const success = this.handleShiftEnter(view);
          return success;
        }
      }
    ])));
    this.lang = window.localStorage.getItem("language");
    let command_name_map = this.getCommandNameMap();
    this.addCommand({
      id: "easy-typing-format-article",
      name: command_name_map.get("format_article"),
      editorCallback: (editor, view) => {
        this.formatArticle(editor, view);
      },
      hotkeys: [{
        modifiers: ["Mod", "Shift"],
        key: "s"
      }]
    });
    this.addCommand({
      id: "easy-typing-select-block",
      name: command_name_map.get("select_block"),
      editorCallback: (editor, view) => {
        this.selectBlockInCurser(editor.cm);
      }
    });
    this.addCommand({
      id: "easy-typing-format-selection",
      name: command_name_map.get("format_selection"),
      editorCallback: (editor, view) => {
        this.formatSelectionOrCurLine(editor, view);
      },
      hotkeys: [{
        modifiers: ["Mod", "Shift"],
        key: "l"
      }]
    });
    this.addCommand({
      id: "easy-typing-delete-blank-line",
      name: command_name_map.get("delete_blank_line"),
      editorCallback: (editor, view) => {
        this.deleteBlankLines(editor);
      },
      hotkeys: [{
        modifiers: ["Mod", "Shift"],
        key: "k"
      }]
    });
    this.addCommand({
      id: "easy-typing-goto-new-line-after-cur-line",
      name: command_name_map.get("goto_new_line_after_cur_line"),
      editorCallback: (editor, view) => {
        this.goNewLineAfterCurLine(editor.cm);
      },
      hotkeys: [{ modifiers: ["Mod"], key: "Enter" }]
    });
    this.addCommand({
      id: "easy-typing-insert-codeblock",
      name: command_name_map.get("insert_codeblock"),
      editorCallback: (editor, view) => {
        this.convert2CodeBlock(editor);
      },
      hotkeys: [{
        modifiers: ["Mod", "Shift"],
        key: "n"
      }]
    });
    this.addCommand({
      id: "easy-typing-format-switch",
      name: command_name_map.get("switch_autoformat"),
      callback: () => this.switchAutoFormatting(),
      hotkeys: [{
        modifiers: ["Ctrl"],
        key: "tab"
      }]
    });
    this.addCommand({
      id: "easy-typing-paste-without-format",
      name: command_name_map.get("paste_wo_format"),
      editorCallback: (editor) => this.normalPaste(editor),
      hotkeys: [
        {
          modifiers: ["Mod", "Shift"],
          key: "v"
        }
      ]
    });
    this.addCommand({
      id: "easy-typing-toggle-comment",
      name: command_name_map.get("toggle_comment"),
      editorCallback: (editor, view) => this.toggleComment(editor.cm),
      hotkeys: [{ modifiers: ["Mod"], key: "/" }]
    });
    this.addSettingTab(new EasyTypingSettingTab(this.app, this));
    this.registerEvent(this.app.workspace.on("active-leaf-change", (leaf) => {
      if (leaf.view.getViewType() == "markdown") {
        let file = this.app.workspace.getActiveFile();
        if (file != null && this.CurActiveMarkdown != file.path) {
          this.CurActiveMarkdown = file.path;
          if (this.settings.debug)
            new import_obsidian3.Notice("new md-file open: " + file.path);
        }
      }
    }));
    if (import_obsidian3.Platform.isMacOS && this.settings.FixMacOSContextMenu) {
      this.registerEvent(this.app.workspace.on("editor-menu", (menu, editor, view) => {
        if (editor.listSelections().length != 1)
          return;
        let selection = editor.listSelections()[0];
        let selected = editor.getSelection();
        if (selected == "\n") {
          editor.setSelection(selection.anchor, selection.anchor);
        }
      }));
    }
    console.log("Easy Typing Plugin loaded.");
  }
  onunload() {
    console.log("Easy Typing Plugin unloaded.");
  }
  async normalPaste(editor) {
    let clipboardText = await navigator.clipboard.readText();
    if (clipboardText === null || clipboardText === "")
      return;
    if (this.settings.debug)
      console.log("Normal Paste!!");
    const editorView = editor.cm;
    let mainSelection = editorView.state.selection.asSingle().main;
    editorView.dispatch({
      changes: { from: mainSelection.from, to: mainSelection.to, insert: clipboardText },
      selection: { anchor: mainSelection.from + clipboardText.length },
      userEvent: "EasyTyping.paste"
    });
  }
  getBlockLinesInPos(state, pos) {
    const strictLineBreaks = this.app.vault.config.strictLineBreaks || false;
    let line = state.doc.lineAt(pos);
    let block_start = line.number;
    let block_end = line.number;
    let reg_headings = /^#+ /;
    for (let i = line.number - 1; i >= 1; i--) {
      let line2 = state.doc.line(i);
      if (getPosLineType2(state, line2.from) == "text" /* text */ && line2.text !== "" && !reg_headings.test(line2.text)) {
        block_start = i;
        continue;
      }
      break;
    }
    for (let i = line.number + 1; i <= state.doc.lines; i++) {
      let line2 = state.doc.line(i);
      if (getPosLineType2(state, line2.from) == "text" /* text */ && line2.text !== "" && !reg_headings.test(line2.text)) {
        block_end = i;
        continue;
      }
      break;
    }
    return [block_start, block_end];
  }
  selectBlockInCurser(view) {
    let selection = view.state.selection.main;
    let line = view.state.doc.lineAt(selection.head);
    if (/^\s*$/.test(line.text))
      return false;
    let [block_start, block_end] = this.getBlockLinesInPos(view.state, selection.head);
    view.dispatch({
      selection: { anchor: view.state.doc.line(block_start).from, head: view.state.doc.line(block_end).to },
      userEvent: "EasyTyping.selectBlockInCurser"
    });
    return true;
  }
  toggleComment(view) {
    const state = view.state;
    const selection = state.selection.main;
    const codeBlockInfo = getCodeBlockInfoInPos(state, selection.from);
    if (codeBlockInfo) {
      return this.toggleCodeBlockComment(view);
    }
    return this.toggleMarkdownComment(selection.from, selection.to, view);
  }
  toggleCodeBlockComment(view) {
    const state = view.state;
    const selection = state.selection.main;
    const codeBlockInfo = getCodeBlockInfoInPos(state, selection.from);
    if (!codeBlockInfo)
      return false;
    const language = codeBlockInfo.language;
    const commentSymbol = this.getCommentSymbol(language.toLowerCase());
    if (!commentSymbol)
      return false;
    let changes = [];
    if (selection.from === selection.to) {
      const line = state.doc.lineAt(selection.from);
      let change = this.toggleCodeBlockLineComment(line.from, line.to, state.doc.sliceString(line.from, line.to), commentSymbol, selection.from);
      if (change && change.selection) {
        changes.push(change);
        view.dispatch({
          changes,
          selection: change.selection,
          userEvent: "EasyTyping.toggleComment"
        });
        return true;
      } else if (change) {
        changes.push(change);
      }
    } else {
      const fromLine = state.doc.lineAt(selection.from);
      const toLine = state.doc.lineAt(selection.to);
      for (let i = fromLine.number; i <= toLine.number; i++) {
        const line = state.doc.line(i);
        let change = this.toggleCodeBlockLineComment(line.from, line.to, state.doc.sliceString(line.from, line.to), commentSymbol);
        if (change) {
          changes.push(change);
        }
      }
    }
    view.dispatch({ changes, userEvent: "EasyTyping.toggleComment" });
    return true;
  }
  toggleCodeBlockLineComment(from, to, text, commentSymbol, cursor_pos) {
    if (text.trim() == "" && cursor_pos) {
      if (typeof commentSymbol === "string") {
        let new_pos = cursor_pos + commentSymbol.length + 1;
        return {
          from: cursor_pos,
          to: cursor_pos,
          insert: commentSymbol + " ",
          selection: { anchor: new_pos, head: new_pos }
        };
      } else {
        let new_pos = cursor_pos + commentSymbol.start.length + 1;
        return {
          from: cursor_pos,
          to: cursor_pos,
          insert: commentSymbol.start + "  " + commentSymbol.end,
          selection: { anchor: new_pos, head: new_pos }
        };
      }
    }
    if (text.trim() == "")
      return null;
    if (typeof commentSymbol === "string") {
      const trimmedText = text.trimStart();
      if (trimmedText.startsWith(commentSymbol)) {
        const commentIndex = text.indexOf(commentSymbol);
        return {
          from: from + commentIndex,
          to: from + commentIndex + commentSymbol.length + (trimmedText.startsWith(commentSymbol + " ") ? 1 : 0),
          insert: ""
        };
      } else {
        const indent = text.length - trimmedText.length;
        return {
          from: from + indent,
          to: from + indent,
          insert: commentSymbol + " "
        };
      }
    } else {
      const trimmedText = text.trim();
      if (trimmedText.startsWith(commentSymbol.start) && trimmedText.endsWith(commentSymbol.end)) {
        const commentStartIndex = text.indexOf(commentSymbol.start);
        return {
          from: from + commentStartIndex,
          to,
          insert: trimmedText.slice(commentSymbol.start.length + 1, -commentSymbol.end.length - 1)
        };
      } else {
        const indent = text.length - text.trimStart().length;
        return {
          from: from + indent,
          to,
          insert: `${commentSymbol.start} ${trimmedText} ${commentSymbol.end}`
        };
      }
    }
  }
  toggleMarkdownComment(from, to, view) {
    const state = view.state;
    const doc = state.doc;
    const changes = [];
    if (from === to) {
      const currentText = doc.sliceString(from - 3, to + 3);
      if (currentText === "%%  %%") {
        changes.push({
          from: from - 3,
          to: to + 3,
          insert: ""
        });
        view.dispatch({
          changes,
          selection: { anchor: from - 3, head: from - 3 },
          userEvent: "EasyTyping.toggleComment"
        });
        return true;
      }
      changes.push({
        from,
        to,
        insert: "%%  %%"
      });
      const newPos = from + 3;
      view.dispatch({
        changes,
        selection: { anchor: newPos, head: newPos },
        userEvent: "EasyTyping.toggleComment"
      });
    } else {
      const selectedText = doc.sliceString(from, to);
      if (selectedText.startsWith("%%") && selectedText.endsWith("%%")) {
        changes.push({
          from,
          to,
          insert: selectedText.slice(2, -2)
        });
      } else {
        changes.push({
          from,
          to,
          insert: `%%${selectedText}%%`
        });
      }
      view.dispatch({ changes, userEvent: "EasyTyping.toggleComment" });
    }
    return true;
  }
  getCommentSymbol(language) {
    const commentSymbols = {
      "js": "//",
      "javascript": "//",
      "ts": "//",
      "typescript": "//",
      "py": "#",
      "python": "#",
      "rb": "#",
      "ruby": "#",
      "java": "//",
      "c": "//",
      "cpp": "//",
      "cs": "//",
      "go": "//",
      "rust": "//",
      "swift": "//",
      "kotlin": "//",
      "php": "//",
      "css": { start: "/*", end: "*/" },
      "scss": { start: "/*", end: "*/" },
      "sql": "--",
      "shell": "#",
      "bash": "#",
      "powershell": "#",
      "html": { start: "<!--", end: "-->" },
      "matlab": "%",
      "markdown": { start: "%%", end: "%%" }
    };
    return commentSymbols[language] || null;
  }
  handleShiftEnter(view) {
    const state = view.state;
    const doc = state.doc;
    const selection = state.selection.main;
    if (selection.anchor != selection.head)
      return false;
    const line = doc.lineAt(selection.head);
    const lineContent = line.text;
    const taskListMatch = lineContent.match(/^(\s*)([-*+] \[.\])\s/);
    if (taskListMatch) {
      const [, indent, listMarker] = taskListMatch;
      let inserted = "\n" + indent + "  ";
      view.dispatch({
        changes: [{ from: selection.anchor, insert: inserted }],
        selection: { anchor: selection.anchor + inserted.length, head: selection.anchor + inserted.length },
        userEvent: "EasyTyping.handleShiftEnter"
      });
      return true;
    }
    return false;
  }
  goNewLineAfterCurLine(view) {
    const state = view.state;
    const doc = state.doc;
    const selection = state.selection.main;
    const line = doc.lineAt(selection.head);
    const lineContent = line.text;
    const listMatch = lineContent.match(/^(\s*)([-*+] \[.\]|[-*+]|\d+\.)\s/);
    const quoteMatch = lineContent.match(/^(\s*)(>+)(\s)?/);
    let changes;
    let newCursorPos;
    let prefix = "";
    if (listMatch) {
      const [, indent, listMarker] = listMatch;
      if (["-", "*", "+"].includes(listMarker)) {
        prefix = indent + listMarker + " ";
      } else if (listMarker.match(/[-*+] \[.\]/)) {
        prefix = indent + listMarker.replace(/\[.\]/g, "[ ]") + " ";
      } else {
        prefix = indent + (parseInt(listMarker) + 1) + ". ";
      }
    } else if (quoteMatch) {
      prefix = quoteMatch[1] + quoteMatch[2] + " ";
    }
    changes = [{ from: line.to, insert: "\n" + prefix }];
    newCursorPos = line.to + 1 + prefix.length;
    const tr = state.update({
      changes,
      selection: { anchor: newCursorPos, head: newCursorPos },
      userEvent: "EasyTyping.goNewLineAfterCurLine"
    });
    view.dispatch(tr);
    return true;
  }
  handleBackspace(view) {
    const state = view.state;
    const doc = state.doc;
    const selection = state.selection.main;
    if (selection.anchor != selection.head)
      return false;
    const line = doc.lineAt(selection.from);
    const lineContent = line.text;
    const listMatchEmpty = lineContent.match(/^\s*([-*+]|\d+\.) $/);
    const quoteMatchEmpty = lineContent.match(/^(\s*)(>+) ?$/);
    if ((listMatchEmpty || quoteMatchEmpty) && selection.anchor == line.to) {
      let changes;
      let newCursorPos;
      if (quoteMatchEmpty) {
        const quote_indent_str = quoteMatchEmpty[1];
        const quoteLevel = quoteMatchEmpty[2].length;
        if (quoteLevel > 1) {
          if (line.number > 1) {
            const prevLine = doc.line(line.number - 1);
            const prevLineContent = prevLine.text;
            const prevQuoteMatchEmpty = prevLineContent.match(/^(\s*)(>+) ?$/);
            if (prevQuoteMatchEmpty && prevQuoteMatchEmpty[1] == quote_indent_str && prevQuoteMatchEmpty[2].length == quoteLevel) {
              let temp_line = quote_indent_str + ">".repeat(quoteLevel - 1) + " ";
              let inseted = temp_line + "\n" + temp_line;
              changes = [{ from: prevLine.from, to: line.to, insert: inseted }];
              newCursorPos = prevLine.from + inseted.length;
            } else {
              const newQuotePrefix = ">".repeat(quoteLevel - 1) + " ";
              changes = [{ from: line.from, to: line.to, insert: newQuotePrefix }];
              newCursorPos = line.from + newQuotePrefix.length;
            }
          } else {
            const newQuotePrefix = ">".repeat(quoteLevel - 1) + " ";
            changes = [{ from: line.from, to: line.to, insert: newQuotePrefix }];
            newCursorPos = line.from + newQuotePrefix.length;
          }
        } else {
          if (line.number > 1) {
            const prevLine = doc.line(line.number - 1);
            const prevLineContent = prevLine.text;
            const prevQuoteMatch = prevLineContent.match(/^\s*(>+)/);
            if (prevQuoteMatch) {
              changes = [{ from: prevLine.to, to: line.to, insert: "" }];
              newCursorPos = prevLine.to;
            } else {
              changes = [{ from: line.from, to: line.to, insert: "" }];
              newCursorPos = line.from;
            }
          } else {
            changes = [{ from: line.from, to: line.to, insert: "" }];
            newCursorPos = line.from;
          }
        }
      } else {
        if (line.number > 1) {
          const prevLine = doc.line(line.number - 1);
          const prevLineContent = prevLine.text;
          const prevListMatch = prevLineContent.match(/^\s*([-*+]|\d+\.)\s/);
          if (prevListMatch) {
            changes = [{ from: prevLine.to, to: line.to, insert: "" }];
            newCursorPos = prevLine.to;
          } else {
            changes = [{ from: line.from, to: line.to, insert: "" }];
            newCursorPos = line.from;
          }
        } else {
          changes = [{ from: line.from, to: line.to, insert: "" }];
          newCursorPos = line.from;
        }
        let nextLineNumber = line.number + 1;
        const currentIndent = lineContent.match(/^\s*/)[0];
        const currentListMatch = lineContent.match(/^\s*(\d+)\.\s/);
        let expectedNextNumber = currentListMatch ? parseInt(currentListMatch[1], 10) + 1 : null;
        while (nextLineNumber <= doc.lines && expectedNextNumber !== null) {
          const nextLine = doc.line(nextLineNumber);
          const nextLineContent = nextLine.text;
          const nextListMatch = nextLineContent.match(/^\s*(\d+)\.\s/);
          if (nextListMatch) {
            const nextIndent = nextLineContent.match(/^\s*/)[0];
            if (nextIndent !== currentIndent) {
              break;
            }
            const nextListNumber = parseInt(nextListMatch[1], 10);
            if (nextListNumber === expectedNextNumber) {
              const newNextLineContent = nextLineContent.replace(/^\s*\d+\.\s/, `${nextIndent}${nextListNumber - 1}. `);
              changes.push({ from: nextLine.from, to: nextLine.to, insert: newNextLineContent });
              expectedNextNumber++;
            } else {
              break;
            }
          } else {
            break;
          }
          nextLineNumber++;
        }
      }
      const tr = state.update({
        changes,
        selection: { anchor: newCursorPos, head: newCursorPos },
        userEvent: "EasyTyping.handleBackspace"
      });
      view.dispatch(tr);
      return true;
    }
    return false;
  }
  isCurrentFileExclude() {
    if (this.CurActiveMarkdown == "") {
      let file = this.app.workspace.getActiveFile();
      if (file != null && this.CurActiveMarkdown != file.path) {
        this.CurActiveMarkdown = file.path;
      } else {
        return true;
      }
    }
    let excludePaths = this.settings.ExcludeFiles.split("\n");
    for (let epath of excludePaths) {
      if (epath.charAt(0) == "/")
        epath = epath.substring(1);
      if (this.CurActiveMarkdown == epath)
        return true;
      let len = epath.length;
      if (this.CurActiveMarkdown.substring(0, len) == epath && (this.CurActiveMarkdown.charAt(len) == "/" || this.CurActiveMarkdown.charAt(len) == "\\" || epath.charAt(len - 1) == "/" || epath.charAt(len - 1) == "\\")) {
        return true;
      }
    }
    return false;
  }
  switchAutoFormatting() {
    this.settings.AutoFormat = !this.settings.AutoFormat;
    let status = this.settings.AutoFormat ? "on" : "off";
    new import_obsidian3.Notice("EasyTyping: Autoformat is " + status + "!");
  }
  convert2CodeBlock(editor) {
    if (this.settings.debug)
      console.log("----- EasyTyping: insert code block-----");
    if (editor.somethingSelected && editor.getSelection() != "") {
      let selected = editor.getSelection();
      let selectedRange = editor.listSelections()[0];
      let anchor = selectedRange.anchor;
      let head = selectedRange.head;
      let replacement = "```\n" + selected + "\n```";
      if (anchor.line > head.line || anchor.line == head.line && anchor.ch > head.ch) {
        let temp = anchor;
        anchor = head;
        head = temp;
      }
      let dstLine = anchor.line;
      if (anchor.ch != 0) {
        replacement = "\n" + replacement;
        dstLine += 1;
      }
      if (head.ch != editor.getLine(head.line).length) {
        replacement = replacement + "\n";
      }
      editor.replaceSelection(replacement);
      editor.setCursor({ line: dstLine, ch: 3 });
    } else {
      let cs = editor.getCursor();
      let replace = "```\n```";
      let dstLine = cs.line;
      if (cs.ch != 0) {
        replace = "\n" + replace;
        dstLine += 1;
      }
      if (cs.ch != editor.getLine(cs.line).length) {
        replace = replace + "\n";
      }
      editor.replaceRange(replace, cs);
      editor.setCursor({ line: dstLine, ch: 3 });
    }
  }
  refreshSelectionReplaceRule() {
    this.SelectionReplaceMap = new Map(this.selectionReplaceMapInitalData);
    for (let i = 0; i < this.settings.userSelRepRuleTrigger.length; i++) {
      let trigger = this.settings.userSelRepRuleTrigger[i];
      let lefts = this.settings.userSelRepRuleValue[i].left;
      let rights = this.settings.userSelRepRuleValue[i].right;
      this.SelectionReplaceMap.set(trigger, { left: lefts, right: rights });
    }
  }
  addUserSelectionRepRule(trigger, left, right) {
    if (this.settings.userSelRepRuleTrigger.includes(trigger))
      return false;
    this.settings.userSelRepRuleTrigger.push(trigger);
    this.settings.userSelRepRuleValue.push({ left, right });
    this.refreshSelectionReplaceRule();
    return true;
  }
  deleteUserSelectionRepRule(idx) {
    if (idx < 0 || idx >= this.settings.userSelRepRuleTrigger.length)
      return;
    this.settings.userSelRepRuleTrigger.splice(idx, 1);
    this.settings.userSelRepRuleValue.splice(idx, 1);
    this.refreshSelectionReplaceRule();
  }
  updateUserSelectionRepRule(idx, left, right) {
    if (idx < 0 || idx >= this.settings.userSelRepRuleTrigger.length)
      return;
    this.settings.userSelRepRuleValue[idx].left = left;
    this.settings.userSelRepRuleValue[idx].right = right;
    this.refreshSelectionReplaceRule();
  }
  refreshUserDeleteRule() {
    this.UserDeleteRules = ruleStringList2RuleList(this.settings.userDeleteRulesStrList);
  }
  addUserDeleteRule(before, after) {
    this.settings.userDeleteRulesStrList.push([before, after]);
    this.refreshUserDeleteRule();
  }
  deleteUserDeleteRule(idx) {
    if (idx >= this.settings.userDeleteRulesStrList.length || idx < 0)
      return;
    this.settings.userDeleteRulesStrList.splice(idx, 1);
    this.refreshUserDeleteRule();
  }
  updateUserDeleteRule(idx, before, after) {
    if (idx >= this.settings.userDeleteRulesStrList.length || idx < 0)
      return;
    this.settings.userDeleteRulesStrList[idx][0] = before;
    this.settings.userDeleteRulesStrList[idx][1] = after;
    this.refreshUserDeleteRule();
  }
  refreshUserConvertRule() {
    this.UserConvertRules = ruleStringList2RuleList(this.settings.userConvertRulesStrList);
  }
  addUserConvertRule(before, after) {
    this.settings.userConvertRulesStrList.push([before, after]);
    this.refreshUserConvertRule();
  }
  deleteUserConvertRule(idx) {
    if (idx >= this.settings.userConvertRulesStrList.length || idx < 0)
      return;
    this.settings.userConvertRulesStrList.splice(idx, 1);
    this.refreshUserConvertRule();
  }
  getCommandNameMap() {
    const lang = window.localStorage.getItem("language");
    let command_name_map_en = /* @__PURE__ */ new Map([
      ["format_article", "Format current article"],
      ["format_selection", "Format selected text or current line"],
      ["delete_blank_line", "Delete blank lines of the selected or whole article"],
      ["insert_codeblock", "Insert code block w/wo selection"],
      ["switch_autoformat", "Switch autoformat"],
      ["paste_wo_format", "Paste without format"],
      ["toggle_comment", "Toggle comment"],
      ["goto_new_line_after_cur_line", "Go to new line after current line"],
      ["select_block", "Select current text block"]
    ]);
    let command_name_map_zh_TW = /* @__PURE__ */ new Map([
      ["format_article", "\u683C\u5F0F\u5316\u5168\u6587"],
      ["format_selection", "\u683C\u5F0F\u5316\u9078\u4E2D\u90E8\u5206/\u7576\u524D\u884C"],
      ["delete_blank_line", "\u522A\u9664\u9078\u4E2D\u90E8\u5206/\u5168\u6587\u7684\u591A\u9918\u7A7A\u767D\u884C"],
      ["insert_codeblock", "\u63D2\u5165\u4EE3\u78BC\u584A"],
      ["switch_autoformat", "\u5207\u63DB\u81EA\u52D5\u683C\u5F0F\u5316\u958B\u95DC"],
      ["paste_wo_format", "\u7121\u683C\u5F0F\u5316\u7C98\u8CBC"],
      ["toggle_comment", "\u5207\u63DB\u8A3B\u91CB"],
      ["goto_new_line_after_cur_line", "\u8DF3\u5230\u7576\u524D\u884C\u5F8C\u7684\u65B0\u884C"],
      ["select_block", "\u9078\u64C7\u7576\u524D\u6587\u672C\u584A"]
    ]);
    let command_name_map_zh = /* @__PURE__ */ new Map([
      ["format_article", "\u683C\u5F0F\u5316\u5168\u6587"],
      ["format_selection", "\u683C\u5F0F\u5316\u9009\u4E2D\u90E8\u5206/\u5F53\u524D\u884C"],
      ["delete_blank_line", "\u522A\u9664\u9009\u4E2D\u90E8\u5206/\u5168\u6587\u7684\u591A\u4F59\u7A7A\u767D\u884C"],
      ["insert_codeblock", "\u63D2\u5165\u4EE3\u7801\u5757"],
      ["switch_autoformat", "\u5207\u6362\u81EA\u52A8\u683C\u5F0F\u5316\u5F00\u5173"],
      ["paste_wo_format", "\u65E0\u683C\u5F0F\u5316\u7C98\u8D34"],
      ["toggle_comment", "\u5207\u6362\u6CE8\u91CA"],
      ["goto_new_line_after_cur_line", "\u8DF3\u5230\u5F53\u524D\u884C\u540E\u65B0\u884C"],
      ["select_block", "\u9009\u62E9\u5F53\u524D\u6587\u672C\u5757"]
    ]);
    let command_name_map_ru = /* @__PURE__ */ new Map([
      ["format_article", "\u0424\u043E\u0440\u043C\u0430\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0442\u0435\u043A\u0443\u0449\u0443\u044E \u0441\u0442\u0430\u0442\u044C\u044E"],
      ["format_selection", "\u0424\u043E\u0440\u043C\u0430\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0432\u044B\u0434\u0435\u043B\u0435\u043D\u043D\u044B\u0439 \u0442\u0435\u043A\u0441\u0442 \u0438\u043B\u0438 \u0442\u0435\u043A\u0443\u0449\u0443\u044E \u0441\u0442\u0440\u043E\u043A\u0443"],
      ["delete_blank_line", "\u0423\u0434\u0430\u043B\u0438\u0442\u044C \u043F\u0443\u0441\u0442\u044B\u0435 \u0441\u0442\u0440\u043E\u043A\u0438 \u0432 \u0432\u044B\u0434\u0435\u043B\u0435\u043D\u043D\u043E\u043C \u0438\u043B\u0438 \u0432\u0441\u0435\u0439 \u0441\u0442\u0430\u0442\u044C\u0435"],
      ["insert_codeblock", "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u044C \u0431\u043B\u043E\u043A \u043A\u043E\u0434\u0430 \u0441/\u0431\u0435\u0437 \u0432\u044B\u0434\u0435\u043B\u0435\u043D\u0438\u0435\u043C"],
      ["switch_autoformat", "\u041F\u0435\u0440\u0435\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u0430\u0432\u0442\u043E\u0444\u043E\u0440\u043C\u0430\u0442\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u0435"],
      ["paste_wo_format", "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u044C \u0431\u0435\u0437 \u0444\u043E\u0440\u043C\u0430\u0442\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u044F"],
      ["toggle_comment", "\u041F\u0435\u0440\u0435\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u043A\u043E\u043C\u043C\u0435\u043D\u0442\u0430\u0440\u0438\u0439"],
      ["goto_new_line_after_cur_line", "\u041F\u0435\u0440\u0435\u0439\u0442\u0438 \u043A \u043D\u043E\u0432\u043E\u0439 \u0441\u0442\u0440\u043E\u043A\u0435 \u043F\u043E\u0441\u043B\u0435 \u0442\u0435\u043A\u0443\u0449\u0435\u0439"],
      ["select_block", "\u0412\u044B\u0431\u0440\u0430\u0442\u044C \u0442\u0435\u043A\u0443\u0449\u0438\u0439 \u0442\u0435\u043A\u0441\u0442\u043E\u0432\u044B\u0439 \u0431\u043B\u043E\u043A"]
    ]);
    let command_name_map = command_name_map_en;
    if (lang == "zh") {
      command_name_map = command_name_map_zh;
    } else if (lang == "zh-TW") {
      command_name_map = command_name_map_zh_TW;
    } else if (lang == "ru") {
      command_name_map = command_name_map_ru;
    }
    return command_name_map;
  }
  updateUserConvertRule(idx, before, after) {
    if (idx >= this.settings.userConvertRulesStrList.length || idx < 0)
      return;
    this.settings.userConvertRulesStrList[idx][0] = before;
    this.settings.userConvertRulesStrList[idx][1] = after;
    this.refreshUserConvertRule();
  }
  async loadSettings() {
    this.settings = Object.assign({}, DEFAULT_SETTINGS, await this.loadData());
  }
  async saveSettings() {
    await this.saveData(this.settings);
  }
};


/* nosourcemap */