---
tags:
  - Project
Status: ongoing
Goal: 顺利完成项目
Deadline: 2025-05-31
---

# 预期效果
1. [x] 设备元器件沿设备中心向四周散开、 复原 ✅ 2025-04-29
2. [x] 点击控制开关，相对应的元器件有响应 ✅ 2025-04-28
3. [x] 门：有显示和隐藏的效果 ✅ 2025-04-28
4. [x] 水管：有水流动的效果 ✅ 2025-04-28
5. [x] 状态显示器：有闪烁、变色的效果 ✅ 2025-04-28
6. [x] 点击某个功能区域：执行相关操作 ✅ 2025-04-30
7. [x] 实现2D 与3D 物体之间的连线，如果物体在视野范围外则连线消失 ✅ 2025-05-06


# 开发工具
- [[Unity]]
- [[Blender]] 


# 需要确定的问题：
1. 整体框架上，是不是总览+漫游？
2. 整体风格上：是不是把风洞的深蓝色风格（天空盒模型、光照、地面）移植过来？
3. 漫游的具体流程是什么样的？
	1. 这里面水管很多，效果都要加上，做起来也蛮费时间的。
	2. 门开关、面板隐藏显示的顺序。
	3. 显示器上的数据展示也要明确
	4. 镜头的焦点放到哪里。怎么切换。
4. 要显示哪些数据，是不是采用 unity + 外部 echart 组件的方式  —— 这种方式需要预研能结合的多深。
5. Mqtt 消息的支持也需要预研。

