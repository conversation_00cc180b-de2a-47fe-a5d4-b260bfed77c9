/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var hd=Object.create;var vi=Object.defineProperty;var gd=Object.getOwnPropertyDescriptor;var yd=Object.getOwnPropertyNames;var bd=Object.getPrototypeOf,xd=Object.prototype.hasOwnProperty;var Y=(n,t)=>()=>(t||n((t={exports:{}}).exports,t),t.exports),Se=(n,t)=>{for(var e in t)vi(n,e,{get:t[e],enumerable:!0})},is=(n,t,e,i)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of yd(t))!xd.call(n,r)&&r!==e&&vi(n,r,{get:()=>t[r],enumerable:!(i=gd(t,r))||i.enumerable});return n};var rs=(n,t,e)=>(e=n!=null?hd(bd(n)):{},is(t||!n||!n.__esModule?vi(e,"default",{value:n,enumerable:!0}):e,n)),wd=n=>is(vi({},"__esModule",{value:!0}),n);var Do=Y((Mx,Ml)=>{"use strict";Ml.exports=sh;function sh(n,t){for(var e=new Array(arguments.length-1),i=0,r=2,o=!0;r<arguments.length;)e[i++]=arguments[r++];return new Promise(function(s,l){e[i]=function(p){if(o)if(o=!1,p)l(p);else{for(var m=new Array(arguments.length-1),y=0;y<m.length;)m[y++]=arguments[y];s.apply(null,m)}};try{n.apply(t||null,e)}catch(c){o&&(o=!1,l(c))}})}});var jl=Y(Ul=>{"use strict";var zi=Ul;zi.length=function(t){var e=t.length;if(!e)return 0;for(var i=0;--e%4>1&&t.charAt(e)==="=";)++i;return Math.ceil(t.length*3)/4-i};var wn=new Array(64),Bl=new Array(123);for(Ye=0;Ye<64;)Bl[wn[Ye]=Ye<26?Ye+65:Ye<52?Ye+71:Ye<62?Ye-4:Ye-59|43]=Ye++;var Ye;zi.encode=function(t,e,i){for(var r=null,o=[],a=0,s=0,l;e<i;){var c=t[e++];switch(s){case 0:o[a++]=wn[c>>2],l=(c&3)<<4,s=1;break;case 1:o[a++]=wn[l|c>>4],l=(c&15)<<2,s=2;break;case 2:o[a++]=wn[l|c>>6],o[a++]=wn[c&63],s=0;break}a>8191&&((r||(r=[])).push(String.fromCharCode.apply(String,o)),a=0)}return s&&(o[a++]=wn[l],o[a++]=61,s===1&&(o[a++]=61)),r?(a&&r.push(String.fromCharCode.apply(String,o.slice(0,a))),r.join("")):String.fromCharCode.apply(String,o.slice(0,a))};var $l="invalid encoding";zi.decode=function(t,e,i){for(var r=i,o=0,a,s=0;s<t.length;){var l=t.charCodeAt(s++);if(l===61&&o>1)break;if((l=Bl[l])===void 0)throw Error($l);switch(o){case 0:a=l,o=1;break;case 1:e[i++]=a<<2|(l&48)>>4,a=l,o=2;break;case 2:e[i++]=(a&15)<<4|(l&60)>>2,a=l,o=3;break;case 3:e[i++]=(a&3)<<6|l,o=0;break}}if(o===1)throw Error($l);return i-r};zi.test=function(t){return/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(t)}});var ql=Y((Bx,Hl)=>{"use strict";Hl.exports=Gi;function Gi(){this._listeners={}}Gi.prototype.on=function(t,e,i){return(this._listeners[t]||(this._listeners[t]=[])).push({fn:e,ctx:i||this}),this};Gi.prototype.off=function(t,e){if(t===void 0)this._listeners={};else if(e===void 0)this._listeners[t]=[];else for(var i=this._listeners[t],r=0;r<i.length;)i[r].fn===e?i.splice(r,1):++r;return this};Gi.prototype.emit=function(t){var e=this._listeners[t];if(e){for(var i=[],r=1;r<arguments.length;)i.push(arguments[r++]);for(r=0;r<e.length;)e[r].fn.apply(e[r++].ctx,i)}return this}});var Kl=Y((Ux,Vl)=>{"use strict";Vl.exports=Wl(Wl);function Wl(n){return typeof Float32Array!="undefined"?function(){var t=new Float32Array([-0]),e=new Uint8Array(t.buffer),i=e[3]===128;function r(l,c,p){t[0]=l,c[p]=e[0],c[p+1]=e[1],c[p+2]=e[2],c[p+3]=e[3]}function o(l,c,p){t[0]=l,c[p]=e[3],c[p+1]=e[2],c[p+2]=e[1],c[p+3]=e[0]}n.writeFloatLE=i?r:o,n.writeFloatBE=i?o:r;function a(l,c){return e[0]=l[c],e[1]=l[c+1],e[2]=l[c+2],e[3]=l[c+3],t[0]}function s(l,c){return e[3]=l[c],e[2]=l[c+1],e[1]=l[c+2],e[0]=l[c+3],t[0]}n.readFloatLE=i?a:s,n.readFloatBE=i?s:a}():function(){function t(i,r,o,a){var s=r<0?1:0;if(s&&(r=-r),r===0)i(1/r>0?0:2147483648,o,a);else if(isNaN(r))i(2143289344,o,a);else if(r>34028234663852886e22)i((s<<31|2139095040)>>>0,o,a);else if(r<11754943508222875e-54)i((s<<31|Math.round(r/1401298464324817e-60))>>>0,o,a);else{var l=Math.floor(Math.log(r)/Math.LN2),c=Math.round(r*Math.pow(2,-l)*8388608)&8388607;i((s<<31|l+127<<23|c)>>>0,o,a)}}n.writeFloatLE=t.bind(null,Zl),n.writeFloatBE=t.bind(null,Yl);function e(i,r,o){var a=i(r,o),s=(a>>31)*2+1,l=a>>>23&255,c=a&8388607;return l===255?c?NaN:s*(1/0):l===0?s*1401298464324817e-60*c:s*Math.pow(2,l-150)*(c+8388608)}n.readFloatLE=e.bind(null,zl),n.readFloatBE=e.bind(null,Gl)}(),typeof Float64Array!="undefined"?function(){var t=new Float64Array([-0]),e=new Uint8Array(t.buffer),i=e[7]===128;function r(l,c,p){t[0]=l,c[p]=e[0],c[p+1]=e[1],c[p+2]=e[2],c[p+3]=e[3],c[p+4]=e[4],c[p+5]=e[5],c[p+6]=e[6],c[p+7]=e[7]}function o(l,c,p){t[0]=l,c[p]=e[7],c[p+1]=e[6],c[p+2]=e[5],c[p+3]=e[4],c[p+4]=e[3],c[p+5]=e[2],c[p+6]=e[1],c[p+7]=e[0]}n.writeDoubleLE=i?r:o,n.writeDoubleBE=i?o:r;function a(l,c){return e[0]=l[c],e[1]=l[c+1],e[2]=l[c+2],e[3]=l[c+3],e[4]=l[c+4],e[5]=l[c+5],e[6]=l[c+6],e[7]=l[c+7],t[0]}function s(l,c){return e[7]=l[c],e[6]=l[c+1],e[5]=l[c+2],e[4]=l[c+3],e[3]=l[c+4],e[2]=l[c+5],e[1]=l[c+6],e[0]=l[c+7],t[0]}n.readDoubleLE=i?a:s,n.readDoubleBE=i?s:a}():function(){function t(i,r,o,a,s,l){var c=a<0?1:0;if(c&&(a=-a),a===0)i(0,s,l+r),i(1/a>0?0:2147483648,s,l+o);else if(isNaN(a))i(0,s,l+r),i(2146959360,s,l+o);else if(a>17976931348623157e292)i(0,s,l+r),i((c<<31|2146435072)>>>0,s,l+o);else{var p;if(a<22250738585072014e-324)p=a/5e-324,i(p>>>0,s,l+r),i((c<<31|p/4294967296)>>>0,s,l+o);else{var m=Math.floor(Math.log(a)/Math.LN2);m===1024&&(m=1023),p=a*Math.pow(2,-m),i(p*4503599627370496>>>0,s,l+r),i((c<<31|m+1023<<20|p*1048576&1048575)>>>0,s,l+o)}}}n.writeDoubleLE=t.bind(null,Zl,0,4),n.writeDoubleBE=t.bind(null,Yl,4,0);function e(i,r,o,a,s){var l=i(a,s+r),c=i(a,s+o),p=(c>>31)*2+1,m=c>>>20&2047,y=4294967296*(c&1048575)+l;return m===2047?y?NaN:p*(1/0):m===0?p*5e-324*y:p*Math.pow(2,m-1075)*(y+4503599627370496)}n.readDoubleLE=e.bind(null,zl,0,4),n.readDoubleBE=e.bind(null,Gl,4,0)}(),n}function Zl(n,t,e){t[e]=n&255,t[e+1]=n>>>8&255,t[e+2]=n>>>16&255,t[e+3]=n>>>24}function Yl(n,t,e){t[e]=n>>>24,t[e+1]=n>>>16&255,t[e+2]=n>>>8&255,t[e+3]=n&255}function zl(n,t){return(n[t]|n[t+1]<<8|n[t+2]<<16|n[t+3]<<24)>>>0}function Gl(n,t){return(n[t]<<24|n[t+1]<<16|n[t+2]<<8|n[t+3])>>>0}});var Co=Y((exports,module)=>{"use strict";module.exports=inquire;function inquire(moduleName){try{var mod=eval("quire".replace(/^/,"re"))(moduleName);if(mod&&(mod.length||Object.keys(mod).length))return mod}catch(n){}return null}});var Jl=Y(Xl=>{"use strict";var Io=Xl;Io.length=function(t){for(var e=0,i=0,r=0;r<t.length;++r)i=t.charCodeAt(r),i<128?e+=1:i<2048?e+=2:(i&64512)===55296&&(t.charCodeAt(r+1)&64512)===56320?(++r,e+=4):e+=3;return e};Io.read=function(t,e,i){var r=i-e;if(r<1)return"";for(var o=null,a=[],s=0,l;e<i;)l=t[e++],l<128?a[s++]=l:l>191&&l<224?a[s++]=(l&31)<<6|t[e++]&63:l>239&&l<365?(l=((l&7)<<18|(t[e++]&63)<<12|(t[e++]&63)<<6|t[e++]&63)-65536,a[s++]=55296+(l>>10),a[s++]=56320+(l&1023)):a[s++]=(l&15)<<12|(t[e++]&63)<<6|t[e++]&63,s>8191&&((o||(o=[])).push(String.fromCharCode.apply(String,a)),s=0);return o?(s&&o.push(String.fromCharCode.apply(String,a.slice(0,s))),o.join("")):String.fromCharCode.apply(String,a.slice(0,s))};Io.write=function(t,e,i){for(var r=i,o,a,s=0;s<t.length;++s)o=t.charCodeAt(s),o<128?e[i++]=o:o<2048?(e[i++]=o>>6|192,e[i++]=o&63|128):(o&64512)===55296&&((a=t.charCodeAt(s+1))&64512)===56320?(o=65536+((o&1023)<<10)+(a&1023),++s,e[i++]=o>>18|240,e[i++]=o>>12&63|128,e[i++]=o>>6&63|128,e[i++]=o&63|128):(e[i++]=o>>12|224,e[i++]=o>>6&63|128,e[i++]=o&63|128);return i-r}});var ec=Y((Hx,Ql)=>{"use strict";Ql.exports=lh;function lh(n,t,e){var i=e||8192,r=i>>>1,o=null,a=i;return function(l){if(l<1||l>r)return n(l);a+l>i&&(o=n(i),a=0);var c=t.call(o,a,a+=l);return a&7&&(a=(a|7)+1),c}}});var nc=Y((qx,tc)=>{"use strict";tc.exports=ge;var ei=et();function ge(n,t){this.lo=n>>>0,this.hi=t>>>0}var Jt=ge.zero=new ge(0,0);Jt.toNumber=function(){return 0};Jt.zzEncode=Jt.zzDecode=function(){return this};Jt.length=function(){return 1};var ch=ge.zeroHash="\0\0\0\0\0\0\0\0";ge.fromNumber=function(t){if(t===0)return Jt;var e=t<0;e&&(t=-t);var i=t>>>0,r=(t-i)/4294967296>>>0;return e&&(r=~r>>>0,i=~i>>>0,++i>4294967295&&(i=0,++r>4294967295&&(r=0))),new ge(i,r)};ge.from=function(t){if(typeof t=="number")return ge.fromNumber(t);if(ei.isString(t))if(ei.Long)t=ei.Long.fromString(t);else return ge.fromNumber(parseInt(t,10));return t.low||t.high?new ge(t.low>>>0,t.high>>>0):Jt};ge.prototype.toNumber=function(t){if(!t&&this.hi>>>31){var e=~this.lo+1>>>0,i=~this.hi>>>0;return e||(i=i+1>>>0),-(e+i*4294967296)}return this.lo+this.hi*4294967296};ge.prototype.toLong=function(t){return ei.Long?new ei.Long(this.lo|0,this.hi|0,Boolean(t)):{low:this.lo|0,high:this.hi|0,unsigned:Boolean(t)}};var Ot=String.prototype.charCodeAt;ge.fromHash=function(t){return t===ch?Jt:new ge((Ot.call(t,0)|Ot.call(t,1)<<8|Ot.call(t,2)<<16|Ot.call(t,3)<<24)>>>0,(Ot.call(t,4)|Ot.call(t,5)<<8|Ot.call(t,6)<<16|Ot.call(t,7)<<24)>>>0)};ge.prototype.toHash=function(){return String.fromCharCode(this.lo&255,this.lo>>>8&255,this.lo>>>16&255,this.lo>>>24,this.hi&255,this.hi>>>8&255,this.hi>>>16&255,this.hi>>>24)};ge.prototype.zzEncode=function(){var t=this.hi>>31;return this.hi=((this.hi<<1|this.lo>>>31)^t)>>>0,this.lo=(this.lo<<1^t)>>>0,this};ge.prototype.zzDecode=function(){var t=-(this.lo&1);return this.lo=((this.lo>>>1|this.hi<<31)^t)>>>0,this.hi=(this.hi>>>1^t)>>>0,this};ge.prototype.length=function(){var t=this.lo,e=(this.lo>>>28|this.hi<<4)>>>0,i=this.hi>>>24;return i===0?e===0?t<16384?t<128?1:2:t<2097152?3:4:e<16384?e<128?5:6:e<2097152?7:8:i<128?9:10}});var et=Y(Fo=>{"use strict";var H=Fo;H.asPromise=Do();H.base64=jl();H.EventEmitter=ql();H.float=Kl();H.inquire=Co();H.utf8=Jl();H.pool=ec();H.LongBits=nc();H.isNode=Boolean(typeof global!="undefined"&&global&&global.process&&global.process.versions&&global.process.versions.node);H.global=H.isNode&&global||typeof window!="undefined"&&window||typeof self!="undefined"&&self||Fo;H.emptyArray=Object.freeze?Object.freeze([]):[];H.emptyObject=Object.freeze?Object.freeze({}):{};H.isInteger=Number.isInteger||function(t){return typeof t=="number"&&isFinite(t)&&Math.floor(t)===t};H.isString=function(t){return typeof t=="string"||t instanceof String};H.isObject=function(t){return t&&typeof t=="object"};H.isset=H.isSet=function(t,e){var i=t[e];return i!=null&&t.hasOwnProperty(e)?typeof i!="object"||(Array.isArray(i)?i.length:Object.keys(i).length)>0:!1};H.Buffer=function(){try{var n=H.inquire("buffer").Buffer;return n.prototype.utf8Write?n:null}catch(t){return null}}();H._Buffer_from=null;H._Buffer_allocUnsafe=null;H.newBuffer=function(t){return typeof t=="number"?H.Buffer?H._Buffer_allocUnsafe(t):new H.Array(t):H.Buffer?H._Buffer_from(t):typeof Uint8Array=="undefined"?t:new Uint8Array(t)};H.Array=typeof Uint8Array!="undefined"?Uint8Array:Array;H.Long=H.global.dcodeIO&&H.global.dcodeIO.Long||H.global.Long||H.inquire("long");H.key2Re=/^true|false|0|1$/;H.key32Re=/^-?(?:0|[1-9][0-9]*)$/;H.key64Re=/^(?:[\\x00-\\xff]{8}|-?(?:0|[1-9][0-9]*))$/;H.longToHash=function(t){return t?H.LongBits.from(t).toHash():H.LongBits.zeroHash};H.longFromHash=function(t,e){var i=H.LongBits.fromHash(t);return H.Long?H.Long.fromBits(i.lo,i.hi,e):i.toNumber(Boolean(e))};function ic(n,t,e){for(var i=Object.keys(t),r=0;r<i.length;++r)(n[i[r]]===void 0||!e)&&(n[i[r]]=t[i[r]]);return n}H.merge=ic;H.lcFirst=function(t){return t.charAt(0).toLowerCase()+t.substring(1)};function rc(n){function t(e,i){if(!(this instanceof t))return new t(e,i);Object.defineProperty(this,"message",{get:function(){return e}}),Error.captureStackTrace?Error.captureStackTrace(this,t):Object.defineProperty(this,"stack",{value:new Error().stack||""}),i&&ic(this,i)}return t.prototype=Object.create(Error.prototype,{constructor:{value:t,writable:!0,enumerable:!1,configurable:!0},name:{get:function(){return n},set:void 0,enumerable:!1,configurable:!0},toString:{value:function(){return this.name+": "+this.message},writable:!0,enumerable:!1,configurable:!0}}),t}H.newError=rc;H.ProtocolError=rc("ProtocolError");H.oneOfGetter=function(t){for(var e={},i=0;i<t.length;++i)e[t[i]]=1;return function(){for(var r=Object.keys(this),o=r.length-1;o>-1;--o)if(e[r[o]]===1&&this[r[o]]!==void 0&&this[r[o]]!==null)return r[o]}};H.oneOfSetter=function(t){return function(e){for(var i=0;i<t.length;++i)t[i]!==e&&delete this[t[i]]}};H.toJSONOptions={longs:String,enums:String,bytes:String,json:!0};H._configure=function(){var n=H.Buffer;if(!n){H._Buffer_from=H._Buffer_allocUnsafe=null;return}H._Buffer_from=n.from!==Uint8Array.from&&n.from||function(e,i){return new n(e,i)},H._Buffer_allocUnsafe=n.allocUnsafe||function(e){return new n(e)}}});var Ki=Y((Zx,lc)=>{"use strict";lc.exports=X;var $e=et(),Lo,Vi=$e.LongBits,oc=$e.base64,ac=$e.utf8;function ti(n,t,e){this.fn=n,this.len=t,this.next=void 0,this.val=e}function Mo(){}function ph(n){this.head=n.head,this.tail=n.tail,this.len=n.len,this.next=n.states}function X(){this.len=0,this.head=new ti(Mo,0,0),this.tail=this.head,this.states=null}var sc=function(){return $e.Buffer?function(){return(X.create=function(){return new Lo})()}:function(){return new X}};X.create=sc();X.alloc=function(t){return new $e.Array(t)};$e.Array!==Array&&(X.alloc=$e.pool(X.alloc,$e.Array.prototype.subarray));X.prototype._push=function(t,e,i){return this.tail=this.tail.next=new ti(t,e,i),this.len+=e,this};function $o(n,t,e){t[e]=n&255}function uh(n,t,e){for(;n>127;)t[e++]=n&127|128,n>>>=7;t[e]=n}function Bo(n,t){this.len=n,this.next=void 0,this.val=t}Bo.prototype=Object.create(ti.prototype);Bo.prototype.fn=uh;X.prototype.uint32=function(t){return this.len+=(this.tail=this.tail.next=new Bo((t=t>>>0)<128?1:t<16384?2:t<2097152?3:t<268435456?4:5,t)).len,this};X.prototype.int32=function(t){return t<0?this._push(Uo,10,Vi.fromNumber(t)):this.uint32(t)};X.prototype.sint32=function(t){return this.uint32((t<<1^t>>31)>>>0)};function Uo(n,t,e){for(;n.hi;)t[e++]=n.lo&127|128,n.lo=(n.lo>>>7|n.hi<<25)>>>0,n.hi>>>=7;for(;n.lo>127;)t[e++]=n.lo&127|128,n.lo=n.lo>>>7;t[e++]=n.lo}X.prototype.uint64=function(t){var e=Vi.from(t);return this._push(Uo,e.length(),e)};X.prototype.int64=X.prototype.uint64;X.prototype.sint64=function(t){var e=Vi.from(t).zzEncode();return this._push(Uo,e.length(),e)};X.prototype.bool=function(t){return this._push($o,1,t?1:0)};function Po(n,t,e){t[e]=n&255,t[e+1]=n>>>8&255,t[e+2]=n>>>16&255,t[e+3]=n>>>24}X.prototype.fixed32=function(t){return this._push(Po,4,t>>>0)};X.prototype.sfixed32=X.prototype.fixed32;X.prototype.fixed64=function(t){var e=Vi.from(t);return this._push(Po,4,e.lo)._push(Po,4,e.hi)};X.prototype.sfixed64=X.prototype.fixed64;X.prototype.float=function(t){return this._push($e.float.writeFloatLE,4,t)};X.prototype.double=function(t){return this._push($e.float.writeDoubleLE,8,t)};var fh=$e.Array.prototype.set?function(t,e,i){e.set(t,i)}:function(t,e,i){for(var r=0;r<t.length;++r)e[i+r]=t[r]};X.prototype.bytes=function(t){var e=t.length>>>0;if(!e)return this._push($o,1,0);if($e.isString(t)){var i=X.alloc(e=oc.length(t));oc.decode(t,i,0),t=i}return this.uint32(e)._push(fh,e,t)};X.prototype.string=function(t){var e=ac.length(t);return e?this.uint32(e)._push(ac.write,e,t):this._push($o,1,0)};X.prototype.fork=function(){return this.states=new ph(this),this.head=this.tail=new ti(Mo,0,0),this.len=0,this};X.prototype.reset=function(){return this.states?(this.head=this.states.head,this.tail=this.states.tail,this.len=this.states.len,this.states=this.states.next):(this.head=this.tail=new ti(Mo,0,0),this.len=0),this};X.prototype.ldelim=function(){var t=this.head,e=this.tail,i=this.len;return this.reset().uint32(i),i&&(this.tail.next=t.next,this.tail=e,this.len+=i),this};X.prototype.finish=function(){for(var t=this.head.next,e=this.constructor.alloc(this.len),i=0;t;)t.fn(t.val,e,i),i+=t.len,t=t.next;return e};X._configure=function(n){Lo=n,X.create=sc(),Lo._configure()}});var uc=Y((Yx,pc)=>{"use strict";pc.exports=tt;var cc=Ki();(tt.prototype=Object.create(cc.prototype)).constructor=tt;var Dt=et();function tt(){cc.call(this)}tt._configure=function(){tt.alloc=Dt._Buffer_allocUnsafe,tt.writeBytesBuffer=Dt.Buffer&&Dt.Buffer.prototype instanceof Uint8Array&&Dt.Buffer.prototype.set.name==="set"?function(t,e,i){e.set(t,i)}:function(t,e,i){if(t.copy)t.copy(e,i,0,t.length);else for(var r=0;r<t.length;)e[i++]=t[r++]}};tt.prototype.bytes=function(t){Dt.isString(t)&&(t=Dt._Buffer_from(t,"base64"));var e=t.length>>>0;return this.uint32(e),e&&this._push(tt.writeBytesBuffer,e,t),this};function dh(n,t,e){n.length<40?Dt.utf8.write(n,t,e):t.utf8Write?t.utf8Write(n,e):t.write(n,e)}tt.prototype.string=function(t){var e=Dt.Buffer.byteLength(t);return this.uint32(e),e&&this._push(dh,e,t),this};tt._configure()});var Ji=Y((zx,gc)=>{"use strict";gc.exports=se;var ze=et(),Ho,mc=ze.LongBits,mh=ze.utf8;function Ge(n,t){return RangeError("index out of range: "+n.pos+" + "+(t||1)+" > "+n.len)}function se(n){this.buf=n,this.pos=0,this.len=n.length}var fc=typeof Uint8Array!="undefined"?function(t){if(t instanceof Uint8Array||Array.isArray(t))return new se(t);throw Error("illegal buffer")}:function(t){if(Array.isArray(t))return new se(t);throw Error("illegal buffer")},hc=function(){return ze.Buffer?function(e){return(se.create=function(r){return ze.Buffer.isBuffer(r)?new Ho(r):fc(r)})(e)}:fc};se.create=hc();se.prototype._slice=ze.Array.prototype.subarray||ze.Array.prototype.slice;se.prototype.uint32=function(){var t=4294967295;return function(){if(t=(this.buf[this.pos]&127)>>>0,this.buf[this.pos++]<128||(t=(t|(this.buf[this.pos]&127)<<7)>>>0,this.buf[this.pos++]<128)||(t=(t|(this.buf[this.pos]&127)<<14)>>>0,this.buf[this.pos++]<128)||(t=(t|(this.buf[this.pos]&127)<<21)>>>0,this.buf[this.pos++]<128)||(t=(t|(this.buf[this.pos]&15)<<28)>>>0,this.buf[this.pos++]<128))return t;if((this.pos+=5)>this.len)throw this.pos=this.len,Ge(this,10);return t}}();se.prototype.int32=function(){return this.uint32()|0};se.prototype.sint32=function(){var t=this.uint32();return t>>>1^-(t&1)|0};function jo(){var n=new mc(0,0),t=0;if(this.len-this.pos>4){for(;t<4;++t)if(n.lo=(n.lo|(this.buf[this.pos]&127)<<t*7)>>>0,this.buf[this.pos++]<128)return n;if(n.lo=(n.lo|(this.buf[this.pos]&127)<<28)>>>0,n.hi=(n.hi|(this.buf[this.pos]&127)>>4)>>>0,this.buf[this.pos++]<128)return n;t=0}else{for(;t<3;++t){if(this.pos>=this.len)throw Ge(this);if(n.lo=(n.lo|(this.buf[this.pos]&127)<<t*7)>>>0,this.buf[this.pos++]<128)return n}return n.lo=(n.lo|(this.buf[this.pos++]&127)<<t*7)>>>0,n}if(this.len-this.pos>4){for(;t<5;++t)if(n.hi=(n.hi|(this.buf[this.pos]&127)<<t*7+3)>>>0,this.buf[this.pos++]<128)return n}else for(;t<5;++t){if(this.pos>=this.len)throw Ge(this);if(n.hi=(n.hi|(this.buf[this.pos]&127)<<t*7+3)>>>0,this.buf[this.pos++]<128)return n}throw Error("invalid varint encoding")}se.prototype.bool=function(){return this.uint32()!==0};function Xi(n,t){return(n[t-4]|n[t-3]<<8|n[t-2]<<16|n[t-1]<<24)>>>0}se.prototype.fixed32=function(){if(this.pos+4>this.len)throw Ge(this,4);return Xi(this.buf,this.pos+=4)};se.prototype.sfixed32=function(){if(this.pos+4>this.len)throw Ge(this,4);return Xi(this.buf,this.pos+=4)|0};function dc(){if(this.pos+8>this.len)throw Ge(this,8);return new mc(Xi(this.buf,this.pos+=4),Xi(this.buf,this.pos+=4))}se.prototype.float=function(){if(this.pos+4>this.len)throw Ge(this,4);var t=ze.float.readFloatLE(this.buf,this.pos);return this.pos+=4,t};se.prototype.double=function(){if(this.pos+8>this.len)throw Ge(this,4);var t=ze.float.readDoubleLE(this.buf,this.pos);return this.pos+=8,t};se.prototype.bytes=function(){var t=this.uint32(),e=this.pos,i=this.pos+t;if(i>this.len)throw Ge(this,t);if(this.pos+=t,Array.isArray(this.buf))return this.buf.slice(e,i);if(e===i){var r=ze.Buffer;return r?r.alloc(0):new this.buf.constructor(0)}return this._slice.call(this.buf,e,i)};se.prototype.string=function(){var t=this.bytes();return mh.read(t,0,t.length)};se.prototype.skip=function(t){if(typeof t=="number"){if(this.pos+t>this.len)throw Ge(this,t);this.pos+=t}else do if(this.pos>=this.len)throw Ge(this);while(this.buf[this.pos++]&128);return this};se.prototype.skipType=function(n){switch(n){case 0:this.skip();break;case 1:this.skip(8);break;case 2:this.skip(this.uint32());break;case 3:for(;(n=this.uint32()&7)!==4;)this.skipType(n);break;case 5:this.skip(4);break;default:throw Error("invalid wire type "+n+" at offset "+this.pos)}return this};se._configure=function(n){Ho=n,se.create=hc(),Ho._configure();var t=ze.Long?"toLong":"toNumber";ze.merge(se.prototype,{int64:function(){return jo.call(this)[t](!1)},uint64:function(){return jo.call(this)[t](!0)},sint64:function(){return jo.call(this).zzDecode()[t](!1)},fixed64:function(){return dc.call(this)[t](!0)},sfixed64:function(){return dc.call(this)[t](!1)}})}});var wc=Y((Gx,xc)=>{"use strict";xc.exports=Qt;var bc=Ji();(Qt.prototype=Object.create(bc.prototype)).constructor=Qt;var yc=et();function Qt(n){bc.call(this,n)}Qt._configure=function(){yc.Buffer&&(Qt.prototype._slice=yc.Buffer.prototype.slice)};Qt.prototype.string=function(){var t=this.uint32();return this.buf.utf8Slice?this.buf.utf8Slice(this.pos,this.pos=Math.min(this.pos+t,this.len)):this.buf.toString("utf-8",this.pos,this.pos=Math.min(this.pos+t,this.len))};Qt._configure()});var Ec=Y((Vx,vc)=>{"use strict";vc.exports=ni;var qo=et();(ni.prototype=Object.create(qo.EventEmitter.prototype)).constructor=ni;function ni(n,t,e){if(typeof n!="function")throw TypeError("rpcImpl must be a function");qo.EventEmitter.call(this),this.rpcImpl=n,this.requestDelimited=Boolean(t),this.responseDelimited=Boolean(e)}ni.prototype.rpcCall=function n(t,e,i,r,o){if(!r)throw TypeError("request must be specified");var a=this;if(!o)return qo.asPromise(n,a,t,e,i,r);if(!a.rpcImpl){setTimeout(function(){o(Error("already ended"))},0);return}try{return a.rpcImpl(t,e[a.requestDelimited?"encodeDelimited":"encode"](r).finish(),function(l,c){if(l)return a.emit("error",l,t),o(l);if(c===null){a.end(!0);return}if(!(c instanceof i))try{c=i[a.responseDelimited?"decodeDelimited":"decode"](c)}catch(p){return a.emit("error",p,t),o(p)}return a.emit("data",c,t),o(null,c)})}catch(s){a.emit("error",s,t),setTimeout(function(){o(s)},0);return}};ni.prototype.end=function(t){return this.rpcImpl&&(t||this.rpcImpl(null,null,null),this.rpcImpl=null,this.emit("end").off()),this}});var Wo=Y(_c=>{"use strict";var hh=_c;hh.Service=Ec()});var Zo=Y((Xx,Tc)=>{"use strict";Tc.exports={}});var Nc=Y(kc=>{"use strict";var Oe=kc;Oe.build="minimal";Oe.Writer=Ki();Oe.BufferWriter=uc();Oe.Reader=Ji();Oe.BufferReader=wc();Oe.util=et();Oe.rpc=Wo();Oe.roots=Zo();Oe.configure=Ac;function Ac(){Oe.util._configure(),Oe.Writer._configure(Oe.BufferWriter),Oe.Reader._configure(Oe.BufferReader)}Ac()});var Rc=Y((Qx,Sc)=>{"use strict";Sc.exports=Yo;function Yo(n,t){typeof n=="string"&&(t=n,n=void 0);var e=[];function i(o){if(typeof o!="string"){var a=r();if(Yo.verbose&&console.log("codegen: "+a),a="return "+a,o){for(var s=Object.keys(o),l=new Array(s.length+1),c=new Array(s.length),p=0;p<s.length;)l[p]=s[p],c[p]=o[s[p++]];return l[p]=a,Function.apply(null,l).apply(null,c)}return Function(a)()}for(var m=new Array(arguments.length-1),y=0;y<m.length;)m[y]=arguments[++y];if(y=0,o=o.replace(/%([%dfijs])/g,function(E,d){var f=m[y++];switch(d){case"d":case"f":return String(Number(f));case"i":return String(Math.floor(f));case"j":return JSON.stringify(f);case"s":return String(f)}return"%"}),y!==m.length)throw Error("parameter count mismatch");return e.push(o),i}function r(o){return"function "+(o||t||"")+"("+(n&&n.join(",")||"")+`){
  `+e.join(`
  `)+`
}`}return i.toString=r,i}Yo.verbose=!1});var Dc=Y((ew,Oc)=>{"use strict";Oc.exports=ii;var gh=Do(),yh=Co(),zo=yh("fs");function ii(n,t,e){return typeof t=="function"?(e=t,t={}):t||(t={}),e?!t.xhr&&zo&&zo.readFile?zo.readFile(n,function(r,o){return r&&typeof XMLHttpRequest!="undefined"?ii.xhr(n,t,e):r?e(r):e(null,t.binary?o:o.toString("utf8"))}):ii.xhr(n,t,e):gh(ii,this,n,t)}ii.xhr=function(t,e,i){var r=new XMLHttpRequest;r.onreadystatechange=function(){if(r.readyState===4){if(r.status!==0&&r.status!==200)return i(Error("status "+r.status));if(e.binary){var a=r.response;if(!a){a=[];for(var s=0;s<r.responseText.length;++s)a.push(r.responseText.charCodeAt(s)&255)}return i(null,typeof Uint8Array!="undefined"?new Uint8Array(a):a)}return i(null,r.responseText)}},e.binary&&("overrideMimeType"in r&&r.overrideMimeType("text/plain; charset=x-user-defined"),r.responseType="arraybuffer"),r.open("GET",t),r.send()}});var Fc=Y(Ic=>{"use strict";var Vo=Ic,Cc=Vo.isAbsolute=function(t){return/^(?:\/|\w+:)/.test(t)},Go=Vo.normalize=function(t){t=t.replace(/\\/g,"/").replace(/\/{2,}/g,"/");var e=t.split("/"),i=Cc(t),r="";i&&(r=e.shift()+"/");for(var o=0;o<e.length;)e[o]===".."?o>0&&e[o-1]!==".."?e.splice(--o,2):i?e.splice(o,1):++o:e[o]==="."?e.splice(o,1):++o;return r+e.join("/")};Vo.resolve=function(t,e,i){return i||(e=Go(e)),Cc(e)?e:(i||(t=Go(t)),(t=t.replace(/(?:\/|^)[^/]+$/,"")).length?Go(t+"/"+e):e)}});var en=Y(Lc=>{"use strict";var ri=Lc,bh=ye(),xh=["double","float","int32","uint32","sint32","fixed32","sfixed32","int64","uint64","sint64","fixed64","sfixed64","bool","string","bytes"];function oi(n,t){var e=0,i={};for(t|=0;e<n.length;)i[xh[e+t]]=n[e++];return i}ri.basic=oi([1,5,0,0,0,5,5,0,0,0,1,1,0,2,2]);ri.defaults=oi([0,0,0,0,0,0,0,0,0,0,0,0,!1,"",bh.emptyArray,null]);ri.long=oi([0,0,0,1,1],7);ri.mapKey=oi([0,0,0,5,5,0,0,0,1,1,0,2],2);ri.packed=oi([1,5,0,0,0,5,5,0,0,0,1,1,0])});var Ct=Y((iw,$c)=>{"use strict";$c.exports=Be;var Qi=tn();((Be.prototype=Object.create(Qi.prototype)).constructor=Be).className="Field";var Pc=Ve(),Mc=en(),pe=ye(),Ko,wh=/^required|optional|repeated$/;Be.fromJSON=function(t,e){return new Be(t,e.id,e.type,e.rule,e.extend,e.options,e.comment)};function Be(n,t,e,i,r,o,a){if(pe.isObject(i)?(a=r,o=i,i=r=void 0):pe.isObject(r)&&(a=o,o=r,r=void 0),Qi.call(this,n,o),!pe.isInteger(t)||t<0)throw TypeError("id must be a non-negative integer");if(!pe.isString(e))throw TypeError("type must be a string");if(i!==void 0&&!wh.test(i=i.toString().toLowerCase()))throw TypeError("rule must be a string rule");if(r!==void 0&&!pe.isString(r))throw TypeError("extend must be a string");i==="proto3_optional"&&(i="optional"),this.rule=i&&i!=="optional"?i:void 0,this.type=e,this.id=t,this.extend=r||void 0,this.required=i==="required",this.optional=!this.required,this.repeated=i==="repeated",this.map=!1,this.message=null,this.partOf=null,this.typeDefault=null,this.defaultValue=null,this.long=pe.Long?Mc.long[e]!==void 0:!1,this.bytes=e==="bytes",this.resolvedType=null,this.extensionField=null,this.declaringField=null,this._packed=null,this.comment=a}Object.defineProperty(Be.prototype,"packed",{get:function(){return this._packed===null&&(this._packed=this.getOption("packed")!==!1),this._packed}});Be.prototype.setOption=function(t,e,i){return t==="packed"&&(this._packed=null),Qi.prototype.setOption.call(this,t,e,i)};Be.prototype.toJSON=function(t){var e=t?Boolean(t.keepComments):!1;return pe.toObject(["rule",this.rule!=="optional"&&this.rule||void 0,"type",this.type,"id",this.id,"extend",this.extend,"options",this.options,"comment",e?this.comment:void 0])};Be.prototype.resolve=function(){if(this.resolved)return this;if((this.typeDefault=Mc.defaults[this.type])===void 0?(this.resolvedType=(this.declaringField?this.declaringField.parent:this.parent).lookupTypeOrEnum(this.type),this.resolvedType instanceof Ko?this.typeDefault=null:this.typeDefault=this.resolvedType.values[Object.keys(this.resolvedType.values)[0]]):this.options&&this.options.proto3_optional&&(this.typeDefault=null),this.options&&this.options.default!=null&&(this.typeDefault=this.options.default,this.resolvedType instanceof Pc&&typeof this.typeDefault=="string"&&(this.typeDefault=this.resolvedType.values[this.typeDefault])),this.options&&((this.options.packed===!0||this.options.packed!==void 0&&this.resolvedType&&!(this.resolvedType instanceof Pc))&&delete this.options.packed,Object.keys(this.options).length||(this.options=void 0)),this.long)this.typeDefault=pe.Long.fromNumber(this.typeDefault,this.type.charAt(0)==="u"),Object.freeze&&Object.freeze(this.typeDefault);else if(this.bytes&&typeof this.typeDefault=="string"){var t;pe.base64.test(this.typeDefault)?pe.base64.decode(this.typeDefault,t=pe.newBuffer(pe.base64.length(this.typeDefault)),0):pe.utf8.write(this.typeDefault,t=pe.newBuffer(pe.utf8.length(this.typeDefault)),0),this.typeDefault=t}return this.map?this.defaultValue=pe.emptyObject:this.repeated?this.defaultValue=pe.emptyArray:this.defaultValue=this.typeDefault,this.parent instanceof Ko&&(this.parent.ctor.prototype[this.name]=this.defaultValue),Qi.prototype.resolve.call(this)};Be.d=function(t,e,i,r){return typeof e=="function"?e=pe.decorateType(e).name:e&&typeof e=="object"&&(e=pe.decorateEnum(e).name),function(a,s){pe.decorateType(a.constructor).add(new Be(s,t,e,i,{default:r}))}};Be._configure=function(t){Ko=t}});var vn=Y((rw,jc)=>{"use strict";jc.exports=Ue;var tr=tn();((Ue.prototype=Object.create(tr.prototype)).constructor=Ue).className="OneOf";var Bc=Ct(),er=ye();function Ue(n,t,e,i){if(Array.isArray(t)||(e=t,t=void 0),tr.call(this,n,e),!(t===void 0||Array.isArray(t)))throw TypeError("fieldNames must be an Array");this.oneof=t||[],this.fieldsArray=[],this.comment=i}Ue.fromJSON=function(t,e){return new Ue(t,e.oneof,e.options,e.comment)};Ue.prototype.toJSON=function(t){var e=t?Boolean(t.keepComments):!1;return er.toObject(["options",this.options,"oneof",this.oneof,"comment",e?this.comment:void 0])};function Uc(n){if(n.parent)for(var t=0;t<n.fieldsArray.length;++t)n.fieldsArray[t].parent||n.parent.add(n.fieldsArray[t])}Ue.prototype.add=function(t){if(!(t instanceof Bc))throw TypeError("field must be a Field");return t.parent&&t.parent!==this.parent&&t.parent.remove(t),this.oneof.push(t.name),this.fieldsArray.push(t),t.partOf=this,Uc(this),this};Ue.prototype.remove=function(t){if(!(t instanceof Bc))throw TypeError("field must be a Field");var e=this.fieldsArray.indexOf(t);if(e<0)throw Error(t+" is not a member of "+this);return this.fieldsArray.splice(e,1),e=this.oneof.indexOf(t.name),e>-1&&this.oneof.splice(e,1),t.partOf=null,this};Ue.prototype.onAdd=function(t){tr.prototype.onAdd.call(this,t);for(var e=this,i=0;i<this.oneof.length;++i){var r=t.get(this.oneof[i]);r&&!r.partOf&&(r.partOf=e,e.fieldsArray.push(r))}Uc(this)};Ue.prototype.onRemove=function(t){for(var e=0,i;e<this.fieldsArray.length;++e)(i=this.fieldsArray[e]).parent&&i.parent.remove(i);tr.prototype.onRemove.call(this,t)};Ue.d=function(){for(var t=new Array(arguments.length),e=0;e<arguments.length;)t[e]=arguments[e++];return function(r,o){er.decorateType(r.constructor).add(new Ue(o,t)),Object.defineProperty(r,o,{get:er.oneOfGetter(t),set:er.oneOfSetter(t)})}}});var Tn=Y((ow,Zc)=>{"use strict";Zc.exports=J;var Xo=tn();((J.prototype=Object.create(Xo.prototype)).constructor=J).className="Namespace";var Hc=Ct(),nr=ye(),vh=vn(),En,ai,_n;J.fromJSON=function(t,e){return new J(t,e.options).addJSON(e.nested)};function qc(n,t){if(n&&n.length){for(var e={},i=0;i<n.length;++i)e[n[i].name]=n[i].toJSON(t);return e}}J.arrayToJSON=qc;J.isReservedId=function(t,e){if(t){for(var i=0;i<t.length;++i)if(typeof t[i]!="string"&&t[i][0]<=e&&t[i][1]>e)return!0}return!1};J.isReservedName=function(t,e){if(t){for(var i=0;i<t.length;++i)if(t[i]===e)return!0}return!1};function J(n,t){Xo.call(this,n,t),this.nested=void 0,this._nestedArray=null}function Wc(n){return n._nestedArray=null,n}Object.defineProperty(J.prototype,"nestedArray",{get:function(){return this._nestedArray||(this._nestedArray=nr.toArray(this.nested))}});J.prototype.toJSON=function(t){return nr.toObject(["options",this.options,"nested",qc(this.nestedArray,t)])};J.prototype.addJSON=function(t){var e=this;if(t)for(var i=Object.keys(t),r=0,o;r<i.length;++r)o=t[i[r]],e.add((o.fields!==void 0?En.fromJSON:o.values!==void 0?_n.fromJSON:o.methods!==void 0?ai.fromJSON:o.id!==void 0?Hc.fromJSON:J.fromJSON)(i[r],o));return this};J.prototype.get=function(t){return this.nested&&this.nested[t]||null};J.prototype.getEnum=function(t){if(this.nested&&this.nested[t]instanceof _n)return this.nested[t].values;throw Error("no such enum: "+t)};J.prototype.add=function(t){if(!(t instanceof Hc&&t.extend!==void 0||t instanceof En||t instanceof vh||t instanceof _n||t instanceof ai||t instanceof J))throw TypeError("object must be a valid nested object");if(!this.nested)this.nested={};else{var e=this.get(t.name);if(e)if(e instanceof J&&t instanceof J&&!(e instanceof En||e instanceof ai)){for(var i=e.nestedArray,r=0;r<i.length;++r)t.add(i[r]);this.remove(e),this.nested||(this.nested={}),t.setOptions(e.options,!0)}else throw Error("duplicate name '"+t.name+"' in "+this)}return this.nested[t.name]=t,t.onAdd(this),Wc(this)};J.prototype.remove=function(t){if(!(t instanceof Xo))throw TypeError("object must be a ReflectionObject");if(t.parent!==this)throw Error(t+" is not a member of "+this);return delete this.nested[t.name],Object.keys(this.nested).length||(this.nested=void 0),t.onRemove(this),Wc(this)};J.prototype.define=function(t,e){if(nr.isString(t))t=t.split(".");else if(!Array.isArray(t))throw TypeError("illegal path");if(t&&t.length&&t[0]==="")throw Error("path must be relative");for(var i=this;t.length>0;){var r=t.shift();if(i.nested&&i.nested[r]){if(i=i.nested[r],!(i instanceof J))throw Error("path conflicts with non-namespace objects")}else i.add(i=new J(r))}return e&&i.addJSON(e),i};J.prototype.resolveAll=function(){for(var t=this.nestedArray,e=0;e<t.length;)t[e]instanceof J?t[e++].resolveAll():t[e++].resolve();return this.resolve()};J.prototype.lookup=function(t,e,i){if(typeof e=="boolean"?(i=e,e=void 0):e&&!Array.isArray(e)&&(e=[e]),nr.isString(t)&&t.length){if(t===".")return this.root;t=t.split(".")}else if(!t.length)return this;if(t[0]==="")return this.root.lookup(t.slice(1),e);var r=this.get(t[0]);if(r){if(t.length===1){if(!e||e.indexOf(r.constructor)>-1)return r}else if(r instanceof J&&(r=r.lookup(t.slice(1),e,!0)))return r}else for(var o=0;o<this.nestedArray.length;++o)if(this._nestedArray[o]instanceof J&&(r=this._nestedArray[o].lookup(t,e,!0)))return r;return this.parent===null||i?null:this.parent.lookup(t,e)};J.prototype.lookupType=function(t){var e=this.lookup(t,[En]);if(!e)throw Error("no such type: "+t);return e};J.prototype.lookupEnum=function(t){var e=this.lookup(t,[_n]);if(!e)throw Error("no such Enum '"+t+"' in "+this);return e};J.prototype.lookupTypeOrEnum=function(t){var e=this.lookup(t,[En,_n]);if(!e)throw Error("no such Type or Enum '"+t+"' in "+this);return e};J.prototype.lookupService=function(t){var e=this.lookup(t,[ai]);if(!e)throw Error("no such Service '"+t+"' in "+this);return e};J._configure=function(n,t,e){En=n,ai=t,_n=e}});var ir=Y((aw,Yc)=>{"use strict";Yc.exports=dt;var Jo=Ct();((dt.prototype=Object.create(Jo.prototype)).constructor=dt).className="MapField";var Eh=en(),si=ye();function dt(n,t,e,i,r,o){if(Jo.call(this,n,t,i,void 0,void 0,r,o),!si.isString(e))throw TypeError("keyType must be a string");this.keyType=e,this.resolvedKeyType=null,this.map=!0}dt.fromJSON=function(t,e){return new dt(t,e.id,e.keyType,e.type,e.options,e.comment)};dt.prototype.toJSON=function(t){var e=t?Boolean(t.keepComments):!1;return si.toObject(["keyType",this.keyType,"type",this.type,"id",this.id,"extend",this.extend,"options",this.options,"comment",e?this.comment:void 0])};dt.prototype.resolve=function(){if(this.resolved)return this;if(Eh.mapKey[this.keyType]===void 0)throw Error("invalid key type: "+this.keyType);return Jo.prototype.resolve.call(this)};dt.d=function(t,e,i){return typeof i=="function"?i=si.decorateType(i).name:i&&typeof i=="object"&&(i=si.decorateEnum(i).name),function(o,a){si.decorateType(o.constructor).add(new dt(a,t,e,i))}}});var rr=Y((sw,zc)=>{"use strict";zc.exports=nn;var Qo=tn();((nn.prototype=Object.create(Qo.prototype)).constructor=nn).className="Method";var An=ye();function nn(n,t,e,i,r,o,a,s,l){if(An.isObject(r)?(a=r,r=o=void 0):An.isObject(o)&&(a=o,o=void 0),!(t===void 0||An.isString(t)))throw TypeError("type must be a string");if(!An.isString(e))throw TypeError("requestType must be a string");if(!An.isString(i))throw TypeError("responseType must be a string");Qo.call(this,n,a),this.type=t||"rpc",this.requestType=e,this.requestStream=r?!0:void 0,this.responseType=i,this.responseStream=o?!0:void 0,this.resolvedRequestType=null,this.resolvedResponseType=null,this.comment=s,this.parsedOptions=l}nn.fromJSON=function(t,e){return new nn(t,e.type,e.requestType,e.responseType,e.requestStream,e.responseStream,e.options,e.comment,e.parsedOptions)};nn.prototype.toJSON=function(t){var e=t?Boolean(t.keepComments):!1;return An.toObject(["type",this.type!=="rpc"&&this.type||void 0,"requestType",this.requestType,"requestStream",this.requestStream,"responseType",this.responseType,"responseStream",this.responseStream,"options",this.options,"comment",e?this.comment:void 0,"parsedOptions",this.parsedOptions])};nn.prototype.resolve=function(){return this.resolved?this:(this.resolvedRequestType=this.parent.lookupType(this.requestType),this.resolvedResponseType=this.parent.lookupType(this.responseType),Qo.prototype.resolve.call(this))}});var or=Y((lw,Vc)=>{"use strict";Vc.exports=je;var It=Tn();((je.prototype=Object.create(It.prototype)).constructor=je).className="Service";var ea=rr(),li=ye(),_h=Wo();function je(n,t){It.call(this,n,t),this.methods={},this._methodsArray=null}je.fromJSON=function(t,e){var i=new je(t,e.options);if(e.methods)for(var r=Object.keys(e.methods),o=0;o<r.length;++o)i.add(ea.fromJSON(r[o],e.methods[r[o]]));return e.nested&&i.addJSON(e.nested),i.comment=e.comment,i};je.prototype.toJSON=function(t){var e=It.prototype.toJSON.call(this,t),i=t?Boolean(t.keepComments):!1;return li.toObject(["options",e&&e.options||void 0,"methods",It.arrayToJSON(this.methodsArray,t)||{},"nested",e&&e.nested||void 0,"comment",i?this.comment:void 0])};Object.defineProperty(je.prototype,"methodsArray",{get:function(){return this._methodsArray||(this._methodsArray=li.toArray(this.methods))}});function Gc(n){return n._methodsArray=null,n}je.prototype.get=function(t){return this.methods[t]||It.prototype.get.call(this,t)};je.prototype.resolveAll=function(){for(var t=this.methodsArray,e=0;e<t.length;++e)t[e].resolve();return It.prototype.resolve.call(this)};je.prototype.add=function(t){if(this.get(t.name))throw Error("duplicate name '"+t.name+"' in "+this);return t instanceof ea?(this.methods[t.name]=t,t.parent=this,Gc(this)):It.prototype.add.call(this,t)};je.prototype.remove=function(t){if(t instanceof ea){if(this.methods[t.name]!==t)throw Error(t+" is not a member of "+this);return delete this.methods[t.name],t.parent=null,Gc(this)}return It.prototype.remove.call(this,t)};je.prototype.create=function(t,e,i){for(var r=new _h.Service(t,e,i),o=0,a;o<this.methodsArray.length;++o){var s=li.lcFirst((a=this._methodsArray[o]).resolve().name).replace(/[^$\w_]/g,"");r[s]=li.codegen(["r","c"],li.isReserved(s)?s+"_":s)("return this.rpcCall(m,q,s,r,c)")({m:a,q:a.resolvedRequestType.ctor,s:a.resolvedResponseType.ctor})}return r}});var ar=Y((cw,Kc)=>{"use strict";Kc.exports=nt;var Th=et();function nt(n){if(n)for(var t=Object.keys(n),e=0;e<t.length;++e)this[t[e]]=n[t[e]]}nt.create=function(t){return this.$type.create(t)};nt.encode=function(t,e){return this.$type.encode(t,e)};nt.encodeDelimited=function(t,e){return this.$type.encodeDelimited(t,e)};nt.decode=function(t){return this.$type.decode(t)};nt.decodeDelimited=function(t){return this.$type.decodeDelimited(t)};nt.verify=function(t){return this.$type.verify(t)};nt.fromObject=function(t){return this.$type.fromObject(t)};nt.toObject=function(t,e){return this.$type.toObject(t,e)};nt.prototype.toJSON=function(){return this.$type.toObject(this,Th.toJSONOptions)}});var ta=Y((pw,Jc)=>{"use strict";Jc.exports=Nh;var Ah=Ve(),mt=en(),Xc=ye();function kh(n){return"missing required '"+n.name+"'"}function Nh(n){var t=Xc.codegen(["r","l"],n.name+"$decode")("if(!(r instanceof Reader))")("r=Reader.create(r)")("var c=l===undefined?r.len:r.pos+l,m=new this.ctor"+(n.fieldsArray.filter(function(s){return s.map}).length?",k,value":""))("while(r.pos<c){")("var t=r.uint32()");n.group&&t("if((t&7)===4)")("break"),t("switch(t>>>3){");for(var e=0;e<n.fieldsArray.length;++e){var i=n._fieldsArray[e].resolve(),r=i.resolvedType instanceof Ah?"int32":i.type,o="m"+Xc.safeProp(i.name);t("case %i: {",i.id),i.map?(t("if(%s===util.emptyObject)",o)("%s={}",o)("var c2 = r.uint32()+r.pos"),mt.defaults[i.keyType]!==void 0?t("k=%j",mt.defaults[i.keyType]):t("k=null"),mt.defaults[r]!==void 0?t("value=%j",mt.defaults[r]):t("value=null"),t("while(r.pos<c2){")("var tag2=r.uint32()")("switch(tag2>>>3){")("case 1: k=r.%s(); break",i.keyType)("case 2:"),mt.basic[r]===void 0?t("value=types[%i].decode(r,r.uint32())",e):t("value=r.%s()",r),t("break")("default:")("r.skipType(tag2&7)")("break")("}")("}"),mt.long[i.keyType]!==void 0?t('%s[typeof k==="object"?util.longToHash(k):k]=value',o):t("%s[k]=value",o)):i.repeated?(t("if(!(%s&&%s.length))",o,o)("%s=[]",o),mt.packed[r]!==void 0&&t("if((t&7)===2){")("var c2=r.uint32()+r.pos")("while(r.pos<c2)")("%s.push(r.%s())",o,r)("}else"),mt.basic[r]===void 0?t(i.resolvedType.group?"%s.push(types[%i].decode(r))":"%s.push(types[%i].decode(r,r.uint32()))",o,e):t("%s.push(r.%s())",o,r)):mt.basic[r]===void 0?t(i.resolvedType.group?"%s=types[%i].decode(r)":"%s=types[%i].decode(r,r.uint32())",o,e):t("%s=r.%s()",o,r),t("break")("}")}for(t("default:")("r.skipType(t&7)")("break")("}")("}"),e=0;e<n._fieldsArray.length;++e){var a=n._fieldsArray[e];a.required&&t("if(!m.hasOwnProperty(%j))",a.name)("throw util.ProtocolError(%j,{instance:m})",kh(a))}return t("return m")}});var ra=Y((uw,Qc)=>{"use strict";Qc.exports=Oh;var Sh=Ve(),na=ye();function He(n,t){return n.name+": "+t+(n.repeated&&t!=="array"?"[]":n.map&&t!=="object"?"{k:"+n.keyType+"}":"")+" expected"}function ia(n,t,e,i){if(t.resolvedType)if(t.resolvedType instanceof Sh){n("switch(%s){",i)("default:")("return%j",He(t,"enum value"));for(var r=Object.keys(t.resolvedType.values),o=0;o<r.length;++o)n("case %i:",t.resolvedType.values[r[o]]);n("break")("}")}else n("{")("var e=types[%i].verify(%s);",e,i)("if(e)")("return%j+e",t.name+".")("}");else switch(t.type){case"int32":case"uint32":case"sint32":case"fixed32":case"sfixed32":n("if(!util.isInteger(%s))",i)("return%j",He(t,"integer"));break;case"int64":case"uint64":case"sint64":case"fixed64":case"sfixed64":n("if(!util.isInteger(%s)&&!(%s&&util.isInteger(%s.low)&&util.isInteger(%s.high)))",i,i,i,i)("return%j",He(t,"integer|Long"));break;case"float":case"double":n('if(typeof %s!=="number")',i)("return%j",He(t,"number"));break;case"bool":n('if(typeof %s!=="boolean")',i)("return%j",He(t,"boolean"));break;case"string":n("if(!util.isString(%s))",i)("return%j",He(t,"string"));break;case"bytes":n('if(!(%s&&typeof %s.length==="number"||util.isString(%s)))',i,i,i)("return%j",He(t,"buffer"));break}return n}function Rh(n,t,e){switch(t.keyType){case"int32":case"uint32":case"sint32":case"fixed32":case"sfixed32":n("if(!util.key32Re.test(%s))",e)("return%j",He(t,"integer key"));break;case"int64":case"uint64":case"sint64":case"fixed64":case"sfixed64":n("if(!util.key64Re.test(%s))",e)("return%j",He(t,"integer|Long key"));break;case"bool":n("if(!util.key2Re.test(%s))",e)("return%j",He(t,"boolean key"));break}return n}function Oh(n){var t=na.codegen(["m"],n.name+"$verify")('if(typeof m!=="object"||m===null)')("return%j","object expected"),e=n.oneofsArray,i={};e.length&&t("var p={}");for(var r=0;r<n.fieldsArray.length;++r){var o=n._fieldsArray[r].resolve(),a="m"+na.safeProp(o.name);if(o.optional&&t("if(%s!=null&&m.hasOwnProperty(%j)){",a,o.name),o.map)t("if(!util.isObject(%s))",a)("return%j",He(o,"object"))("var k=Object.keys(%s)",a)("for(var i=0;i<k.length;++i){"),Rh(t,o,"k[i]"),ia(t,o,r,a+"[k[i]]")("}");else if(o.repeated)t("if(!Array.isArray(%s))",a)("return%j",He(o,"array"))("for(var i=0;i<%s.length;++i){",a),ia(t,o,r,a+"[i]")("}");else{if(o.partOf){var s=na.safeProp(o.partOf.name);i[o.partOf.name]===1&&t("if(p%s===1)",s)("return%j",o.partOf.name+": multiple values"),i[o.partOf.name]=1,t("p%s=1",s)}ia(t,o,r,a)}o.optional&&t("}")}return t("return null")}});var sa=Y(tp=>{"use strict";var ep=tp,ci=Ve(),it=ye();function oa(n,t,e,i){var r=!1;if(t.resolvedType)if(t.resolvedType instanceof ci){n("switch(d%s){",i);for(var o=t.resolvedType.values,a=Object.keys(o),s=0;s<a.length;++s)o[a[s]]===t.typeDefault&&!r&&(n("default:")('if(typeof(d%s)==="number"){m%s=d%s;break}',i,i,i),t.repeated||n("break"),r=!0),n("case%j:",a[s])("case %i:",o[a[s]])("m%s=%j",i,o[a[s]])("break");n("}")}else n('if(typeof d%s!=="object")',i)("throw TypeError(%j)",t.fullName+": object expected")("m%s=types[%i].fromObject(d%s)",i,e,i);else{var l=!1;switch(t.type){case"double":case"float":n("m%s=Number(d%s)",i,i);break;case"uint32":case"fixed32":n("m%s=d%s>>>0",i,i);break;case"int32":case"sint32":case"sfixed32":n("m%s=d%s|0",i,i);break;case"uint64":l=!0;case"int64":case"sint64":case"fixed64":case"sfixed64":n("if(util.Long)")("(m%s=util.Long.fromValue(d%s)).unsigned=%j",i,i,l)('else if(typeof d%s==="string")',i)("m%s=parseInt(d%s,10)",i,i)('else if(typeof d%s==="number")',i)("m%s=d%s",i,i)('else if(typeof d%s==="object")',i)("m%s=new util.LongBits(d%s.low>>>0,d%s.high>>>0).toNumber(%s)",i,i,i,l?"true":"");break;case"bytes":n('if(typeof d%s==="string")',i)("util.base64.decode(d%s,m%s=util.newBuffer(util.base64.length(d%s)),0)",i,i,i)("else if(d%s.length >= 0)",i)("m%s=d%s",i,i);break;case"string":n("m%s=String(d%s)",i,i);break;case"bool":n("m%s=Boolean(d%s)",i,i);break}}return n}ep.fromObject=function(t){var e=t.fieldsArray,i=it.codegen(["d"],t.name+"$fromObject")("if(d instanceof this.ctor)")("return d");if(!e.length)return i("return new this.ctor");i("var m=new this.ctor");for(var r=0;r<e.length;++r){var o=e[r].resolve(),a=it.safeProp(o.name);o.map?(i("if(d%s){",a)('if(typeof d%s!=="object")',a)("throw TypeError(%j)",o.fullName+": object expected")("m%s={}",a)("for(var ks=Object.keys(d%s),i=0;i<ks.length;++i){",a),oa(i,o,r,a+"[ks[i]]")("}")("}")):o.repeated?(i("if(d%s){",a)("if(!Array.isArray(d%s))",a)("throw TypeError(%j)",o.fullName+": array expected")("m%s=[]",a)("for(var i=0;i<d%s.length;++i){",a),oa(i,o,r,a+"[i]")("}")("}")):(o.resolvedType instanceof ci||i("if(d%s!=null){",a),oa(i,o,r,a),o.resolvedType instanceof ci||i("}"))}return i("return m")};function aa(n,t,e,i){if(t.resolvedType)t.resolvedType instanceof ci?n("d%s=o.enums===String?(types[%i].values[m%s]===undefined?m%s:types[%i].values[m%s]):m%s",i,e,i,i,e,i,i):n("d%s=types[%i].toObject(m%s,o)",i,e,i);else{var r=!1;switch(t.type){case"double":case"float":n("d%s=o.json&&!isFinite(m%s)?String(m%s):m%s",i,i,i,i);break;case"uint64":r=!0;case"int64":case"sint64":case"fixed64":case"sfixed64":n('if(typeof m%s==="number")',i)("d%s=o.longs===String?String(m%s):m%s",i,i,i)("else")("d%s=o.longs===String?util.Long.prototype.toString.call(m%s):o.longs===Number?new util.LongBits(m%s.low>>>0,m%s.high>>>0).toNumber(%s):m%s",i,i,i,i,r?"true":"",i);break;case"bytes":n("d%s=o.bytes===String?util.base64.encode(m%s,0,m%s.length):o.bytes===Array?Array.prototype.slice.call(m%s):m%s",i,i,i,i,i);break;default:n("d%s=m%s",i,i);break}}return n}ep.toObject=function(t){var e=t.fieldsArray.slice().sort(it.compareFieldsById);if(!e.length)return it.codegen()("return {}");for(var i=it.codegen(["m","o"],t.name+"$toObject")("if(!o)")("o={}")("var d={}"),r=[],o=[],a=[],s=0;s<e.length;++s)e[s].partOf||(e[s].resolve().repeated?r:e[s].map?o:a).push(e[s]);if(r.length){for(i("if(o.arrays||o.defaults){"),s=0;s<r.length;++s)i("d%s=[]",it.safeProp(r[s].name));i("}")}if(o.length){for(i("if(o.objects||o.defaults){"),s=0;s<o.length;++s)i("d%s={}",it.safeProp(o[s].name));i("}")}if(a.length){for(i("if(o.defaults){"),s=0;s<a.length;++s){var l=a[s],c=it.safeProp(l.name);if(l.resolvedType instanceof ci)i("d%s=o.enums===String?%j:%j",c,l.resolvedType.valuesById[l.typeDefault],l.typeDefault);else if(l.long)i("if(util.Long){")("var n=new util.Long(%i,%i,%j)",l.typeDefault.low,l.typeDefault.high,l.typeDefault.unsigned)("d%s=o.longs===String?n.toString():o.longs===Number?n.toNumber():n",c)("}else")("d%s=o.longs===String?%j:%i",c,l.typeDefault.toString(),l.typeDefault.toNumber());else if(l.bytes){var p="["+Array.prototype.slice.call(l.typeDefault).join(",")+"]";i("if(o.bytes===String)d%s=%j",c,String.fromCharCode.apply(String,l.typeDefault))("else{")("d%s=%s",c,p)("if(o.bytes!==Array)d%s=util.newBuffer(d%s)",c,c)("}")}else i("d%s=%j",c,l.typeDefault)}i("}")}var m=!1;for(s=0;s<e.length;++s){var l=e[s],y=t._fieldsArray.indexOf(l),c=it.safeProp(l.name);l.map?(m||(m=!0,i("var ks2")),i("if(m%s&&(ks2=Object.keys(m%s)).length){",c,c)("d%s={}",c)("for(var j=0;j<ks2.length;++j){"),aa(i,l,y,c+"[ks2[j]]")("}")):l.repeated?(i("if(m%s&&m%s.length){",c,c)("d%s=[]",c)("for(var j=0;j<m%s.length;++j){",c),aa(i,l,y,c+"[j]")("}")):(i("if(m%s!=null&&m.hasOwnProperty(%j)){",c,l.name),aa(i,l,y,c),l.partOf&&i("if(o.oneofs)")("d%s=%j",it.safeProp(l.partOf.name),l.name)),i("}")}return i("return d")}});var la=Y(np=>{"use strict";var Dh=np,Ch=ar();Dh[".google.protobuf.Any"]={fromObject:function(n){if(n&&n["@type"]){var t=n["@type"].substring(n["@type"].lastIndexOf("/")+1),e=this.lookup(t);if(e){var i=n["@type"].charAt(0)==="."?n["@type"].slice(1):n["@type"];return i.indexOf("/")===-1&&(i="/"+i),this.create({type_url:i,value:e.encode(e.fromObject(n)).finish()})}}return this.fromObject(n)},toObject:function(n,t){var e="type.googleapis.com/",i="",r="";if(t&&t.json&&n.type_url&&n.value){r=n.type_url.substring(n.type_url.lastIndexOf("/")+1),i=n.type_url.substring(0,n.type_url.lastIndexOf("/")+1);var o=this.lookup(r);o&&(n=o.decode(n.value))}if(!(n instanceof this.ctor)&&n instanceof Ch){var a=n.$type.toObject(n,t),s=n.$type.fullName[0]==="."?n.$type.fullName.slice(1):n.$type.fullName;return i===""&&(i=e),r=i+s,a["@type"]=r,a}return this.toObject(n,t)}}});var cr=Y((mw,rp)=>{"use strict";rp.exports=ne;var Ke=Tn();((ne.prototype=Object.create(Ke.prototype)).constructor=ne).className="Type";var Ih=Ve(),ua=vn(),sr=Ct(),Fh=ir(),Lh=or(),ca=ar(),pa=Ji(),Ph=Ki(),ve=ye(),Mh=fa(),$h=ta(),Bh=ra(),ip=sa(),Uh=la();function ne(n,t){Ke.call(this,n,t),this.fields={},this.oneofs=void 0,this.extensions=void 0,this.reserved=void 0,this.group=void 0,this._fieldsById=null,this._fieldsArray=null,this._oneofsArray=null,this._ctor=null}Object.defineProperties(ne.prototype,{fieldsById:{get:function(){if(this._fieldsById)return this._fieldsById;this._fieldsById={};for(var n=Object.keys(this.fields),t=0;t<n.length;++t){var e=this.fields[n[t]],i=e.id;if(this._fieldsById[i])throw Error("duplicate id "+i+" in "+this);this._fieldsById[i]=e}return this._fieldsById}},fieldsArray:{get:function(){return this._fieldsArray||(this._fieldsArray=ve.toArray(this.fields))}},oneofsArray:{get:function(){return this._oneofsArray||(this._oneofsArray=ve.toArray(this.oneofs))}},ctor:{get:function(){return this._ctor||(this.ctor=ne.generateConstructor(this)())},set:function(n){var t=n.prototype;t instanceof ca||((n.prototype=new ca).constructor=n,ve.merge(n.prototype,t)),n.$type=n.prototype.$type=this,ve.merge(n,ca,!0),this._ctor=n;for(var e=0;e<this.fieldsArray.length;++e)this._fieldsArray[e].resolve();var i={};for(e=0;e<this.oneofsArray.length;++e)i[this._oneofsArray[e].resolve().name]={get:ve.oneOfGetter(this._oneofsArray[e].oneof),set:ve.oneOfSetter(this._oneofsArray[e].oneof)};e&&Object.defineProperties(n.prototype,i)}}});ne.generateConstructor=function(t){for(var e=ve.codegen(["p"],t.name),i=0,r;i<t.fieldsArray.length;++i)(r=t._fieldsArray[i]).map?e("this%s={}",ve.safeProp(r.name)):r.repeated&&e("this%s=[]",ve.safeProp(r.name));return e("if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)")("this[ks[i]]=p[ks[i]]")};function lr(n){return n._fieldsById=n._fieldsArray=n._oneofsArray=null,delete n.encode,delete n.decode,delete n.verify,n}ne.fromJSON=function(t,e){var i=new ne(t,e.options);i.extensions=e.extensions,i.reserved=e.reserved;for(var r=Object.keys(e.fields),o=0;o<r.length;++o)i.add((typeof e.fields[r[o]].keyType!="undefined"?Fh.fromJSON:sr.fromJSON)(r[o],e.fields[r[o]]));if(e.oneofs)for(r=Object.keys(e.oneofs),o=0;o<r.length;++o)i.add(ua.fromJSON(r[o],e.oneofs[r[o]]));if(e.nested)for(r=Object.keys(e.nested),o=0;o<r.length;++o){var a=e.nested[r[o]];i.add((a.id!==void 0?sr.fromJSON:a.fields!==void 0?ne.fromJSON:a.values!==void 0?Ih.fromJSON:a.methods!==void 0?Lh.fromJSON:Ke.fromJSON)(r[o],a))}return e.extensions&&e.extensions.length&&(i.extensions=e.extensions),e.reserved&&e.reserved.length&&(i.reserved=e.reserved),e.group&&(i.group=!0),e.comment&&(i.comment=e.comment),i};ne.prototype.toJSON=function(t){var e=Ke.prototype.toJSON.call(this,t),i=t?Boolean(t.keepComments):!1;return ve.toObject(["options",e&&e.options||void 0,"oneofs",Ke.arrayToJSON(this.oneofsArray,t),"fields",Ke.arrayToJSON(this.fieldsArray.filter(function(r){return!r.declaringField}),t)||{},"extensions",this.extensions&&this.extensions.length?this.extensions:void 0,"reserved",this.reserved&&this.reserved.length?this.reserved:void 0,"group",this.group||void 0,"nested",e&&e.nested||void 0,"comment",i?this.comment:void 0])};ne.prototype.resolveAll=function(){for(var t=this.fieldsArray,e=0;e<t.length;)t[e++].resolve();var i=this.oneofsArray;for(e=0;e<i.length;)i[e++].resolve();return Ke.prototype.resolveAll.call(this)};ne.prototype.get=function(t){return this.fields[t]||this.oneofs&&this.oneofs[t]||this.nested&&this.nested[t]||null};ne.prototype.add=function(t){if(this.get(t.name))throw Error("duplicate name '"+t.name+"' in "+this);if(t instanceof sr&&t.extend===void 0){if(this._fieldsById?this._fieldsById[t.id]:this.fieldsById[t.id])throw Error("duplicate id "+t.id+" in "+this);if(this.isReservedId(t.id))throw Error("id "+t.id+" is reserved in "+this);if(this.isReservedName(t.name))throw Error("name '"+t.name+"' is reserved in "+this);return t.parent&&t.parent.remove(t),this.fields[t.name]=t,t.message=this,t.onAdd(this),lr(this)}return t instanceof ua?(this.oneofs||(this.oneofs={}),this.oneofs[t.name]=t,t.onAdd(this),lr(this)):Ke.prototype.add.call(this,t)};ne.prototype.remove=function(t){if(t instanceof sr&&t.extend===void 0){if(!this.fields||this.fields[t.name]!==t)throw Error(t+" is not a member of "+this);return delete this.fields[t.name],t.parent=null,t.onRemove(this),lr(this)}if(t instanceof ua){if(!this.oneofs||this.oneofs[t.name]!==t)throw Error(t+" is not a member of "+this);return delete this.oneofs[t.name],t.parent=null,t.onRemove(this),lr(this)}return Ke.prototype.remove.call(this,t)};ne.prototype.isReservedId=function(t){return Ke.isReservedId(this.reserved,t)};ne.prototype.isReservedName=function(t){return Ke.isReservedName(this.reserved,t)};ne.prototype.create=function(t){return new this.ctor(t)};ne.prototype.setup=function(){for(var t=this.fullName,e=[],i=0;i<this.fieldsArray.length;++i)e.push(this._fieldsArray[i].resolve().resolvedType);this.encode=Mh(this)({Writer:Ph,types:e,util:ve}),this.decode=$h(this)({Reader:pa,types:e,util:ve}),this.verify=Bh(this)({types:e,util:ve}),this.fromObject=ip.fromObject(this)({types:e,util:ve}),this.toObject=ip.toObject(this)({types:e,util:ve});var r=Uh[t];if(r){var o=Object.create(this);o.fromObject=this.fromObject,this.fromObject=r.fromObject.bind(o),o.toObject=this.toObject,this.toObject=r.toObject.bind(o)}return this};ne.prototype.encode=function(t,e){return this.setup().encode(t,e)};ne.prototype.encodeDelimited=function(t,e){return this.encode(t,e&&e.len?e.fork():e).ldelim()};ne.prototype.decode=function(t,e){return this.setup().decode(t,e)};ne.prototype.decodeDelimited=function(t){return t instanceof pa||(t=pa.create(t)),this.decode(t,t.uint32())};ne.prototype.verify=function(t){return this.setup().verify(t)};ne.prototype.fromObject=function(t){return this.setup().fromObject(t)};ne.prototype.toObject=function(t,e){return this.setup().toObject(t,e)};ne.d=function(t){return function(i){ve.decorateType(i,t)}}});var fr=Y((hw,cp)=>{"use strict";cp.exports=Ce;var ur=Tn();((Ce.prototype=Object.create(ur.prototype)).constructor=Ce).className="Root";var ma=Ct(),ap=Ve(),jh=vn(),Ft=ye(),sp,da,pi;function Ce(n){ur.call(this,"",n),this.deferred=[],this.files=[]}Ce.fromJSON=function(t,e){return e||(e=new Ce),t.options&&e.setOptions(t.options),e.addJSON(t.nested)};Ce.prototype.resolvePath=Ft.path.resolve;Ce.prototype.fetch=Ft.fetch;function lp(){}Ce.prototype.load=function n(t,e,i){typeof e=="function"&&(i=e,e=void 0);var r=this;if(!i)return Ft.asPromise(n,r,t,e);var o=i===lp;function a(w,E){if(i){var d=i;if(i=null,o)throw w;d(w,E)}}function s(w){var E=w.lastIndexOf("google/protobuf/");if(E>-1){var d=w.substring(E);if(d in pi)return d}return null}function l(w,E){try{if(Ft.isString(E)&&E.charAt(0)==="{"&&(E=JSON.parse(E)),!Ft.isString(E))r.setOptions(E.options).addJSON(E.nested);else{da.filename=w;var d=da(E,r,e),f,g=0;if(d.imports)for(;g<d.imports.length;++g)(f=s(d.imports[g])||r.resolvePath(w,d.imports[g]))&&c(f);if(d.weakImports)for(g=0;g<d.weakImports.length;++g)(f=s(d.weakImports[g])||r.resolvePath(w,d.weakImports[g]))&&c(f,!0)}}catch(v){a(v)}!o&&!p&&a(null,r)}function c(w,E){if(w=s(w)||w,!(r.files.indexOf(w)>-1)){if(r.files.push(w),w in pi){o?l(w,pi[w]):(++p,setTimeout(function(){--p,l(w,pi[w])}));return}if(o){var d;try{d=Ft.fs.readFileSync(w).toString("utf8")}catch(f){E||a(f);return}l(w,d)}else++p,r.fetch(w,function(f,g){if(--p,!!i){if(f){E?p||a(null,r):a(f);return}l(w,g)}})}}var p=0;Ft.isString(t)&&(t=[t]);for(var m=0,y;m<t.length;++m)(y=r.resolvePath("",t[m]))&&c(y);if(o)return r;p||a(null,r)};Ce.prototype.loadSync=function(t,e){if(!Ft.isNode)throw Error("not supported");return this.load(t,e,lp)};Ce.prototype.resolveAll=function(){if(this.deferred.length)throw Error("unresolvable extensions: "+this.deferred.map(function(t){return"'extend "+t.extend+"' in "+t.parent.fullName}).join(", "));return ur.prototype.resolveAll.call(this)};var pr=/^[A-Z]/;function op(n,t){var e=t.parent.lookup(t.extend);if(e){var i=new ma(t.fullName,t.id,t.type,t.rule,void 0,t.options);return e.get(i.name)||(i.declaringField=t,t.extensionField=i,e.add(i)),!0}return!1}Ce.prototype._handleAdd=function(t){if(t instanceof ma)t.extend!==void 0&&!t.extensionField&&(op(this,t)||this.deferred.push(t));else if(t instanceof ap)pr.test(t.name)&&(t.parent[t.name]=t.values);else if(!(t instanceof jh)){if(t instanceof sp)for(var e=0;e<this.deferred.length;)op(this,this.deferred[e])?this.deferred.splice(e,1):++e;for(var i=0;i<t.nestedArray.length;++i)this._handleAdd(t._nestedArray[i]);pr.test(t.name)&&(t.parent[t.name]=t)}};Ce.prototype._handleRemove=function(t){if(t instanceof ma){if(t.extend!==void 0)if(t.extensionField)t.extensionField.parent.remove(t.extensionField),t.extensionField=null;else{var e=this.deferred.indexOf(t);e>-1&&this.deferred.splice(e,1)}}else if(t instanceof ap)pr.test(t.name)&&delete t.parent[t.name];else if(t instanceof ur){for(var i=0;i<t.nestedArray.length;++i)this._handleRemove(t._nestedArray[i]);pr.test(t.name)&&delete t.parent[t.name]}};Ce._configure=function(n,t,e){sp=n,da=t,pi=e}});var ye=Y((gw,up)=>{"use strict";var le=up.exports=et(),pp=Zo(),ha,ga;le.codegen=Rc();le.fetch=Dc();le.path=Fc();le.fs=le.inquire("fs");le.toArray=function(t){if(t){for(var e=Object.keys(t),i=new Array(e.length),r=0;r<e.length;)i[r]=t[e[r++]];return i}return[]};le.toObject=function(t){for(var e={},i=0;i<t.length;){var r=t[i++],o=t[i++];o!==void 0&&(e[r]=o)}return e};var Hh=/\\/g,qh=/"/g;le.isReserved=function(t){return/^(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$/.test(t)};le.safeProp=function(t){return!/^[$\w_]+$/.test(t)||le.isReserved(t)?'["'+t.replace(Hh,"\\\\").replace(qh,'\\"')+'"]':"."+t};le.ucFirst=function(t){return t.charAt(0).toUpperCase()+t.substring(1)};var Wh=/_([a-z])/g;le.camelCase=function(t){return t.substring(0,1)+t.substring(1).replace(Wh,function(e,i){return i.toUpperCase()})};le.compareFieldsById=function(t,e){return t.id-e.id};le.decorateType=function(t,e){if(t.$type)return e&&t.$type.name!==e&&(le.decorateRoot.remove(t.$type),t.$type.name=e,le.decorateRoot.add(t.$type)),t.$type;ha||(ha=cr());var i=new ha(e||t.name);return le.decorateRoot.add(i),i.ctor=t,Object.defineProperty(t,"$type",{value:i,enumerable:!1}),Object.defineProperty(t.prototype,"$type",{value:i,enumerable:!1}),i};var Zh=0;le.decorateEnum=function(t){if(t.$type)return t.$type;ga||(ga=Ve());var e=new ga("Enum"+Zh++,t);return le.decorateRoot.add(e),Object.defineProperty(t,"$type",{value:e,enumerable:!1}),e};le.setProperty=function(t,e,i){function r(o,a,s){var l=a.shift();if(l==="__proto__"||l==="prototype")return o;if(a.length>0)o[l]=r(o[l]||{},a,s);else{var c=o[l];c&&(s=[].concat(c).concat(s)),o[l]=s}return o}if(typeof t!="object")throw TypeError("dst must be an object");if(!e)throw TypeError("path must be specified");return e=e.split("."),r(t,e,i)};Object.defineProperty(le,"decorateRoot",{get:function(){return pp.decorated||(pp.decorated=new(fr()))}})});var tn=Y((yw,fp)=>{"use strict";fp.exports=Ie;Ie.className="ReflectionObject";var dr=ye(),mr;function Ie(n,t){if(!dr.isString(n))throw TypeError("name must be a string");if(t&&!dr.isObject(t))throw TypeError("options must be an object");this.options=t,this.parsedOptions=null,this.name=n,this.parent=null,this.resolved=!1,this.comment=null,this.filename=null}Object.defineProperties(Ie.prototype,{root:{get:function(){for(var n=this;n.parent!==null;)n=n.parent;return n}},fullName:{get:function(){for(var n=[this.name],t=this.parent;t;)n.unshift(t.name),t=t.parent;return n.join(".")}}});Ie.prototype.toJSON=function(){throw Error()};Ie.prototype.onAdd=function(t){this.parent&&this.parent!==t&&this.parent.remove(this),this.parent=t,this.resolved=!1;var e=t.root;e instanceof mr&&e._handleAdd(this)};Ie.prototype.onRemove=function(t){var e=t.root;e instanceof mr&&e._handleRemove(this),this.parent=null,this.resolved=!1};Ie.prototype.resolve=function(){return this.resolved?this:(this.root instanceof mr&&(this.resolved=!0),this)};Ie.prototype.getOption=function(t){if(this.options)return this.options[t]};Ie.prototype.setOption=function(t,e,i){return(!i||!this.options||this.options[t]===void 0)&&((this.options||(this.options={}))[t]=e),this};Ie.prototype.setParsedOption=function(t,e,i){this.parsedOptions||(this.parsedOptions=[]);var r=this.parsedOptions;if(i){var o=r.find(function(l){return Object.prototype.hasOwnProperty.call(l,t)});if(o){var a=o[t];dr.setProperty(a,i,e)}else o={},o[t]=dr.setProperty({},i,e),r.push(o)}else{var s={};s[t]=e,r.push(s)}return this};Ie.prototype.setOptions=function(t,e){if(t)for(var i=Object.keys(t),r=0;r<i.length;++r)this.setOption(i[r],t[i[r]],e);return this};Ie.prototype.toString=function(){var t=this.constructor.className,e=this.fullName;return e.length?t+" "+e:t};Ie._configure=function(n){mr=n}});var Ve=Y((bw,hp)=>{"use strict";hp.exports=rt;var dp=tn();((rt.prototype=Object.create(dp.prototype)).constructor=rt).className="Enum";var mp=Tn(),hr=ye();function rt(n,t,e,i,r,o){if(dp.call(this,n,e),t&&typeof t!="object")throw TypeError("values must be an object");if(this.valuesById={},this.values=Object.create(this.valuesById),this.comment=i,this.comments=r||{},this.valuesOptions=o,this.reserved=void 0,t)for(var a=Object.keys(t),s=0;s<a.length;++s)typeof t[a[s]]=="number"&&(this.valuesById[this.values[a[s]]=t[a[s]]]=a[s])}rt.fromJSON=function(t,e){var i=new rt(t,e.values,e.options,e.comment,e.comments);return i.reserved=e.reserved,i};rt.prototype.toJSON=function(t){var e=t?Boolean(t.keepComments):!1;return hr.toObject(["options",this.options,"valuesOptions",this.valuesOptions,"values",this.values,"reserved",this.reserved&&this.reserved.length?this.reserved:void 0,"comment",e?this.comment:void 0,"comments",e?this.comments:void 0])};rt.prototype.add=function(t,e,i,r){if(!hr.isString(t))throw TypeError("name must be a string");if(!hr.isInteger(e))throw TypeError("id must be an integer");if(this.values[t]!==void 0)throw Error("duplicate name '"+t+"' in "+this);if(this.isReservedId(e))throw Error("id "+e+" is reserved in "+this);if(this.isReservedName(t))throw Error("name '"+t+"' is reserved in "+this);if(this.valuesById[e]!==void 0){if(!(this.options&&this.options.allow_alias))throw Error("duplicate id "+e+" in "+this);this.values[t]=e}else this.valuesById[this.values[t]=e]=t;return r&&(this.valuesOptions===void 0&&(this.valuesOptions={}),this.valuesOptions[t]=r||null),this.comments[t]=i||null,this};rt.prototype.remove=function(t){if(!hr.isString(t))throw TypeError("name must be a string");var e=this.values[t];if(e==null)throw Error("name '"+t+"' does not exist in "+this);return delete this.valuesById[e],delete this.values[t],delete this.comments[t],this.valuesOptions&&delete this.valuesOptions[t],this};rt.prototype.isReservedId=function(t){return mp.isReservedId(this.reserved,t)};rt.prototype.isReservedName=function(t){return mp.isReservedName(this.reserved,t)}});var fa=Y((xw,yp)=>{"use strict";yp.exports=zh;var Yh=Ve(),ya=en(),ba=ye();function gp(n,t,e,i){return t.resolvedType.group?n("types[%i].encode(%s,w.uint32(%i)).uint32(%i)",e,i,(t.id<<3|3)>>>0,(t.id<<3|4)>>>0):n("types[%i].encode(%s,w.uint32(%i).fork()).ldelim()",e,i,(t.id<<3|2)>>>0)}function zh(n){for(var t=ba.codegen(["m","w"],n.name+"$encode")("if(!w)")("w=Writer.create()"),e,i,r=n.fieldsArray.slice().sort(ba.compareFieldsById),e=0;e<r.length;++e){var o=r[e].resolve(),a=n._fieldsArray.indexOf(o),s=o.resolvedType instanceof Yh?"int32":o.type,l=ya.basic[s];i="m"+ba.safeProp(o.name),o.map?(t("if(%s!=null&&Object.hasOwnProperty.call(m,%j)){",i,o.name)("for(var ks=Object.keys(%s),i=0;i<ks.length;++i){",i)("w.uint32(%i).fork().uint32(%i).%s(ks[i])",(o.id<<3|2)>>>0,8|ya.mapKey[o.keyType],o.keyType),l===void 0?t("types[%i].encode(%s[ks[i]],w.uint32(18).fork()).ldelim().ldelim()",a,i):t(".uint32(%i).%s(%s[ks[i]]).ldelim()",16|l,s,i),t("}")("}")):o.repeated?(t("if(%s!=null&&%s.length){",i,i),o.packed&&ya.packed[s]!==void 0?t("w.uint32(%i).fork()",(o.id<<3|2)>>>0)("for(var i=0;i<%s.length;++i)",i)("w.%s(%s[i])",s,i)("w.ldelim()"):(t("for(var i=0;i<%s.length;++i)",i),l===void 0?gp(t,o,a,i+"[i]"):t("w.uint32(%i).%s(%s[i])",(o.id<<3|l)>>>0,s,i)),t("}")):(o.optional&&t("if(%s!=null&&Object.hasOwnProperty.call(m,%j))",i,o.name),l===void 0?gp(t,o,a,i):t("w.uint32(%i).%s(%s)",(o.id<<3|l)>>>0,s,i))}return t("return w")}});var xp=Y((ww,bp)=>{"use strict";var V=bp.exports=Nc();V.build="light";function Gh(n,t,e){return typeof t=="function"?(e=t,t=new V.Root):t||(t=new V.Root),t.load(n,e)}V.load=Gh;function Vh(n,t){return t||(t=new V.Root),t.loadSync(n)}V.loadSync=Vh;V.encoder=fa();V.decoder=ta();V.verifier=ra();V.converter=sa();V.ReflectionObject=tn();V.Namespace=Tn();V.Root=fr();V.Enum=Ve();V.Type=cr();V.Field=Ct();V.OneOf=vn();V.MapField=ir();V.Service=or();V.Method=rr();V.Message=ar();V.wrappers=la();V.types=en();V.util=ye();V.ReflectionObject._configure(V.Root);V.Namespace._configure(V.Type,V.Service,V.Enum);V.Root._configure(V.Type);V.Field._configure(V.Type)});var wa=Y((vw,Ep)=>{"use strict";Ep.exports=vp;var xa=/[\s{}=;:[\],'"()<>]/g,Kh=/(?:"([^"\\]*(?:\\.[^"\\]*)*)")/g,Xh=/(?:'([^'\\]*(?:\\.[^'\\]*)*)')/g,Jh=/^ *[*/]+ */,Qh=/^\s*\*?\/*/,eg=/\n/g,tg=/\s/,ng=/\\(.?)/g,ig={0:"\0",r:"\r",n:`
`,t:"	"};function wp(n){return n.replace(ng,function(t,e){switch(e){case"\\":case"":return e;default:return ig[e]||""}})}vp.unescape=wp;function vp(n,t){n=n.toString();var e=0,i=n.length,r=1,o=0,a={},s=[],l=null;function c(A){return Error("illegal "+A+" (line "+r+")")}function p(){var A=l==="'"?Xh:Kh;A.lastIndex=e-1;var D=A.exec(n);if(!D)throw c("string");return e=A.lastIndex,f(l),l=null,wp(D[1])}function m(A){return n.charAt(A)}function y(A,D,T){var k={type:n.charAt(A++),lineEmpty:!1,leading:T},b;t?b=2:b=3;var C=A-b,O;do if(--C<0||(O=n.charAt(C))===`
`){k.lineEmpty=!0;break}while(O===" "||O==="	");for(var S=n.substring(A,D).split(eg),M=0;M<S.length;++M)S[M]=S[M].replace(t?Qh:Jh,"").trim();k.text=S.join(`
`).trim(),a[r]=k,o=r}function w(A){var D=E(A),T=n.substring(A,D),k=/^\s*\/\//.test(T);return k}function E(A){for(var D=A;D<i&&m(D)!==`
`;)D++;return D}function d(){if(s.length>0)return s.shift();if(l)return p();var A,D,T,k,b,C=e===0;do{if(e===i)return null;for(A=!1;tg.test(T=m(e));)if(T===`
`&&(C=!0,++r),++e===i)return null;if(m(e)==="/"){if(++e===i)throw c("comment");if(m(e)==="/")if(t){if(k=e,b=!1,w(e-1)){b=!0;do if(e=E(e),e===i||(e++,!C))break;while(w(e))}else e=Math.min(i,E(e)+1);b&&(y(k,e,C),C=!0),r++,A=!0}else{for(b=m(k=e+1)==="/";m(++e)!==`
`;)if(e===i)return null;++e,b&&(y(k,e-1,C),C=!0),++r,A=!0}else if((T=m(e))==="*"){k=e+1,b=t||m(k)==="*";do{if(T===`
`&&++r,++e===i)throw c("comment");D=T,T=m(e)}while(D!=="*"||T!=="/");++e,b&&(y(k,e-2,C),C=!0),A=!0}else return"/"}}while(A);var O=e;xa.lastIndex=0;var S=xa.test(m(O++));if(!S)for(;O<i&&!xa.test(m(O));)++O;var M=n.substring(e,e=O);return(M==='"'||M==="'")&&(l=M),M}function f(A){s.push(A)}function g(){if(!s.length){var A=d();if(A===null)return null;f(A)}return s[0]}function v(A,D){var T=g(),k=T===A;if(k)return d(),!0;if(!D)throw c("token '"+T+"', '"+A+"' expected");return!1}function _(A){var D=null,T;return A===void 0?(T=a[r-1],delete a[r-1],T&&(t||T.type==="*"||T.lineEmpty)&&(D=T.leading?T.text:null)):(o<A&&g(),T=a[A],delete a[A],T&&!T.lineEmpty&&(t||T.type==="/")&&(D=T.leading?null:T.text)),D}return Object.defineProperty({next:d,peek:g,push:f,skip:v,cmnt:_},"line",{get:function(){return r}})}});var Sp=Y((Ew,Np)=>{"use strict";Np.exports=ht;ht.filename=null;ht.defaults={keepCase:!1};var rg=wa(),_p=fr(),Tp=cr(),Ap=Ct(),og=ir(),kp=vn(),ag=Ve(),sg=or(),lg=rr(),va=en(),Ea=ye(),cg=/^[1-9][0-9]*$/,pg=/^-?[1-9][0-9]*$/,ug=/^0[x][0-9a-fA-F]+$/,fg=/^-?0[x][0-9a-fA-F]+$/,dg=/^0[0-7]+$/,mg=/^-?0[0-7]+$/,hg=/^(?![eE])[0-9]*(?:\.[0-9]*)?(?:[eE][+-]?[0-9]+)?$/,ot=/^[a-zA-Z_][a-zA-Z_0-9]*$/,at=/^(?:\.?[a-zA-Z_][a-zA-Z_0-9]*)(?:\.[a-zA-Z_][a-zA-Z_0-9]*)*$/,gg=/^(?:\.[a-zA-Z_][a-zA-Z_0-9]*)+$/;function ht(n,t,e){t instanceof _p||(e=t,t=new _p),e||(e=ht.defaults);var i=e.preferTrailingComment||!1,r=rg(n,e.alternateCommentMode||!1),o=r.next,a=r.push,s=r.peek,l=r.skip,c=r.cmnt,p=!0,m,y,w,E,d=!1,f=t,g=e.keepCase?function(x){return x}:Ea.camelCase;function v(x,N,I){var P=ht.filename;return I||(ht.filename=null),Error("illegal "+(N||"token")+" '"+x+"' ("+(P?P+", ":"")+"line "+r.line+")")}function _(){var x=[],N;do{if((N=o())!=='"'&&N!=="'")throw v(N);x.push(o()),l(N),N=s()}while(N==='"'||N==="'");return x.join("")}function A(x){var N=o();switch(N){case"'":case'"':return a(N),_();case"true":case"TRUE":return!0;case"false":case"FALSE":return!1}try{return T(N,!0)}catch(I){if(x&&at.test(N))return N;throw v(N,"value")}}function D(x,N){var I,P;do N&&((I=s())==='"'||I==="'")?x.push(_()):x.push([P=k(o()),l("to",!0)?k(o()):P]);while(l(",",!0));l(";")}function T(x,N){var I=1;switch(x.charAt(0)==="-"&&(I=-1,x=x.substring(1)),x){case"inf":case"INF":case"Inf":return I*(1/0);case"nan":case"NAN":case"Nan":case"NaN":return NaN;case"0":return 0}if(cg.test(x))return I*parseInt(x,10);if(ug.test(x))return I*parseInt(x,16);if(dg.test(x))return I*parseInt(x,8);if(hg.test(x))return I*parseFloat(x);throw v(x,"number",N)}function k(x,N){switch(x){case"max":case"MAX":case"Max":return *********;case"0":return 0}if(!N&&x.charAt(0)==="-")throw v(x,"id");if(pg.test(x))return parseInt(x,10);if(fg.test(x))return parseInt(x,16);if(mg.test(x))return parseInt(x,8);throw v(x,"id")}function b(){if(m!==void 0)throw v("package");if(m=o(),!at.test(m))throw v(m,"name");f=f.define(m),l(";")}function C(){var x=s(),N;switch(x){case"weak":N=w||(w=[]),o();break;case"public":o();default:N=y||(y=[]);break}x=_(),l(";"),N.push(x)}function O(){if(l("="),E=_(),d=E==="proto3",!d&&E!=="proto2")throw v(E,"syntax");l(";")}function S(x,N){switch(N){case"option":return U(x,N),l(";"),!0;case"message":return L(x,N),!0;case"enum":return z(x,N),!0;case"service":return pn(x,N),!0;case"extend":return u(x,N),!0}return!1}function M(x,N,I){var P=r.line;if(x&&(typeof x.comment!="string"&&(x.comment=c()),x.filename=ht.filename),l("{",!0)){for(var $;($=o())!=="}";)N($);l(";",!0)}else I&&I(),l(";"),x&&(typeof x.comment!="string"||i)&&(x.comment=c(P)||x.comment)}function L(x,N){if(!ot.test(N=o()))throw v(N,"type name");var I=new Tp(N);M(I,function($){if(!S(I,$))switch($){case"map":F(I,$);break;case"required":case"repeated":j(I,$);break;case"optional":d?j(I,"proto3_optional"):j(I,"optional");break;case"oneof":ee(I,$);break;case"extensions":D(I.extensions||(I.extensions=[]));break;case"reserved":D(I.reserved||(I.reserved=[]),!0);break;default:if(!d||!at.test($))throw v($);a($),j(I,"optional");break}}),x.add(I)}function j(x,N,I){var P=o();if(P==="group"){Z(x,N);return}for(;P.endsWith(".")||s().startsWith(".");)P+=o();if(!at.test(P))throw v(P,"type");var $=o();if(!ot.test($))throw v($,"name");$=g($),l("=");var W=new Ap($,k(o()),P,N,I);if(M(W,function(oe){if(oe==="option")U(W,oe),l(";");else throw v(oe)},function(){lt(W)}),N==="proto3_optional"){var ie=new kp("_"+$);W.setOption("proto3_optional",!0),ie.add(W),x.add(ie)}else x.add(W);!d&&W.repeated&&(va.packed[P]!==void 0||va.basic[P]===void 0)&&W.setOption("packed",!1,!0)}function Z(x,N){var I=o();if(!ot.test(I))throw v(I,"name");var P=Ea.lcFirst(I);I===P&&(I=Ea.ucFirst(I)),l("=");var $=k(o()),W=new Tp(I);W.group=!0;var ie=new Ap(P,$,I,N);ie.filename=ht.filename,M(W,function(oe){switch(oe){case"option":U(W,oe),l(";");break;case"required":case"repeated":j(W,oe);break;case"optional":d?j(W,"proto3_optional"):j(W,"optional");break;case"message":L(W,oe);break;case"enum":z(W,oe);break;default:throw v(oe)}}),x.add(W).add(ie)}function F(x){l("<");var N=o();if(va.mapKey[N]===void 0)throw v(N,"type");l(",");var I=o();if(!at.test(I))throw v(I,"type");l(">");var P=o();if(!ot.test(P))throw v(P,"name");l("=");var $=new og(g(P),k(o()),N,I);M($,function(ie){if(ie==="option")U($,ie),l(";");else throw v(ie)},function(){lt($)}),x.add($)}function ee(x,N){if(!ot.test(N=o()))throw v(N,"name");var I=new kp(g(N));M(I,function($){$==="option"?(U(I,$),l(";")):(a($),j(I,"optional"))}),x.add(I)}function z(x,N){if(!ot.test(N=o()))throw v(N,"name");var I=new ag(N);M(I,function($){switch($){case"option":U(I,$),l(";");break;case"reserved":D(I.reserved||(I.reserved=[]),!0);break;default:We(I,$)}}),x.add(I)}function We(x,N){if(!ot.test(N))throw v(N,"name");l("=");var I=k(o(),!0),P={options:void 0};P.setOption=function($,W){this.options===void 0&&(this.options={}),this.options[$]=W},M(P,function(W){if(W==="option")U(P,W),l(";");else throw v(W)},function(){lt(P)}),x.add(N,I,P.comment,P.options)}function U(x,N){var I=l("(",!0);if(!at.test(N=o()))throw v(N,"name");var P=N,$=P,W;I&&(l(")"),P="("+P+")",$=P,N=s(),gg.test(N)&&(W=N.slice(1),P+=N,o())),l("=");var ie=Ne(x,P);Q(x,$,ie,W)}function Ne(x,N){if(l("{",!0)){for(var I={};!l("}",!0);){if(!ot.test(R=o()))throw v(R,"name");if(R===null)throw v(R,"end of input");var P,$=R;if(l(":",!0),s()==="{")P=Ne(x,N+"."+R);else if(s()==="["){P=[];var W;if(l("[",!0)){do W=A(!0),P.push(W);while(l(",",!0));l("]"),typeof W!="undefined"&&G(x,N+"."+R,W)}}else P=A(!0),G(x,N+"."+R,P);var ie=I[$];ie&&(P=[].concat(ie).concat(P)),I[$]=P,l(",",!0),l(";",!0)}return I}var te=A(!0);return G(x,N,te),te}function G(x,N,I){x.setOption&&x.setOption(N,I)}function Q(x,N,I,P){x.setParsedOption&&x.setParsedOption(N,I,P)}function lt(x){if(l("[",!0)){do U(x,"option");while(l(",",!0));l("]")}return x}function pn(x,N){if(!ot.test(N=o()))throw v(N,"service name");var I=new sg(N);M(I,function($){if(!S(I,$))if($==="rpc")h(I,$);else throw v($)}),x.add(I)}function h(x,N){var I=c(),P=N;if(!ot.test(N=o()))throw v(N,"name");var $=N,W,ie,te,oe;if(l("("),l("stream",!0)&&(ie=!0),!at.test(N=o())||(W=N,l(")"),l("returns"),l("("),l("stream",!0)&&(oe=!0),!at.test(N=o())))throw v(N);te=N,l(")");var un=new lg($,P,W,te,ie,oe);un.comment=I,M(un,function(qt){if(qt==="option")U(un,qt),l(";");else throw v(qt)}),x.add(un)}function u(x,N){if(!at.test(N=o()))throw v(N,"reference");var I=N;M(null,function($){switch($){case"required":case"repeated":j(x,$,I);break;case"optional":d?j(x,"proto3_optional",I):j(x,"optional",I);break;default:if(!d||!at.test($))throw v($);a($),j(x,"optional",I);break}})}for(var R;(R=o())!==null;)switch(R){case"package":if(!p)throw v(R);b();break;case"import":if(!p)throw v(R);C();break;case"syntax":if(!p)throw v(R);O();break;case"option":U(f,R),l(";");break;default:if(S(f,R)){p=!1;continue}throw v(R)}return ht.filename=null,{package:m,imports:y,weakImports:w,syntax:E,root:t}}});var Dp=Y((_w,Op)=>{"use strict";Op.exports=Xe;var yg=/\/|\./;function Xe(n,t){yg.test(n)||(n="google/protobuf/"+n+".proto",t={nested:{google:{nested:{protobuf:{nested:t}}}}}),Xe[n]=t}Xe("any",{Any:{fields:{type_url:{type:"string",id:1},value:{type:"bytes",id:2}}}});var Rp;Xe("duration",{Duration:Rp={fields:{seconds:{type:"int64",id:1},nanos:{type:"int32",id:2}}}});Xe("timestamp",{Timestamp:Rp});Xe("empty",{Empty:{fields:{}}});Xe("struct",{Struct:{fields:{fields:{keyType:"string",type:"Value",id:1}}},Value:{oneofs:{kind:{oneof:["nullValue","numberValue","stringValue","boolValue","structValue","listValue"]}},fields:{nullValue:{type:"NullValue",id:1},numberValue:{type:"double",id:2},stringValue:{type:"string",id:3},boolValue:{type:"bool",id:4},structValue:{type:"Struct",id:5},listValue:{type:"ListValue",id:6}}},NullValue:{values:{NULL_VALUE:0}},ListValue:{fields:{values:{rule:"repeated",type:"Value",id:1}}}});Xe("wrappers",{DoubleValue:{fields:{value:{type:"double",id:1}}},FloatValue:{fields:{value:{type:"float",id:1}}},Int64Value:{fields:{value:{type:"int64",id:1}}},UInt64Value:{fields:{value:{type:"uint64",id:1}}},Int32Value:{fields:{value:{type:"int32",id:1}}},UInt32Value:{fields:{value:{type:"uint32",id:1}}},BoolValue:{fields:{value:{type:"bool",id:1}}},StringValue:{fields:{value:{type:"string",id:1}}},BytesValue:{fields:{value:{type:"bytes",id:1}}}});Xe("field_mask",{FieldMask:{fields:{paths:{rule:"repeated",type:"string",id:1}}}});Xe.get=function(t){return Xe[t]||null}});var Ip=Y((Tw,Cp)=>{"use strict";var Lt=Cp.exports=xp();Lt.build="full";Lt.tokenize=wa();Lt.parse=Sp();Lt.common=Dp();Lt.Root._configure(Lt.Type,Lt.parse,Lt.common)});var Lp=Y((Aw,Fp)=>{"use strict";Fp.exports=Ip()});var Df=Y(Rr=>{(function(n){n.parser=function(h,u){return new e(h,u)},n.SAXParser=e,n.SAXStream=c,n.createStream=l,n.MAX_BUFFER_LENGTH=64*1024;var t=["comment","sgmlDecl","textNode","tagName","doctype","procInstName","procInstBody","entity","attribName","attribValue","cdata","script"];n.EVENTS=["text","processinginstruction","sgmldeclaration","doctype","comment","opentagstart","attribute","opentag","closetag","opencdata","cdata","closecdata","error","end","ready","script","opennamespace","closenamespace"];function e(h,u){if(!(this instanceof e))return new e(h,u);var R=this;r(R),R.q=R.c="",R.bufferCheckPosition=n.MAX_BUFFER_LENGTH,R.opt=u||{},R.opt.lowercase=R.opt.lowercase||R.opt.lowercasetags,R.looseCase=R.opt.lowercase?"toLowerCase":"toUpperCase",R.tags=[],R.closed=R.closedRoot=R.sawRoot=!1,R.tag=R.error=null,R.strict=!!h,R.noscript=!!(h||R.opt.noscript),R.state=b.BEGIN,R.strictEntities=R.opt.strictEntities,R.ENTITIES=R.strictEntities?Object.create(n.XML_ENTITIES):Object.create(n.ENTITIES),R.attribList=[],R.opt.xmlns&&(R.ns=Object.create(E)),R.trackPosition=R.opt.position!==!1,R.trackPosition&&(R.position=R.line=R.column=0),O(R,"onready")}Object.create||(Object.create=function(h){function u(){}u.prototype=h;var R=new u;return R}),Object.keys||(Object.keys=function(h){var u=[];for(var R in h)h.hasOwnProperty(R)&&u.push(R);return u});function i(h){for(var u=Math.max(n.MAX_BUFFER_LENGTH,10),R=0,x=0,N=t.length;x<N;x++){var I=h[t[x]].length;if(I>u)switch(t[x]){case"textNode":M(h);break;case"cdata":S(h,"oncdata",h.cdata),h.cdata="";break;case"script":S(h,"onscript",h.script),h.script="";break;default:j(h,"Max buffer length exceeded: "+t[x])}R=Math.max(R,I)}var P=n.MAX_BUFFER_LENGTH-R;h.bufferCheckPosition=P+h.position}function r(h){for(var u=0,R=t.length;u<R;u++)h[t[u]]=""}function o(h){M(h),h.cdata!==""&&(S(h,"oncdata",h.cdata),h.cdata=""),h.script!==""&&(S(h,"onscript",h.script),h.script="")}e.prototype={end:function(){Z(this)},write:pn,resume:function(){return this.error=null,this},close:function(){return this.write(null)},flush:function(){o(this)}};var a;try{a=require("stream").Stream}catch(h){a=function(){}}a||(a=function(){});var s=n.EVENTS.filter(function(h){return h!=="error"&&h!=="end"});function l(h,u){return new c(h,u)}function c(h,u){if(!(this instanceof c))return new c(h,u);a.apply(this),this._parser=new e(h,u),this.writable=!0,this.readable=!0;var R=this;this._parser.onend=function(){R.emit("end")},this._parser.onerror=function(x){R.emit("error",x),R._parser.error=null},this._decoder=null,s.forEach(function(x){Object.defineProperty(R,"on"+x,{get:function(){return R._parser["on"+x]},set:function(N){if(!N)return R.removeAllListeners(x),R._parser["on"+x]=N,N;R.on(x,N)},enumerable:!0,configurable:!1})})}c.prototype=Object.create(a.prototype,{constructor:{value:c}}),c.prototype.write=function(h){if(typeof Buffer=="function"&&typeof Buffer.isBuffer=="function"&&Buffer.isBuffer(h)){if(!this._decoder){var u=require("string_decoder").StringDecoder;this._decoder=new u("utf8")}h=this._decoder.write(h)}return this._parser.write(h.toString()),this.emit("data",h),!0},c.prototype.end=function(h){return h&&h.length&&this.write(h),this._parser.end(),!0},c.prototype.on=function(h,u){var R=this;return!R._parser["on"+h]&&s.indexOf(h)!==-1&&(R._parser["on"+h]=function(){var x=arguments.length===1?[arguments[0]]:Array.apply(null,arguments);x.splice(0,0,h),R.emit.apply(R,x)}),a.prototype.on.call(R,h,u)};var p="[CDATA[",m="DOCTYPE",y="http://www.w3.org/XML/1998/namespace",w="http://www.w3.org/2000/xmlns/",E={xml:y,xmlns:w},d=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,f=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040.\d-]/,g=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,v=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040.\d-]/;function _(h){return h===" "||h===`
`||h==="\r"||h==="	"}function A(h){return h==='"'||h==="'"}function D(h){return h===">"||_(h)}function T(h,u){return h.test(u)}function k(h,u){return!T(h,u)}var b=0;n.STATE={BEGIN:b++,BEGIN_WHITESPACE:b++,TEXT:b++,TEXT_ENTITY:b++,OPEN_WAKA:b++,SGML_DECL:b++,SGML_DECL_QUOTED:b++,DOCTYPE:b++,DOCTYPE_QUOTED:b++,DOCTYPE_DTD:b++,DOCTYPE_DTD_QUOTED:b++,COMMENT_STARTING:b++,COMMENT:b++,COMMENT_ENDING:b++,COMMENT_ENDED:b++,CDATA:b++,CDATA_ENDING:b++,CDATA_ENDING_2:b++,PROC_INST:b++,PROC_INST_BODY:b++,PROC_INST_ENDING:b++,OPEN_TAG:b++,OPEN_TAG_SLASH:b++,ATTRIB:b++,ATTRIB_NAME:b++,ATTRIB_NAME_SAW_WHITE:b++,ATTRIB_VALUE:b++,ATTRIB_VALUE_QUOTED:b++,ATTRIB_VALUE_CLOSED:b++,ATTRIB_VALUE_UNQUOTED:b++,ATTRIB_VALUE_ENTITY_Q:b++,ATTRIB_VALUE_ENTITY_U:b++,CLOSE_TAG:b++,CLOSE_TAG_SAW_WHITE:b++,SCRIPT:b++,SCRIPT_ENDING:b++},n.XML_ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'"},n.ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'",AElig:198,Aacute:193,Acirc:194,Agrave:192,Aring:197,Atilde:195,Auml:196,Ccedil:199,ETH:208,Eacute:201,Ecirc:202,Egrave:200,Euml:203,Iacute:205,Icirc:206,Igrave:204,Iuml:207,Ntilde:209,Oacute:211,Ocirc:212,Ograve:210,Oslash:216,Otilde:213,Ouml:214,THORN:222,Uacute:218,Ucirc:219,Ugrave:217,Uuml:220,Yacute:221,aacute:225,acirc:226,aelig:230,agrave:224,aring:229,atilde:227,auml:228,ccedil:231,eacute:233,ecirc:234,egrave:232,eth:240,euml:235,iacute:237,icirc:238,igrave:236,iuml:239,ntilde:241,oacute:243,ocirc:244,ograve:242,oslash:248,otilde:245,ouml:246,szlig:223,thorn:254,uacute:250,ucirc:251,ugrave:249,uuml:252,yacute:253,yuml:255,copy:169,reg:174,nbsp:160,iexcl:161,cent:162,pound:163,curren:164,yen:165,brvbar:166,sect:167,uml:168,ordf:170,laquo:171,not:172,shy:173,macr:175,deg:176,plusmn:177,sup1:185,sup2:178,sup3:179,acute:180,micro:181,para:182,middot:183,cedil:184,ordm:186,raquo:187,frac14:188,frac12:189,frac34:190,iquest:191,times:215,divide:247,OElig:338,oelig:339,Scaron:352,scaron:353,Yuml:376,fnof:402,circ:710,tilde:732,Alpha:913,Beta:914,Gamma:915,Delta:916,Epsilon:917,Zeta:918,Eta:919,Theta:920,Iota:921,Kappa:922,Lambda:923,Mu:924,Nu:925,Xi:926,Omicron:927,Pi:928,Rho:929,Sigma:931,Tau:932,Upsilon:933,Phi:934,Chi:935,Psi:936,Omega:937,alpha:945,beta:946,gamma:947,delta:948,epsilon:949,zeta:950,eta:951,theta:952,iota:953,kappa:954,lambda:955,mu:956,nu:957,xi:958,omicron:959,pi:960,rho:961,sigmaf:962,sigma:963,tau:964,upsilon:965,phi:966,chi:967,psi:968,omega:969,thetasym:977,upsih:978,piv:982,ensp:8194,emsp:8195,thinsp:8201,zwnj:8204,zwj:8205,lrm:8206,rlm:8207,ndash:8211,mdash:8212,lsquo:8216,rsquo:8217,sbquo:8218,ldquo:8220,rdquo:8221,bdquo:8222,dagger:8224,Dagger:8225,bull:8226,hellip:8230,permil:8240,prime:8242,Prime:8243,lsaquo:8249,rsaquo:8250,oline:8254,frasl:8260,euro:8364,image:8465,weierp:8472,real:8476,trade:8482,alefsym:8501,larr:8592,uarr:8593,rarr:8594,darr:8595,harr:8596,crarr:8629,lArr:8656,uArr:8657,rArr:8658,dArr:8659,hArr:8660,forall:8704,part:8706,exist:8707,empty:8709,nabla:8711,isin:8712,notin:8713,ni:8715,prod:8719,sum:8721,minus:8722,lowast:8727,radic:8730,prop:8733,infin:8734,ang:8736,and:8743,or:8744,cap:8745,cup:8746,int:8747,there4:8756,sim:8764,cong:8773,asymp:8776,ne:8800,equiv:8801,le:8804,ge:8805,sub:8834,sup:8835,nsub:8836,sube:8838,supe:8839,oplus:8853,otimes:8855,perp:8869,sdot:8901,lceil:8968,rceil:8969,lfloor:8970,rfloor:8971,lang:9001,rang:9002,loz:9674,spades:9824,clubs:9827,hearts:9829,diams:9830},Object.keys(n.ENTITIES).forEach(function(h){var u=n.ENTITIES[h],R=typeof u=="number"?String.fromCharCode(u):u;n.ENTITIES[h]=R});for(var C in n.STATE)n.STATE[n.STATE[C]]=C;b=n.STATE;function O(h,u,R){h[u]&&h[u](R)}function S(h,u,R){h.textNode&&M(h),O(h,u,R)}function M(h){h.textNode=L(h.opt,h.textNode),h.textNode&&O(h,"ontext",h.textNode),h.textNode=""}function L(h,u){return h.trim&&(u=u.trim()),h.normalize&&(u=u.replace(/\s+/g," ")),u}function j(h,u){return M(h),h.trackPosition&&(u+=`
Line: `+h.line+`
Column: `+h.column+`
Char: `+h.c),u=new Error(u),h.error=u,O(h,"onerror",u),h}function Z(h){return h.sawRoot&&!h.closedRoot&&F(h,"Unclosed root tag"),h.state!==b.BEGIN&&h.state!==b.BEGIN_WHITESPACE&&h.state!==b.TEXT&&j(h,"Unexpected end"),M(h),h.c="",h.closed=!0,O(h,"onend"),e.call(h,h.strict,h.opt),h}function F(h,u){if(typeof h!="object"||!(h instanceof e))throw new Error("bad call to strictFail");h.strict&&j(h,u)}function ee(h){h.strict||(h.tagName=h.tagName[h.looseCase]());var u=h.tags[h.tags.length-1]||h,R=h.tag={name:h.tagName,attributes:{}};h.opt.xmlns&&(R.ns=u.ns),h.attribList.length=0,S(h,"onopentagstart",R)}function z(h,u){var R=h.indexOf(":"),x=R<0?["",h]:h.split(":"),N=x[0],I=x[1];return u&&h==="xmlns"&&(N="xmlns",I=""),{prefix:N,local:I}}function We(h){if(h.strict||(h.attribName=h.attribName[h.looseCase]()),h.attribList.indexOf(h.attribName)!==-1||h.tag.attributes.hasOwnProperty(h.attribName)){h.attribName=h.attribValue="";return}if(h.opt.xmlns){var u=z(h.attribName,!0),R=u.prefix,x=u.local;if(R==="xmlns")if(x==="xml"&&h.attribValue!==y)F(h,"xml: prefix must be bound to "+y+`
Actual: `+h.attribValue);else if(x==="xmlns"&&h.attribValue!==w)F(h,"xmlns: prefix must be bound to "+w+`
Actual: `+h.attribValue);else{var N=h.tag,I=h.tags[h.tags.length-1]||h;N.ns===I.ns&&(N.ns=Object.create(I.ns)),N.ns[x]=h.attribValue}h.attribList.push([h.attribName,h.attribValue])}else h.tag.attributes[h.attribName]=h.attribValue,S(h,"onattribute",{name:h.attribName,value:h.attribValue});h.attribName=h.attribValue=""}function U(h,u){if(h.opt.xmlns){var R=h.tag,x=z(h.tagName);R.prefix=x.prefix,R.local=x.local,R.uri=R.ns[x.prefix]||"",R.prefix&&!R.uri&&(F(h,"Unbound namespace prefix: "+JSON.stringify(h.tagName)),R.uri=x.prefix);var N=h.tags[h.tags.length-1]||h;R.ns&&N.ns!==R.ns&&Object.keys(R.ns).forEach(function(ns){S(h,"onopennamespace",{prefix:ns,uri:R.ns[ns]})});for(var I=0,P=h.attribList.length;I<P;I++){var $=h.attribList[I],W=$[0],ie=$[1],te=z(W,!0),oe=te.prefix,un=te.local,Yr=oe===""?"":R.ns[oe]||"",qt={name:W,value:ie,prefix:oe,local:un,uri:Yr};oe&&oe!=="xmlns"&&!Yr&&(F(h,"Unbound namespace prefix: "+JSON.stringify(oe)),qt.uri=oe),h.tag.attributes[W]=qt,S(h,"onattribute",qt)}h.attribList.length=0}h.tag.isSelfClosing=!!u,h.sawRoot=!0,h.tags.push(h.tag),S(h,"onopentag",h.tag),u||(!h.noscript&&h.tagName.toLowerCase()==="script"?h.state=b.SCRIPT:h.state=b.TEXT,h.tag=null,h.tagName=""),h.attribName=h.attribValue="",h.attribList.length=0}function Ne(h){if(!h.tagName){F(h,"Weird empty close tag."),h.textNode+="</>",h.state=b.TEXT;return}if(h.script){if(h.tagName!=="script"){h.script+="</"+h.tagName+">",h.tagName="",h.state=b.SCRIPT;return}S(h,"onscript",h.script),h.script=""}var u=h.tags.length,R=h.tagName;h.strict||(R=R[h.looseCase]());for(var x=R;u--;){var N=h.tags[u];if(N.name!==x)F(h,"Unexpected close tag");else break}if(u<0){F(h,"Unmatched closing tag: "+h.tagName),h.textNode+="</"+h.tagName+">",h.state=b.TEXT;return}h.tagName=R;for(var I=h.tags.length;I-- >u;){var P=h.tag=h.tags.pop();h.tagName=h.tag.name,S(h,"onclosetag",h.tagName);var $={};for(var W in P.ns)$[W]=P.ns[W];var ie=h.tags[h.tags.length-1]||h;h.opt.xmlns&&P.ns!==ie.ns&&Object.keys(P.ns).forEach(function(te){var oe=P.ns[te];S(h,"onclosenamespace",{prefix:te,uri:oe})})}u===0&&(h.closedRoot=!0),h.tagName=h.attribValue=h.attribName="",h.attribList.length=0,h.state=b.TEXT}function G(h){var u=h.entity,R=u.toLowerCase(),x,N="";return h.ENTITIES[u]?h.ENTITIES[u]:h.ENTITIES[R]?h.ENTITIES[R]:(u=R,u.charAt(0)==="#"&&(u.charAt(1)==="x"?(u=u.slice(2),x=parseInt(u,16),N=x.toString(16)):(u=u.slice(1),x=parseInt(u,10),N=x.toString(10))),u=u.replace(/^0+/,""),isNaN(x)||N.toLowerCase()!==u?(F(h,"Invalid character entity"),"&"+h.entity+";"):String.fromCodePoint(x))}function Q(h,u){u==="<"?(h.state=b.OPEN_WAKA,h.startTagPosition=h.position):_(u)||(F(h,"Non-whitespace before first tag."),h.textNode=u,h.state=b.TEXT)}function lt(h,u){var R="";return u<h.length&&(R=h.charAt(u)),R}function pn(h){var u=this;if(this.error)throw this.error;if(u.closed)return j(u,"Cannot write after close. Assign an onready handler.");if(h===null)return Z(u);typeof h=="object"&&(h=h.toString());for(var R=0,x="";x=lt(h,R++),u.c=x,!!x;)switch(u.trackPosition&&(u.position++,x===`
`?(u.line++,u.column=0):u.column++),u.state){case b.BEGIN:if(u.state=b.BEGIN_WHITESPACE,x==="\uFEFF")continue;Q(u,x);continue;case b.BEGIN_WHITESPACE:Q(u,x);continue;case b.TEXT:if(u.sawRoot&&!u.closedRoot){for(var N=R-1;x&&x!=="<"&&x!=="&";)x=lt(h,R++),x&&u.trackPosition&&(u.position++,x===`
`?(u.line++,u.column=0):u.column++);u.textNode+=h.substring(N,R-1)}x==="<"&&!(u.sawRoot&&u.closedRoot&&!u.strict)?(u.state=b.OPEN_WAKA,u.startTagPosition=u.position):(!_(x)&&(!u.sawRoot||u.closedRoot)&&F(u,"Text data outside of root node."),x==="&"?u.state=b.TEXT_ENTITY:u.textNode+=x);continue;case b.SCRIPT:x==="<"?u.state=b.SCRIPT_ENDING:u.script+=x;continue;case b.SCRIPT_ENDING:x==="/"?u.state=b.CLOSE_TAG:(u.script+="<"+x,u.state=b.SCRIPT);continue;case b.OPEN_WAKA:if(x==="!")u.state=b.SGML_DECL,u.sgmlDecl="";else if(!_(x))if(T(d,x))u.state=b.OPEN_TAG,u.tagName=x;else if(x==="/")u.state=b.CLOSE_TAG,u.tagName="";else if(x==="?")u.state=b.PROC_INST,u.procInstName=u.procInstBody="";else{if(F(u,"Unencoded <"),u.startTagPosition+1<u.position){var I=u.position-u.startTagPosition;x=new Array(I).join(" ")+x}u.textNode+="<"+x,u.state=b.TEXT}continue;case b.SGML_DECL:(u.sgmlDecl+x).toUpperCase()===p?(S(u,"onopencdata"),u.state=b.CDATA,u.sgmlDecl="",u.cdata=""):u.sgmlDecl+x==="--"?(u.state=b.COMMENT,u.comment="",u.sgmlDecl=""):(u.sgmlDecl+x).toUpperCase()===m?(u.state=b.DOCTYPE,(u.doctype||u.sawRoot)&&F(u,"Inappropriately located doctype declaration"),u.doctype="",u.sgmlDecl=""):x===">"?(S(u,"onsgmldeclaration",u.sgmlDecl),u.sgmlDecl="",u.state=b.TEXT):(A(x)&&(u.state=b.SGML_DECL_QUOTED),u.sgmlDecl+=x);continue;case b.SGML_DECL_QUOTED:x===u.q&&(u.state=b.SGML_DECL,u.q=""),u.sgmlDecl+=x;continue;case b.DOCTYPE:x===">"?(u.state=b.TEXT,S(u,"ondoctype",u.doctype),u.doctype=!0):(u.doctype+=x,x==="["?u.state=b.DOCTYPE_DTD:A(x)&&(u.state=b.DOCTYPE_QUOTED,u.q=x));continue;case b.DOCTYPE_QUOTED:u.doctype+=x,x===u.q&&(u.q="",u.state=b.DOCTYPE);continue;case b.DOCTYPE_DTD:u.doctype+=x,x==="]"?u.state=b.DOCTYPE:A(x)&&(u.state=b.DOCTYPE_DTD_QUOTED,u.q=x);continue;case b.DOCTYPE_DTD_QUOTED:u.doctype+=x,x===u.q&&(u.state=b.DOCTYPE_DTD,u.q="");continue;case b.COMMENT:x==="-"?u.state=b.COMMENT_ENDING:u.comment+=x;continue;case b.COMMENT_ENDING:x==="-"?(u.state=b.COMMENT_ENDED,u.comment=L(u.opt,u.comment),u.comment&&S(u,"oncomment",u.comment),u.comment=""):(u.comment+="-"+x,u.state=b.COMMENT);continue;case b.COMMENT_ENDED:x!==">"?(F(u,"Malformed comment"),u.comment+="--"+x,u.state=b.COMMENT):u.state=b.TEXT;continue;case b.CDATA:x==="]"?u.state=b.CDATA_ENDING:u.cdata+=x;continue;case b.CDATA_ENDING:x==="]"?u.state=b.CDATA_ENDING_2:(u.cdata+="]"+x,u.state=b.CDATA);continue;case b.CDATA_ENDING_2:x===">"?(u.cdata&&S(u,"oncdata",u.cdata),S(u,"onclosecdata"),u.cdata="",u.state=b.TEXT):x==="]"?u.cdata+="]":(u.cdata+="]]"+x,u.state=b.CDATA);continue;case b.PROC_INST:x==="?"?u.state=b.PROC_INST_ENDING:_(x)?u.state=b.PROC_INST_BODY:u.procInstName+=x;continue;case b.PROC_INST_BODY:if(!u.procInstBody&&_(x))continue;x==="?"?u.state=b.PROC_INST_ENDING:u.procInstBody+=x;continue;case b.PROC_INST_ENDING:x===">"?(S(u,"onprocessinginstruction",{name:u.procInstName,body:u.procInstBody}),u.procInstName=u.procInstBody="",u.state=b.TEXT):(u.procInstBody+="?"+x,u.state=b.PROC_INST_BODY);continue;case b.OPEN_TAG:T(f,x)?u.tagName+=x:(ee(u),x===">"?U(u):x==="/"?u.state=b.OPEN_TAG_SLASH:(_(x)||F(u,"Invalid character in tag name"),u.state=b.ATTRIB));continue;case b.OPEN_TAG_SLASH:x===">"?(U(u,!0),Ne(u)):(F(u,"Forward-slash in opening tag not followed by >"),u.state=b.ATTRIB);continue;case b.ATTRIB:if(_(x))continue;x===">"?U(u):x==="/"?u.state=b.OPEN_TAG_SLASH:T(d,x)?(u.attribName=x,u.attribValue="",u.state=b.ATTRIB_NAME):F(u,"Invalid attribute name");continue;case b.ATTRIB_NAME:x==="="?u.state=b.ATTRIB_VALUE:x===">"?(F(u,"Attribute without value"),u.attribValue=u.attribName,We(u),U(u)):_(x)?u.state=b.ATTRIB_NAME_SAW_WHITE:T(f,x)?u.attribName+=x:F(u,"Invalid attribute name");continue;case b.ATTRIB_NAME_SAW_WHITE:if(x==="=")u.state=b.ATTRIB_VALUE;else{if(_(x))continue;F(u,"Attribute without value"),u.tag.attributes[u.attribName]="",u.attribValue="",S(u,"onattribute",{name:u.attribName,value:""}),u.attribName="",x===">"?U(u):T(d,x)?(u.attribName=x,u.state=b.ATTRIB_NAME):(F(u,"Invalid attribute name"),u.state=b.ATTRIB)}continue;case b.ATTRIB_VALUE:if(_(x))continue;A(x)?(u.q=x,u.state=b.ATTRIB_VALUE_QUOTED):(F(u,"Unquoted attribute value"),u.state=b.ATTRIB_VALUE_UNQUOTED,u.attribValue=x);continue;case b.ATTRIB_VALUE_QUOTED:if(x!==u.q){x==="&"?u.state=b.ATTRIB_VALUE_ENTITY_Q:u.attribValue+=x;continue}We(u),u.q="",u.state=b.ATTRIB_VALUE_CLOSED;continue;case b.ATTRIB_VALUE_CLOSED:_(x)?u.state=b.ATTRIB:x===">"?U(u):x==="/"?u.state=b.OPEN_TAG_SLASH:T(d,x)?(F(u,"No whitespace between attributes"),u.attribName=x,u.attribValue="",u.state=b.ATTRIB_NAME):F(u,"Invalid attribute name");continue;case b.ATTRIB_VALUE_UNQUOTED:if(!D(x)){x==="&"?u.state=b.ATTRIB_VALUE_ENTITY_U:u.attribValue+=x;continue}We(u),x===">"?U(u):u.state=b.ATTRIB;continue;case b.CLOSE_TAG:if(u.tagName)x===">"?Ne(u):T(f,x)?u.tagName+=x:u.script?(u.script+="</"+u.tagName,u.tagName="",u.state=b.SCRIPT):(_(x)||F(u,"Invalid tagname in closing tag"),u.state=b.CLOSE_TAG_SAW_WHITE);else{if(_(x))continue;k(d,x)?u.script?(u.script+="</"+x,u.state=b.SCRIPT):F(u,"Invalid tagname in closing tag."):u.tagName=x}continue;case b.CLOSE_TAG_SAW_WHITE:if(_(x))continue;x===">"?Ne(u):F(u,"Invalid characters in closing tag");continue;case b.TEXT_ENTITY:case b.ATTRIB_VALUE_ENTITY_Q:case b.ATTRIB_VALUE_ENTITY_U:var P,$;switch(u.state){case b.TEXT_ENTITY:P=b.TEXT,$="textNode";break;case b.ATTRIB_VALUE_ENTITY_Q:P=b.ATTRIB_VALUE_QUOTED,$="attribValue";break;case b.ATTRIB_VALUE_ENTITY_U:P=b.ATTRIB_VALUE_UNQUOTED,$="attribValue";break}if(x===";")if(u.opt.unparsedEntities){var W=G(u);u.entity="",u.state=P,u.write(W)}else u[$]+=G(u),u.entity="",u.state=P;else T(u.entity.length?v:g,x)?u.entity+=x:(F(u,"Invalid character in entity name"),u[$]+="&"+u.entity+x,u.entity="",u.state=P);continue;default:throw new Error(u,"Unknown state: "+u.state)}return u.position>=u.bufferCheckPosition&&i(u),u}String.fromCodePoint||function(){var h=String.fromCharCode,u=Math.floor,R=function(){var x=16384,N=[],I,P,$=-1,W=arguments.length;if(!W)return"";for(var ie="";++$<W;){var te=Number(arguments[$]);if(!isFinite(te)||te<0||te>1114111||u(te)!==te)throw RangeError("Invalid code point: "+te);te<=65535?N.push(te):(te-=65536,I=(te>>10)+55296,P=te%1024+56320,N.push(I,P)),($+1===W||N.length>x)&&(ie+=h.apply(null,N),N.length=0)}return ie};Object.defineProperty?Object.defineProperty(String,"fromCodePoint",{value:R,configurable:!0,writable:!0}):String.fromCodePoint=R}()})(typeof Rr=="undefined"?Rr.sax={}:Rr)});var Lf=Y((o2,Ff)=>{var Cf=/[&<>"]/g,yy=RegExp(Cf.source),by={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"},xy=n=>{let t=String(n);return t&&yy.test(t)?t.replace(Cf,e=>by[e]):t},wy=(n,t)=>typeof n=="string"&&typeof t=="string"||n&&n[0].$name===t.$name,vy=(n,t)=>typeof n=="string"?n+t:n.concat(t),Ey=n=>n.reduce((t,e)=>{let i=t.length-1,r=t[i];return wy(r,e)?t[i]=vy(r,e):t.push(typeof e=="string"?e:[e]),t},[]),_y=(n,t,e)=>{let i=null,r={};return Object.keys(n).forEach(o=>{let a=Or(n[o])?n[o]:null;if(a)switch(o){case"$name":!t&&a&&(r.$name=a);break;case"$attrs":i=a;break;case"$markup":r.$markup=If(a);break;default:a&&(r[o]=e(a))}}),{stripped:r,$attrs:i}},Ty=n=>Array.isArray(n)&&n.length===1?n[0]:n,Ay=(n,t)=>Or(t)?n?Object.keys(t).length===1&&t.$name?Object.assign(t,n):Object.assign(t,{$attrs:n}):t:n,ky=n=>{if(!n)return;let t=Object.keys(n),[e]=t;return t.length===1&&e!=="$name"&&e!=="id"?n[t[0]]:n},Za=(n,t,e)=>{let i=e?l=>l:Ty,r=i(n);if(!Or(r))return null;if(typeof r!="object"||Array.isArray(r))return r;let{$attrs:o,stripped:a}=_y(r,t,i),s=Ay(o,a);return ky(s)},Ny=n=>{let t,e={},i=!0;return n.every(r=>(typeof r=="string"?t="$text":t=r[0].$name,e[t]?(i=!1,!1):(e[t]=!0,!0))),i},Ya=(n=[],t=[],e=!1)=>{let i=Array.isArray(n)?n:[n],r=Array.isArray(t)?t:[t],o=i.concat(r);return o.length===1&&!e?o[0]:o},If=(n,t)=>{let e=[];return n!==null&&typeof n=="object"&&n.constructor===Array?(n.forEach(i=>{e.push(Za(i,t))}),e):Za(n,t)},Sy=(n,t)=>{let e,i={};return Object.keys(n).forEach(r=>{r!=="$markup"&&(i[r]=n[r])}),n.$markup&&n.$markup.forEach(r=>{typeof r=="string"?i.$text=Ya(i.$text,r,t):typeof r=="object"&&(r.constructor===Array?e=r[0].$name:e=r.$name,i[e]=Ya(i[e],If(r,!0),t))}),i},Or=n=>n!=null&&(n.length==null||n.length!==0)&&(typeof n!="object"||Object.keys(n).length!==0);Ff.exports={condenseArray:Ey,escape:xy,isSomething:Or,moosh:Ya,objectifyMarkup:Sy,shouldObjectifyMarkup:Ny,simplifyNode:Za}});var $f=Y((a2,Mf)=>{var{EventEmitter:Ry}=require("events"),Oy=Df(),$t=Lf(),Dy=1,In=0,Bt=-1,Pf={preserveMarkup:In,simplifyNodes:!0,useArrays:In,lowercase:!0,trim:!0,normalize:!0,cdataAsText:!1,strict:!1},bi=function(t,e=Pf){let i=new Ry,r=[],o=Object.assign({},Pf,e),{preserveMarkup:a,simplifyNodes:s,useArrays:l,lowercase:c,trim:p,normalize:m,cdataAsText:y,strict:w}=o,E=null,d=null,f=Oy.createStream(w,{lowercase:c,trim:p,normalize:m,cdataAsText:y});return f.on("opentag",g=>{r.length===0&&!i.listeners(`tag:${g.name}`).length||(E={$name:g.name,$attrs:g.attributes},l>Bt&&(E.$markup=[]),r.push(E))}),f.on("text",g=>{E&&(l>Bt?E.$markup.push(g):E.$text?E.$text+=g:E.$text=g)}),f.on("opencdata",()=>{E&&(y||(d={$name:"$cdata",text:""},l>Bt?E.$markup.push(d):E.$cdata=d))}),f.on("cdata",g=>{E&&(d!==null?d.text+=g:l>Bt?E.$markup.push(g):E.$text?E.$text+=g:E.$text=g)}),f.on("closecdata",()=>{d=null}),f.on("script",g=>{E&&(E.$script=g)}),f.on("closetag",g=>{let v,_=null,A=l>In;r.length!==0&&(l>Bt&&(a<=Bt?(E.$markup=$t.condenseArray(E.$markup),E=$t.objectifyMarkup(E,A)):a===In&&(v=$t.condenseArray(E.$markup),$t.shouldObjectifyMarkup(v)&&(E.$markup=v,E=$t.objectifyMarkup(E,A)))),i.listeners(`tag:${g}`).length&&i.emit(`tag:${g}`,s?$t.simplifyNode(E,!1,l>In):E),r.pop(),r.length>0&&(_=r[r.length-1],l>Bt?_.$markup.push(E):_[g]||(_[g]=$t.simplifyNode(E,!0))),E=_)}),f.on("end",()=>{i.emit("end")}),f.on("error",g=>{i.emit("error",g)}),t.pipe(f),i.pause=function(){t.pause()},i.resume=function(){t.resume()},i};bi.ALWAYS=Dy;bi.SOMETIMES=In;bi.NEVER=Bt;bi.toXml=function(t,{indent:e="",selfClosing:i=!0,escape:r=$t.escape,nodeName:o}={}){let a=e?`
`:"";function s(l,c,p){let m="",y,w=c,E=p?a+p:"",d=p+e,f="";return l=l||"",l.constructor===Array?(l.forEach(g=>{m+=s(g,w,p)}),m):(!w&&l.$name&&(w=l.$name),w&&(m=`${E}<${w}`,l.$attrs&&typeof l.$attrs=="object"&&(y=Object.keys(l.$attrs),y.forEach(g=>{m+=` ${g}=${JSON.stringify(String(l.$attrs[g]))}`}))),l==null||l===""||(typeof l=="object"?(y=Object.keys(l),y.forEach(g=>{let v=l[g];switch(g){case"$name":case"$attrs":break;case"$text":case"$markup":f+=s(v,null,d);break;case"$script":f+=s(v,"script",d);break;case"$cdata":f+=`${E}<![CDATA[${v}]]>`;break;default:f+=s(v,g,d)}})):f+=E+r(l)),w?f?m+=`>${f}${a}${p}</${w}>`:i?m+="/>":m+=`></${w}>`:m+=f,m)}return s(t,o,"")};Mf.exports=bi});var I1={};Se(I1,{ATTACHMENT_EXTS:()=>Ln,AUTH_REDIRECT_URI:()=>$r,ImportContext:()=>Wr,ImporterModal:()=>wi,default:()=>Zr});module.exports=wd(I1);var Ht=require("obsidian");var st=require("obsidian");var wt=class{constructor(t){this.importer=t,this.app=t.app}};var Mn=class extends wt{constructor(e,i){super(e);this.scan=i.mergableDataObject,this.objects=this.scan.mergeableDataObjectData.mergeableDataObjectEntry}async format(){let e=[];for(let i of this.objects){if(!i.customMap)continue;let r=i.customMap.mapEntry[0].value.stringValue,o=await this.importer.database.get`
				SELECT z_pk, zmedia, ztypeuti FROM ziccloudsyncingobject 
				WHERE zidentifier = ${r}`,a=await this.importer.resolveAttachment(o.Z_PK,"com.apple.notes.gallery");if(a||(a=await this.importer.resolveAttachment(o.ZMEDIA,o.ZTYPEUTI)),a)e.push(this.importer.app.fileManager.generateMarkdownLink(a,"/"));else return"**Cannot decode scan**"}return`
${e.join(`
`)}
`}};Mn.protobufType="ciofecaforensics.MergableDataProto";var $n=class extends wt{constructor(e,i){super(e);this.rowLocations={};this.columnLocations={};this.table=i.mergableDataObject;let r=this.table.mergeableDataObjectData;this.keys=r.mergeableDataObjectKeyItem,this.types=r.mergeableDataObjectTypeItem,this.uuids=r.mergeableDataObjectUuidItem.map(this.uuidToString),this.objects=r.mergeableDataObjectEntry}async parse(){let e=this.objects.find(r=>r.customMap&&this.types[r.customMap.type]=="com.apple.notes.ICTable");if(!e)return null;let i=null;for(let r of e.customMap.mapEntry){let o=this.objects[r.value.objectIndex];switch(this.keys[r.key]){case"crRows":[this.rowLocations,this.rowCount]=this.findLocations(o);break;case"crColumns":[this.columnLocations,this.columnCount]=this.findLocations(o);break;case"cellColumns":i=o;break}}return i?await this.computeCells(i):null}findLocations(e){let i=[],r={};for(let o of e.orderedSet.ordering.array.attachment)i.push(this.uuidToString(o.uuid));for(let o of e.orderedSet.ordering.contents.element){let a=this.getTargetUuid(o.key),s=this.getTargetUuid(o.value);r[s]=i.indexOf(a)}return[r,i.length]}async computeCells(e){let i=Array(this.rowCount).fill(0).map(()=>Array(this.columnCount));for(let r of e.dictionary.element){let o=this.columnLocations[this.getTargetUuid(r.key)],a=this.objects[r.value.objectIndex];for(let s of a.dictionary.element){let l=this.rowLocations[this.getTargetUuid(s.key)],c=this.objects[s.value.objectIndex];if(!(l in i)||!c)continue;let p=new Wt(this.importer,c);i[l][o]=await p.format(!0)}}return i}async format(){let e=await this.parse();if(!e)return"";let i=`
`;for(let r=0;r<e.length;r++)i+=`| ${e[r].join(" | ")} |
`,r==0&&(i+=`|${" -- |".repeat(e[0].length)}
`);return i+`
`}getTargetUuid(e){let r=this.objects[e.objectIndex].customMap.mapEntry[0].value.unsignedIntegerValue;return this.uuids[r]}uuidToString(e){return Buffer.from(e).toString("hex")}};$n.protobufType="ciofecaforensics.MergableDataProto";var vd=/(^\s+|(?:\s+)?\n(?:\s+)?|\s+$)/,zr=/applenotes:note\/([-0-9a-f]+)(?:\?ownerIdentifier=.*)?/,Ed=".AppleColorEmojiUI",as=[100,101,102,103],Wt=class extends wt{constructor(e,i){super(e);this.listNumber=0;this.listIndent=0;this.multiRun=0;this.note=i.note}parseTokens(){let e=0,i=0,r=0,o=[];for(;e<this.note.attributeRun.length;){let a,s="",l=!0;do a=this.note.attributeRun[e],r=r+a.length,s+=this.note.noteText.substring(i,r),i=r,l=e==this.note.attributeRun.length-1?!1:ss(a,this.note.attributeRun[e+1]),e++;while(l);for(let c of s.split(vd))c&&o.push({attr:a,fragment:c})}return o}async format(e=!1){var a;let i=this.parseTokens(),r=!e&&this.importer.omitFirstLine&&this.note.noteText.contains(`
`),o="";for(let s=0;s<i.length;s++){let{attr:l,fragment:c}=i[s];if(r)if(c.contains(`
`)||l.attachmentInfo)r=!1;else continue;l.fragment=c,l.atLineStart=s==0?!0:(a=i[s-1])==null?void 0:a.fragment.contains(`
`),o+=this.formatMultiRun(l),!/\S/.test(l.fragment)||this.multiRun==1?o+=l.fragment:l.attachmentInfo?o+=await this.formatAttachment(l):l.superscript||l.underlined||l.color||l.font||this.multiRun==2?o+=await this.formatHtmlAttr(l):o+=await this.formatAttr(l)}return this.multiRun!=0&&(o+=this.formatMultiRun({})),e&&o.replace(`
`,"<br>").replace("|","&#124;"),o.trim()}formatMultiRun(e){var o,a,s,l,c,p;let i=(o=e.paragraphStyle)==null?void 0:o.styleType,r="";switch(this.multiRun){case 3:(((a=e.paragraphStyle)==null?void 0:a.indentAmount)==0&&!as.includes(i)||_d(e))&&(this.multiRun=0);break;case 1:i!=4&&(this.multiRun=0,r+="```\n");break;case 2:(s=e.paragraphStyle)!=null&&s.alignment||(this.multiRun=0,r+=`</p>
`);break}if(this.multiRun==0){if(i==4)this.multiRun=1,r+="\n```\n";else if(as.includes(i))this.multiRun=3,(l=e.paragraphStyle)!=null&&l.indentAmount&&(r+=`
- &nbsp;
`);else if((c=e.paragraphStyle)!=null&&c.alignment){this.multiRun=2;let m=this.convertAlign((p=e==null?void 0:e.paragraphStyle)==null?void 0:p.alignment);r+=`
<p style="text-align:${m};margin:0">`}}return r}async formatHtmlAttr(e){var r,o;e.strikethrough&&(e.fragment=`<s>${e.fragment}</s>`),e.underlined&&(e.fragment=`<u>${e.fragment}</u>`),e.superscript==1&&(e.fragment=`<sup>${e.fragment}</sup>`),e.superscript==-1&&(e.fragment=`<sub>${e.fragment}</sub>`);let i="";switch(e.fontWeight){case 1:e.fragment=`<b>${e.fragment}</b>`;break;case 2:e.fragment=`<i>${e.fragment}</i>`;break;case 3:e.fragment=`<b><i>${e.fragment}</i></b>`;break}return(r=e.font)!=null&&r.fontName&&e.font.fontName!==Ed&&(i+=`font-family:${e.font.fontName};`),(o=e.font)!=null&&o.pointSize&&(i+=`font-size:${e.font.pointSize}pt;`),e.color&&(i+=`color:${this.convertColor(e.color)};`),e.link&&!zr.test(e.link)?(i&&(i=` style="${i}"`),e.fragment=`<a href="${e.link}" rel="noopener" class="external-link" target="_blank"${i}>${e.fragment}</a>`):i&&(e.link&&(e.fragment=await this.getInternalLink(e.link,e.fragment)),e.fragment=`<span style="${i}">${e.fragment}</span>`),e.atLineStart?this.formatParagraph(e):e.fragment}async formatAttr(e){switch(e.fontWeight){case 1:e.fragment=`**${e.fragment}**`;break;case 2:e.fragment=`*${e.fragment}*`;break;case 3:e.fragment=`***${e.fragment}***`;break}return e.strikethrough&&(e.fragment=`~~${e.fragment}~~`),e.link&&e.link!=e.fragment&&(zr.test(e.link)?e.fragment=await this.getInternalLink(e.link,e.fragment):e.fragment=`[${e.fragment}](${e.link})`),e.atLineStart?this.formatParagraph(e):e.fragment}formatParagraph(e){var a,s,l,c,p,m;let i="	".repeat(((a=e.paragraphStyle)==null?void 0:a.indentAmount)||0),r=(s=e.paragraphStyle)==null?void 0:s.styleType,o=(l=e.paragraphStyle)!=null&&l.blockquote?"> ":"";switch(this.listNumber!=0&&(r!==102||this.listIndent!==((c=e.paragraphStyle)==null?void 0:c.indentAmount))&&(this.listIndent=((p=e.paragraphStyle)==null?void 0:p.indentAmount)||0,this.listNumber=0),r){case 0:return`${o}# ${e.fragment}`;case 1:return`${o}## ${e.fragment}`;case 2:return`${o}### ${e.fragment}`;case 101:case 100:return`${o}${i}- ${e.fragment}`;case 102:return this.listNumber++,`${o}${i}${this.listNumber}. ${e.fragment}`;case 103:let y=(m=e.paragraphStyle.checklist)!=null&&m.done?"[x]":"[ ]";return`${o}${i}- ${y} ${e.fragment}`}return this.multiRun==3&&(o+=i),`${o}${e.fragment}`}async formatAttachment(e){var l,c,p;let i,r,o;switch((l=e.attachmentInfo)==null?void 0:l.typeUti){case"com.apple.notes.inlinetextattachment.hashtag":case"com.apple.notes.inlinetextattachment.mention":return i=await this.importer.database.get`
					SELECT zalttext FROM ziccloudsyncingobject 
					WHERE zidentifier = ${e.attachmentInfo.attachmentIdentifier}`,i.ZALTTEXT;case"com.apple.notes.inlinetextattachment.link":return i=await this.importer.database.get`
					SELECT ztokencontentidentifier FROM ziccloudsyncingobject 
					WHERE zidentifier = ${e.attachmentInfo.attachmentIdentifier}`,await this.getInternalLink(i.ZTOKENCONTENTIDENTIFIER);case"com.apple.notes.table":return i=await this.importer.database.get`
					SELECT hex(zmergeabledata1) as zhexdata FROM ziccloudsyncingobject 
					WHERE zidentifier = ${e.attachmentInfo.attachmentIdentifier}`,o=this.importer.decodeData(i.zhexdata,$n),await o.format();case"public.url":return i=await this.importer.database.get`
					SELECT ztitle, zurlstring FROM ziccloudsyncingobject 
					WHERE zidentifier = ${e.attachmentInfo.attachmentIdentifier}`,`[**${i.ZTITLE}**](${i.ZURLSTRING})`;case"com.apple.notes.gallery":return i=await this.importer.database.get`
					SELECT hex(zmergeabledata1) as zhexdata FROM ziccloudsyncingobject 
					WHERE zidentifier = ${e.attachmentInfo.attachmentIdentifier}`,o=this.importer.decodeData(i.zhexdata,Mn),await o.format();case"com.apple.paper.doc.scan":case"com.apple.drawing":case"com.apple.drawing.2":case"com.apple.paper":i=await this.importer.database.get`
					SELECT z_pk, zhandwritingsummary 
					FROM (SELECT *, NULL AS zhandwritingsummary FROM ziccloudsyncingobject) 
					WHERE zidentifier = ${e.attachmentInfo.attachmentIdentifier}`,r=i==null?void 0:i.Z_PK;break;default:i=await this.importer.database.get`
					SELECT zmedia FROM ziccloudsyncingobject 
					WHERE zidentifier = ${(c=e.attachmentInfo)==null?void 0:c.attachmentIdentifier}`,r=i==null?void 0:i.ZMEDIA;break}if(!r)return` **(unknown attachment: ${(p=e.attachmentInfo)==null?void 0:p.typeUti})** `;let a=await this.importer.resolveAttachment(r,e.attachmentInfo.typeUti),s=a?`
${this.app.fileManager.generateMarkdownLink(a,"/")}
`:" **(error reading attachment)**";return this.importer.includeHandwriting&&i.ZHANDWRITINGSUMMARY&&(s=`
> [!Handwriting]-
> ${i.ZHANDWRITINGSUMMARY.replace(`
`,`
> `)}${s}`),s}async getInternalLink(e,i=void 0){let r=e.match(zr)[1],o=await this.importer.database.get`
			SELECT z_pk FROM ziccloudsyncingobject 
			WHERE zidentifier = ${r.toUpperCase()}`,a=await this.importer.resolveNote(o.Z_PK);return a?this.app.fileManager.generateMarkdownLink(a,this.importer.rootFolder.path,void 0,i):"(unknown file link)"}convertColor(e){let i="#";for(let r of Object.values(e))i+=Math.floor(r*255).toString(16);return i}convertAlign(e){switch(e){default:return"left";case 1:return"center";case 2:return"right";case 3:return"justify"}}};Wt.protobufType="ciofecaforensics.Document";function _d(n){return n.attachmentInfo?!n.attachmentInfo.typeUti.includes("com.apple.notes.inlinetextattachment"):!1}function ss(n,t){var e,i;if(!t||n.$type!=t.$type)return!1;for(let r of n.$type.fieldsArray)if(r.name!="length"){if((e=n[r.name])!=null&&e.$type&&((i=t[r.name])!=null&&i.$type)){if(!ss(n[r.name],t[r.name]))return!1}else if(n[r.name]!=t[r.name])return!1}return!0}var ls={nested:{ciofecaforensics:{nested:{Color:{fields:{red:{type:"float",id:1},green:{type:"float",id:2},blue:{type:"float",id:3},alpha:{type:"float",id:4}}},AttachmentInfo:{fields:{attachmentIdentifier:{type:"string",id:1},typeUti:{type:"string",id:2}}},Font:{fields:{fontName:{type:"string",id:1},pointSize:{type:"float",id:2},fontHints:{type:"int32",id:3}}},ParagraphStyle:{fields:{styleType:{type:"int32",id:1,options:{default:-1}},alignment:{type:"int32",id:2},indentAmount:{type:"int32",id:4},checklist:{type:"Checklist",id:5},blockquote:{type:"int32",id:8}}},Checklist:{fields:{uuid:{type:"bytes",id:1},done:{type:"int32",id:2}}},DictionaryElement:{fields:{key:{type:"ObjectID",id:1},value:{type:"ObjectID",id:2}}},Dictionary:{fields:{element:{rule:"repeated",type:"DictionaryElement",id:1,options:{packed:!1}}}},ObjectID:{fields:{unsignedIntegerValue:{type:"uint64",id:2},stringValue:{type:"string",id:4},objectIndex:{type:"int32",id:6}}},RegisterLatest:{fields:{contents:{type:"ObjectID",id:2}}},MapEntry:{fields:{key:{type:"int32",id:1},value:{type:"ObjectID",id:2}}},AttributeRun:{fields:{length:{type:"int32",id:1},paragraphStyle:{type:"ParagraphStyle",id:2},font:{type:"Font",id:3},fontWeight:{type:"int32",id:5},underlined:{type:"int32",id:6},strikethrough:{type:"int32",id:7},superscript:{type:"int32",id:8},link:{type:"string",id:9},color:{type:"Color",id:10},attachmentInfo:{type:"AttachmentInfo",id:12}}},NoteStoreProto:{fields:{document:{type:"Document",id:2}}},Document:{fields:{version:{type:"int32",id:2},note:{type:"Note",id:3}}},Note:{fields:{noteText:{type:"string",id:2},attributeRun:{rule:"repeated",type:"AttributeRun",id:5,options:{packed:!1}}}},MergableDataProto:{fields:{mergableDataObject:{type:"MergableDataObject",id:2}}},MergableDataObject:{fields:{version:{type:"int32",id:2},mergeableDataObjectData:{type:"MergeableDataObjectData",id:3}}},MergeableDataObjectData:{fields:{mergeableDataObjectEntry:{rule:"repeated",type:"MergeableDataObjectEntry",id:3,options:{packed:!1}},mergeableDataObjectKeyItem:{rule:"repeated",type:"string",id:4},mergeableDataObjectTypeItem:{rule:"repeated",type:"string",id:5},mergeableDataObjectUuidItem:{rule:"repeated",type:"bytes",id:6}}},MergeableDataObjectEntry:{fields:{registerLatest:{type:"RegisterLatest",id:1},list:{type:"List",id:5},dictionary:{type:"Dictionary",id:6},unknownMessage:{type:"UnknownMergeableDataObjectEntryMessage",id:9},note:{type:"Note",id:10},customMap:{type:"MergeableDataObjectMap",id:13},orderedSet:{type:"OrderedSet",id:16}}},UnknownMergeableDataObjectEntryMessage:{fields:{unknownEntry:{type:"UnknownMergeableDataObjectEntryMessageEntry",id:1}}},UnknownMergeableDataObjectEntryMessageEntry:{fields:{unknownInt1:{type:"int32",id:1},unknownInt2:{type:"int64",id:2}}},MergeableDataObjectMap:{fields:{type:{type:"int32",id:1},mapEntry:{rule:"repeated",type:"MapEntry",id:3,options:{packed:!1}}}},OrderedSet:{fields:{ordering:{type:"OrderedSetOrdering",id:1},elements:{type:"Dictionary",id:2}}},OrderedSetOrdering:{fields:{array:{type:"OrderedSetOrderingArray",id:1},contents:{type:"Dictionary",id:2}}},OrderedSetOrderingArray:{fields:{contents:{type:"Note",id:1},attachment:{rule:"repeated",type:"OrderedSetOrderingArrayAttachment",id:2,options:{packed:!1}}}},OrderedSetOrderingArrayAttachment:{fields:{index:{type:"int32",id:1},uuid:{type:"bytes",id:2}}},List:{fields:{listEntry:{rule:"repeated",type:"ListEntry",id:1,options:{packed:!1}}}},ListEntry:{fields:{id:{type:"ObjectID",id:2},details:{type:"ListEntryDetails",id:3},additionalDetails:{type:"ListEntryDetails",id:4}}},ListEntryDetails:{fields:{listEntryDetailsKey:{type:"ListEntryDetailsKey",id:1},id:{type:"ObjectID",id:2}}},ListEntryDetailsKey:{fields:{listEntryDetailsTypeIndex:{type:"int32",id:1},listEntryDetailsKey:{type:"int32",id:2}}}}}}};var De=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535],Ss=1440,Td=0,Ad=4,kd=9,Nd=5,Sd=[96,7,256,0,8,80,0,8,16,84,8,115,82,7,31,0,8,112,0,8,48,0,9,192,80,7,10,0,8,96,0,8,32,0,9,160,0,8,0,0,8,128,0,8,64,0,9,224,80,7,6,0,8,88,0,8,24,0,9,144,83,7,59,0,8,120,0,8,56,0,9,208,81,7,17,0,8,104,0,8,40,0,9,176,0,8,8,0,8,136,0,8,72,0,9,240,80,7,4,0,8,84,0,8,20,85,8,227,83,7,43,0,8,116,0,8,52,0,9,200,81,7,13,0,8,100,0,8,36,0,9,168,0,8,4,0,8,132,0,8,68,0,9,232,80,7,8,0,8,92,0,8,28,0,9,152,84,7,83,0,8,124,0,8,60,0,9,216,82,7,23,0,8,108,0,8,44,0,9,184,0,8,12,0,8,140,0,8,76,0,9,248,80,7,3,0,8,82,0,8,18,85,8,163,83,7,35,0,8,114,0,8,50,0,9,196,81,7,11,0,8,98,0,8,34,0,9,164,0,8,2,0,8,130,0,8,66,0,9,228,80,7,7,0,8,90,0,8,26,0,9,148,84,7,67,0,8,122,0,8,58,0,9,212,82,7,19,0,8,106,0,8,42,0,9,180,0,8,10,0,8,138,0,8,74,0,9,244,80,7,5,0,8,86,0,8,22,192,8,0,83,7,51,0,8,118,0,8,54,0,9,204,81,7,15,0,8,102,0,8,38,0,9,172,0,8,6,0,8,134,0,8,70,0,9,236,80,7,9,0,8,94,0,8,30,0,9,156,84,7,99,0,8,126,0,8,62,0,9,220,82,7,27,0,8,110,0,8,46,0,9,188,0,8,14,0,8,142,0,8,78,0,9,252,96,7,256,0,8,81,0,8,17,85,8,131,82,7,31,0,8,113,0,8,49,0,9,194,80,7,10,0,8,97,0,8,33,0,9,162,0,8,1,0,8,129,0,8,65,0,9,226,80,7,6,0,8,89,0,8,25,0,9,146,83,7,59,0,8,121,0,8,57,0,9,210,81,7,17,0,8,105,0,8,41,0,9,178,0,8,9,0,8,137,0,8,73,0,9,242,80,7,4,0,8,85,0,8,21,80,8,258,83,7,43,0,8,117,0,8,53,0,9,202,81,7,13,0,8,101,0,8,37,0,9,170,0,8,5,0,8,133,0,8,69,0,9,234,80,7,8,0,8,93,0,8,29,0,9,154,84,7,83,0,8,125,0,8,61,0,9,218,82,7,23,0,8,109,0,8,45,0,9,186,0,8,13,0,8,141,0,8,77,0,9,250,80,7,3,0,8,83,0,8,19,85,8,195,83,7,35,0,8,115,0,8,51,0,9,198,81,7,11,0,8,99,0,8,35,0,9,166,0,8,3,0,8,131,0,8,67,0,9,230,80,7,7,0,8,91,0,8,27,0,9,150,84,7,67,0,8,123,0,8,59,0,9,214,82,7,19,0,8,107,0,8,43,0,9,182,0,8,11,0,8,139,0,8,75,0,9,246,80,7,5,0,8,87,0,8,23,192,8,0,83,7,51,0,8,119,0,8,55,0,9,206,81,7,15,0,8,103,0,8,39,0,9,174,0,8,7,0,8,135,0,8,71,0,9,238,80,7,9,0,8,95,0,8,31,0,9,158,84,7,99,0,8,127,0,8,63,0,9,222,82,7,27,0,8,111,0,8,47,0,9,190,0,8,15,0,8,143,0,8,79,0,9,254,96,7,256,0,8,80,0,8,16,84,8,115,82,7,31,0,8,112,0,8,48,0,9,193,80,7,10,0,8,96,0,8,32,0,9,161,0,8,0,0,8,128,0,8,64,0,9,225,80,7,6,0,8,88,0,8,24,0,9,145,83,7,59,0,8,120,0,8,56,0,9,209,81,7,17,0,8,104,0,8,40,0,9,177,0,8,8,0,8,136,0,8,72,0,9,241,80,7,4,0,8,84,0,8,20,85,8,227,83,7,43,0,8,116,0,8,52,0,9,201,81,7,13,0,8,100,0,8,36,0,9,169,0,8,4,0,8,132,0,8,68,0,9,233,80,7,8,0,8,92,0,8,28,0,9,153,84,7,83,0,8,124,0,8,60,0,9,217,82,7,23,0,8,108,0,8,44,0,9,185,0,8,12,0,8,140,0,8,76,0,9,249,80,7,3,0,8,82,0,8,18,85,8,163,83,7,35,0,8,114,0,8,50,0,9,197,81,7,11,0,8,98,0,8,34,0,9,165,0,8,2,0,8,130,0,8,66,0,9,229,80,7,7,0,8,90,0,8,26,0,9,149,84,7,67,0,8,122,0,8,58,0,9,213,82,7,19,0,8,106,0,8,42,0,9,181,0,8,10,0,8,138,0,8,74,0,9,245,80,7,5,0,8,86,0,8,22,192,8,0,83,7,51,0,8,118,0,8,54,0,9,205,81,7,15,0,8,102,0,8,38,0,9,173,0,8,6,0,8,134,0,8,70,0,9,237,80,7,9,0,8,94,0,8,30,0,9,157,84,7,99,0,8,126,0,8,62,0,9,221,82,7,27,0,8,110,0,8,46,0,9,189,0,8,14,0,8,142,0,8,78,0,9,253,96,7,256,0,8,81,0,8,17,85,8,131,82,7,31,0,8,113,0,8,49,0,9,195,80,7,10,0,8,97,0,8,33,0,9,163,0,8,1,0,8,129,0,8,65,0,9,227,80,7,6,0,8,89,0,8,25,0,9,147,83,7,59,0,8,121,0,8,57,0,9,211,81,7,17,0,8,105,0,8,41,0,9,179,0,8,9,0,8,137,0,8,73,0,9,243,80,7,4,0,8,85,0,8,21,80,8,258,83,7,43,0,8,117,0,8,53,0,9,203,81,7,13,0,8,101,0,8,37,0,9,171,0,8,5,0,8,133,0,8,69,0,9,235,80,7,8,0,8,93,0,8,29,0,9,155,84,7,83,0,8,125,0,8,61,0,9,219,82,7,23,0,8,109,0,8,45,0,9,187,0,8,13,0,8,141,0,8,77,0,9,251,80,7,3,0,8,83,0,8,19,85,8,195,83,7,35,0,8,115,0,8,51,0,9,199,81,7,11,0,8,99,0,8,35,0,9,167,0,8,3,0,8,131,0,8,67,0,9,231,80,7,7,0,8,91,0,8,27,0,9,151,84,7,67,0,8,123,0,8,59,0,9,215,82,7,19,0,8,107,0,8,43,0,9,183,0,8,11,0,8,139,0,8,75,0,9,247,80,7,5,0,8,87,0,8,23,192,8,0,83,7,51,0,8,119,0,8,55,0,9,207,81,7,15,0,8,103,0,8,39,0,9,175,0,8,7,0,8,135,0,8,71,0,9,239,80,7,9,0,8,95,0,8,31,0,9,159,84,7,99,0,8,127,0,8,63,0,9,223,82,7,27,0,8,111,0,8,47,0,9,191,0,8,15,0,8,143,0,8,79,0,9,255],Rd=[80,5,1,87,5,257,83,5,17,91,5,4097,81,5,5,89,5,1025,85,5,65,93,5,16385,80,5,3,88,5,513,84,5,33,92,5,8193,82,5,9,90,5,2049,86,5,129,192,5,24577,80,5,2,87,5,385,83,5,25,91,5,6145,81,5,7,89,5,1537,85,5,97,93,5,24577,80,5,4,88,5,769,84,5,49,92,5,12289,82,5,13,90,5,3073,86,5,193,192,5,24577],Od=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],Dd=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,112,112],Cd=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],Id=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],vt=15;function Xr(){let n=this,t,e,i,r,o,a;function s(c,p,m,y,w,E,d,f,g,v,_){let A,D,T,k,b,C,O,S,M,L,j,Z,F,ee,z;L=0,b=m;do i[c[p+L]]++,L++,b--;while(b!==0);if(i[0]==m)return d[0]=-1,f[0]=0,0;for(S=f[0],C=1;C<=vt&&i[C]===0;C++);for(O=C,S<C&&(S=C),b=vt;b!==0&&i[b]===0;b--);for(T=b,S>b&&(S=b),f[0]=S,ee=1<<C;C<b;C++,ee<<=1)if((ee-=i[C])<0)return-3;if((ee-=i[b])<0)return-3;for(i[b]+=ee,a[1]=C=0,L=1,F=2;--b!==0;)a[F]=C+=i[L],F++,L++;b=0,L=0;do(C=c[p+L])!==0&&(_[a[C]++]=b),L++;while(++b<m);for(m=a[T],a[0]=b=0,L=0,k=-1,Z=-S,o[0]=0,j=0,z=0;O<=T;O++)for(A=i[O];A--!==0;){for(;O>Z+S;){if(k++,Z+=S,z=T-Z,z=z>S?S:z,(D=1<<(C=O-Z))>A+1&&(D-=A+1,F=O,C<z))for(;++C<z&&!((D<<=1)<=i[++F]);)D-=i[F];if(z=1<<C,v[0]+z>Ss)return-3;o[k]=j=v[0],v[0]+=z,k!==0?(a[k]=b,r[0]=C,r[1]=S,C=b>>>Z-S,r[2]=j-o[k-1]-C,g.set(r,(o[k-1]+C)*3)):d[0]=j}for(r[1]=O-Z,L>=m?r[0]=128+64:_[L]<y?(r[0]=_[L]<256?0:32+64,r[2]=_[L++]):(r[0]=E[_[L]-y]+16+64,r[2]=w[_[L++]-y]),D=1<<O-Z,C=b>>>Z;C<z;C+=D)g.set(r,(j+C)*3);for(C=1<<O-1;b&C;C>>>=1)b^=C;for(b^=C,M=(1<<Z)-1;(b&M)!=a[k];)k--,Z-=S,M=(1<<Z)-1}return ee!==0&&T!=1?-5:0}function l(c){let p;for(t||(t=[],e=[],i=new Int32Array(vt+1),r=[],o=new Int32Array(vt),a=new Int32Array(vt+1)),e.length<c&&(e=[]),p=0;p<c;p++)e[p]=0;for(p=0;p<vt+1;p++)i[p]=0;for(p=0;p<3;p++)r[p]=0;o.set(i.subarray(0,vt),0),a.set(i.subarray(0,vt+1),0)}n.inflate_trees_bits=function(c,p,m,y,w){let E;return l(19),t[0]=0,E=s(c,0,19,19,null,null,m,p,y,t,e),E==-3?w.msg="oversubscribed dynamic bit lengths tree":(E==-5||p[0]===0)&&(w.msg="incomplete dynamic bit lengths tree",E=-3),E},n.inflate_trees_dynamic=function(c,p,m,y,w,E,d,f,g){let v;return l(288),t[0]=0,v=s(m,0,c,257,Od,Dd,E,y,f,t,e),v!=0||y[0]===0?(v==-3?g.msg="oversubscribed literal/length tree":v!=-4&&(g.msg="incomplete literal/length tree",v=-3),v):(l(288),v=s(m,c,p,0,Cd,Id,d,w,f,t,e),v!=0||w[0]===0&&c>257?(v==-3?g.msg="oversubscribed distance tree":v==-5?(g.msg="incomplete distance tree",v=-3):v!=-4&&(g.msg="empty distance tree with lengths",v=-3),v):0)}}Xr.inflate_trees_fixed=function(n,t,e,i){return n[0]=kd,t[0]=Nd,e[0]=Sd,i[0]=Rd,0};var Ei=0,cs=1,ps=2,us=3,fs=4,ds=5,ms=6,Gr=7,hs=8,_i=9;function Fd(){let n=this,t,e=0,i,r=0,o=0,a=0,s=0,l=0,c=0,p=0,m,y=0,w,E=0;function d(f,g,v,_,A,D,T,k){let b,C,O,S,M,L,j,Z,F,ee,z,We,U,Ne,G,Q;j=k.next_in_index,Z=k.avail_in,M=T.bitb,L=T.bitk,F=T.write,ee=F<T.read?T.read-F-1:T.end-F,z=De[f],We=De[g];do{for(;L<20;)Z--,M|=(k.read_byte(j++)&255)<<L,L+=8;if(b=M&z,C=v,O=_,Q=(O+b)*3,(S=C[Q])===0){M>>=C[Q+1],L-=C[Q+1],T.win[F++]=C[Q+2],ee--;continue}do{if(M>>=C[Q+1],L-=C[Q+1],S&16){for(S&=15,U=C[Q+2]+(M&De[S]),M>>=S,L-=S;L<15;)Z--,M|=(k.read_byte(j++)&255)<<L,L+=8;b=M&We,C=A,O=D,Q=(O+b)*3,S=C[Q];do if(M>>=C[Q+1],L-=C[Q+1],S&16){for(S&=15;L<S;)Z--,M|=(k.read_byte(j++)&255)<<L,L+=8;if(Ne=C[Q+2]+(M&De[S]),M>>=S,L-=S,ee-=U,F>=Ne)G=F-Ne,F-G>0&&2>F-G?(T.win[F++]=T.win[G++],T.win[F++]=T.win[G++],U-=2):(T.win.set(T.win.subarray(G,G+2),F),F+=2,G+=2,U-=2);else{G=F-Ne;do G+=T.end;while(G<0);if(S=T.end-G,U>S){if(U-=S,F-G>0&&S>F-G)do T.win[F++]=T.win[G++];while(--S!==0);else T.win.set(T.win.subarray(G,G+S),F),F+=S,G+=S,S=0;G=0}}if(F-G>0&&U>F-G)do T.win[F++]=T.win[G++];while(--U!==0);else T.win.set(T.win.subarray(G,G+U),F),F+=U,G+=U,U=0;break}else if(!(S&64))b+=C[Q+2],b+=M&De[S],Q=(O+b)*3,S=C[Q];else return k.msg="invalid distance code",U=k.avail_in-Z,U=L>>3<U?L>>3:U,Z+=U,j-=U,L-=U<<3,T.bitb=M,T.bitk=L,k.avail_in=Z,k.total_in+=j-k.next_in_index,k.next_in_index=j,T.write=F,-3;while(!0);break}if(S&64)return S&32?(U=k.avail_in-Z,U=L>>3<U?L>>3:U,Z+=U,j-=U,L-=U<<3,T.bitb=M,T.bitk=L,k.avail_in=Z,k.total_in+=j-k.next_in_index,k.next_in_index=j,T.write=F,1):(k.msg="invalid literal/length code",U=k.avail_in-Z,U=L>>3<U?L>>3:U,Z+=U,j-=U,L-=U<<3,T.bitb=M,T.bitk=L,k.avail_in=Z,k.total_in+=j-k.next_in_index,k.next_in_index=j,T.write=F,-3);if(b+=C[Q+2],b+=M&De[S],Q=(O+b)*3,(S=C[Q])===0){M>>=C[Q+1],L-=C[Q+1],T.win[F++]=C[Q+2],ee--;break}}while(!0)}while(ee>=258&&Z>=10);return U=k.avail_in-Z,U=L>>3<U?L>>3:U,Z+=U,j-=U,L-=U<<3,T.bitb=M,T.bitk=L,k.avail_in=Z,k.total_in+=j-k.next_in_index,k.next_in_index=j,T.write=F,0}n.init=function(f,g,v,_,A,D){t=Ei,c=f,p=g,m=v,y=_,w=A,E=D,i=null},n.proc=function(f,g,v){let _,A,D,T=0,k=0,b=0,C,O,S,M;for(b=g.next_in_index,C=g.avail_in,T=f.bitb,k=f.bitk,O=f.write,S=O<f.read?f.read-O-1:f.end-O;;)switch(t){case Ei:if(S>=258&&C>=10&&(f.bitb=T,f.bitk=k,g.avail_in=C,g.total_in+=b-g.next_in_index,g.next_in_index=b,f.write=O,v=d(c,p,m,y,w,E,f,g),b=g.next_in_index,C=g.avail_in,T=f.bitb,k=f.bitk,O=f.write,S=O<f.read?f.read-O-1:f.end-O,v!=0)){t=v==1?Gr:_i;break}o=c,i=m,r=y,t=cs;case cs:for(_=o;k<_;){if(C!==0)v=0;else return f.bitb=T,f.bitk=k,g.avail_in=C,g.total_in+=b-g.next_in_index,g.next_in_index=b,f.write=O,f.inflate_flush(g,v);C--,T|=(g.read_byte(b++)&255)<<k,k+=8}if(A=(r+(T&De[_]))*3,T>>>=i[A+1],k-=i[A+1],D=i[A],D===0){a=i[A+2],t=ms;break}if(D&16){s=D&15,e=i[A+2],t=ps;break}if(!(D&64)){o=D,r=A/3+i[A+2];break}if(D&32){t=Gr;break}return t=_i,g.msg="invalid literal/length code",v=-3,f.bitb=T,f.bitk=k,g.avail_in=C,g.total_in+=b-g.next_in_index,g.next_in_index=b,f.write=O,f.inflate_flush(g,v);case ps:for(_=s;k<_;){if(C!==0)v=0;else return f.bitb=T,f.bitk=k,g.avail_in=C,g.total_in+=b-g.next_in_index,g.next_in_index=b,f.write=O,f.inflate_flush(g,v);C--,T|=(g.read_byte(b++)&255)<<k,k+=8}e+=T&De[_],T>>=_,k-=_,o=p,i=w,r=E,t=us;case us:for(_=o;k<_;){if(C!==0)v=0;else return f.bitb=T,f.bitk=k,g.avail_in=C,g.total_in+=b-g.next_in_index,g.next_in_index=b,f.write=O,f.inflate_flush(g,v);C--,T|=(g.read_byte(b++)&255)<<k,k+=8}if(A=(r+(T&De[_]))*3,T>>=i[A+1],k-=i[A+1],D=i[A],D&16){s=D&15,l=i[A+2],t=fs;break}if(!(D&64)){o=D,r=A/3+i[A+2];break}return t=_i,g.msg="invalid distance code",v=-3,f.bitb=T,f.bitk=k,g.avail_in=C,g.total_in+=b-g.next_in_index,g.next_in_index=b,f.write=O,f.inflate_flush(g,v);case fs:for(_=s;k<_;){if(C!==0)v=0;else return f.bitb=T,f.bitk=k,g.avail_in=C,g.total_in+=b-g.next_in_index,g.next_in_index=b,f.write=O,f.inflate_flush(g,v);C--,T|=(g.read_byte(b++)&255)<<k,k+=8}l+=T&De[_],T>>=_,k-=_,t=ds;case ds:for(M=O-l;M<0;)M+=f.end;for(;e!==0;){if(S===0&&(O==f.end&&f.read!==0&&(O=0,S=O<f.read?f.read-O-1:f.end-O),S===0&&(f.write=O,v=f.inflate_flush(g,v),O=f.write,S=O<f.read?f.read-O-1:f.end-O,O==f.end&&f.read!==0&&(O=0,S=O<f.read?f.read-O-1:f.end-O),S===0)))return f.bitb=T,f.bitk=k,g.avail_in=C,g.total_in+=b-g.next_in_index,g.next_in_index=b,f.write=O,f.inflate_flush(g,v);f.win[O++]=f.win[M++],S--,M==f.end&&(M=0),e--}t=Ei;break;case ms:if(S===0&&(O==f.end&&f.read!==0&&(O=0,S=O<f.read?f.read-O-1:f.end-O),S===0&&(f.write=O,v=f.inflate_flush(g,v),O=f.write,S=O<f.read?f.read-O-1:f.end-O,O==f.end&&f.read!==0&&(O=0,S=O<f.read?f.read-O-1:f.end-O),S===0)))return f.bitb=T,f.bitk=k,g.avail_in=C,g.total_in+=b-g.next_in_index,g.next_in_index=b,f.write=O,f.inflate_flush(g,v);v=0,f.win[O++]=a,S--,t=Ei;break;case Gr:if(k>7&&(k-=8,C++,b--),f.write=O,v=f.inflate_flush(g,v),O=f.write,S=O<f.read?f.read-O-1:f.end-O,f.read!=f.write)return f.bitb=T,f.bitk=k,g.avail_in=C,g.total_in+=b-g.next_in_index,g.next_in_index=b,f.write=O,f.inflate_flush(g,v);t=hs;case hs:return v=1,f.bitb=T,f.bitk=k,g.avail_in=C,g.total_in+=b-g.next_in_index,g.next_in_index=b,f.write=O,f.inflate_flush(g,v);case _i:return v=-3,f.bitb=T,f.bitk=k,g.avail_in=C,g.total_in+=b-g.next_in_index,g.next_in_index=b,f.write=O,f.inflate_flush(g,v);default:return v=-2,f.bitb=T,f.bitk=k,g.avail_in=C,g.total_in+=b-g.next_in_index,g.next_in_index=b,f.write=O,f.inflate_flush(g,v)}},n.free=function(){}}var gs=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],fn=0,Vr=1,ys=2,bs=3,xs=4,ws=5,Ti=6,Ai=7,vs=8,Zt=9;function Ld(n,t){let e=this,i=fn,r=0,o=0,a=0,s,l=[0],c=[0],p=new Fd,m=0,y=new Int32Array(Ss*3),w=0,E=new Xr;e.bitk=0,e.bitb=0,e.win=new Uint8Array(t),e.end=t,e.read=0,e.write=0,e.reset=function(d,f){f&&(f[0]=w),i==Ti&&p.free(d),i=fn,e.bitk=0,e.bitb=0,e.read=e.write=0},e.reset(n,null),e.inflate_flush=function(d,f){let g,v,_;return v=d.next_out_index,_=e.read,g=(_<=e.write?e.write:e.end)-_,g>d.avail_out&&(g=d.avail_out),g!==0&&f==-5&&(f=0),d.avail_out-=g,d.total_out+=g,d.next_out.set(e.win.subarray(_,_+g),v),v+=g,_+=g,_==e.end&&(_=0,e.write==e.end&&(e.write=0),g=e.write-_,g>d.avail_out&&(g=d.avail_out),g!==0&&f==-5&&(f=0),d.avail_out-=g,d.total_out+=g,d.next_out.set(e.win.subarray(_,_+g),v),v+=g,_+=g),d.next_out_index=v,e.read=_,f},e.proc=function(d,f){let g,v,_,A,D,T,k,b;for(A=d.next_in_index,D=d.avail_in,v=e.bitb,_=e.bitk,T=e.write,k=T<e.read?e.read-T-1:e.end-T;;){let C,O,S,M,L,j,Z,F;switch(i){case fn:for(;_<3;){if(D!==0)f=0;else return e.bitb=v,e.bitk=_,d.avail_in=D,d.total_in+=A-d.next_in_index,d.next_in_index=A,e.write=T,e.inflate_flush(d,f);D--,v|=(d.read_byte(A++)&255)<<_,_+=8}switch(g=v&7,m=g&1,g>>>1){case 0:v>>>=3,_-=3,g=_&7,v>>>=g,_-=g,i=Vr;break;case 1:C=[],O=[],S=[[]],M=[[]],Xr.inflate_trees_fixed(C,O,S,M),p.init(C[0],O[0],S[0],0,M[0],0),v>>>=3,_-=3,i=Ti;break;case 2:v>>>=3,_-=3,i=bs;break;case 3:return v>>>=3,_-=3,i=Zt,d.msg="invalid block type",f=-3,e.bitb=v,e.bitk=_,d.avail_in=D,d.total_in+=A-d.next_in_index,d.next_in_index=A,e.write=T,e.inflate_flush(d,f)}break;case Vr:for(;_<32;){if(D!==0)f=0;else return e.bitb=v,e.bitk=_,d.avail_in=D,d.total_in+=A-d.next_in_index,d.next_in_index=A,e.write=T,e.inflate_flush(d,f);D--,v|=(d.read_byte(A++)&255)<<_,_+=8}if((~v>>>16&65535)!=(v&65535))return i=Zt,d.msg="invalid stored block lengths",f=-3,e.bitb=v,e.bitk=_,d.avail_in=D,d.total_in+=A-d.next_in_index,d.next_in_index=A,e.write=T,e.inflate_flush(d,f);r=v&65535,v=_=0,i=r!==0?ys:m!==0?Ai:fn;break;case ys:if(D===0||k===0&&(T==e.end&&e.read!==0&&(T=0,k=T<e.read?e.read-T-1:e.end-T),k===0&&(e.write=T,f=e.inflate_flush(d,f),T=e.write,k=T<e.read?e.read-T-1:e.end-T,T==e.end&&e.read!==0&&(T=0,k=T<e.read?e.read-T-1:e.end-T),k===0)))return e.bitb=v,e.bitk=_,d.avail_in=D,d.total_in+=A-d.next_in_index,d.next_in_index=A,e.write=T,e.inflate_flush(d,f);if(f=0,g=r,g>D&&(g=D),g>k&&(g=k),e.win.set(d.read_buf(A,g),T),A+=g,D-=g,T+=g,k-=g,(r-=g)!==0)break;i=m!==0?Ai:fn;break;case bs:for(;_<14;){if(D!==0)f=0;else return e.bitb=v,e.bitk=_,d.avail_in=D,d.total_in+=A-d.next_in_index,d.next_in_index=A,e.write=T,e.inflate_flush(d,f);D--,v|=(d.read_byte(A++)&255)<<_,_+=8}if(o=g=v&16383,(g&31)>29||(g>>5&31)>29)return i=Zt,d.msg="too many length or distance symbols",f=-3,e.bitb=v,e.bitk=_,d.avail_in=D,d.total_in+=A-d.next_in_index,d.next_in_index=A,e.write=T,e.inflate_flush(d,f);if(g=258+(g&31)+(g>>5&31),!s||s.length<g)s=[];else for(b=0;b<g;b++)s[b]=0;v>>>=14,_-=14,a=0,i=xs;case xs:for(;a<4+(o>>>10);){for(;_<3;){if(D!==0)f=0;else return e.bitb=v,e.bitk=_,d.avail_in=D,d.total_in+=A-d.next_in_index,d.next_in_index=A,e.write=T,e.inflate_flush(d,f);D--,v|=(d.read_byte(A++)&255)<<_,_+=8}s[gs[a++]]=v&7,v>>>=3,_-=3}for(;a<19;)s[gs[a++]]=0;if(l[0]=7,g=E.inflate_trees_bits(s,l,c,y,d),g!=0)return f=g,f==-3&&(s=null,i=Zt),e.bitb=v,e.bitk=_,d.avail_in=D,d.total_in+=A-d.next_in_index,d.next_in_index=A,e.write=T,e.inflate_flush(d,f);a=0,i=ws;case ws:for(;g=o,!(a>=258+(g&31)+(g>>5&31));){let ee,z;for(g=l[0];_<g;){if(D!==0)f=0;else return e.bitb=v,e.bitk=_,d.avail_in=D,d.total_in+=A-d.next_in_index,d.next_in_index=A,e.write=T,e.inflate_flush(d,f);D--,v|=(d.read_byte(A++)&255)<<_,_+=8}if(g=y[(c[0]+(v&De[g]))*3+1],z=y[(c[0]+(v&De[g]))*3+2],z<16)v>>>=g,_-=g,s[a++]=z;else{for(b=z==18?7:z-14,ee=z==18?11:3;_<g+b;){if(D!==0)f=0;else return e.bitb=v,e.bitk=_,d.avail_in=D,d.total_in+=A-d.next_in_index,d.next_in_index=A,e.write=T,e.inflate_flush(d,f);D--,v|=(d.read_byte(A++)&255)<<_,_+=8}if(v>>>=g,_-=g,ee+=v&De[b],v>>>=b,_-=b,b=a,g=o,b+ee>258+(g&31)+(g>>5&31)||z==16&&b<1)return s=null,i=Zt,d.msg="invalid bit length repeat",f=-3,e.bitb=v,e.bitk=_,d.avail_in=D,d.total_in+=A-d.next_in_index,d.next_in_index=A,e.write=T,e.inflate_flush(d,f);z=z==16?s[b-1]:0;do s[b++]=z;while(--ee!==0);a=b}}if(c[0]=-1,L=[],j=[],Z=[],F=[],L[0]=9,j[0]=6,g=o,g=E.inflate_trees_dynamic(257+(g&31),1+(g>>5&31),s,L,j,Z,F,y,d),g!=0)return g==-3&&(s=null,i=Zt),f=g,e.bitb=v,e.bitk=_,d.avail_in=D,d.total_in+=A-d.next_in_index,d.next_in_index=A,e.write=T,e.inflate_flush(d,f);p.init(L[0],j[0],y,Z[0],y,F[0]),i=Ti;case Ti:if(e.bitb=v,e.bitk=_,d.avail_in=D,d.total_in+=A-d.next_in_index,d.next_in_index=A,e.write=T,(f=p.proc(e,d,f))!=1)return e.inflate_flush(d,f);if(f=0,p.free(d),A=d.next_in_index,D=d.avail_in,v=e.bitb,_=e.bitk,T=e.write,k=T<e.read?e.read-T-1:e.end-T,m===0){i=fn;break}i=Ai;case Ai:if(e.write=T,f=e.inflate_flush(d,f),T=e.write,k=T<e.read?e.read-T-1:e.end-T,e.read!=e.write)return e.bitb=v,e.bitk=_,d.avail_in=D,d.total_in+=A-d.next_in_index,d.next_in_index=A,e.write=T,e.inflate_flush(d,f);i=vs;case vs:return f=1,e.bitb=v,e.bitk=_,d.avail_in=D,d.total_in+=A-d.next_in_index,d.next_in_index=A,e.write=T,e.inflate_flush(d,f);case Zt:return f=-3,e.bitb=v,e.bitk=_,d.avail_in=D,d.total_in+=A-d.next_in_index,d.next_in_index=A,e.write=T,e.inflate_flush(d,f);default:return f=-2,e.bitb=v,e.bitk=_,d.avail_in=D,d.total_in+=A-d.next_in_index,d.next_in_index=A,e.write=T,e.inflate_flush(d,f)}}},e.free=function(d){e.reset(d,null),e.win=null,y=null},e.set_dictionary=function(d,f,g){e.win.set(d.subarray(f,f+g),0),e.read=e.write=g},e.sync_point=function(){return i==Vr?1:0}}var Pd=32,Md=8,$d=0,Es=1,_s=2,Ts=3,As=4,ks=5,Kr=6,Bn=7,Ns=12,Et=13,Bd=[0,0,255,255];function Ud(){let n=this;n.mode=0,n.method=0,n.was=[0],n.need=0,n.marker=0,n.wbits=0;function t(e){return!e||!e.istate?-2:(e.total_in=e.total_out=0,e.msg=null,e.istate.mode=Bn,e.istate.blocks.reset(e,null),0)}n.inflateEnd=function(e){return n.blocks&&n.blocks.free(e),n.blocks=null,0},n.inflateInit=function(e,i){return e.msg=null,n.blocks=null,i<8||i>15?(n.inflateEnd(e),-2):(n.wbits=i,e.istate.blocks=new Ld(e,1<<i),t(e),0)},n.inflate=function(e,i){let r,o;if(!e||!e.istate||!e.next_in)return-2;let a=e.istate;for(i=i==Ad?-5:0,r=-5;;)switch(a.mode){case $d:if(e.avail_in===0)return r;if(r=i,e.avail_in--,e.total_in++,((a.method=e.read_byte(e.next_in_index++))&15)!=Md){a.mode=Et,e.msg="unknown compression method",a.marker=5;break}if((a.method>>4)+8>a.wbits){a.mode=Et,e.msg="invalid win size",a.marker=5;break}a.mode=Es;case Es:if(e.avail_in===0)return r;if(r=i,e.avail_in--,e.total_in++,o=e.read_byte(e.next_in_index++)&255,((a.method<<8)+o)%31!==0){a.mode=Et,e.msg="incorrect header check",a.marker=5;break}if(!(o&Pd)){a.mode=Bn;break}a.mode=_s;case _s:if(e.avail_in===0)return r;r=i,e.avail_in--,e.total_in++,a.need=(e.read_byte(e.next_in_index++)&255)<<24&4278190080,a.mode=Ts;case Ts:if(e.avail_in===0)return r;r=i,e.avail_in--,e.total_in++,a.need+=(e.read_byte(e.next_in_index++)&255)<<16&16711680,a.mode=As;case As:if(e.avail_in===0)return r;r=i,e.avail_in--,e.total_in++,a.need+=(e.read_byte(e.next_in_index++)&255)<<8&65280,a.mode=ks;case ks:return e.avail_in===0?r:(r=i,e.avail_in--,e.total_in++,a.need+=e.read_byte(e.next_in_index++)&255,a.mode=Kr,2);case Kr:return a.mode=Et,e.msg="need dictionary",a.marker=0,-2;case Bn:if(r=a.blocks.proc(e,r),r==-3){a.mode=Et,a.marker=0;break}if(r==0&&(r=i),r!=1)return r;r=i,a.blocks.reset(e,a.was),a.mode=Ns;case Ns:return e.avail_in=0,1;case Et:return-3;default:return-2}},n.inflateSetDictionary=function(e,i,r){let o=0,a=r;if(!e||!e.istate||e.istate.mode!=Kr)return-2;let s=e.istate;return a>=1<<s.wbits&&(a=(1<<s.wbits)-1,o=r-a),s.blocks.set_dictionary(i,o,a),s.mode=Bn,0},n.inflateSync=function(e){let i,r,o,a,s;if(!e||!e.istate)return-2;let l=e.istate;if(l.mode!=Et&&(l.mode=Et,l.marker=0),(i=e.avail_in)===0)return-5;for(r=e.next_in_index,o=l.marker;i!==0&&o<4;)e.read_byte(r)==Bd[o]?o++:e.read_byte(r)!==0?o=0:o=4-o,r++,i--;return e.total_in+=r-e.next_in_index,e.next_in_index=r,e.avail_in=i,l.marker=o,o!=4?-3:(a=e.total_in,s=e.total_out,t(e),e.total_in=a,e.total_out=s,l.mode=Bn,0)},n.inflateSyncPoint=function(e){return!e||!e.istate||!e.istate.blocks?-2:e.istate.blocks.sync_point()}}function Rs(){}Rs.prototype={inflateInit(n){let t=this;return t.istate=new Ud,n||(n=15),t.istate.inflateInit(t,n)},inflate(n){let t=this;return t.istate?t.istate.inflate(t,n):-2},inflateEnd(){let n=this;if(!n.istate)return-2;let t=n.istate.inflateEnd(n);return n.istate=null,t},inflateSync(){let n=this;return n.istate?n.istate.inflateSync(n):-2},inflateSetDictionary(n,t){let e=this;return e.istate?e.istate.inflateSetDictionary(e,n,t):-2},read_byte(n){return this.next_in[n]},read_buf(n,t){return this.next_in.subarray(n,n+t)}};function Os(n){let t=this,e=new Rs,i=n&&n.chunkSize?Math.floor(n.chunkSize*2):128*1024,r=Td,o=new Uint8Array(i),a=!1;e.inflateInit(),e.next_out=o,t.append=function(s,l){let c=[],p,m,y=0,w=0,E=0;if(s.length!==0){e.next_in_index=0,e.next_in=s,e.avail_in=s.length;do{if(e.next_out_index=0,e.avail_out=i,e.avail_in===0&&!a&&(e.next_in_index=0,a=!0),p=e.inflate(r),a&&p===-5){if(e.avail_in!==0)throw new Error("inflating: bad input")}else if(p!==0&&p!==1)throw new Error("inflating: "+e.msg);if((a||p===1)&&e.avail_in===s.length)throw new Error("inflating: bad input");e.next_out_index&&(e.next_out_index===i?c.push(new Uint8Array(o)):c.push(o.subarray(0,e.next_out_index))),E+=e.next_out_index,l&&e.next_in_index>0&&e.next_in_index!=y&&(l(e.next_in_index),y=e.next_in_index)}while(e.avail_in>0||e.avail_out===0);return c.length>1?(m=new Uint8Array(E),c.forEach(function(d){m.set(d,w),w+=d.length})):m=c[0]?new Uint8Array(c[0]):new Uint8Array,m}},t.flush=function(){e.inflateEnd()}}var Ds="/",db=new Date(2107,11,31),mb=new Date(1980,0,1),_e=void 0,dn="undefined",ki="function";var Un=class{constructor(t){return class extends TransformStream{constructor(e,i){let r=new t(i);super({transform(o,a){a.enqueue(r.append(o))},flush(o){let a=r.flush();a&&o.enqueue(a)}})}}}};var jd=64,Cs=2;try{typeof navigator!=dn&&navigator.hardwareConcurrency&&(Cs=navigator.hardwareConcurrency)}catch(n){}var Hd={chunkSize:512*1024,maxWorkers:Cs,terminateWorkerTimeout:5e3,useWebWorkers:!0,useCompressionStream:!0,workerScripts:_e,CompressionStreamNative:typeof CompressionStream!=dn&&CompressionStream,DecompressionStreamNative:typeof DecompressionStream!=dn&&DecompressionStream},Tt=Object.assign({},Hd);function Jr(){return Tt}function Is(n){return Math.max(n.chunkSize,jd)}function Ni(n){let{baseURL:t,chunkSize:e,maxWorkers:i,terminateWorkerTimeout:r,useCompressionStream:o,useWebWorkers:a,Deflate:s,Inflate:l,CompressionStream:c,DecompressionStream:p,workerScripts:m}=n;if(_t("baseURL",t),_t("chunkSize",e),_t("maxWorkers",i),_t("terminateWorkerTimeout",r),_t("useCompressionStream",o),_t("useWebWorkers",a),s&&(Tt.CompressionStream=new Un(s)),l&&(Tt.DecompressionStream=new Un(l)),_t("CompressionStream",c),_t("DecompressionStream",p),m!==_e){let{deflate:y,inflate:w}=m;if((y||w)&&(Tt.workerScripts||(Tt.workerScripts={})),y){if(!Array.isArray(y))throw new Error("workerScripts.deflate must be an array");Tt.workerScripts.deflate=y}if(w){if(!Array.isArray(w))throw new Error("workerScripts.inflate must be an array");Tt.workerScripts.inflate=w}}}function _t(n,t){t!==_e&&(Tt[n]=t)}var Fs=[];for(let n=0;n<256;n++){let t=n;for(let e=0;e<8;e++)t&1?t=t>>>1^3988292384:t=t>>>1;Fs[n]=t}var ct=class{constructor(t){this.crc=t||-1}append(t){let e=this.crc|0;for(let i=0,r=t.length|0;i<r;i++)e=e>>>8^Fs[(e^t[i])&255];this.crc=e}get(){return~this.crc}};var jn=class extends TransformStream{constructor(){let t,e=new ct;super({transform(i,r){e.append(i),r.enqueue(i)},flush(){let i=new Uint8Array(4);new DataView(i.buffer).setUint32(0,e.get()),t.value=i}}),t=this}};function Ls(n){if(typeof TextEncoder=="undefined"){n=unescape(encodeURIComponent(n));let t=new Uint8Array(n.length);for(let e=0;e<t.length;e++)t[e]=n.charCodeAt(e);return t}else return new TextEncoder().encode(n)}var Te={concat(n,t){if(n.length===0||t.length===0)return n.concat(t);let e=n[n.length-1],i=Te.getPartial(e);return i===32?n.concat(t):Te._shiftRight(t,i,e|0,n.slice(0,n.length-1))},bitLength(n){let t=n.length;if(t===0)return 0;let e=n[t-1];return(t-1)*32+Te.getPartial(e)},clamp(n,t){if(n.length*32<t)return n;n=n.slice(0,Math.ceil(t/32));let e=n.length;return t=t&31,e>0&&t&&(n[e-1]=Te.partial(t,n[e-1]&2147483648>>t-1,1)),n},partial(n,t,e){return n===32?t:(e?t|0:t<<32-n)+n*1099511627776},getPartial(n){return Math.round(n/1099511627776)||32},_shiftRight(n,t,e,i){for(i===void 0&&(i=[]);t>=32;t-=32)i.push(e),e=0;if(t===0)return i.concat(n);for(let a=0;a<n.length;a++)i.push(e|n[a]>>>t),e=n[a]<<32-t;let r=n.length?n[n.length-1]:0,o=Te.getPartial(r);return i.push(Te.partial(t+o&31,t+o>32?e:i.pop(),1)),i}},Hn={bytes:{fromBits(n){let e=Te.bitLength(n)/8,i=new Uint8Array(e),r;for(let o=0;o<e;o++)o&3||(r=n[o/4]),i[o]=r>>>24,r<<=8;return i},toBits(n){let t=[],e,i=0;for(e=0;e<n.length;e++)i=i<<8|n[e],(e&3)===3&&(t.push(i),i=0);return e&3&&t.push(Te.partial(8*(e&3),i)),t}}},Ps={};Ps.sha1=class{constructor(n){let t=this;t.blockSize=512,t._init=[1732584193,4023233417,2562383102,271733878,3285377520],t._key=[1518500249,1859775393,2400959708,3395469782],n?(t._h=n._h.slice(0),t._buffer=n._buffer.slice(0),t._length=n._length):t.reset()}reset(){let n=this;return n._h=n._init.slice(0),n._buffer=[],n._length=0,n}update(n){let t=this;typeof n=="string"&&(n=Hn.utf8String.toBits(n));let e=t._buffer=Te.concat(t._buffer,n),i=t._length,r=t._length=i+Te.bitLength(n);if(r>9007199254740991)throw new Error("Cannot hash more than 2^53 - 1 bits");let o=new Uint32Array(e),a=0;for(let s=t.blockSize+i-(t.blockSize+i&t.blockSize-1);s<=r;s+=t.blockSize)t._block(o.subarray(16*a,16*(a+1))),a+=1;return e.splice(0,16*a),t}finalize(){let n=this,t=n._buffer,e=n._h;t=Te.concat(t,[Te.partial(1,1)]);for(let i=t.length+2;i&15;i++)t.push(0);for(t.push(Math.floor(n._length/4294967296)),t.push(n._length|0);t.length;)n._block(t.splice(0,16));return n.reset(),e}_f(n,t,e,i){if(n<=19)return t&e|~t&i;if(n<=39)return t^e^i;if(n<=59)return t&e|t&i|e&i;if(n<=79)return t^e^i}_S(n,t){return t<<n|t>>>32-n}_block(n){let t=this,e=t._h,i=Array(80);for(let c=0;c<16;c++)i[c]=n[c];let r=e[0],o=e[1],a=e[2],s=e[3],l=e[4];for(let c=0;c<=79;c++){c>=16&&(i[c]=t._S(1,i[c-3]^i[c-8]^i[c-14]^i[c-16]));let p=t._S(5,r)+t._f(c,o,a,s)+l+i[c]+t._key[Math.floor(c/20)]|0;l=s,s=a,a=t._S(30,o),o=r,r=p}e[0]=e[0]+r|0,e[1]=e[1]+o|0,e[2]=e[2]+a|0,e[3]=e[3]+s|0,e[4]=e[4]+l|0}};var Qr={};Qr.aes=class{constructor(n){let t=this;t._tables=[[[],[],[],[],[]],[[],[],[],[],[]]],t._tables[0][0][0]||t._precompute();let e=t._tables[0][4],i=t._tables[1],r=n.length,o,a,s,l=1;if(r!==4&&r!==6&&r!==8)throw new Error("invalid aes key size");for(t._key=[a=n.slice(0),s=[]],o=r;o<4*r+28;o++){let c=a[o-1];(o%r===0||r===8&&o%r===4)&&(c=e[c>>>24]<<24^e[c>>16&255]<<16^e[c>>8&255]<<8^e[c&255],o%r===0&&(c=c<<8^c>>>24^l<<24,l=l<<1^(l>>7)*283)),a[o]=a[o-r]^c}for(let c=0;o;c++,o--){let p=a[c&3?o:o-4];o<=4||c<4?s[c]=p:s[c]=i[0][e[p>>>24]]^i[1][e[p>>16&255]]^i[2][e[p>>8&255]]^i[3][e[p&255]]}}encrypt(n){return this._crypt(n,0)}decrypt(n){return this._crypt(n,1)}_precompute(){let n=this._tables[0],t=this._tables[1],e=n[4],i=t[4],r=[],o=[],a,s,l,c;for(let p=0;p<256;p++)o[(r[p]=p<<1^(p>>7)*283)^p]=p;for(let p=a=0;!e[p];p^=s||1,a=o[a]||1){let m=a^a<<1^a<<2^a<<3^a<<4;m=m>>8^m&255^99,e[p]=m,i[m]=p,c=r[l=r[s=r[p]]];let y=c*16843009^l*65537^s*257^p*16843008,w=r[m]*257^m*16843008;for(let E=0;E<4;E++)n[E][p]=w=w<<24^w>>>8,t[E][m]=y=y<<24^y>>>8}for(let p=0;p<5;p++)n[p]=n[p].slice(0),t[p]=t[p].slice(0)}_crypt(n,t){if(n.length!==4)throw new Error("invalid aes block size");let e=this._key[t],i=e.length/4-2,r=[0,0,0,0],o=this._tables[t],a=o[0],s=o[1],l=o[2],c=o[3],p=o[4],m=n[0]^e[0],y=n[t?3:1]^e[1],w=n[2]^e[2],E=n[t?1:3]^e[3],d=4,f,g,v;for(let _=0;_<i;_++)f=a[m>>>24]^s[y>>16&255]^l[w>>8&255]^c[E&255]^e[d],g=a[y>>>24]^s[w>>16&255]^l[E>>8&255]^c[m&255]^e[d+1],v=a[w>>>24]^s[E>>16&255]^l[m>>8&255]^c[y&255]^e[d+2],E=a[E>>>24]^s[m>>16&255]^l[y>>8&255]^c[w&255]^e[d+3],d+=4,m=f,y=g,w=v;for(let _=0;_<4;_++)r[t?3&-_:_]=p[m>>>24]<<24^p[y>>16&255]<<16^p[w>>8&255]<<8^p[E&255]^e[d++],f=m,m=y,y=w,w=E,E=f;return r}};var Ms={getRandomValues(n){let t=new Uint32Array(n.buffer),e=i=>{let r=987654321,o=4294967295;return function(){return r=36969*(r&65535)+(r>>16)&o,i=18e3*(i&65535)+(i>>16)&o,(((r<<16)+i&o)/4294967296+.5)*(Math.random()>.5?1:-1)}};for(let i=0,r;i<n.length;i+=4){let o=e((r||Math.random())*4294967296);r=o()*987654071,t[i/4]=o()*4294967296|0}return n}},eo={};eo.ctrGladman=class{constructor(n,t){this._prf=n,this._initIv=t,this._iv=t}reset(){this._iv=this._initIv}update(n){return this.calculate(this._prf,n,this._iv)}incWord(n){if((n>>24&255)===255){let t=n>>16&255,e=n>>8&255,i=n&255;t===255?(t=0,e===255?(e=0,i===255?i=0:++i):++e):++t,n=0,n+=t<<16,n+=e<<8,n+=i}else n+=1<<24;return n}incCounter(n){(n[0]=this.incWord(n[0]))===0&&(n[1]=this.incWord(n[1]))}calculate(n,t,e){let i;if(!(i=t.length))return[];let r=Te.bitLength(t);for(let o=0;o<i;o+=4){this.incCounter(e);let a=n.encrypt(e);t[o]^=a[0],t[o+1]^=a[1],t[o+2]^=a[2],t[o+3]^=a[3]}return Te.clamp(t,r)}};var At={importKey(n){return new At.hmacSha1(Hn.bytes.toBits(n))},pbkdf2(n,t,e,i){if(e=e||1e4,i<0||e<0)throw new Error("invalid params to pbkdf2");let r=(i>>5)+1<<2,o,a,s,l,c,p=new ArrayBuffer(r),m=new DataView(p),y=0,w=Te;for(t=Hn.bytes.toBits(t),c=1;y<(r||1);c++){for(o=a=n.encrypt(w.concat(t,[c])),s=1;s<e;s++)for(a=n.encrypt(a),l=0;l<a.length;l++)o[l]^=a[l];for(s=0;y<(r||1)&&s<o.length;s++)m.setInt32(y,o[s]),y+=4}return p.slice(0,i/8)}};At.hmacSha1=class{constructor(n){let t=this,e=t._hash=Ps.sha1,i=[[],[]];t._baseHash=[new e,new e];let r=t._baseHash[0].blockSize/32;n.length>r&&(n=new e().update(n).finalize());for(let o=0;o<r;o++)i[0][o]=n[o]^909522486,i[1][o]=n[o]^1549556828;t._baseHash[0].update(i[0]),t._baseHash[1].update(i[1]),t._resultHash=new e(t._baseHash[0])}reset(){let n=this;n._resultHash=new n._hash(n._baseHash[0]),n._updated=!1}update(n){let t=this;t._updated=!0,t._resultHash.update(n)}digest(){let n=this,t=n._resultHash.finalize(),e=new n._hash(n._baseHash[1]).update(t).finalize();return n.reset(),e}encrypt(n){if(this._updated)throw new Error("encrypt on already updated hmac called!");return this.update(n),this.digest(n)}};var qd=typeof crypto!="undefined"&&typeof crypto.getRandomValues=="function",kt="Invalid password",Yt="Invalid signature",pt="zipjs-abort-check-password";function Si(n){return qd?crypto.getRandomValues(n):Ms.getRandomValues(n)}var mn=16,Wd="raw",Us={name:"PBKDF2"},Zd={name:"HMAC"},Yd="SHA-1",zd=Object.assign({hash:Zd},Us),to=Object.assign({iterations:1e3,hash:{name:Yd}},Us),Gd=["deriveBits"],Wn=[8,12,16],qn=[16,24,32],Nt=10,Vd=[0,0,0,0],js="undefined",Hs="function",Di=typeof crypto!=js,zn=Di&&crypto.subtle,qs=Di&&typeof zn!=js,Je=Hn.bytes,Kd=Qr.aes,Xd=eo.ctrGladman,Jd=At.hmacSha1,$s=Di&&qs&&typeof zn.importKey==Hs,Bs=Di&&qs&&typeof zn.deriveBits==Hs,Ri=class extends TransformStream{constructor({password:t,signed:e,encryptionStrength:i,checkPasswordOnly:r}){super({start(){Object.assign(this,{ready:new Promise(o=>this.resolveReady=o),password:t,signed:e,strength:i-1,pending:new Uint8Array})},async transform(o,a){let s=this,{password:l,strength:c,resolveReady:p,ready:m}=s;l?(await Qd(s,c,l,Fe(o,0,Wn[c]+2)),o=Fe(o,Wn[c]+2),r?a.error(new Error(pt)):p()):await m;let y=new Uint8Array(o.length-Nt-(o.length-Nt)%mn);a.enqueue(Ws(s,o,y,0,Nt,!0))},async flush(o){let{signed:a,ctr:s,hmac:l,pending:c,ready:p}=this;await p;let m=Fe(c,0,c.length-Nt),y=Fe(c,c.length-Nt),w=new Uint8Array;if(m.length){let E=Yn(Je,m);l.update(E);let d=s.update(E);w=Zn(Je,d)}if(a){let E=Fe(Zn(Je,l.digest()),0,Nt);for(let d=0;d<Nt;d++)if(E[d]!=y[d])throw new Error(Yt)}o.enqueue(w)}})}},Oi=class extends TransformStream{constructor({password:t,encryptionStrength:e}){let i;super({start(){Object.assign(this,{ready:new Promise(r=>this.resolveReady=r),password:t,strength:e-1,pending:new Uint8Array})},async transform(r,o){let a=this,{password:s,strength:l,resolveReady:c,ready:p}=a,m=new Uint8Array;s?(m=await em(a,l,s),c()):await p;let y=new Uint8Array(m.length+r.length-r.length%mn);y.set(m,0),o.enqueue(Ws(a,r,y,m.length,0))},async flush(r){let{ctr:o,hmac:a,pending:s,ready:l}=this;await l;let c=new Uint8Array;if(s.length){let p=o.update(Yn(Je,s));a.update(p),c=Zn(Je,p)}i.signature=Zn(Je,a.digest()).slice(0,Nt),r.enqueue(no(c,i.signature))}}),i=this}};function Ws(n,t,e,i,r,o){let{ctr:a,hmac:s,pending:l}=n,c=t.length-r;l.length&&(t=no(l,t),e=im(e,c-c%mn));let p;for(p=0;p<=c-mn;p+=mn){let m=Yn(Je,Fe(t,p,p+mn));o&&s.update(m);let y=a.update(m);o||s.update(y),e.set(Zn(Je,y),p+i)}return n.pending=Fe(t,p),e}async function Qd(n,t,e,i){let r=await Zs(n,t,e,Fe(i,0,Wn[t])),o=Fe(i,Wn[t]);if(r[0]!=o[0]||r[1]!=o[1])throw new Error(kt)}async function em(n,t,e){let i=Si(new Uint8Array(Wn[t])),r=await Zs(n,t,e,i);return no(i,r)}async function Zs(n,t,e,i){n.password=null;let r=Ls(e),o=await tm(Wd,r,zd,!1,Gd),a=await nm(Object.assign({salt:i},to),o,8*(qn[t]*2+2)),s=new Uint8Array(a),l=Yn(Je,Fe(s,0,qn[t])),c=Yn(Je,Fe(s,qn[t],qn[t]*2)),p=Fe(s,qn[t]*2);return Object.assign(n,{keys:{key:l,authentication:c,passwordVerification:p},ctr:new Xd(new Kd(l),Array.from(Vd)),hmac:new Jd(c)}),p}async function tm(n,t,e,i,r){if($s)try{return await zn.importKey(n,t,e,i,r)}catch(o){return $s=!1,At.importKey(t)}else return At.importKey(t)}async function nm(n,t,e){if(Bs)try{return await zn.deriveBits(n,t,e)}catch(i){return Bs=!1,At.pbkdf2(t,n.salt,to.iterations,e)}else return At.pbkdf2(t,n.salt,to.iterations,e)}function no(n,t){let e=n;return n.length+t.length&&(e=new Uint8Array(n.length+t.length),e.set(n,0),e.set(t,n.length)),e}function im(n,t){if(t&&t>n.length){let e=n;n=new Uint8Array(t),n.set(e,0)}return n}function Fe(n,t,e){return n.subarray(t,e)}function Zn(n,t){return n.fromBits(t)}function Yn(n,t){return n.toBits(t)}var hn=12,Ci=class extends TransformStream{constructor({password:t,passwordVerification:e,checkPasswordOnly:i}){super({start(){Object.assign(this,{password:t,passwordVerification:e}),Vs(this,t)},transform(r,o){let a=this;if(a.password){let s=Ys(a,r.subarray(0,hn));if(a.password=null,s[hn-1]!=a.passwordVerification)throw new Error(kt);r=r.subarray(hn)}i?o.error(new Error(pt)):o.enqueue(Ys(a,r))}})}},Ii=class extends TransformStream{constructor({password:t,passwordVerification:e}){super({start(){Object.assign(this,{password:t,passwordVerification:e}),Vs(this,t)},transform(i,r){let o=this,a,s;if(o.password){o.password=null;let l=Si(new Uint8Array(hn));l[hn-1]=o.passwordVerification,a=new Uint8Array(i.length+l.length),a.set(zs(o,l),0),s=hn}else a=new Uint8Array(i.length),s=0;a.set(zs(o,i),s),r.enqueue(a)}})}};function Ys(n,t){let e=new Uint8Array(t.length);for(let i=0;i<t.length;i++)e[i]=Ks(n)^t[i],io(n,e[i]);return e}function zs(n,t){let e=new Uint8Array(t.length);for(let i=0;i<t.length;i++)e[i]=Ks(n)^t[i],io(n,t[i]);return e}function Vs(n,t){let e=[305419896,591751049,878082192];Object.assign(n,{keys:e,crcKey0:new ct(e[0]),crcKey2:new ct(e[2])});for(let i=0;i<t.length;i++)io(n,t.charCodeAt(i))}function io(n,t){let[e,i,r]=n.keys;n.crcKey0.append([t]),e=~n.crcKey0.get(),i=Gs(Math.imul(Gs(i+Xs(e)),134775813)+1),n.crcKey2.append([i>>>24]),r=~n.crcKey2.get(),n.keys=[e,i,r]}function Ks(n){let t=n.keys[2]|2;return Xs(Math.imul(t,t^1)>>>8)}function Xs(n){return n&255}function Gs(n){return n&4294967295}var Js="deflate-raw",Fi=class extends TransformStream{constructor(t,{chunkSize:e,CompressionStream:i,CompressionStreamNative:r}){super({});let{compressed:o,encrypted:a,useCompressionStream:s,zipCrypto:l,signed:c,level:p}=t,m=this,y,w,E=Qs(super.readable);(!a||l)&&c&&(y=new jn,E=Qe(E,y)),o&&(E=tl(E,s,{level:p,chunkSize:e},r,i)),a&&(l?E=Qe(E,new Ii(t)):(w=new Oi(t),E=Qe(E,w))),el(m,E,async()=>{let d;a&&!l&&(d=w.signature),(!a||l)&&c&&(d=new DataView(y.value.buffer).getUint32(0)),m.signature=d})}},Li=class extends TransformStream{constructor(t,{chunkSize:e,DecompressionStream:i,DecompressionStreamNative:r}){super({});let{zipCrypto:o,encrypted:a,signed:s,signature:l,compressed:c,useCompressionStream:p}=t,m,y,w=Qs(super.readable);a&&(o?w=Qe(w,new Ci(t)):(y=new Ri(t),w=Qe(w,y))),c&&(w=tl(w,p,{chunkSize:e},r,i)),(!a||o)&&s&&(m=new jn,w=Qe(w,m)),el(this,w,async()=>{if((!a||o)&&s){let E=new DataView(m.value.buffer);if(l!=E.getUint32(0,!1))throw new Error(Yt)}})}};function Qs(n){return Qe(n,new TransformStream({transform(t,e){t&&t.length&&e.enqueue(t)}}))}function el(n,t,e){t=Qe(t,new TransformStream({flush:e})),Object.defineProperty(n,"readable",{get(){return t}})}function tl(n,t,e,i,r){try{let o=t&&i?i:r;n=Qe(n,new o(Js,e))}catch(o){if(t)n=Qe(n,new r(Js,e));else throw o}return n}function Qe(n,t){return n.pipeThrough(t)}var nl="message",il="start",rl="pull",ro="data",ol="ack",al="close",sl="deflate",Mi="inflate";var Pi=class extends TransformStream{constructor(t,e){super({});let i=this,{codecType:r}=t,o;r.startsWith(sl)?o=Fi:r.startsWith(Mi)&&(o=Li);let a=0,s=new o(t,e),l=super.readable,c=new TransformStream({transform(p,m){p&&p.length&&(a+=p.length,m.enqueue(p))},flush(){let{signature:p}=s;Object.assign(i,{signature:p,size:a})}});Object.defineProperty(i,"readable",{get(){return l.pipeThrough(s).pipeThrough(c)}})}};var rm=typeof Worker!=dn;var gn=class{constructor(t,{readable:e,writable:i},{options:r,config:o,streamOptions:a,useWebWorkers:s,transferStreams:l,scripts:c},p){let{signal:m}=a;return Object.assign(t,{busy:!0,readable:e.pipeThrough(new ao(e,a,o),{signal:m}),writable:i,options:Object.assign({},r),scripts:c,transferStreams:l,terminate(){let{worker:y,busy:w}=t;y&&!w&&(y.terminate(),t.interface=null)},onTaskFinished(){t.busy=!1,p(t)}}),(s&&rm?am:om)(t,o)}},ao=class extends TransformStream{constructor(t,{onstart:e,onprogress:i,size:r,onend:o},{chunkSize:a}){let s=0;super({start(){e&&oo(e,r)},async transform(l,c){s+=l.length,i&&await oo(i,s,r),c.enqueue(l)},flush(){t.size=s,o&&oo(o,s)}},{highWaterMark:1,size:()=>a})}};async function oo(n,...t){try{await n(...t)}catch(e){}}function om(n,t){return{run:()=>sm(n,t)}}function am(n,{baseURL:t,chunkSize:e}){return n.interface||Object.assign(n,{worker:pm(n.scripts[0],t,n),interface:{run:()=>lm(n,{chunkSize:e})}}),n.interface}async function sm({options:n,readable:t,writable:e,onTaskFinished:i},r){let o=new Pi(n,r);try{await t.pipeThrough(o).pipeTo(e,{preventClose:!0,preventAbort:!0});let{signature:a,size:s}=o;return{signature:a,size:s}}finally{i()}}async function lm(n,t){let e,i,r=new Promise((y,w)=>{e=y,i=w});Object.assign(n,{reader:null,writer:null,resolveResult:e,rejectResult:i,result:r});let{readable:o,options:a,scripts:s}=n,{writable:l,closed:c}=cm(n.writable);so({type:il,scripts:s.slice(1),options:a,config:t,readable:o,writable:l},n)||Object.assign(n,{reader:o.getReader(),writer:l.getWriter()});let m=await r;try{await l.getWriter().close()}catch(y){}return await c,m}function cm(n){let t=n.getWriter(),e,i=new Promise(o=>e=o);return{writable:new WritableStream({async write(o){await t.ready,await t.write(o)},close(){t.releaseLock(),e()},abort(o){return t.abort(o)}}),closed:i}}var ll=!0,cl=!0;function pm(n,t,e){let i={type:"module"},r,o;typeof n==ki&&(n=n());try{r=new URL(n,t)}catch(a){r=n}if(ll)try{o=new Worker(r)}catch(a){ll=!1,o=new Worker(r,i)}else o=new Worker(r,i);return o.addEventListener(nl,a=>um(a,e)),o}function so(n,{worker:t,writer:e,onTaskFinished:i,transferStreams:r}){try{let{value:o,readable:a,writable:s}=n,l=[];if(o&&(n.value=o.buffer,l.push(n.value)),r&&cl?(a&&l.push(a),s&&l.push(s)):n.readable=n.writable=null,l.length)try{return t.postMessage(n,l),!0}catch(c){cl=!1,n.readable=n.writable=null,t.postMessage(n)}else t.postMessage(n)}catch(o){throw e&&e.releaseLock(),i(),o}}async function um({data:n},t){let{type:e,value:i,messageId:r,result:o,error:a}=n,{reader:s,writer:l,resolveResult:c,rejectResult:p,onTaskFinished:m}=t;try{if(a){let{message:w,stack:E,code:d,name:f}=a,g=new Error(w);Object.assign(g,{stack:E,code:d,name:f}),y(g)}else{if(e==rl){let{value:w,done:E}=await s.read();so({type:ro,value:w,done:E,messageId:r},t)}e==ro&&(await l.ready,await l.write(new Uint8Array(i)),so({type:ol,messageId:r},t)),e==al&&y(null,o)}}catch(w){y(w)}function y(w,E){w?p(w):c(E),l&&l.releaseLock(),m()}}var zt=[],lo=[];var pl=0;async function fl(n,t){let{options:e,config:i}=t,{transferStreams:r,useWebWorkers:o,useCompressionStream:a,codecType:s,compressed:l,signed:c,encrypted:p}=e,{workerScripts:m,maxWorkers:y,terminateWorkerTimeout:w}=i;t.transferStreams=r||r===_e;let E=!l&&!c&&!p&&!t.transferStreams;t.useWebWorkers=!E&&(o||o===_e&&i.useWebWorkers),t.scripts=t.useWebWorkers&&m?m[s]:[],e.useCompressionStream=a||a===_e&&i.useCompressionStream;let d,f=zt.find(v=>!v.busy);if(f)ul(f),d=new gn(f,n,t,g);else if(zt.length<y){let v={indexWorker:pl};pl++,zt.push(v),d=new gn(v,n,t,g)}else d=await new Promise(v=>lo.push({resolve:v,stream:n,workerOptions:t}));return d.run();function g(v){if(lo.length){let[{resolve:_,stream:A,workerOptions:D}]=lo.splice(0,1);_(new gn(v,A,D,g))}else v.worker?(ul(v),Number.isFinite(w)&&w>=0&&(v.terminateTimeout=setTimeout(()=>{zt=zt.filter(_=>_!=v),v.terminate()},w))):zt=zt.filter(_=>_!=v)}}function ul(n){let{terminateTimeout:t}=n;t&&(clearTimeout(t),n.terminateTimeout=null)}var fm="Writer iterator completed too soon";var dm="Content-Type";var mm=64*1024,dl="writable",Gn=class{constructor(){this.size=0}init(){this.initialized=!0}},yn=class extends Gn{get readable(){let t=this,{chunkSize:e=mm}=t,i=new ReadableStream({start(){this.chunkOffset=0},async pull(r){let{offset:o=0,size:a,diskNumberStart:s}=i,{chunkOffset:l}=this;r.enqueue(await xe(t,o+l,Math.min(e,a-l),s)),l+e>a?r.close():this.chunkOffset+=e}});return i}};var St=class extends yn{constructor(t){super(),Object.assign(this,{blob:t,size:t.size})}async readUint8Array(t,e){let i=this,r=t+e,a=await(t||r<i.size?i.blob.slice(t,r):i.blob).arrayBuffer();return a.byteLength>e&&(a=a.slice(t,r)),new Uint8Array(a)}},Vn=class extends Gn{constructor(t){super();let e=this,i=new TransformStream,r=[];t&&r.push([dm,t]),Object.defineProperty(e,dl,{get(){return i.writable}}),e.blob=new Response(i.readable,{headers:r}).blob()}getData(){return this.blob}};var $i=class extends Vn{constructor(t){super(t),Object.assign(this,{encoding:t,utf8:!t||t.toLowerCase()=="utf-8"})}async getData(){let{encoding:t,utf8:e}=this,i=await super.getData();if(i.text&&e)return i.text();{let r=new FileReader;return new Promise((o,a)=>{Object.assign(r,{onload:({target:s})=>o(s.result),onerror:()=>a(r.error)}),r.readAsText(i,t)})}}};var co=class extends yn{constructor(t){super(),this.readers=t}async init(){let t=this,{readers:e}=t;t.lastDiskNumber=0,t.lastDiskOffset=0,await Promise.all(e.map(async(i,r)=>{await i.init(),r!=e.length-1&&(t.lastDiskOffset+=i.size),t.size+=i.size})),super.init()}async readUint8Array(t,e,i=0){let r=this,{readers:o}=this,a,s=i;s==-1&&(s=o.length-1);let l=t;for(;l>=o[s].size;)l-=o[s].size,s++;let c=o[s],p=c.size;if(l+e<=p)a=await xe(c,l,e);else{let m=p-l;a=new Uint8Array(e),a.set(await xe(c,l,m)),a.set(await r.readUint8Array(t+m,e-m,i),m)}return r.lastDiskNumber=Math.max(s,r.lastDiskNumber),a}},Bi=class extends Gn{constructor(t,e=4294967295){super();let i=this;Object.assign(i,{diskNumber:0,diskOffset:0,size:0,maxSize:e,availableSize:e});let r,o,a,s=new WritableStream({async write(p){let{availableSize:m}=i;if(a)p.length>=m?(await l(p.slice(0,m)),await c(),i.diskOffset+=r.size,i.diskNumber++,a=null,await this.write(p.slice(m))):await l(p);else{let{value:y,done:w}=await t.next();if(w&&!y)throw new Error(fm);r=y,r.size=0,r.maxSize&&(i.maxSize=r.maxSize),i.availableSize=i.maxSize,await Kn(r),o=y.writable,a=o.getWriter(),await this.write(p)}},async close(){await a.ready,await c()}});Object.defineProperty(i,dl,{get(){return s}});async function l(p){let m=p.length;m&&(await a.ready,await a.write(p),r.size+=m,i.size+=m,i.availableSize-=m)}async function c(){o.size=r.size,await a.close()}}};async function Kn(n,t){n.init&&!n.initialized&&await n.init(t)}function ml(n){return Array.isArray(n)&&(n=new co(n)),n instanceof ReadableStream&&(n={readable:n}),n}function hl(n){n.writable===_e&&typeof n.next==ki&&(n=new Bi(n)),n instanceof WritableStream&&(n={writable:n});let{writable:t}=n;return t.size===_e&&(t.size=0),n instanceof Bi||Object.assign(n,{diskNumber:0,diskOffset:0,availableSize:1/0,maxSize:1/0}),n}function xe(n,t,e,i){return n.readUint8Array(t,e,i)}var gl="\0\u263A\u263B\u2665\u2666\u2663\u2660\u2022\u25D8\u25CB\u25D9\u2642\u2640\u266A\u266B\u263C\u25BA\u25C4\u2195\u203C\xB6\xA7\u25AC\u21A8\u2191\u2193\u2192\u2190\u221F\u2194\u25B2\u25BC !\"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}~\u2302\xC7\xFC\xE9\xE2\xE4\xE0\xE5\xE7\xEA\xEB\xE8\xEF\xEE\xEC\xC4\xC5\xC9\xE6\xC6\xF4\xF6\xF2\xFB\xF9\xFF\xD6\xDC\xA2\xA3\xA5\u20A7\u0192\xE1\xED\xF3\xFA\xF1\xD1\xAA\xBA\xBF\u2310\xAC\xBD\xBC\xA1\xAB\xBB\u2591\u2592\u2593\u2502\u2524\u2561\u2562\u2556\u2555\u2563\u2551\u2557\u255D\u255C\u255B\u2510\u2514\u2534\u252C\u251C\u2500\u253C\u255E\u255F\u255A\u2554\u2569\u2566\u2560\u2550\u256C\u2567\u2568\u2564\u2565\u2559\u2558\u2552\u2553\u256B\u256A\u2518\u250C\u2588\u2584\u258C\u2590\u2580\u03B1\xDF\u0393\u03C0\u03A3\u03C3\xB5\u03C4\u03A6\u0398\u03A9\u03B4\u221E\u03C6\u03B5\u2229\u2261\xB1\u2265\u2264\u2320\u2321\xF7\u2248\xB0\u2219\xB7\u221A\u207F\xB2\u25A0 ".split(""),hm=gl.length==256;function yl(n){if(hm){let t="";for(let e=0;e<n.length;e++)t+=gl[n[e]];return t}else return new TextDecoder().decode(n)}function Ui(n,t){return t&&t.trim().toLowerCase()=="cp437"?yl(n):new TextDecoder(t).decode(n)}var po="filename",uo="rawFilename",fo="comment",mo="rawComment",ho="uncompressedSize",go="compressedSize",yo="offset",ji="diskNumberStart",bo="lastModDate",xo="rawLastModDate",wo="lastAccessDate",bl="rawLastAccessDate",vo="creationDate",xl="rawCreationDate",gm="internalFileAttribute",ym="externalFileAttribute",bm="msDosCompatible",xm="zip64",wm=[po,uo,go,ho,bo,xo,fo,mo,wo,vo,yo,ji,ji,gm,ym,bm,xm,"directory","bitFlag","encrypted","signature","filenameUTF8","commentUTF8","compressionMethod","version","versionMadeBy","extraField","rawExtraField","extraFieldZip64","extraFieldUnicodePath","extraFieldUnicodeComment","extraFieldAES","extraFieldNTFS","extraFieldExtendedTimestamp"],Xn=class{constructor(t){wm.forEach(e=>this[e]=t[e])}};var To="File format is not recognized",Pm="End of central directory not found",Mm="End of Zip64 central directory not found",$m="End of Zip64 central directory locator not found",Bm="Central directory header not found",Um="Local file header not found",jm="Zip64 extra field not found",Hm="File contains encrypted entry",qm="Encryption method not supported",kl="Compression method not supported",Nl="Split zip file",Sl="utf-8",Rl="cp437",Wm=[[ho,4294967295],[go,4294967295],[yo,4294967295],[ji,65535]],Zm={[65535]:{getValue:ae,bytes:4},[4294967295]:{getValue:qi,bytes:8}},Vt=class{constructor(t,e={}){Object.assign(this,{reader:ml(t),options:e,config:Jr()})}async*getEntriesGenerator(t={}){let e=this,{reader:i}=e,{config:r}=e;if(await Kn(i),(i.size===_e||!i.readUint8Array)&&(i=new St(await new Response(i.readable).blob()),await Kn(i)),i.size<22)throw new Error(To);i.chunkSize=Is(r);let o=await Km(i,101010256,i.size,22,65535*16);if(!o){let O=await xe(i,0,4),S=he(O);throw ae(S)==134695760?new Error(Nl):new Error(Pm)}let a=he(o),s=ae(a,12),l=ae(a,16),c=o.offset,p=me(a,20),m=c+22+p,y=me(a,4),w=i.lastDiskNumber||0,E=me(a,6),d=me(a,8),f=0,g=0;if(l==4294967295||s==4294967295||d==65535||E==65535){let O=await xe(i,o.offset-20,20),S=he(O);if(ae(S,0)!=117853008)throw new Error(Mm);l=qi(S,8);let M=await xe(i,l,56,-1),L=he(M),j=o.offset-20-56;if(ae(L,0)!=101075792&&l!=j){let Z=l;l=j,f=l-Z,M=await xe(i,l,56,-1),L=he(M)}if(ae(L,0)!=101075792)throw new Error($m);y==65535&&(y=ae(L,16)),E==65535&&(E=ae(L,20)),d==65535&&(d=qi(L,32)),s==4294967295&&(s=qi(L,40)),l-=s}if(w!=y)throw new Error(Nl);if(l<0||l>=i.size)throw new Error(To);let v=0,_=await xe(i,l,s,E),A=he(_);if(s){let O=o.offset-s;if(ae(A,v)!=33639248&&l!=O){let S=l;l=O,f=l-S,_=await xe(i,l,s,E),A=he(_)}}let D=o.offset-l-(i.lastDiskOffset||0);if(s!=D&&D>=0&&(s=D,_=await xe(i,l,s,E),A=he(_)),l<0||l>=i.size)throw new Error(To);let T=Le(e,t,"filenameEncoding"),k=Le(e,t,"commentEncoding");for(let O=0;O<d;O++){let S=new ko(i,r,e.options);if(ae(A,v)!=33639248)throw new Error(Bm);Dl(S,A,v+6);let M=Boolean(S.bitFlag.languageEncodingFlag),L=v+46,j=L+S.filenameLength,Z=j+S.extraFieldLength,F=me(A,v+4),ee=(F&0)==0,z=_.subarray(L,j),We=me(A,v+32),U=Z+We,Ne=_.subarray(Z,U),G=M,Q=M,lt=ee&&(bn(A,v+38)&16)==16,pn=ae(A,v+42)+f;Object.assign(S,{versionMadeBy:F,msDosCompatible:ee,compressedSize:0,uncompressedSize:0,commentLength:We,directory:lt,offset:pn,diskNumberStart:me(A,v+34),internalFileAttribute:me(A,v+36),externalFileAttribute:ae(A,v+38),rawFilename:z,filenameUTF8:G,commentUTF8:Q,rawExtraField:_.subarray(j,Z)});let[h,u]=await Promise.all([Ui(z,G?Sl:T||Rl),Ui(Ne,Q?Sl:k||Rl)]);Object.assign(S,{rawComment:Ne,filename:h,comment:u,directory:lt||h.endsWith(Ds)}),g=Math.max(pn,g),await Cl(S,S,A,v+6);let R=new Xn(S);R.getData=(N,I)=>S.getData(N,R,I),v=U;let{onprogress:x}=t;if(x)try{await x(O+1,d,new Xn(S))}catch(N){}yield R}let b=Le(e,t,"extractPrependedData"),C=Le(e,t,"extractAppendedData");return b&&(e.prependedData=g>0?await xe(i,0,g):new Uint8Array),e.comment=p?await xe(i,c+22,p):new Uint8Array,C&&(e.appendedData=m<i.size?await xe(i,m,i.size-m):new Uint8Array),!0}async getEntries(t={}){let e=[];for await(let i of this.getEntriesGenerator(t))e.push(i);return e}async close(){}};var ko=class{constructor(t,e,i){Object.assign(this,{reader:t,config:e,options:i})}async getData(t,e,i={}){let r=this,{reader:o,offset:a,diskNumberStart:s,extraFieldAES:l,compressionMethod:c,config:p,bitFlag:m,signature:y,rawLastModDate:w,uncompressedSize:E,compressedSize:d}=r,f=r.localDirectory={},g=await xe(o,a,30,s),v=he(g),_=Le(r,i,"password");if(_=_&&_.length&&_,l&&l.originalCompressionMethod!=99)throw new Error(kl);if(c!=0&&c!=8)throw new Error(kl);if(ae(v,0)!=67324752)throw new Error(Um);Dl(f,v,4),f.rawExtraField=f.extraFieldLength?await xe(o,a+30+f.filenameLength,f.extraFieldLength,s):new Uint8Array,await Cl(r,f,v,4),Object.assign(e,{lastAccessDate:f.lastAccessDate,creationDate:f.creationDate});let A=r.encrypted&&f.encrypted,D=A&&!l;if(A){if(!D&&l.strength===_e)throw new Error(qm);if(!_)throw new Error(Hm)}let T=a+30+f.filenameLength+f.extraFieldLength,k=d,b=o.readable;Object.assign(b,{diskNumberStart:s,offset:T,size:k});let C=Le(r,i,"signal"),O=Le(r,i,"checkPasswordOnly");O&&(t=new WritableStream),t=hl(t),await Kn(t,E);let{writable:S}=t,{onstart:M,onprogress:L,onend:j}=i,Z={options:{codecType:Mi,password:_,zipCrypto:D,encryptionStrength:l&&l.strength,signed:Le(r,i,"checkSignature"),passwordVerification:D&&(m.dataDescriptor?w>>>8&255:y>>>24&255),signature:y,compressed:c!=0,encrypted:A,useWebWorkers:Le(r,i,"useWebWorkers"),useCompressionStream:Le(r,i,"useCompressionStream"),transferStreams:Le(r,i,"transferStreams"),checkPasswordOnly:O},config:p,streamOptions:{signal:C,size:k,onstart:M,onprogress:L,onend:j}},F=0;try{({outputSize:F}=await fl({readable:b,writable:S},Z))}catch(ee){if(!O||ee.message!=pt)throw ee}finally{let ee=Le(r,i,"preventClose");S.size+=F,!ee&&!S.locked&&await S.getWriter().close()}return O?void 0:t.getData?t.getData():S}};function Dl(n,t,e){let i=n.rawBitFlag=me(t,e+2),r=(i&1)==1,o=ae(t,e+6);Object.assign(n,{encrypted:r,version:me(t,e),bitFlag:{level:(i&6)>>1,dataDescriptor:(i&8)==8,languageEncodingFlag:(i&2048)==2048},rawLastModDate:o,lastModDate:Xm(o),filenameLength:me(t,e+22),extraFieldLength:me(t,e+24)})}async function Cl(n,t,e,i){let{rawExtraField:r}=t,o=t.extraField=new Map,a=he(new Uint8Array(r)),s=0;try{for(;s<r.length;){let d=me(a,s),f=me(a,s+2);o.set(d,{type:d,data:r.slice(s+4,s+4+f)}),s+=4+f}}catch(d){}let l=me(e,i+4);Object.assign(t,{signature:ae(e,i+10),uncompressedSize:ae(e,i+18),compressedSize:ae(e,i+14)});let c=o.get(1);c&&(Ym(c,t),t.extraFieldZip64=c);let p=o.get(28789);p&&(await Ol(p,po,uo,t,n),t.extraFieldUnicodePath=p);let m=o.get(25461);m&&(await Ol(m,fo,mo,t,n),t.extraFieldUnicodeComment=m);let y=o.get(39169);y?(zm(y,t,l),t.extraFieldAES=y):t.compressionMethod=l;let w=o.get(10);w&&(Gm(w,t),t.extraFieldNTFS=w);let E=o.get(21589);E&&(Vm(E,t),t.extraFieldExtendedTimestamp=E)}function Ym(n,t){t.zip64=!0;let e=he(n.data),i=Wm.filter(([r,o])=>t[r]==o);for(let r=0,o=0;r<i.length;r++){let[a,s]=i[r];if(t[a]==s){let l=Zm[s];t[a]=n[a]=l.getValue(e,o),o+=l.bytes}else if(n[a])throw new Error(jm)}}async function Ol(n,t,e,i,r){let o=he(n.data),a=new ct;a.append(r[e]);let s=he(new Uint8Array(4));s.setUint32(0,a.get(),!0);let l=ae(o,1);Object.assign(n,{version:bn(o,0),[t]:Ui(n.data.subarray(5)),valid:!r.bitFlag.languageEncodingFlag&&l==ae(s,0)}),n.valid&&(i[t]=n[t],i[t+"UTF8"]=!0)}function zm(n,t,e){let i=he(n.data),r=bn(i,4);Object.assign(n,{vendorVersion:bn(i,0),vendorId:bn(i,2),strength:r,originalCompressionMethod:e,compressionMethod:me(i,5)}),t.compressionMethod=n.compressionMethod}function Gm(n,t){let e=he(n.data),i=4,r;try{for(;i<n.data.length&&!r;){let o=me(e,i),a=me(e,i+2);o==1&&(r=n.data.slice(i+4,i+4+a)),i+=4+a}}catch(o){}try{if(r&&r.length==24){let o=he(r),a=o.getBigUint64(0,!0),s=o.getBigUint64(8,!0),l=o.getBigUint64(16,!0);Object.assign(n,{rawLastModDate:a,rawLastAccessDate:s,rawCreationDate:l});let c=Ao(a),p=Ao(s),m=Ao(l),y={lastModDate:c,lastAccessDate:p,creationDate:m};Object.assign(n,y),Object.assign(t,y)}}catch(o){}}function Vm(n,t){let e=he(n.data),i=bn(e,0),r=[],o=[];(i&1)==1&&(r.push(bo),o.push(xo)),(i&2)==2&&(r.push(wo),o.push(bl)),(i&4)==4&&(r.push(vo),o.push(xl));let a=1;r.forEach((s,l)=>{if(n.data.length>=a+4){let c=ae(e,a);t[s]=n[s]=new Date(c*1e3);let p=o[l];n[p]=c}a+=4})}async function Km(n,t,e,i,r){let o=new Uint8Array(4),a=he(o);Jm(a,0,t);let s=i+r;return await l(i)||await l(Math.min(s,e));async function l(c){let p=e-c,m=await xe(n,p,c);for(let y=m.length-i;y>=0;y--)if(m[y]==o[0]&&m[y+1]==o[1]&&m[y+2]==o[2]&&m[y+3]==o[3])return{offset:p+y,buffer:m.slice(y,y+i).buffer}}}function Le(n,t,e){return t[e]===_e?n.options[e]:t[e]}function Xm(n){let t=(n&4294901760)>>16,e=n&65535;try{return new Date(1980+((t&65024)>>9),((t&480)>>5)-1,t&31,(e&63488)>>11,(e&2016)>>5,(e&31)*2,0)}catch(i){}}function Ao(n){return new Date(Number(n/BigInt(1e4)-BigInt(116444736e5)))}function bn(n,t){return n.getUint8(t)}function me(n,t){return n.getUint16(t,!0)}function ae(n,t){return n.getUint32(t,!0)}function qi(n,t){return Number(n.getBigUint64(t,!0))}function Jm(n,t,e){n.setUint32(t,e,!0)}function he(n){return new DataView(n.buffer)}Ni({Inflate:Os});var Kt=require("obsidian");function Il(n){let t=()=>URL.createObjectURL(new Blob([`const{Array:e,Object:t,Number:n,Math:r,Error:s,Uint8Array:i,Uint16Array:o,Uint32Array:c,Int32Array:f,Map:a,DataView:l,Promise:u,TextEncoder:w,crypto:h,postMessage:d,TransformStream:p,ReadableStream:y,WritableStream:m,CompressionStream:b,DecompressionStream:g}=self;class k{constructor(e){return class extends p{constructor(t,n){const r=new e(n);super({transform(e,t){t.enqueue(r.append(e))},flush(e){const t=r.flush();t&&e.enqueue(t)}})}}}}const v=[];for(let e=0;256>e;e++){let t=e;for(let e=0;8>e;e++)1&t?t=t>>>1^3988292384:t>>>=1;v[e]=t}class S{constructor(e){this.t=e||-1}append(e){let t=0|this.t;for(let n=0,r=0|e.length;r>n;n++)t=t>>>8^v[255&(t^e[n])];this.t=t}get(){return~this.t}}class z extends p{constructor(){let e;const t=new S;super({transform(e,n){t.append(e),n.enqueue(e)},flush(){const n=new i(4);new l(n.buffer).setUint32(0,t.get()),e.value=n}}),e=this}}const C={concat(e,t){if(0===e.length||0===t.length)return e.concat(t);const n=e[e.length-1],r=C.i(n);return 32===r?e.concat(t):C.o(t,r,0|n,e.slice(0,e.length-1))},l(e){const t=e.length;if(0===t)return 0;const n=e[t-1];return 32*(t-1)+C.i(n)},u(e,t){if(32*e.length<t)return e;const n=(e=e.slice(0,r.ceil(t/32))).length;return t&=31,n>0&&t&&(e[n-1]=C.h(t,e[n-1]&2147483648>>t-1,1)),e},h:(e,t,n)=>32===e?t:(n?0|t:t<<32-e)+1099511627776*e,i:e=>r.round(e/1099511627776)||32,o(e,t,n,r){for(void 0===r&&(r=[]);t>=32;t-=32)r.push(n),n=0;if(0===t)return r.concat(e);for(let s=0;s<e.length;s++)r.push(n|e[s]>>>t),n=e[s]<<32-t;const s=e.length?e[e.length-1]:0,i=C.i(s);return r.push(C.h(t+i&31,t+i>32?n:r.pop(),1)),r}},x={p:{m(e){const t=C.l(e)/8,n=new i(t);let r;for(let s=0;t>s;s++)0==(3&s)&&(r=e[s/4]),n[s]=r>>>24,r<<=8;return n},g(e){const t=[];let n,r=0;for(n=0;n<e.length;n++)r=r<<8|e[n],3==(3&n)&&(t.push(r),r=0);return 3&n&&t.push(C.h(8*(3&n),r)),t}}},_=class{constructor(e){const t=this;t.blockSize=512,t.k=[1732584193,4023233417,2562383102,271733878,3285377520],t.v=[1518500249,1859775393,2400959708,3395469782],e?(t.S=e.S.slice(0),t.C=e.C.slice(0),t._=e._):t.reset()}reset(){const e=this;return e.S=e.k.slice(0),e.C=[],e._=0,e}update(e){const t=this;"string"==typeof e&&(e=x.A.g(e));const n=t.C=C.concat(t.C,e),r=t._,i=t._=r+C.l(e);if(i>9007199254740991)throw new s("Cannot hash more than 2^53 - 1 bits");const o=new c(n);let f=0;for(let e=t.blockSize+r-(t.blockSize+r&t.blockSize-1);i>=e;e+=t.blockSize)t.I(o.subarray(16*f,16*(f+1))),f+=1;return n.splice(0,16*f),t}D(){const e=this;let t=e.C;const n=e.S;t=C.concat(t,[C.h(1,1)]);for(let e=t.length+2;15&e;e++)t.push(0);for(t.push(r.floor(e._/4294967296)),t.push(0|e._);t.length;)e.I(t.splice(0,16));return e.reset(),n}V(e,t,n,r){return e>19?e>39?e>59?e>79?void 0:t^n^r:t&n|t&r|n&r:t^n^r:t&n|~t&r}P(e,t){return t<<e|t>>>32-e}I(t){const n=this,s=n.S,i=e(80);for(let e=0;16>e;e++)i[e]=t[e];let o=s[0],c=s[1],f=s[2],a=s[3],l=s[4];for(let e=0;79>=e;e++){16>e||(i[e]=n.P(1,i[e-3]^i[e-8]^i[e-14]^i[e-16]));const t=n.P(5,o)+n.V(e,c,f,a)+l+i[e]+n.v[r.floor(e/20)]|0;l=a,a=f,f=n.P(30,c),c=o,o=t}s[0]=s[0]+o|0,s[1]=s[1]+c|0,s[2]=s[2]+f|0,s[3]=s[3]+a|0,s[4]=s[4]+l|0}},A={getRandomValues(e){const t=new c(e.buffer),n=e=>{let t=987654321;const n=4294967295;return()=>(t=36969*(65535&t)+(t>>16)&n,(((t<<16)+(e=18e3*(65535&e)+(e>>16)&n)&n)/4294967296+.5)*(r.random()>.5?1:-1))};for(let s,i=0;i<e.length;i+=4){const e=n(4294967296*(s||r.random()));s=987654071*e(),t[i/4]=4294967296*e()|0}return e}},I={importKey:e=>new I.R(x.p.g(e)),B(e,t,n,r){if(n=n||1e4,0>r||0>n)throw new s("invalid params to pbkdf2");const i=1+(r>>5)<<2;let o,c,f,a,u;const w=new ArrayBuffer(i),h=new l(w);let d=0;const p=C;for(t=x.p.g(t),u=1;(i||1)>d;u++){for(o=c=e.encrypt(p.concat(t,[u])),f=1;n>f;f++)for(c=e.encrypt(c),a=0;a<c.length;a++)o[a]^=c[a];for(f=0;(i||1)>d&&f<o.length;f++)h.setInt32(d,o[f]),d+=4}return w.slice(0,r/8)},R:class{constructor(e){const t=this,n=t.M=_,r=[[],[]];t.K=[new n,new n];const s=t.K[0].blockSize/32;e.length>s&&(e=(new n).update(e).D());for(let t=0;s>t;t++)r[0][t]=909522486^e[t],r[1][t]=1549556828^e[t];t.K[0].update(r[0]),t.K[1].update(r[1]),t.U=new n(t.K[0])}reset(){const e=this;e.U=new e.M(e.K[0]),e.N=!1}update(e){this.N=!0,this.U.update(e)}digest(){const e=this,t=e.U.D(),n=new e.M(e.K[1]).update(t).D();return e.reset(),n}encrypt(e){if(this.N)throw new s("encrypt on already updated hmac called!");return this.update(e),this.digest(e)}}},D=void 0!==h&&"function"==typeof h.getRandomValues,V="Invalid password",P="Invalid signature",R="zipjs-abort-check-password";function B(e){return D?h.getRandomValues(e):A.getRandomValues(e)}const E=16,M={name:"PBKDF2"},K=t.assign({hash:{name:"HMAC"}},M),U=t.assign({iterations:1e3,hash:{name:"SHA-1"}},M),N=["deriveBits"],O=[8,12,16],T=[16,24,32],W=10,j=[0,0,0,0],H="undefined",L="function",F=typeof h!=H,q=F&&h.subtle,G=F&&typeof q!=H,J=x.p,Q=class{constructor(e){const t=this;t.O=[[[],[],[],[],[]],[[],[],[],[],[]]],t.O[0][0][0]||t.T();const n=t.O[0][4],r=t.O[1],i=e.length;let o,c,f,a=1;if(4!==i&&6!==i&&8!==i)throw new s("invalid aes key size");for(t.v=[c=e.slice(0),f=[]],o=i;4*i+28>o;o++){let e=c[o-1];(o%i==0||8===i&&o%i==4)&&(e=n[e>>>24]<<24^n[e>>16&255]<<16^n[e>>8&255]<<8^n[255&e],o%i==0&&(e=e<<8^e>>>24^a<<24,a=a<<1^283*(a>>7))),c[o]=c[o-i]^e}for(let e=0;o;e++,o--){const t=c[3&e?o:o-4];f[e]=4>=o||4>e?t:r[0][n[t>>>24]]^r[1][n[t>>16&255]]^r[2][n[t>>8&255]]^r[3][n[255&t]]}}encrypt(e){return this.W(e,0)}decrypt(e){return this.W(e,1)}T(){const e=this.O[0],t=this.O[1],n=e[4],r=t[4],s=[],i=[];let o,c,f,a;for(let e=0;256>e;e++)i[(s[e]=e<<1^283*(e>>7))^e]=e;for(let l=o=0;!n[l];l^=c||1,o=i[o]||1){let i=o^o<<1^o<<2^o<<3^o<<4;i=i>>8^255&i^99,n[l]=i,r[i]=l,a=s[f=s[c=s[l]]];let u=16843009*a^65537*f^257*c^16843008*l,w=257*s[i]^16843008*i;for(let n=0;4>n;n++)e[n][l]=w=w<<24^w>>>8,t[n][i]=u=u<<24^u>>>8}for(let n=0;5>n;n++)e[n]=e[n].slice(0),t[n]=t[n].slice(0)}W(e,t){if(4!==e.length)throw new s("invalid aes block size");const n=this.v[t],r=n.length/4-2,i=[0,0,0,0],o=this.O[t],c=o[0],f=o[1],a=o[2],l=o[3],u=o[4];let w,h,d,p=e[0]^n[0],y=e[t?3:1]^n[1],m=e[2]^n[2],b=e[t?1:3]^n[3],g=4;for(let e=0;r>e;e++)w=c[p>>>24]^f[y>>16&255]^a[m>>8&255]^l[255&b]^n[g],h=c[y>>>24]^f[m>>16&255]^a[b>>8&255]^l[255&p]^n[g+1],d=c[m>>>24]^f[b>>16&255]^a[p>>8&255]^l[255&y]^n[g+2],b=c[b>>>24]^f[p>>16&255]^a[y>>8&255]^l[255&m]^n[g+3],g+=4,p=w,y=h,m=d;for(let e=0;4>e;e++)i[t?3&-e:e]=u[p>>>24]<<24^u[y>>16&255]<<16^u[m>>8&255]<<8^u[255&b]^n[g++],w=p,p=y,y=m,m=b,b=w;return i}},X=class{constructor(e,t){this.j=e,this.H=t,this.L=t}reset(){this.L=this.H}update(e){return this.F(this.j,e,this.L)}q(e){if(255==(e>>24&255)){let t=e>>16&255,n=e>>8&255,r=255&e;255===t?(t=0,255===n?(n=0,255===r?r=0:++r):++n):++t,e=0,e+=t<<16,e+=n<<8,e+=r}else e+=1<<24;return e}G(e){0===(e[0]=this.q(e[0]))&&(e[1]=this.q(e[1]))}F(e,t,n){let r;if(!(r=t.length))return[];const s=C.l(t);for(let s=0;r>s;s+=4){this.G(n);const r=e.encrypt(n);t[s]^=r[0],t[s+1]^=r[1],t[s+2]^=r[2],t[s+3]^=r[3]}return C.u(t,s)}},Y=I.R;let Z=F&&G&&typeof q.importKey==L,$=F&&G&&typeof q.deriveBits==L;class ee extends p{constructor({password:e,signed:n,encryptionStrength:r,checkPasswordOnly:o}){super({start(){t.assign(this,{ready:new u((e=>this.J=e)),password:e,signed:n,X:r-1,pending:new i})},async transform(e,t){const n=this,{password:r,X:c,J:f,ready:a}=n;r?(await(async(e,t,n,r)=>{const i=await re(e,t,n,ie(r,0,O[t])),o=ie(r,O[t]);if(i[0]!=o[0]||i[1]!=o[1])throw new s(V)})(n,c,r,ie(e,0,O[c]+2)),e=ie(e,O[c]+2),o?t.error(new s(R)):f()):await a;const l=new i(e.length-W-(e.length-W)%E);t.enqueue(ne(n,e,l,0,W,!0))},async flush(e){const{signed:t,Y:n,Z:r,pending:o,ready:c}=this;await c;const f=ie(o,0,o.length-W),a=ie(o,o.length-W);let l=new i;if(f.length){const e=ce(J,f);r.update(e);const t=n.update(e);l=oe(J,t)}if(t){const e=ie(oe(J,r.digest()),0,W);for(let t=0;W>t;t++)if(e[t]!=a[t])throw new s(P)}e.enqueue(l)}})}}class te extends p{constructor({password:e,encryptionStrength:n}){let r;super({start(){t.assign(this,{ready:new u((e=>this.J=e)),password:e,X:n-1,pending:new i})},async transform(e,t){const n=this,{password:r,X:s,J:o,ready:c}=n;let f=new i;r?(f=await(async(e,t,n)=>{const r=B(new i(O[t]));return se(r,await re(e,t,n,r))})(n,s,r),o()):await c;const a=new i(f.length+e.length-e.length%E);a.set(f,0),t.enqueue(ne(n,e,a,f.length,0))},async flush(e){const{Y:t,Z:n,pending:s,ready:o}=this;await o;let c=new i;if(s.length){const e=t.update(ce(J,s));n.update(e),c=oe(J,e)}r.signature=oe(J,n.digest()).slice(0,W),e.enqueue(se(c,r.signature))}}),r=this}}function ne(e,t,n,r,s,o){const{Y:c,Z:f,pending:a}=e,l=t.length-s;let u;for(a.length&&(t=se(a,t),n=((e,t)=>{if(t&&t>e.length){const n=e;(e=new i(t)).set(n,0)}return e})(n,l-l%E)),u=0;l-E>=u;u+=E){const e=ce(J,ie(t,u,u+E));o&&f.update(e);const s=c.update(e);o||f.update(s),n.set(oe(J,s),u+r)}return e.pending=ie(t,u),n}async function re(n,r,s,o){n.password=null;const c=(e=>{if(void 0===w){const t=new i((e=unescape(encodeURIComponent(e))).length);for(let n=0;n<t.length;n++)t[n]=e.charCodeAt(n);return t}return(new w).encode(e)})(s),f=await(async(e,t,n,r,s)=>{if(!Z)return I.importKey(t);try{return await q.importKey("raw",t,n,!1,s)}catch(e){return Z=!1,I.importKey(t)}})(0,c,K,0,N),a=await(async(e,t,n)=>{if(!$)return I.B(t,e.salt,U.iterations,n);try{return await q.deriveBits(e,t,n)}catch(r){return $=!1,I.B(t,e.salt,U.iterations,n)}})(t.assign({salt:o},U),f,8*(2*T[r]+2)),l=new i(a),u=ce(J,ie(l,0,T[r])),h=ce(J,ie(l,T[r],2*T[r])),d=ie(l,2*T[r]);return t.assign(n,{keys:{key:u,$:h,passwordVerification:d},Y:new X(new Q(u),e.from(j)),Z:new Y(h)}),d}function se(e,t){let n=e;return e.length+t.length&&(n=new i(e.length+t.length),n.set(e,0),n.set(t,e.length)),n}function ie(e,t,n){return e.subarray(t,n)}function oe(e,t){return e.m(t)}function ce(e,t){return e.g(t)}class fe extends p{constructor({password:e,passwordVerification:n,checkPasswordOnly:r}){super({start(){t.assign(this,{password:e,passwordVerification:n}),we(this,e)},transform(e,t){const n=this;if(n.password){const t=le(n,e.subarray(0,12));if(n.password=null,t[11]!=n.passwordVerification)throw new s(V);e=e.subarray(12)}r?t.error(new s(R)):t.enqueue(le(n,e))}})}}class ae extends p{constructor({password:e,passwordVerification:n}){super({start(){t.assign(this,{password:e,passwordVerification:n}),we(this,e)},transform(e,t){const n=this;let r,s;if(n.password){n.password=null;const t=B(new i(12));t[11]=n.passwordVerification,r=new i(e.length+t.length),r.set(ue(n,t),0),s=12}else r=new i(e.length),s=0;r.set(ue(n,e),s),t.enqueue(r)}})}}function le(e,t){const n=new i(t.length);for(let r=0;r<t.length;r++)n[r]=de(e)^t[r],he(e,n[r]);return n}function ue(e,t){const n=new i(t.length);for(let r=0;r<t.length;r++)n[r]=de(e)^t[r],he(e,t[r]);return n}function we(e,n){const r=[305419896,591751049,878082192];t.assign(e,{keys:r,ee:new S(r[0]),te:new S(r[2])});for(let t=0;t<n.length;t++)he(e,n.charCodeAt(t))}function he(e,t){let[n,s,i]=e.keys;e.ee.append([t]),n=~e.ee.get(),s=ye(r.imul(ye(s+pe(n)),134775813)+1),e.te.append([s>>>24]),i=~e.te.get(),e.keys=[n,s,i]}function de(e){const t=2|e.keys[2];return pe(r.imul(t,1^t)>>>8)}function pe(e){return 255&e}function ye(e){return 4294967295&e}const me="deflate-raw";class be extends p{constructor(e,{chunkSize:t,CompressionStream:n,CompressionStreamNative:r}){super({});const{compressed:s,encrypted:i,useCompressionStream:o,zipCrypto:c,signed:f,level:a}=e,u=this;let w,h,d=ke(super.readable);i&&!c||!f||(w=new z,d=ze(d,w)),s&&(d=Se(d,o,{level:a,chunkSize:t},r,n)),i&&(c?d=ze(d,new ae(e)):(h=new te(e),d=ze(d,h))),ve(u,d,(async()=>{let e;i&&!c&&(e=h.signature),i&&!c||!f||(e=new l(w.value.buffer).getUint32(0)),u.signature=e}))}}class ge extends p{constructor(e,{chunkSize:t,DecompressionStream:n,DecompressionStreamNative:r}){super({});const{zipCrypto:i,encrypted:o,signed:c,signature:f,compressed:a,useCompressionStream:u}=e;let w,h,d=ke(super.readable);o&&(i?d=ze(d,new fe(e)):(h=new ee(e),d=ze(d,h))),a&&(d=Se(d,u,{chunkSize:t},r,n)),o&&!i||!c||(w=new z,d=ze(d,w)),ve(this,d,(async()=>{if((!o||i)&&c){const e=new l(w.value.buffer);if(f!=e.getUint32(0,!1))throw new s(P)}}))}}function ke(e){return ze(e,new p({transform(e,t){e&&e.length&&t.enqueue(e)}}))}function ve(e,n,r){n=ze(n,new p({flush:r})),t.defineProperty(e,"readable",{get:()=>n})}function Se(e,t,n,r,s){try{e=ze(e,new(t&&r?r:s)(me,n))}catch(r){if(!t)throw r;e=ze(e,new s(me,n))}return e}function ze(e,t){return e.pipeThrough(t)}const Ce="data";class xe extends p{constructor(e,n){super({});const r=this,{codecType:s}=e;let i;s.startsWith("deflate")?i=be:s.startsWith("inflate")&&(i=ge);let o=0;const c=new i(e,n),f=super.readable,a=new p({transform(e,t){e&&e.length&&(o+=e.length,t.enqueue(e))},flush(){const{signature:e}=c;t.assign(r,{signature:e,size:o})}});t.defineProperty(r,"readable",{get:()=>f.pipeThrough(c).pipeThrough(a)})}}const _e=new a,Ae=new a;let Ie=0;async function De(e){try{const{options:t,scripts:r,config:s}=e;r&&r.length&&importScripts.apply(void 0,r),self.initCodec&&self.initCodec(),s.CompressionStreamNative=self.CompressionStream,s.DecompressionStreamNative=self.DecompressionStream,self.Deflate&&(s.CompressionStream=new k(self.Deflate)),self.Inflate&&(s.DecompressionStream=new k(self.Inflate));const i={highWaterMark:1,size:()=>s.chunkSize},o=e.readable||new y({async pull(e){const t=new u((e=>_e.set(Ie,e)));Ve({type:"pull",messageId:Ie}),Ie=(Ie+1)%n.MAX_SAFE_INTEGER;const{value:r,done:s}=await t;e.enqueue(r),s&&e.close()}},i),c=e.writable||new m({async write(e){let t;const r=new u((e=>t=e));Ae.set(Ie,t),Ve({type:Ce,value:e,messageId:Ie}),Ie=(Ie+1)%n.MAX_SAFE_INTEGER,await r}},i),f=new xe(t,s);await o.pipeThrough(f).pipeTo(c,{preventClose:!0,preventAbort:!0});try{await c.getWriter().close()}catch(e){}const{signature:a,size:l}=f;Ve({type:"close",result:{signature:a,size:l}})}catch(e){Pe(e)}}function Ve(e){let{value:t}=e;if(t)if(t.length)try{t=new i(t),e.value=t.buffer,d(e,[e.value])}catch(t){d(e)}else d(e);else d(e)}function Pe(e){const{message:t,stack:n,code:r,name:s}=e;d({error:{message:t,stack:n,code:r,name:s}})}addEventListener("message",(({data:e})=>{const{type:t,messageId:n,value:r,done:s}=e;try{if("start"==t&&De(e),t==Ce){const e=_e.get(n);_e.delete(n),e({value:new i(r),done:s})}if("ack"==t){const e=Ae.get(n);Ae.delete(n),e()}}catch(e){Pe(e)}}));const Re=-2;function Be(t){return Ee(t.map((([t,n])=>new e(t).fill(n,0,t))))}function Ee(t){return t.reduce(((t,n)=>t.concat(e.isArray(n)?Ee(n):n)),[])}const Me=[0,1,2,3].concat(...Be([[2,4],[2,5],[4,6],[4,7],[8,8],[8,9],[16,10],[16,11],[32,12],[32,13],[64,14],[64,15],[2,0],[1,16],[1,17],[2,18],[2,19],[4,20],[4,21],[8,22],[8,23],[16,24],[16,25],[32,26],[32,27],[64,28],[64,29]]));function Ke(){const e=this;function t(e,t){let n=0;do{n|=1&e,e>>>=1,n<<=1}while(--t>0);return n>>>1}e.ne=n=>{const s=e.re,i=e.ie.se,o=e.ie.oe;let c,f,a,l=-1;for(n.ce=0,n.fe=573,c=0;o>c;c++)0!==s[2*c]?(n.ae[++n.ce]=l=c,n.le[c]=0):s[2*c+1]=0;for(;2>n.ce;)a=n.ae[++n.ce]=2>l?++l:0,s[2*a]=1,n.le[a]=0,n.ue--,i&&(n.we-=i[2*a+1]);for(e.he=l,c=r.floor(n.ce/2);c>=1;c--)n.de(s,c);a=o;do{c=n.ae[1],n.ae[1]=n.ae[n.ce--],n.de(s,1),f=n.ae[1],n.ae[--n.fe]=c,n.ae[--n.fe]=f,s[2*a]=s[2*c]+s[2*f],n.le[a]=r.max(n.le[c],n.le[f])+1,s[2*c+1]=s[2*f+1]=a,n.ae[1]=a++,n.de(s,1)}while(n.ce>=2);n.ae[--n.fe]=n.ae[1],(t=>{const n=e.re,r=e.ie.se,s=e.ie.pe,i=e.ie.ye,o=e.ie.me;let c,f,a,l,u,w,h=0;for(l=0;15>=l;l++)t.be[l]=0;for(n[2*t.ae[t.fe]+1]=0,c=t.fe+1;573>c;c++)f=t.ae[c],l=n[2*n[2*f+1]+1]+1,l>o&&(l=o,h++),n[2*f+1]=l,f>e.he||(t.be[l]++,u=0,i>f||(u=s[f-i]),w=n[2*f],t.ue+=w*(l+u),r&&(t.we+=w*(r[2*f+1]+u)));if(0!==h){do{for(l=o-1;0===t.be[l];)l--;t.be[l]--,t.be[l+1]+=2,t.be[o]--,h-=2}while(h>0);for(l=o;0!==l;l--)for(f=t.be[l];0!==f;)a=t.ae[--c],a>e.he||(n[2*a+1]!=l&&(t.ue+=(l-n[2*a+1])*n[2*a],n[2*a+1]=l),f--)}})(n),((e,n,r)=>{const s=[];let i,o,c,f=0;for(i=1;15>=i;i++)s[i]=f=f+r[i-1]<<1;for(o=0;n>=o;o++)c=e[2*o+1],0!==c&&(e[2*o]=t(s[c]++,c))})(s,e.he,n.be)}}function Ue(e,t,n,r,s){const i=this;i.se=e,i.pe=t,i.ye=n,i.oe=r,i.me=s}Ke.ge=[0,1,2,3,4,5,6,7].concat(...Be([[2,8],[2,9],[2,10],[2,11],[4,12],[4,13],[4,14],[4,15],[8,16],[8,17],[8,18],[8,19],[16,20],[16,21],[16,22],[16,23],[32,24],[32,25],[32,26],[31,27],[1,28]])),Ke.ke=[0,1,2,3,4,5,6,7,8,10,12,14,16,20,24,28,32,40,48,56,64,80,96,112,128,160,192,224,0],Ke.ve=[0,1,2,3,4,6,8,12,16,24,32,48,64,96,128,192,256,384,512,768,1024,1536,2048,3072,4096,6144,8192,12288,16384,24576],Ke.Se=e=>256>e?Me[e]:Me[256+(e>>>7)],Ke.ze=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],Ke.Ce=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],Ke.xe=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],Ke._e=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];const Ne=Be([[144,8],[112,9],[24,7],[8,8]]);Ue.Ae=Ee([12,140,76,204,44,172,108,236,28,156,92,220,60,188,124,252,2,130,66,194,34,162,98,226,18,146,82,210,50,178,114,242,10,138,74,202,42,170,106,234,26,154,90,218,58,186,122,250,6,134,70,198,38,166,102,230,22,150,86,214,54,182,118,246,14,142,78,206,46,174,110,238,30,158,94,222,62,190,126,254,1,129,65,193,33,161,97,225,17,145,81,209,49,177,113,241,9,137,73,201,41,169,105,233,25,153,89,217,57,185,121,249,5,133,69,197,37,165,101,229,21,149,85,213,53,181,117,245,13,141,77,205,45,173,109,237,29,157,93,221,61,189,125,253,19,275,147,403,83,339,211,467,51,307,179,435,115,371,243,499,11,267,139,395,75,331,203,459,43,299,171,427,107,363,235,491,27,283,155,411,91,347,219,475,59,315,187,443,123,379,251,507,7,263,135,391,71,327,199,455,39,295,167,423,103,359,231,487,23,279,151,407,87,343,215,471,55,311,183,439,119,375,247,503,15,271,143,399,79,335,207,463,47,303,175,431,111,367,239,495,31,287,159,415,95,351,223,479,63,319,191,447,127,383,255,511,0,64,32,96,16,80,48,112,8,72,40,104,24,88,56,120,4,68,36,100,20,84,52,116,3,131,67,195,35,163,99,227].map(((e,t)=>[e,Ne[t]])));const Oe=Be([[30,5]]);function Te(e,t,n,r,s){const i=this;i.Ie=e,i.De=t,i.Ve=n,i.Pe=r,i.Re=s}Ue.Be=Ee([0,16,8,24,4,20,12,28,2,18,10,26,6,22,14,30,1,17,9,25,5,21,13,29,3,19,11,27,7,23].map(((e,t)=>[e,Oe[t]]))),Ue.Ee=new Ue(Ue.Ae,Ke.ze,257,286,15),Ue.Me=new Ue(Ue.Be,Ke.Ce,0,30,15),Ue.Ke=new Ue(null,Ke.xe,0,19,7);const We=[new Te(0,0,0,0,0),new Te(4,4,8,4,1),new Te(4,5,16,8,1),new Te(4,6,32,32,1),new Te(4,4,16,16,2),new Te(8,16,32,32,2),new Te(8,16,128,128,2),new Te(8,32,128,256,2),new Te(32,128,258,1024,2),new Te(32,258,258,4096,2)],je=["need dictionary","stream end","","","stream error","data error","","buffer error","",""],He=113,Le=666,Fe=262;function qe(e,t,n,r){const s=e[2*t],i=e[2*n];return i>s||s==i&&r[t]<=r[n]}function Ge(){const e=this;let t,n,s,c,f,a,l,u,w,h,d,p,y,m,b,g,k,v,S,z,C,x,_,A,I,D,V,P,R,B,E,M,K;const U=new Ke,N=new Ke,O=new Ke;let T,W,j,H,L,F;function q(){let t;for(t=0;286>t;t++)E[2*t]=0;for(t=0;30>t;t++)M[2*t]=0;for(t=0;19>t;t++)K[2*t]=0;E[512]=1,e.ue=e.we=0,W=j=0}function G(e,t){let n,r=-1,s=e[1],i=0,o=7,c=4;0===s&&(o=138,c=3),e[2*(t+1)+1]=65535;for(let f=0;t>=f;f++)n=s,s=e[2*(f+1)+1],++i<o&&n==s||(c>i?K[2*n]+=i:0!==n?(n!=r&&K[2*n]++,K[32]++):i>10?K[36]++:K[34]++,i=0,r=n,0===s?(o=138,c=3):n==s?(o=6,c=3):(o=7,c=4))}function J(t){e.Ue[e.pending++]=t}function Q(e){J(255&e),J(e>>>8&255)}function X(e,t){let n;const r=t;F>16-r?(n=e,L|=n<<F&65535,Q(L),L=n>>>16-F,F+=r-16):(L|=e<<F&65535,F+=r)}function Y(e,t){const n=2*e;X(65535&t[n],65535&t[n+1])}function Z(e,t){let n,r,s=-1,i=e[1],o=0,c=7,f=4;for(0===i&&(c=138,f=3),n=0;t>=n;n++)if(r=i,i=e[2*(n+1)+1],++o>=c||r!=i){if(f>o)do{Y(r,K)}while(0!=--o);else 0!==r?(r!=s&&(Y(r,K),o--),Y(16,K),X(o-3,2)):o>10?(Y(18,K),X(o-11,7)):(Y(17,K),X(o-3,3));o=0,s=r,0===i?(c=138,f=3):r==i?(c=6,f=3):(c=7,f=4)}}function $(){16==F?(Q(L),L=0,F=0):8>F||(J(255&L),L>>>=8,F-=8)}function ee(t,n){let s,i,o;if(e.Ne[W]=t,e.Oe[W]=255&n,W++,0===t?E[2*n]++:(j++,t--,E[2*(Ke.ge[n]+256+1)]++,M[2*Ke.Se(t)]++),0==(8191&W)&&V>2){for(s=8*W,i=C-k,o=0;30>o;o++)s+=M[2*o]*(5+Ke.Ce[o]);if(s>>>=3,j<r.floor(W/2)&&s<r.floor(i/2))return!0}return W==T-1}function te(t,n){let r,s,i,o,c=0;if(0!==W)do{r=e.Ne[c],s=e.Oe[c],c++,0===r?Y(s,t):(i=Ke.ge[s],Y(i+256+1,t),o=Ke.ze[i],0!==o&&(s-=Ke.ke[i],X(s,o)),r--,i=Ke.Se(r),Y(i,n),o=Ke.Ce[i],0!==o&&(r-=Ke.ve[i],X(r,o)))}while(W>c);Y(256,t),H=t[513]}function ne(){F>8?Q(L):F>0&&J(255&L),L=0,F=0}function re(t,n,r){X(0+(r?1:0),3),((t,n)=>{ne(),H=8,Q(n),Q(~n),e.Ue.set(u.subarray(t,t+n),e.pending),e.pending+=n})(t,n)}function se(n){((t,n,r)=>{let s,i,o=0;V>0?(U.ne(e),N.ne(e),o=(()=>{let t;for(G(E,U.he),G(M,N.he),O.ne(e),t=18;t>=3&&0===K[2*Ke._e[t]+1];t--);return e.ue+=14+3*(t+1),t})(),s=e.ue+3+7>>>3,i=e.we+3+7>>>3,i>s||(s=i)):s=i=n+5,n+4>s||-1==t?i==s?(X(2+(r?1:0),3),te(Ue.Ae,Ue.Be)):(X(4+(r?1:0),3),((e,t,n)=>{let r;for(X(e-257,5),X(t-1,5),X(n-4,4),r=0;n>r;r++)X(K[2*Ke._e[r]+1],3);Z(E,e-1),Z(M,t-1)})(U.he+1,N.he+1,o+1),te(E,M)):re(t,n,r),q(),r&&ne()})(0>k?-1:k,C-k,n),k=C,t.Te()}function ie(){let e,n,r,s;do{if(s=w-_-C,0===s&&0===C&&0===_)s=f;else if(-1==s)s--;else if(C>=f+f-Fe){u.set(u.subarray(f,f+f),0),x-=f,C-=f,k-=f,e=y,r=e;do{n=65535&d[--r],d[r]=f>n?0:n-f}while(0!=--e);e=f,r=e;do{n=65535&h[--r],h[r]=f>n?0:n-f}while(0!=--e);s+=f}if(0===t.We)return;e=t.je(u,C+_,s),_+=e,3>_||(p=255&u[C],p=(p<<g^255&u[C+1])&b)}while(Fe>_&&0!==t.We)}function oe(e){let t,n,r=I,s=C,i=A;const o=C>f-Fe?C-(f-Fe):0;let c=B;const a=l,w=C+258;let d=u[s+i-1],p=u[s+i];R>A||(r>>=2),c>_&&(c=_);do{if(t=e,u[t+i]==p&&u[t+i-1]==d&&u[t]==u[s]&&u[++t]==u[s+1]){s+=2,t++;do{}while(u[++s]==u[++t]&&u[++s]==u[++t]&&u[++s]==u[++t]&&u[++s]==u[++t]&&u[++s]==u[++t]&&u[++s]==u[++t]&&u[++s]==u[++t]&&u[++s]==u[++t]&&w>s);if(n=258-(w-s),s=w-258,n>i){if(x=e,i=n,n>=c)break;d=u[s+i-1],p=u[s+i]}}}while((e=65535&h[e&a])>o&&0!=--r);return i>_?_:i}e.le=[],e.be=[],e.ae=[],E=[],M=[],K=[],e.de=(t,n)=>{const r=e.ae,s=r[n];let i=n<<1;for(;i<=e.ce&&(i<e.ce&&qe(t,r[i+1],r[i],e.le)&&i++,!qe(t,s,r[i],e.le));)r[n]=r[i],n=i,i<<=1;r[n]=s},e.He=(t,S,x,W,j,G)=>(W||(W=8),j||(j=8),G||(G=0),t.Le=null,-1==S&&(S=6),1>j||j>9||8!=W||9>x||x>15||0>S||S>9||0>G||G>2?Re:(t.Fe=e,a=x,f=1<<a,l=f-1,m=j+7,y=1<<m,b=y-1,g=r.floor((m+3-1)/3),u=new i(2*f),h=[],d=[],T=1<<j+6,e.Ue=new i(4*T),s=4*T,e.Ne=new o(T),e.Oe=new i(T),V=S,P=G,(t=>(t.qe=t.Ge=0,t.Le=null,e.pending=0,e.Je=0,n=He,c=0,U.re=E,U.ie=Ue.Ee,N.re=M,N.ie=Ue.Me,O.re=K,O.ie=Ue.Ke,L=0,F=0,H=8,q(),(()=>{w=2*f,d[y-1]=0;for(let e=0;y-1>e;e++)d[e]=0;D=We[V].De,R=We[V].Ie,B=We[V].Ve,I=We[V].Pe,C=0,k=0,_=0,v=A=2,z=0,p=0})(),0))(t))),e.Qe=()=>42!=n&&n!=He&&n!=Le?Re:(e.Oe=null,e.Ne=null,e.Ue=null,d=null,h=null,u=null,e.Fe=null,n==He?-3:0),e.Xe=(e,t,n)=>{let r=0;return-1==t&&(t=6),0>t||t>9||0>n||n>2?Re:(We[V].Re!=We[t].Re&&0!==e.qe&&(r=e.Ye(1)),V!=t&&(V=t,D=We[V].De,R=We[V].Ie,B=We[V].Ve,I=We[V].Pe),P=n,r)},e.Ze=(e,t,r)=>{let s,i=r,o=0;if(!t||42!=n)return Re;if(3>i)return 0;for(i>f-Fe&&(i=f-Fe,o=r-i),u.set(t.subarray(o,o+i),0),C=i,k=i,p=255&u[0],p=(p<<g^255&u[1])&b,s=0;i-3>=s;s++)p=(p<<g^255&u[s+2])&b,h[s&l]=d[p],d[p]=s;return 0},e.Ye=(r,i)=>{let o,w,m,I,R;if(i>4||0>i)return Re;if(!r.$e||!r.et&&0!==r.We||n==Le&&4!=i)return r.Le=je[4],Re;if(0===r.tt)return r.Le=je[7],-5;var B;if(t=r,I=c,c=i,42==n&&(w=8+(a-8<<4)<<8,m=(V-1&255)>>1,m>3&&(m=3),w|=m<<6,0!==C&&(w|=32),w+=31-w%31,n=He,J((B=w)>>8&255),J(255&B)),0!==e.pending){if(t.Te(),0===t.tt)return c=-1,0}else if(0===t.We&&I>=i&&4!=i)return t.Le=je[7],-5;if(n==Le&&0!==t.We)return r.Le=je[7],-5;if(0!==t.We||0!==_||0!=i&&n!=Le){switch(R=-1,We[V].Re){case 0:R=(e=>{let n,r=65535;for(r>s-5&&(r=s-5);;){if(1>=_){if(ie(),0===_&&0==e)return 0;if(0===_)break}if(C+=_,_=0,n=k+r,(0===C||C>=n)&&(_=C-n,C=n,se(!1),0===t.tt))return 0;if(C-k>=f-Fe&&(se(!1),0===t.tt))return 0}return se(4==e),0===t.tt?4==e?2:0:4==e?3:1})(i);break;case 1:R=(e=>{let n,r=0;for(;;){if(Fe>_){if(ie(),Fe>_&&0==e)return 0;if(0===_)break}if(3>_||(p=(p<<g^255&u[C+2])&b,r=65535&d[p],h[C&l]=d[p],d[p]=C),0===r||(C-r&65535)>f-Fe||2!=P&&(v=oe(r)),3>v)n=ee(0,255&u[C]),_--,C++;else if(n=ee(C-x,v-3),_-=v,v>D||3>_)C+=v,v=0,p=255&u[C],p=(p<<g^255&u[C+1])&b;else{v--;do{C++,p=(p<<g^255&u[C+2])&b,r=65535&d[p],h[C&l]=d[p],d[p]=C}while(0!=--v);C++}if(n&&(se(!1),0===t.tt))return 0}return se(4==e),0===t.tt?4==e?2:0:4==e?3:1})(i);break;case 2:R=(e=>{let n,r,s=0;for(;;){if(Fe>_){if(ie(),Fe>_&&0==e)return 0;if(0===_)break}if(3>_||(p=(p<<g^255&u[C+2])&b,s=65535&d[p],h[C&l]=d[p],d[p]=C),A=v,S=x,v=2,0!==s&&D>A&&f-Fe>=(C-s&65535)&&(2!=P&&(v=oe(s)),5>=v&&(1==P||3==v&&C-x>4096)&&(v=2)),3>A||v>A)if(0!==z){if(n=ee(0,255&u[C-1]),n&&se(!1),C++,_--,0===t.tt)return 0}else z=1,C++,_--;else{r=C+_-3,n=ee(C-1-S,A-3),_-=A-1,A-=2;do{++C>r||(p=(p<<g^255&u[C+2])&b,s=65535&d[p],h[C&l]=d[p],d[p]=C)}while(0!=--A);if(z=0,v=2,C++,n&&(se(!1),0===t.tt))return 0}}return 0!==z&&(n=ee(0,255&u[C-1]),z=0),se(4==e),0===t.tt?4==e?2:0:4==e?3:1})(i)}if(2!=R&&3!=R||(n=Le),0==R||2==R)return 0===t.tt&&(c=-1),0;if(1==R){if(1==i)X(2,3),Y(256,Ue.Ae),$(),9>1+H+10-F&&(X(2,3),Y(256,Ue.Ae),$()),H=7;else if(re(0,0,!1),3==i)for(o=0;y>o;o++)d[o]=0;if(t.Te(),0===t.tt)return c=-1,0}}return 4!=i?0:1}}function Je(){const e=this;e.nt=0,e.rt=0,e.We=0,e.qe=0,e.tt=0,e.Ge=0}function Qe(e){const t=new Je,n=(o=e&&e.chunkSize?e.chunkSize:65536)+5*(r.floor(o/16383)+1);var o;const c=new i(n);let f=e?e.level:-1;void 0===f&&(f=-1),t.He(f),t.$e=c,this.append=(e,r)=>{let o,f,a=0,l=0,u=0;const w=[];if(e.length){t.nt=0,t.et=e,t.We=e.length;do{if(t.rt=0,t.tt=n,o=t.Ye(0),0!=o)throw new s("deflating: "+t.Le);t.rt&&(t.rt==n?w.push(new i(c)):w.push(c.subarray(0,t.rt))),u+=t.rt,r&&t.nt>0&&t.nt!=a&&(r(t.nt),a=t.nt)}while(t.We>0||0===t.tt);return w.length>1?(f=new i(u),w.forEach((e=>{f.set(e,l),l+=e.length}))):f=w[0]?new i(w[0]):new i,f}},this.flush=()=>{let e,r,o=0,f=0;const a=[];do{if(t.rt=0,t.tt=n,e=t.Ye(4),1!=e&&0!=e)throw new s("deflating: "+t.Le);n-t.tt>0&&a.push(c.slice(0,t.rt)),f+=t.rt}while(t.We>0||0===t.tt);return t.Qe(),r=new i(f),a.forEach((e=>{r.set(e,o),o+=e.length})),r}}Je.prototype={He(e,t){const n=this;return n.Fe=new Ge,t||(t=15),n.Fe.He(n,e,t)},Ye(e){const t=this;return t.Fe?t.Fe.Ye(t,e):Re},Qe(){const e=this;if(!e.Fe)return Re;const t=e.Fe.Qe();return e.Fe=null,t},Xe(e,t){const n=this;return n.Fe?n.Fe.Xe(n,e,t):Re},Ze(e,t){const n=this;return n.Fe?n.Fe.Ze(n,e,t):Re},je(e,t,n){const r=this;let s=r.We;return s>n&&(s=n),0===s?0:(r.We-=s,e.set(r.et.subarray(r.nt,r.nt+s),t),r.nt+=s,r.qe+=s,s)},Te(){const e=this;let t=e.Fe.pending;t>e.tt&&(t=e.tt),0!==t&&(e.$e.set(e.Fe.Ue.subarray(e.Fe.Je,e.Fe.Je+t),e.rt),e.rt+=t,e.Fe.Je+=t,e.Ge+=t,e.tt-=t,e.Fe.pending-=t,0===e.Fe.pending&&(e.Fe.Je=0))}};const Xe=-2,Ye=-3,Ze=-5,$e=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535],et=[96,7,256,0,8,80,0,8,16,84,8,115,82,7,31,0,8,112,0,8,48,0,9,192,80,7,10,0,8,96,0,8,32,0,9,160,0,8,0,0,8,128,0,8,64,0,9,224,80,7,6,0,8,88,0,8,24,0,9,144,83,7,59,0,8,120,0,8,56,0,9,208,81,7,17,0,8,104,0,8,40,0,9,176,0,8,8,0,8,136,0,8,72,0,9,240,80,7,4,0,8,84,0,8,20,85,8,227,83,7,43,0,8,116,0,8,52,0,9,200,81,7,13,0,8,100,0,8,36,0,9,168,0,8,4,0,8,132,0,8,68,0,9,232,80,7,8,0,8,92,0,8,28,0,9,152,84,7,83,0,8,124,0,8,60,0,9,216,82,7,23,0,8,108,0,8,44,0,9,184,0,8,12,0,8,140,0,8,76,0,9,248,80,7,3,0,8,82,0,8,18,85,8,163,83,7,35,0,8,114,0,8,50,0,9,196,81,7,11,0,8,98,0,8,34,0,9,164,0,8,2,0,8,130,0,8,66,0,9,228,80,7,7,0,8,90,0,8,26,0,9,148,84,7,67,0,8,122,0,8,58,0,9,212,82,7,19,0,8,106,0,8,42,0,9,180,0,8,10,0,8,138,0,8,74,0,9,244,80,7,5,0,8,86,0,8,22,192,8,0,83,7,51,0,8,118,0,8,54,0,9,204,81,7,15,0,8,102,0,8,38,0,9,172,0,8,6,0,8,134,0,8,70,0,9,236,80,7,9,0,8,94,0,8,30,0,9,156,84,7,99,0,8,126,0,8,62,0,9,220,82,7,27,0,8,110,0,8,46,0,9,188,0,8,14,0,8,142,0,8,78,0,9,252,96,7,256,0,8,81,0,8,17,85,8,131,82,7,31,0,8,113,0,8,49,0,9,194,80,7,10,0,8,97,0,8,33,0,9,162,0,8,1,0,8,129,0,8,65,0,9,226,80,7,6,0,8,89,0,8,25,0,9,146,83,7,59,0,8,121,0,8,57,0,9,210,81,7,17,0,8,105,0,8,41,0,9,178,0,8,9,0,8,137,0,8,73,0,9,242,80,7,4,0,8,85,0,8,21,80,8,258,83,7,43,0,8,117,0,8,53,0,9,202,81,7,13,0,8,101,0,8,37,0,9,170,0,8,5,0,8,133,0,8,69,0,9,234,80,7,8,0,8,93,0,8,29,0,9,154,84,7,83,0,8,125,0,8,61,0,9,218,82,7,23,0,8,109,0,8,45,0,9,186,0,8,13,0,8,141,0,8,77,0,9,250,80,7,3,0,8,83,0,8,19,85,8,195,83,7,35,0,8,115,0,8,51,0,9,198,81,7,11,0,8,99,0,8,35,0,9,166,0,8,3,0,8,131,0,8,67,0,9,230,80,7,7,0,8,91,0,8,27,0,9,150,84,7,67,0,8,123,0,8,59,0,9,214,82,7,19,0,8,107,0,8,43,0,9,182,0,8,11,0,8,139,0,8,75,0,9,246,80,7,5,0,8,87,0,8,23,192,8,0,83,7,51,0,8,119,0,8,55,0,9,206,81,7,15,0,8,103,0,8,39,0,9,174,0,8,7,0,8,135,0,8,71,0,9,238,80,7,9,0,8,95,0,8,31,0,9,158,84,7,99,0,8,127,0,8,63,0,9,222,82,7,27,0,8,111,0,8,47,0,9,190,0,8,15,0,8,143,0,8,79,0,9,254,96,7,256,0,8,80,0,8,16,84,8,115,82,7,31,0,8,112,0,8,48,0,9,193,80,7,10,0,8,96,0,8,32,0,9,161,0,8,0,0,8,128,0,8,64,0,9,225,80,7,6,0,8,88,0,8,24,0,9,145,83,7,59,0,8,120,0,8,56,0,9,209,81,7,17,0,8,104,0,8,40,0,9,177,0,8,8,0,8,136,0,8,72,0,9,241,80,7,4,0,8,84,0,8,20,85,8,227,83,7,43,0,8,116,0,8,52,0,9,201,81,7,13,0,8,100,0,8,36,0,9,169,0,8,4,0,8,132,0,8,68,0,9,233,80,7,8,0,8,92,0,8,28,0,9,153,84,7,83,0,8,124,0,8,60,0,9,217,82,7,23,0,8,108,0,8,44,0,9,185,0,8,12,0,8,140,0,8,76,0,9,249,80,7,3,0,8,82,0,8,18,85,8,163,83,7,35,0,8,114,0,8,50,0,9,197,81,7,11,0,8,98,0,8,34,0,9,165,0,8,2,0,8,130,0,8,66,0,9,229,80,7,7,0,8,90,0,8,26,0,9,149,84,7,67,0,8,122,0,8,58,0,9,213,82,7,19,0,8,106,0,8,42,0,9,181,0,8,10,0,8,138,0,8,74,0,9,245,80,7,5,0,8,86,0,8,22,192,8,0,83,7,51,0,8,118,0,8,54,0,9,205,81,7,15,0,8,102,0,8,38,0,9,173,0,8,6,0,8,134,0,8,70,0,9,237,80,7,9,0,8,94,0,8,30,0,9,157,84,7,99,0,8,126,0,8,62,0,9,221,82,7,27,0,8,110,0,8,46,0,9,189,0,8,14,0,8,142,0,8,78,0,9,253,96,7,256,0,8,81,0,8,17,85,8,131,82,7,31,0,8,113,0,8,49,0,9,195,80,7,10,0,8,97,0,8,33,0,9,163,0,8,1,0,8,129,0,8,65,0,9,227,80,7,6,0,8,89,0,8,25,0,9,147,83,7,59,0,8,121,0,8,57,0,9,211,81,7,17,0,8,105,0,8,41,0,9,179,0,8,9,0,8,137,0,8,73,0,9,243,80,7,4,0,8,85,0,8,21,80,8,258,83,7,43,0,8,117,0,8,53,0,9,203,81,7,13,0,8,101,0,8,37,0,9,171,0,8,5,0,8,133,0,8,69,0,9,235,80,7,8,0,8,93,0,8,29,0,9,155,84,7,83,0,8,125,0,8,61,0,9,219,82,7,23,0,8,109,0,8,45,0,9,187,0,8,13,0,8,141,0,8,77,0,9,251,80,7,3,0,8,83,0,8,19,85,8,195,83,7,35,0,8,115,0,8,51,0,9,199,81,7,11,0,8,99,0,8,35,0,9,167,0,8,3,0,8,131,0,8,67,0,9,231,80,7,7,0,8,91,0,8,27,0,9,151,84,7,67,0,8,123,0,8,59,0,9,215,82,7,19,0,8,107,0,8,43,0,9,183,0,8,11,0,8,139,0,8,75,0,9,247,80,7,5,0,8,87,0,8,23,192,8,0,83,7,51,0,8,119,0,8,55,0,9,207,81,7,15,0,8,103,0,8,39,0,9,175,0,8,7,0,8,135,0,8,71,0,9,239,80,7,9,0,8,95,0,8,31,0,9,159,84,7,99,0,8,127,0,8,63,0,9,223,82,7,27,0,8,111,0,8,47,0,9,191,0,8,15,0,8,143,0,8,79,0,9,255],tt=[80,5,1,87,5,257,83,5,17,91,5,4097,81,5,5,89,5,1025,85,5,65,93,5,16385,80,5,3,88,5,513,84,5,33,92,5,8193,82,5,9,90,5,2049,86,5,129,192,5,24577,80,5,2,87,5,385,83,5,25,91,5,6145,81,5,7,89,5,1537,85,5,97,93,5,24577,80,5,4,88,5,769,84,5,49,92,5,12289,82,5,13,90,5,3073,86,5,193,192,5,24577],nt=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],rt=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,112,112],st=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],it=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13];function ot(){let e,t,n,r,s,i;function o(e,t,o,c,f,a,l,u,w,h,d){let p,y,m,b,g,k,v,S,z,C,x,_,A,I,D;C=0,g=o;do{n[e[t+C]]++,C++,g--}while(0!==g);if(n[0]==o)return l[0]=-1,u[0]=0,0;for(S=u[0],k=1;15>=k&&0===n[k];k++);for(v=k,k>S&&(S=k),g=15;0!==g&&0===n[g];g--);for(m=g,S>g&&(S=g),u[0]=S,I=1<<k;g>k;k++,I<<=1)if(0>(I-=n[k]))return Ye;if(0>(I-=n[g]))return Ye;for(n[g]+=I,i[1]=k=0,C=1,A=2;0!=--g;)i[A]=k+=n[C],A++,C++;g=0,C=0;do{0!==(k=e[t+C])&&(d[i[k]++]=g),C++}while(++g<o);for(o=i[m],i[0]=g=0,C=0,b=-1,_=-S,s[0]=0,x=0,D=0;m>=v;v++)for(p=n[v];0!=p--;){for(;v>_+S;){if(b++,_+=S,D=m-_,D=D>S?S:D,(y=1<<(k=v-_))>p+1&&(y-=p+1,A=v,D>k))for(;++k<D&&(y<<=1)>n[++A];)y-=n[A];if(D=1<<k,h[0]+D>1440)return Ye;s[b]=x=h[0],h[0]+=D,0!==b?(i[b]=g,r[0]=k,r[1]=S,k=g>>>_-S,r[2]=x-s[b-1]-k,w.set(r,3*(s[b-1]+k))):l[0]=x}for(r[1]=v-_,o>C?d[C]<c?(r[0]=256>d[C]?0:96,r[2]=d[C++]):(r[0]=a[d[C]-c]+16+64,r[2]=f[d[C++]-c]):r[0]=192,y=1<<v-_,k=g>>>_;D>k;k+=y)w.set(r,3*(x+k));for(k=1<<v-1;0!=(g&k);k>>>=1)g^=k;for(g^=k,z=(1<<_)-1;(g&z)!=i[b];)b--,_-=S,z=(1<<_)-1}return 0!==I&&1!=m?Ze:0}function c(o){let c;for(e||(e=[],t=[],n=new f(16),r=[],s=new f(15),i=new f(16)),t.length<o&&(t=[]),c=0;o>c;c++)t[c]=0;for(c=0;16>c;c++)n[c]=0;for(c=0;3>c;c++)r[c]=0;s.set(n.subarray(0,15),0),i.set(n.subarray(0,16),0)}this.st=(n,r,s,i,f)=>{let a;return c(19),e[0]=0,a=o(n,0,19,19,null,null,s,r,i,e,t),a==Ye?f.Le="oversubscribed dynamic bit lengths tree":a!=Ze&&0!==r[0]||(f.Le="incomplete dynamic bit lengths tree",a=Ye),a},this.it=(n,r,s,i,f,a,l,u,w)=>{let h;return c(288),e[0]=0,h=o(s,0,n,257,nt,rt,a,i,u,e,t),0!=h||0===i[0]?(h==Ye?w.Le="oversubscribed literal/length tree":-4!=h&&(w.Le="incomplete literal/length tree",h=Ye),h):(c(288),h=o(s,n,r,0,st,it,l,f,u,e,t),0!=h||0===f[0]&&n>257?(h==Ye?w.Le="oversubscribed distance tree":h==Ze?(w.Le="incomplete distance tree",h=Ye):-4!=h&&(w.Le="empty distance tree with lengths",h=Ye),h):0)}}function ct(){const e=this;let t,n,r,s,i=0,o=0,c=0,f=0,a=0,l=0,u=0,w=0,h=0,d=0;function p(e,t,n,r,s,i,o,c){let f,a,l,u,w,h,d,p,y,m,b,g,k,v,S,z;d=c.nt,p=c.We,w=o.ot,h=o.ct,y=o.write,m=y<o.read?o.read-y-1:o.end-y,b=$e[e],g=$e[t];do{for(;20>h;)p--,w|=(255&c.ft(d++))<<h,h+=8;if(f=w&b,a=n,l=r,z=3*(l+f),0!==(u=a[z]))for(;;){if(w>>=a[z+1],h-=a[z+1],0!=(16&u)){for(u&=15,k=a[z+2]+(w&$e[u]),w>>=u,h-=u;15>h;)p--,w|=(255&c.ft(d++))<<h,h+=8;for(f=w&g,a=s,l=i,z=3*(l+f),u=a[z];;){if(w>>=a[z+1],h-=a[z+1],0!=(16&u)){for(u&=15;u>h;)p--,w|=(255&c.ft(d++))<<h,h+=8;if(v=a[z+2]+(w&$e[u]),w>>=u,h-=u,m-=k,v>y){S=y-v;do{S+=o.end}while(0>S);if(u=o.end-S,k>u){if(k-=u,y-S>0&&u>y-S)do{o.lt[y++]=o.lt[S++]}while(0!=--u);else o.lt.set(o.lt.subarray(S,S+u),y),y+=u,S+=u,u=0;S=0}}else S=y-v,y-S>0&&2>y-S?(o.lt[y++]=o.lt[S++],o.lt[y++]=o.lt[S++],k-=2):(o.lt.set(o.lt.subarray(S,S+2),y),y+=2,S+=2,k-=2);if(y-S>0&&k>y-S)do{o.lt[y++]=o.lt[S++]}while(0!=--k);else o.lt.set(o.lt.subarray(S,S+k),y),y+=k,S+=k,k=0;break}if(0!=(64&u))return c.Le="invalid distance code",k=c.We-p,k=k>h>>3?h>>3:k,p+=k,d-=k,h-=k<<3,o.ot=w,o.ct=h,c.We=p,c.qe+=d-c.nt,c.nt=d,o.write=y,Ye;f+=a[z+2],f+=w&$e[u],z=3*(l+f),u=a[z]}break}if(0!=(64&u))return 0!=(32&u)?(k=c.We-p,k=k>h>>3?h>>3:k,p+=k,d-=k,h-=k<<3,o.ot=w,o.ct=h,c.We=p,c.qe+=d-c.nt,c.nt=d,o.write=y,1):(c.Le="invalid literal/length code",k=c.We-p,k=k>h>>3?h>>3:k,p+=k,d-=k,h-=k<<3,o.ot=w,o.ct=h,c.We=p,c.qe+=d-c.nt,c.nt=d,o.write=y,Ye);if(f+=a[z+2],f+=w&$e[u],z=3*(l+f),0===(u=a[z])){w>>=a[z+1],h-=a[z+1],o.lt[y++]=a[z+2],m--;break}}else w>>=a[z+1],h-=a[z+1],o.lt[y++]=a[z+2],m--}while(m>=258&&p>=10);return k=c.We-p,k=k>h>>3?h>>3:k,p+=k,d-=k,h-=k<<3,o.ot=w,o.ct=h,c.We=p,c.qe+=d-c.nt,c.nt=d,o.write=y,0}e.init=(e,i,o,c,f,a)=>{t=0,u=e,w=i,r=o,h=c,s=f,d=a,n=null},e.ut=(e,y,m)=>{let b,g,k,v,S,z,C,x=0,_=0,A=0;for(A=y.nt,v=y.We,x=e.ot,_=e.ct,S=e.write,z=S<e.read?e.read-S-1:e.end-S;;)switch(t){case 0:if(z>=258&&v>=10&&(e.ot=x,e.ct=_,y.We=v,y.qe+=A-y.nt,y.nt=A,e.write=S,m=p(u,w,r,h,s,d,e,y),A=y.nt,v=y.We,x=e.ot,_=e.ct,S=e.write,z=S<e.read?e.read-S-1:e.end-S,0!=m)){t=1==m?7:9;break}c=u,n=r,o=h,t=1;case 1:for(b=c;b>_;){if(0===v)return e.ot=x,e.ct=_,y.We=v,y.qe+=A-y.nt,y.nt=A,e.write=S,e.wt(y,m);m=0,v--,x|=(255&y.ft(A++))<<_,_+=8}if(g=3*(o+(x&$e[b])),x>>>=n[g+1],_-=n[g+1],k=n[g],0===k){f=n[g+2],t=6;break}if(0!=(16&k)){a=15&k,i=n[g+2],t=2;break}if(0==(64&k)){c=k,o=g/3+n[g+2];break}if(0!=(32&k)){t=7;break}return t=9,y.Le="invalid literal/length code",m=Ye,e.ot=x,e.ct=_,y.We=v,y.qe+=A-y.nt,y.nt=A,e.write=S,e.wt(y,m);case 2:for(b=a;b>_;){if(0===v)return e.ot=x,e.ct=_,y.We=v,y.qe+=A-y.nt,y.nt=A,e.write=S,e.wt(y,m);m=0,v--,x|=(255&y.ft(A++))<<_,_+=8}i+=x&$e[b],x>>=b,_-=b,c=w,n=s,o=d,t=3;case 3:for(b=c;b>_;){if(0===v)return e.ot=x,e.ct=_,y.We=v,y.qe+=A-y.nt,y.nt=A,e.write=S,e.wt(y,m);m=0,v--,x|=(255&y.ft(A++))<<_,_+=8}if(g=3*(o+(x&$e[b])),x>>=n[g+1],_-=n[g+1],k=n[g],0!=(16&k)){a=15&k,l=n[g+2],t=4;break}if(0==(64&k)){c=k,o=g/3+n[g+2];break}return t=9,y.Le="invalid distance code",m=Ye,e.ot=x,e.ct=_,y.We=v,y.qe+=A-y.nt,y.nt=A,e.write=S,e.wt(y,m);case 4:for(b=a;b>_;){if(0===v)return e.ot=x,e.ct=_,y.We=v,y.qe+=A-y.nt,y.nt=A,e.write=S,e.wt(y,m);m=0,v--,x|=(255&y.ft(A++))<<_,_+=8}l+=x&$e[b],x>>=b,_-=b,t=5;case 5:for(C=S-l;0>C;)C+=e.end;for(;0!==i;){if(0===z&&(S==e.end&&0!==e.read&&(S=0,z=S<e.read?e.read-S-1:e.end-S),0===z&&(e.write=S,m=e.wt(y,m),S=e.write,z=S<e.read?e.read-S-1:e.end-S,S==e.end&&0!==e.read&&(S=0,z=S<e.read?e.read-S-1:e.end-S),0===z)))return e.ot=x,e.ct=_,y.We=v,y.qe+=A-y.nt,y.nt=A,e.write=S,e.wt(y,m);e.lt[S++]=e.lt[C++],z--,C==e.end&&(C=0),i--}t=0;break;case 6:if(0===z&&(S==e.end&&0!==e.read&&(S=0,z=S<e.read?e.read-S-1:e.end-S),0===z&&(e.write=S,m=e.wt(y,m),S=e.write,z=S<e.read?e.read-S-1:e.end-S,S==e.end&&0!==e.read&&(S=0,z=S<e.read?e.read-S-1:e.end-S),0===z)))return e.ot=x,e.ct=_,y.We=v,y.qe+=A-y.nt,y.nt=A,e.write=S,e.wt(y,m);m=0,e.lt[S++]=f,z--,t=0;break;case 7:if(_>7&&(_-=8,v++,A--),e.write=S,m=e.wt(y,m),S=e.write,z=S<e.read?e.read-S-1:e.end-S,e.read!=e.write)return e.ot=x,e.ct=_,y.We=v,y.qe+=A-y.nt,y.nt=A,e.write=S,e.wt(y,m);t=8;case 8:return m=1,e.ot=x,e.ct=_,y.We=v,y.qe+=A-y.nt,y.nt=A,e.write=S,e.wt(y,m);case 9:return m=Ye,e.ot=x,e.ct=_,y.We=v,y.qe+=A-y.nt,y.nt=A,e.write=S,e.wt(y,m);default:return m=Xe,e.ot=x,e.ct=_,y.We=v,y.qe+=A-y.nt,y.nt=A,e.write=S,e.wt(y,m)}},e.ht=()=>{}}ot.dt=(e,t,n,r)=>(e[0]=9,t[0]=5,n[0]=et,r[0]=tt,0);const ft=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];function at(e,t){const n=this;let r,s=0,o=0,c=0,a=0;const l=[0],u=[0],w=new ct;let h=0,d=new f(4320);const p=new ot;n.ct=0,n.ot=0,n.lt=new i(t),n.end=t,n.read=0,n.write=0,n.reset=(e,t)=>{t&&(t[0]=0),6==s&&w.ht(e),s=0,n.ct=0,n.ot=0,n.read=n.write=0},n.reset(e,null),n.wt=(e,t)=>{let r,s,i;return s=e.rt,i=n.read,r=(i>n.write?n.end:n.write)-i,r>e.tt&&(r=e.tt),0!==r&&t==Ze&&(t=0),e.tt-=r,e.Ge+=r,e.$e.set(n.lt.subarray(i,i+r),s),s+=r,i+=r,i==n.end&&(i=0,n.write==n.end&&(n.write=0),r=n.write-i,r>e.tt&&(r=e.tt),0!==r&&t==Ze&&(t=0),e.tt-=r,e.Ge+=r,e.$e.set(n.lt.subarray(i,i+r),s),s+=r,i+=r),e.rt=s,n.read=i,t},n.ut=(e,t)=>{let i,f,y,m,b,g,k,v;for(m=e.nt,b=e.We,f=n.ot,y=n.ct,g=n.write,k=g<n.read?n.read-g-1:n.end-g;;){let S,z,C,x,_,A,I,D;switch(s){case 0:for(;3>y;){if(0===b)return n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);t=0,b--,f|=(255&e.ft(m++))<<y,y+=8}switch(i=7&f,h=1&i,i>>>1){case 0:f>>>=3,y-=3,i=7&y,f>>>=i,y-=i,s=1;break;case 1:S=[],z=[],C=[[]],x=[[]],ot.dt(S,z,C,x),w.init(S[0],z[0],C[0],0,x[0],0),f>>>=3,y-=3,s=6;break;case 2:f>>>=3,y-=3,s=3;break;case 3:return f>>>=3,y-=3,s=9,e.Le="invalid block type",t=Ye,n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t)}break;case 1:for(;32>y;){if(0===b)return n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);t=0,b--,f|=(255&e.ft(m++))<<y,y+=8}if((~f>>>16&65535)!=(65535&f))return s=9,e.Le="invalid stored block lengths",t=Ye,n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);o=65535&f,f=y=0,s=0!==o?2:0!==h?7:0;break;case 2:if(0===b)return n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);if(0===k&&(g==n.end&&0!==n.read&&(g=0,k=g<n.read?n.read-g-1:n.end-g),0===k&&(n.write=g,t=n.wt(e,t),g=n.write,k=g<n.read?n.read-g-1:n.end-g,g==n.end&&0!==n.read&&(g=0,k=g<n.read?n.read-g-1:n.end-g),0===k)))return n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);if(t=0,i=o,i>b&&(i=b),i>k&&(i=k),n.lt.set(e.je(m,i),g),m+=i,b-=i,g+=i,k-=i,0!=(o-=i))break;s=0!==h?7:0;break;case 3:for(;14>y;){if(0===b)return n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);t=0,b--,f|=(255&e.ft(m++))<<y,y+=8}if(c=i=16383&f,(31&i)>29||(i>>5&31)>29)return s=9,e.Le="too many length or distance symbols",t=Ye,n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);if(i=258+(31&i)+(i>>5&31),!r||r.length<i)r=[];else for(v=0;i>v;v++)r[v]=0;f>>>=14,y-=14,a=0,s=4;case 4:for(;4+(c>>>10)>a;){for(;3>y;){if(0===b)return n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);t=0,b--,f|=(255&e.ft(m++))<<y,y+=8}r[ft[a++]]=7&f,f>>>=3,y-=3}for(;19>a;)r[ft[a++]]=0;if(l[0]=7,i=p.st(r,l,u,d,e),0!=i)return(t=i)==Ye&&(r=null,s=9),n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);a=0,s=5;case 5:for(;i=c,258+(31&i)+(i>>5&31)>a;){let o,w;for(i=l[0];i>y;){if(0===b)return n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);t=0,b--,f|=(255&e.ft(m++))<<y,y+=8}if(i=d[3*(u[0]+(f&$e[i]))+1],w=d[3*(u[0]+(f&$e[i]))+2],16>w)f>>>=i,y-=i,r[a++]=w;else{for(v=18==w?7:w-14,o=18==w?11:3;i+v>y;){if(0===b)return n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);t=0,b--,f|=(255&e.ft(m++))<<y,y+=8}if(f>>>=i,y-=i,o+=f&$e[v],f>>>=v,y-=v,v=a,i=c,v+o>258+(31&i)+(i>>5&31)||16==w&&1>v)return r=null,s=9,e.Le="invalid bit length repeat",t=Ye,n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);w=16==w?r[v-1]:0;do{r[v++]=w}while(0!=--o);a=v}}if(u[0]=-1,_=[],A=[],I=[],D=[],_[0]=9,A[0]=6,i=c,i=p.it(257+(31&i),1+(i>>5&31),r,_,A,I,D,d,e),0!=i)return i==Ye&&(r=null,s=9),t=i,n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);w.init(_[0],A[0],d,I[0],d,D[0]),s=6;case 6:if(n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,1!=(t=w.ut(n,e,t)))return n.wt(e,t);if(t=0,w.ht(e),m=e.nt,b=e.We,f=n.ot,y=n.ct,g=n.write,k=g<n.read?n.read-g-1:n.end-g,0===h){s=0;break}s=7;case 7:if(n.write=g,t=n.wt(e,t),g=n.write,k=g<n.read?n.read-g-1:n.end-g,n.read!=n.write)return n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);s=8;case 8:return t=1,n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);case 9:return t=Ye,n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);default:return t=Xe,n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t)}}},n.ht=e=>{n.reset(e,null),n.lt=null,d=null},n.yt=(e,t,r)=>{n.lt.set(e.subarray(t,t+r),0),n.read=n.write=r},n.bt=()=>1==s?1:0}const lt=13,ut=[0,0,255,255];function wt(){const e=this;function t(e){return e&&e.gt?(e.qe=e.Ge=0,e.Le=null,e.gt.mode=7,e.gt.kt.reset(e,null),0):Xe}e.mode=0,e.method=0,e.vt=[0],e.St=0,e.marker=0,e.zt=0,e.Ct=t=>(e.kt&&e.kt.ht(t),e.kt=null,0),e.xt=(n,r)=>(n.Le=null,e.kt=null,8>r||r>15?(e.Ct(n),Xe):(e.zt=r,n.gt.kt=new at(n,1<<r),t(n),0)),e._t=(e,t)=>{let n,r;if(!e||!e.gt||!e.et)return Xe;const s=e.gt;for(t=4==t?Ze:0,n=Ze;;)switch(s.mode){case 0:if(0===e.We)return n;if(n=t,e.We--,e.qe++,8!=(15&(s.method=e.ft(e.nt++)))){s.mode=lt,e.Le="unknown compression method",s.marker=5;break}if(8+(s.method>>4)>s.zt){s.mode=lt,e.Le="invalid win size",s.marker=5;break}s.mode=1;case 1:if(0===e.We)return n;if(n=t,e.We--,e.qe++,r=255&e.ft(e.nt++),((s.method<<8)+r)%31!=0){s.mode=lt,e.Le="incorrect header check",s.marker=5;break}if(0==(32&r)){s.mode=7;break}s.mode=2;case 2:if(0===e.We)return n;n=t,e.We--,e.qe++,s.St=(255&e.ft(e.nt++))<<24&4278190080,s.mode=3;case 3:if(0===e.We)return n;n=t,e.We--,e.qe++,s.St+=(255&e.ft(e.nt++))<<16&16711680,s.mode=4;case 4:if(0===e.We)return n;n=t,e.We--,e.qe++,s.St+=(255&e.ft(e.nt++))<<8&65280,s.mode=5;case 5:return 0===e.We?n:(n=t,e.We--,e.qe++,s.St+=255&e.ft(e.nt++),s.mode=6,2);case 6:return s.mode=lt,e.Le="need dictionary",s.marker=0,Xe;case 7:if(n=s.kt.ut(e,n),n==Ye){s.mode=lt,s.marker=0;break}if(0==n&&(n=t),1!=n)return n;n=t,s.kt.reset(e,s.vt),s.mode=12;case 12:return e.We=0,1;case lt:return Ye;default:return Xe}},e.At=(e,t,n)=>{let r=0,s=n;if(!e||!e.gt||6!=e.gt.mode)return Xe;const i=e.gt;return s<1<<i.zt||(s=(1<<i.zt)-1,r=n-s),i.kt.yt(t,r,s),i.mode=7,0},e.It=e=>{let n,r,s,i,o;if(!e||!e.gt)return Xe;const c=e.gt;if(c.mode!=lt&&(c.mode=lt,c.marker=0),0===(n=e.We))return Ze;for(r=e.nt,s=c.marker;0!==n&&4>s;)e.ft(r)==ut[s]?s++:s=0!==e.ft(r)?0:4-s,r++,n--;return e.qe+=r-e.nt,e.nt=r,e.We=n,c.marker=s,4!=s?Ye:(i=e.qe,o=e.Ge,t(e),e.qe=i,e.Ge=o,c.mode=7,0)},e.Dt=e=>e&&e.gt&&e.gt.kt?e.gt.kt.bt():Xe}function ht(){}function dt(e){const t=new ht,n=e&&e.chunkSize?r.floor(2*e.chunkSize):131072,o=new i(n);let c=!1;t.xt(),t.$e=o,this.append=(e,r)=>{const f=[];let a,l,u=0,w=0,h=0;if(0!==e.length){t.nt=0,t.et=e,t.We=e.length;do{if(t.rt=0,t.tt=n,0!==t.We||c||(t.nt=0,c=!0),a=t._t(0),c&&a===Ze){if(0!==t.We)throw new s("inflating: bad input")}else if(0!==a&&1!==a)throw new s("inflating: "+t.Le);if((c||1===a)&&t.We===e.length)throw new s("inflating: bad input");t.rt&&(t.rt===n?f.push(new i(o)):f.push(o.subarray(0,t.rt))),h+=t.rt,r&&t.nt>0&&t.nt!=u&&(r(t.nt),u=t.nt)}while(t.We>0||0===t.tt);return f.length>1?(l=new i(h),f.forEach((e=>{l.set(e,w),w+=e.length}))):l=f[0]?new i(f[0]):new i,l}},this.flush=()=>{t.Ct()}}ht.prototype={xt(e){const t=this;return t.gt=new wt,e||(e=15),t.gt.xt(t,e)},_t(e){const t=this;return t.gt?t.gt._t(t,e):Xe},Ct(){const e=this;if(!e.gt)return Xe;const t=e.gt.Ct(e);return e.gt=null,t},It(){const e=this;return e.gt?e.gt.It(e):Xe},At(e,t){const n=this;return n.gt?n.gt.At(n,e,t):Xe},ft(e){return this.et[e]},je(e,t){return this.et.subarray(e,e+t)}},self.initCodec=()=>{self.Deflate=Qe,self.Inflate=dt};
`],{type:"text/javascript"}));n({workerScripts:{inflate:[t],deflate:[t]}})}Il(Ni);var K=Kt.Platform.isDesktopApp?window.require("node:original-fs"):null,Pe=Kt.Platform.isDesktopApp?K.promises:null,Xt=Kt.Platform.isDesktopApp?window.require("node:os"):null,q=Kt.Platform.isDesktopApp?window.require("node:path"):null,Jn=Kt.Platform.isDesktopApp?window.require("node:url"):null,Fl=Kt.Platform.isDesktopApp?window.require("node:zlib"):null;function Zi(n,t=0,e=n.byteLength){return n.buffer.slice(n.byteOffset+t,n.byteOffset+t+e)}var Me=class{constructor(t){this.type="file";this.filepath=t;let e=this.name=q.basename(t);this.fullpath=e;let i=q.extname(e);this.extension=i.substring(1).toLowerCase(),this.basename=q.basename(e,i)}async readText(){return Pe.readFile(this.filepath,"utf8")}async read(){let t=await Pe.readFile(this.filepath);return Zi(t)}async readZip(t){let e=null;try{e=await Pe.open(this.filepath,"r");let i=await e.stat();return await t(new Vt(new No(e,i.size)))}finally{await(e==null?void 0:e.close())}}createReadStream(){return K.createReadStream(this.filepath)}toString(){return this.filepath}},ut=class{constructor(t){this.type="folder";this.filepath=t,this.name=q.basename(t)}async list(){let{filepath:t}=this,e=await Pe.readdir(t,{withFileTypes:!0}),i=[];for(let r of e)r.isFile()?i.push(new Me(q.join(t,r.name))):r.isDirectory()&&i.push(new ut(q.join(t,r.name)));return i}toString(){return this.filepath}},Wi=class{constructor(t){this.type="file";this.file=t;let e=this.name=t.name;this.fullpath=e;let{basename:i,extension:r}=re(e);this.basename=i,this.extension=r}readText(){let{file:t}=this;return t.text?t.text():new Promise((e,i)=>{let r=new FileReader;r.addEventListener("load",()=>e(r.result)),r.addEventListener("error",i),r.readAsText(this.file)})}async read(){let{file:t}=this;return t.arrayBuffer?t.arrayBuffer():new Promise((e,i)=>{let r=new FileReader;r.addEventListener("load",()=>e(r.result)),r.addEventListener("error",i),r.readAsArrayBuffer(this.file)})}async readZip(t){return t(new Vt(new St(this.file)))}toString(){return this.file.toString()}};async function So(n,t){let e=[];for(let i of n)try{i.type==="folder"?e.push(...await So(await i.list(),t)):i.type==="file"&&(!t||t(i))&&e.push(i)}catch(r){console.log("Skipping path: ",i.name,r)}return e}function re(n){let t=Math.max(n.lastIndexOf("/"),n.lastIndexOf("\\")),e=n,i="";t>=0&&(e=n.substring(t+1),i=n.substring(0,t));let[r,o]=Ro(e);return{parent:i,name:e,basename:r,extension:o}}function Ro(n){let t=n.lastIndexOf("."),e=n,i="";return t>0&&(e=n.substring(0,t),i=n.substring(t+1).toLowerCase()),[e,i]}var No=class extends yn{constructor(e,i){super(e);this.fd=e,this.size=i}async readUint8Array(e,i){let r=Buffer.alloc(i),o=await this.fd.read(r,0,i,e);return new Uint8Array(Zi(r,0,o.bytesRead))}};var Ll=require("obsidian"),Qm=/[\/\?<>\\:\*\|"]/g,eh=/[\x00-\x1f\x80-\x9f]/g,th=/^\.+$/,nh=/^(con|prn|aux|nul|com[0-9]|lpt[0-9])(\..*)?$/i,ih=/[\. ]+$/,rh=/^\./,oh=/[\[\]#|^]/g;function Ze(n){return n.replace(Qm,"").replace(eh,"").replace(th,"").replace(nh,"").replace(ih,"").replace(rh,"").replace(oh,"")}function xn(n){let t=[];for(let e=0;e<n;e++)t.push((Math.random()*16|0).toString(16));return t.join("")}function ft(n){return new DOMParser().parseFromString(n,"text/html").documentElement}function ah(n){return n.buffer.slice(n.byteOffset,n.byteOffset+n.byteLength)}function Oo(n){return ah(new TextEncoder().encode(n))}function Yi(n){return Object.isEmpty(n)?"":`---
`+(0,Ll.stringifyYaml)(n)+`---
`}function Qn(n,t,e="..."){return n.length<t?n:n.substring(0,t)+e}var Re=require("obsidian");var Pl=300,ce=class{constructor(t,e){this.files=[];this.outputLocation="";this.notAvailable=!1;this.outputFolder=null;this.app=t,this.vault=t.vault,this.modal=e,this.init()}registerAuthCallback(t){this.modal.plugin.registerAuthCallback(t)}addFileChooserSetting(t,e,i=!1){let r=new Re.Setting(this.modal.contentEl).setName("Files to import").setDesc("Pick the files that you want to import.").addButton(a=>a.setButtonText(i?"Choose files":"Choose file").onClick(async()=>{if(Re.Platform.isDesktopApp){let s=["openFile","dontAddToRecent"];i&&s.push("multiSelections");let l=window.electron.remote.dialog.showOpenDialogSync({title:"Pick files to import",properties:s,filters:[{name:t,extensions:e}]});l&&l.length>0&&(this.files=l.map(c=>new Me(c)),o())}else{let s=createEl("input");s.type="file",s.accept=e.map(l=>"."+l.toLowerCase()).join(","),s.addEventListener("change",()=>{if(!s.files)return;let l=Array.from(s.files);l.length>0&&(this.files=l.map(c=>new Wi(c)).filter(c=>e.contains(c.extension)),o())}),s.click()}}));i&&Re.Platform.isDesktopApp&&r.addButton(a=>a.setButtonText("Choose folders").onClick(async()=>{if(Re.Platform.isDesktopApp){let s=window.electron.remote.dialog.showOpenDialogSync({title:"Pick folders to import",properties:["openDirectory","multiSelections","dontAddToRecent"]});if(s&&s.length>0){r.setDesc("Reading folders...");let l=s.map(c=>new ut(c));this.files=await So(l,c=>e.contains(c.extension)),o()}}}));let o=()=>{let a=document.createDocumentFragment(),s=this.files.length,l=this.files.map(c=>c.name).join(", ");l.length>Pl&&(l=l.substring(0,Pl)+"..."),a.createEl("span",{text:`These ${s} files will be imported: `}),a.createEl("br"),a.createEl("span",{cls:"u-pop",text:l}),r.setDesc(a)}}addOutputLocationSetting(t){this.outputLocation=t,new Re.Setting(this.modal.contentEl).setName("Output folder").setDesc("Choose a folder in the vault to put the imported files. Leave empty to output to vault root.").addText(e=>e.setValue(t).onChange(i=>{this.outputLocation=i,this.outputFolder=null}))}async getOutputFolder(){if(this.outputFolder)return this.outputFolder;let{vault:t}=this.app,e=this.outputLocation;e===""&&(e="/");let i=t.getAbstractFileByPath(e);return(i===null||!(i instanceof Re.TFolder))&&(await t.createFolder(e),i=t.getAbstractFileByPath(e)),i instanceof Re.TFolder?(this.outputFolder=i,i):null}async getAvailablePathForAttachment(t,e){let i=await this.getOutputFolder(),r=i?{parent:i}:null,{basename:o,extension:a}=re(t),s=await this.vault.getAvailablePathForAttachments(o,a,r),l=re(s),c=l.extension?"."+l.extension:"."+a,p=1,m=s;for(;e.includes(m)||this.vault.getAbstractFileByPath(m);)m=q.join(l.parent,`${l.name} ${p}${c}`),p++;return m}async createFolders(t){let e=t.split("/").map(o=>o.replace(/^\.+/,"")).join("/"),i=(0,Re.normalizePath)(e),r=this.vault.getAbstractFileByPathInsensitive(i);if(r&&r instanceof Re.TFolder)return r;if(await this.vault.createFolder(i),r=this.vault.getAbstractFileByPathInsensitive(i),!(r instanceof Re.TFolder))throw new Error(`Failed to create folder at "${t}"`);return r}async saveAsMarkdownFile(t,e,i){let r=Ze(e);return await this.app.fileManager.createNewMarkdownFile(t,r,i)}};var Zp=rs(Lp());var ka=require("obsidian");var Pp=n=>{let e=gr.execSync(`${n} --version`).toString().toString().match(/(\d+)\.(\d+).(\d+)/);return(e==null?void 0:e[1])>3||(e==null?void 0:e[2])>32},Mp=n=>{let t=[];if(!n)return t;let e=[],i=0,r=-1,o={};for(;i<n.length;){let a="";if(n[i]==="'")for(i++;i<n.length;)if(n[i]!=="'"){let s=n.indexOf("'",i);a+=n.substring(i,s),i=s}else if(n[i+1]==="'")a+=n[i],i+=2;else{i++;break}else if(n[i]==="N")a=null,i+=4;else{let s=Math.min(...[n.indexOf(",",i),n.indexOf(`
`,i)].filter(l=>l>0),n.length-1);a=parseFloat(n.substring(i,s)),i=s}r==-1?e.push(a):(o[e[r]]=a,r++),(n[i]==`
`||e.length<r)&&(r!==-1&&t.push(o),r=0,o={}),i++}return t};function $p(n){for(var t=n[0],e=1,i=arguments.length;e<i;e++)t+=arguments[e]+n[e];return t}var{isArray:bg}=Array,yr=class extends String{},Bp=(n,...t)=>{let e=[n[0]],i=[e];for(let r=0;r<t.length;r++)t[r]instanceof yr?e[e.length-1]+=t[r]+n[r+1]:(bg(t[r])?(e.push(...t[r].slice(1).map(o=>",")),i.push(...t[r].length?t[r]:[""])):i.push(t[r]),e.push(n[r+1]));return i},Up=n=>new yr(n);var ui=(n,t)=>{let e="SQLITE_ERROR",i=new Error(e+": "+t);return i.code=e,n(i),""},jp=(...n)=>Up($p(...n)),{from:xg}=Array,wg=/'/g,vg=n=>n.toString(16).padStart(2,"0"),Eg=n=>`x'${xg(n,vg).join("")}'`,_g=n=>{switch(typeof n){case"string":return"'"+n.replace(wg,"''")+"'";case"number":if(!isFinite(n))return;case"boolean":return+n;case"object":case"undefined":switch(!0){case!n:return"NULL";case n instanceof Date:return"'"+n.toISOString()+"'";case n instanceof Buffer:case n instanceof ArrayBuffer:n=new Uint8Array(n);case n instanceof Uint8Array:case n instanceof Uint8ClampedArray:return Eg(n)}}},_a=(n,t)=>{let[e,...i]=Bp(...t),r=[e[0]];for(let a=0;a<i.length;a++){let s=_g(i[a]);if(s===void 0)return ui(n,"incompatible "+typeof s+"value");r.push(s,e[a+1])}let o=r.join("").trim();return o.length?o:ui(n,"empty query")};var Ta=ka.Platform.isDesktopApp?window.require("node:crypto"):null,gr=ka.Platform.isDesktopApp?window.require("node:child_process"):null,Na=Ta==null?void 0:Ta.randomUUID(),Tg=`[{"_":"${Na}"}]
`,Ag=`'_'
'${Na}'
`,{isArray:kg}=Array,{parse:qp}=JSON,{defineProperty:Ng}=Object,Sg=()=>{},Rg=(n,t,e,i,r,o)=>{let a=[],{stdout:s,stderr:l}=gr.spawn(i,r,o).on("close",p=>{if(c||p!==0){p!==0&&ui(t,"busy DB or query too slow");return}let m=a.join("").trim();if(e==="query")n(m);else{let y=qp(m||"[]");n(e==="get"&&kg(y)?y.shift():y)}});s.on("data",p=>{a.push(p)});let c=!1;l.on("data",p=>{c=!0,ui(t,"".trim.call(p))})},Og=(n,t,e)=>{let i=Pp(n),r=i?Tg:Ag,{stdin:o,stdout:a,stderr:s}=gr.spawn(n,t);i?o.write(`.mode json
`):o.write(`.mode quote
.headers on
`),e&&o.write(`.timeout ${e}
`);let l=Promise.resolve();return(c,p,m,y,w)=>{m==="close"?(o.write(`.quit
`),l=null):l&&(l=l.then(()=>new Promise(E=>{let d="",f=_=>{d+=_;let A=!1;for(;d.endsWith(r);)A=!0,d=d.slice(0,-r.length);if(A){for(v();d.startsWith(r);)d=d.slice(r.length);if(m==="query")c(d);else{let D=i?qp(d||"[]"):Mp(d);c(m==="get"?D.shift():D)}}},g=_=>{v(),p(new Error(_))},v=()=>{E(),a.removeListener("data",f),s.removeListener("data",g)};a.on("data",f),s.once("data",g),o.write(`${w[w.length-1]};
`),o.write(`SELECT '${Na}' as _;
`)})))}},Aa=(n,t,e,i,r)=>(...o)=>new Promise((a,s)=>{let l=_a(s,o);l.length&&(n==="get"&&/^SELECT\s+/i.test(l)&&!/\s+LIMIT\s+\d+$/i.test(l)&&(l+=" LIMIT 1"),e(a,s,n,t,i.concat(l),r))}),Hp="";function Sa(n,t={}){n===":memory:"&&(n=Hp||(Hp=q.join(Xt.tmpdir(),randomUUID())));let e=t.timeout||0,i=t.bin||"sqlite3",r=[n,"-bail"],o={timeout:e};t.readonly&&r.push("-readonly"),e&&r.push("-cmd",".timeout "+e);let a=r.concat("-json"),s=t.exec||(t.persistent?Og(i,r,e):Rg);return{transaction(){let l=[];return Ng((...c)=>{l.push(c)},"commit",{value(){return new Promise((c,p)=>{let m=["BEGIN TRANSACTION"];for(let y of l){let w=_a(p,y);if(!w.length)return;m.push(w)}m.push("COMMIT"),s(c,p,"query",i,r.concat(m.join(";")),o)})}})},query:Aa("query",i,s,r,o),get:Aa("get",i,s,a,o),all:Aa("all",i,s,a,o),close:t.persistent?()=>s(null,null,"close"):Sg,raw:jp}}var Ra="Library/Group Containers/group.com.apple.notes",Wp="NoteStore.sqlite",Dg=*********,br=class extends ce{constructor(){super(...arguments);this.owners={};this.resolvedAccounts={};this.resolvedFiles={};this.resolvedFolders={};this.multiAccount=!1;this.noteCount=0;this.parsedNotes=0;this.omitFirstLine=!0;this.importTrashed=!1;this.includeHandwriting=!1;this.trashFolders=[]}init(){if(!st.Platform.isMacOS||!st.Platform.isDesktop){this.modal.contentEl.createEl("p",{text:"Due to platform limitations, Apple Notes cannot be exported from this device. Open your vault on a Mac to export from Apple Notes."}),this.notAvailable=!0;return}this.addOutputLocationSetting("Apple Notes"),new st.Setting(this.modal.contentEl).setName("Import recently deleted notes").setDesc('Import notes in the "Recently Deleted" folder. Unlike in Apple Notes, they will not be automatically removed after a set amount of time.').addToggle(e=>e.setValue(!1).onChange(async i=>this.importTrashed=i)),new st.Setting(this.modal.contentEl).setName("Omit first line").setDesc("Don't include the first line in the text, since Apple Notes uses it as the title. It will still be used as the note name.").addToggle(e=>e.setValue(!0).onChange(async i=>this.omitFirstLine=i)),new st.Setting(this.modal.contentEl).setName("Include handwriting text").setDesc("When Apple Notes has detected handwriting in drawings, include it as text before the drawing.").addToggle(e=>e.setValue(!1).onChange(async i=>this.includeHandwriting=i))}async getNotesDatabase(){let e=q.join(Xt.homedir(),Ra),i=window.electron.remote.dialog.showOpenDialogSync({defaultPath:e,properties:["openDirectory"],message:'Select the "group.com.apple.notes" folder to allow Obsidian to read Apple Notes data.'});if(!(i!=null&&i.includes(e)))return new st.Notice("Data import failed. Ensure you have selected the correct Apple Notes data folder."),null;let r=q.join(e,Wp),o=q.join(Xt.tmpdir(),Wp);return await Pe.copyFile(r,o),await Pe.copyFile(r+"-shm",o+"-shm"),await Pe.copyFile(r+"-wal",o+"-wal"),new Sa(o,{readonly:!0,persistent:!0})}async import(e){if(this.ctx=e,this.protobufRoot=Zp.Root.fromJSON(ls),this.rootFolder=await this.getOutputFolder(),!this.rootFolder){new st.Notice("Please select a location to export to.");return}if(this.database=await this.getNotesDatabase(),!this.database)return;this.keys=Object.fromEntries((await this.database.all`SELECT z_ent, z_name FROM z_primarykey`).map(a=>[a.Z_NAME,a.Z_ENT]));let i=await this.database.all`
			SELECT z_pk FROM ziccloudsyncingobject WHERE z_ent = ${this.keys.ICAccount}
		`,r=await this.database.all`
			SELECT z_pk, ztitle2 FROM ziccloudsyncingobject WHERE z_ent = ${this.keys.ICFolder}
		`;for(let a of i)await this.resolveAccount(a.Z_PK);for(let a of r)try{await this.resolveFolder(a.Z_PK)}catch(s){this.ctx.reportFailed(a.ZTITLE2,s==null?void 0:s.message),console.error(s)}let o=await this.database.all`
			SELECT
				z_pk, zfolder, ztitle1 FROM ziccloudsyncingobject
			WHERE
				z_ent = ${this.keys.ICNote}
				AND ztitle1 IS NOT NULL
				AND zfolder NOT IN (${this.trashFolders})
		`;this.noteCount=o.length;for(let a of o)try{await this.resolveNote(a.Z_PK)}catch(s){this.ctx.reportFailed(a.ZTITLE1,s==null?void 0:s.message),console.error(s)}this.database.close()}async resolveAccount(e){!this.multiAccount&&Object.keys(this.resolvedAccounts).length&&(this.multiAccount=!0);let i=await this.database.get`
			SELECT zname, zidentifier FROM ziccloudsyncingobject
			WHERE z_ent = ${this.keys.ICAccount} AND z_pk = ${e}
		`;this.resolvedAccounts[e]={name:i.ZNAME,uuid:i.ZIDENTIFIER,path:q.join(Xt.homedir(),Ra,"Accounts",i.ZIDENTIFIER)}}async resolveFolder(e){var a;if(e in this.resolvedFiles)return this.resolvedFolders[e];let i=await this.database.get`
			SELECT ztitle2, zparent, zidentifier, zfoldertype, zowner
			FROM ziccloudsyncingobject
			WHERE z_ent = ${this.keys.ICFolder} AND z_pk = ${e}
		`,r;if(i.ZFOLDERTYPE==3)return null;if(!this.importTrashed&&i.ZFOLDERTYPE==1)return this.trashFolders.push(e),null;if(i.ZPARENT!==null)r=((a=await this.resolveFolder(i.ZPARENT))==null?void 0:a.path)+"/";else if(this.multiAccount){let s=this.resolvedAccounts[i.ZOWNER].name;r=`${this.rootFolder.path}/${s}/`}else r=`${this.rootFolder.path}/`;i.ZIDENTIFIER.startsWith("DefaultFolder")||(r+=Ze(i.ZTITLE2));let o=await this.createFolders(r);return this.resolvedFolders[e]=o,this.owners[e]=i.ZOWNER,o}async resolveNote(e){if(e in this.resolvedFiles)return this.resolvedFiles[e];let i=await this.database.get`
			SELECT
				nd.z_pk, hex(nd.zdata) as zhexdata, zcso.ztitle1, zfolder,
				zcreationdate1, zcreationdate2, zcreationdate3, zmodificationdate1, zispasswordprotected
			FROM
				zicnotedata AS nd,
				(SELECT
					*, NULL AS zcreationdate3, NULL AS zcreationdate2,
					NULL AS zispasswordprotected FROM ziccloudsyncingobject
				) AS zcso
			WHERE
				zcso.z_pk = nd.znote
				AND zcso.z_pk = ${e}
		`;if(i.ZISPASSWORDPROTECTED)return this.ctx.reportSkipped(i.ZTITLE1,"note is password protected"),null;let r=this.resolvedFolders[i.ZFOLDER]||this.rootFolder,o=`${i.ZTITLE1}.md`,a=await this.saveAsMarkdownFile(r,o,"");this.ctx.status(`Importing note ${o}`),this.resolvedFiles[e]=a,this.owners[e]=this.owners[i.ZFOLDER];let s=this.decodeData(i.zhexdata,Wt);return this.vault.modify(a,await s.format(),{ctime:this.decodeTime(i.ZCREATIONDATE3||i.ZCREATIONDATE2||i.ZCREATIONDATE1),mtime:this.decodeTime(i.ZMODIFICATIONDATE1)}),this.parsedNotes++,this.ctx.reportProgress(this.parsedNotes,this.noteCount),a}async resolveAttachment(e,i){if(e in this.resolvedFiles)return this.resolvedFiles[e];let r,o,a,s,l;switch(i){case"com.apple.paper.doc.scan":s=await this.database.get`
					SELECT
						zidentifier, zfallbackpdfgeneration, zcreationdate, zmodificationdate, znote
					FROM
						(SELECT *, NULL AS zfallbackpdfgeneration FROM ziccloudsyncingobject)
					WHERE
						z_ent = ${this.keys.ICAttachment}
						AND z_pk = ${e}
				`,r=q.join("FallbackPDFs",s.ZIDENTIFIER,s.ZFALLBACKPDFGENERATION||"","FallbackPDF.pdf"),o="Scan",a="pdf";break;case"com.apple.notes.gallery":s=await this.database.get`
					SELECT
						zidentifier, zsizeheight, zsizewidth, zcreationdate, zmodificationdate, znote
					FROM ziccloudsyncingobject
					WHERE
						z_ent = ${this.keys.ICAttachment}
						AND z_pk = ${e}
				`,r=q.join("Previews",`${s.ZIDENTIFIER}-1-${s.ZSIZEWIDTH}x${s.ZSIZEHEIGHT}-0.jpeg`),o="Scan Page",a="jpg";break;case"com.apple.paper":s=await this.database.get`
					SELECT
						zidentifier, zfallbackimagegeneration, zcreationdate, zmodificationdate,
						znote, zhandwritingsummary
					FROM
						(SELECT *, NULL AS zfallbackimagegeneration FROM ziccloudsyncingobject)
					WHERE
						z_ent = ${this.keys.ICAttachment}
						AND z_pk = ${e}
				`,s.ZFALLBACKIMAGEGENERATION?r=q.join("FallbackImages",s.ZIDENTIFIER,s.ZFALLBACKIMAGEGENERATION,"FallbackImage.png"):r=q.join("FallbackImages",`${s.ZIDENTIFIER}.jpg`),o="Drawing",a="png";break;default:s=await this.database.get`
					SELECT
						a.zidentifier, a.zfilename,
						a.zgeneration1, b.zcreationdate, b.zmodificationdate, b.znote
					FROM
						(SELECT *, NULL AS zgeneration1 FROM ziccloudsyncingobject) AS a,
						ziccloudsyncingobject AS b
					WHERE
						a.z_ent = ${this.keys.ICMedia}
						AND a.z_pk = ${e}
						AND a.z_pk = b.zmedia
				`,r=q.join("Media",s.ZIDENTIFIER,s.ZGENERATION1||"",s.ZFILENAME),[o,a]=Ro(s.ZFILENAME);break}try{let c=await this.getAttachmentSource(this.resolvedAccounts[this.owners[s.ZNOTE]],r),p=await this.getAvailablePathForAttachment(`${o}.${a}`,[]);l=await this.vault.createBinary(p,c,{ctime:this.decodeTime(s.ZCREATIONDATE),mtime:this.decodeTime(s.ZMODIFICATIONDATE)})}catch(c){return this.ctx.reportFailed(r),console.error(c),null}return this.resolvedFiles[e]=l,this.ctx.reportAttachmentSuccess(this.resolvedFiles[e].path),l}decodeData(e,i){let r=Fl.gunzipSync(Buffer.from(e,"hex")),o=this.protobufRoot.lookupType(i.protobufType).decode(r);return new i(this,o)}decodeTime(e){return!e||e<1?new Date().getTime():Math.floor((e+Dg)*1e3)}async getAttachmentSource(e,i){try{return await Pe.readFile(q.join(e.path,i))}catch(r){return await Pe.readFile(q.join(Xt.homedir(),Ra,i))}}};var kn=require("obsidian");var fi=class{constructor(t,e){this.type="file";this.entry=e,this.fullpath=t.fullpath+"/"+e.filename;let{parent:i,name:r,basename:o,extension:a}=re(e.filename);this.parent=i,this.name=r,this.basename=o,this.extension=a}async readText(){return this.entry.getData(new $i)}async read(){return(await this.entry.getData(new Vn)).arrayBuffer()}get filepath(){return this.entry.filename}get size(){return this.entry.uncompressedSize}get ctime(){return this.entry.creationDate}get mtime(){return this.entry.lastModDate}async readZip(t){return t(new Vt(new St(new Blob([await this.read()]))))}};async function gt(n,t){await n.readZip(async e=>{let r=(await e.getEntries()).filter(o=>!o.directory&&!!o.getData).map(o=>new fi(n,o));return t(e,r)})}var xr=class extends ce{constructor(){super(...arguments);this.attachmentMap={}}init(){this.addFileChooserSetting("Bear2bk",["bear2bk"]),this.addOutputLocationSetting("Bear")}async import(e){let{files:i}=this;if(i.length===0){new kn.Notice("Please pick at least one file to import.");return}let r=await this.getOutputFolder();if(!r){new kn.Notice("Please select a location to export to.");return}let o=r,a=new RegExp("\\[[^\\]]*\\]\\((assets/[^\\)]+)\\)","gm"),s=await this.createFolders(`${r.path}/archive`),l=await this.createFolders(`${r.path}/trash`);for(let c of i){if(e.isCancelled())return;e.status("Processing "+c.name),await gt(c,async(p,m)=>{let y=await this.collectMetadata(e,m);for(let w of m){if(e.isCancelled())return;let{fullpath:E,filepath:d,parent:f,name:g,extension:v}=w;if(!(g==="info.json"||g==="tags.json")){e.status("Processing "+g);try{if(v==="md"||v==="markdown"){let _=re(f).basename;e.status("Importing note "+_);let A=await w.readText();A=this.removeMarkdownHeader(_,A);let D=[...A.matchAll(a)];if(D.length>0)for(let O of D){let[S,M]=O,L=q.join(f,decodeURI(M)),j=await this.getAttachmentStoragePath(L);j=encodeURI(j);let Z=S.replace(M,j);A=A.replace(S,Z)}let T=(0,kn.normalizePath)(_),k=y[f],b=o;k!=null&&k.archived?b=s:k!=null&&k.trashed&&(b=l);let C=await this.saveAsMarkdownFile(b,T,A);k!=null&&k.ctime&&(k!=null&&k.mtime)&&await this.modifFileTimestamps(k,C),e.reportNoteSuccess(_)}else if(d.match(/\/assets\//g)){e.status("Importing asset "+w.name);let _=await this.getAttachmentStoragePath(w.filepath),A=await w.read();await this.vault.createBinary(_,A),e.reportAttachmentSuccess(w.fullpath)}else e.reportSkipped(E,"unknown type of file")}catch(_){e.reportFailed(E,_)}}}})}}async modifFileTimestamps(e,i){let r={ctime:e.ctime,mtime:e.mtime};await this.vault.append(i,"",r)}async collectMetadata(e,i){let r={};for(let o of i){if(e.isCancelled())return r;if(o.name!=="info.json")continue;let a=await o.readText(),l=JSON.parse(a)["net.shinyfrog.bear"],c=Date.parse(l.creationDate),p=Date.parse(l.modificationDate);r[o.parent]={ctime:isNaN(c)?void 0:c,mtime:isNaN(p)?void 0:p,archived:l.archived===1,trashed:l.trashed===1}}return r}async getAttachmentStoragePath(e){let i=(0,kn.normalizePath)(e);if(this.attachmentMap[i])return this.attachmentMap[i];let r=Object.values(this.attachmentMap),o=await this.getAvailablePathForAttachment(i,r);return o=o.replace(/:/g,""),this.attachmentMap[i]=o,o}removeMarkdownHeader(e,i){if(!i.startsWith("# "))return i;let r=i.indexOf(`
`),o=r>0?i.substring(2,r):i.substring(2);return o=o.trim(),o!==e.trim()&&o!==""?i:r>0?i.substring(r+1):""}};var xi=require("obsidian");var Bf=require("obsidian");var Yp=require("obsidian");var zp=n=>({...n,created:di(n.created),statusupdated:di(n.statusupdated),updated:di(n.updated),duedate:di(n.duedate),taskflag:n.taskflag==="true",reminderdate:n.reminder?di(n.reminder.reminderdate):void 0,sortweight:n.sortweight}),di=n=>n?(0,Yp.moment)(n,"YYYYMMDDThhmmssZ").toDate():void 0;var Gp=/highlight-(?:text|source)-([a-z0-9]+)/;function Cg(n){n.addRule("highlightedCodeBlock",{filter:function(t){var e=t.firstChild;return t.nodeName==="DIV"&&Gp.test(t.className)&&e&&e.nodeName==="PRE"},replacement:function(t,e,i){var r=e.className||"",o=(r.match(Gp)||[null,""])[1];return`

`+i.fence+o+`
`+e.firstChild.textContent+`
`+i.fence+`

`}})}function Ig(n){n.addRule("strikethrough",{filter:["del","s","strike"],replacement:function(t){return"~"+t+"~"}})}var Fg=Array.prototype.indexOf,Lg=Array.prototype.every,Nn={};Nn.tableCell={filter:["th","td"],replacement:function(n,t){return Da(Kp(t))?n:Oa(n,t)}};Nn.tableRow={filter:"tr",replacement:function(n,t){let e=Kp(t);if(Da(e))return n;var i="",r={left:":--",right:"--:",center:":-:"};if(Pg(t)){let l=Xp(e);for(var o=0;o<l;o++){let c=l>=t.childNodes.length?null:t.childNodes[o];var a="---",s=c?(c.getAttribute("align")||"").toLowerCase():"";s&&(a=r[s]||a),c?i+=Oa(a,t.childNodes[o]):i+=Oa(a,null,o)}}return`
`+n+(i?`
`+i:"")}};Nn.table={filter:function(n){return n.nodeName==="TABLE"},replacement:function(n,t){if(Da(t))return n;n=n.replace(/\n+/g,`
`);var e=n.trim().split(`
`);e.length>=2&&(e=e[1]);var i=e.indexOf("| ---")===0,r=Xp(t),o="";return r&&!i&&(o="|"+"     |".repeat(r)+`
|`+" --- |".repeat(r)),`

`+o+n+`

`}};Nn.tableSection={filter:["thead","tbody","tfoot"],replacement:function(n){return n}};function Pg(n){var t=n.parentNode;return t.nodeName==="THEAD"||t.firstChild===n&&(t.nodeName==="TABLE"||Mg(t))&&Lg.call(n.childNodes,function(e){return e.nodeName==="TH"})}function Mg(n){var t=n.previousSibling;return n.nodeName==="TBODY"&&(!t||t.nodeName==="THEAD"&&/^\s*$/i.test(t.textContent))}function Oa(n,t=null,e=null){e===null&&(e=Fg.call(t.parentNode.childNodes,t));var i=" ";e===0&&(i="| ");let r=n.trim().replace(/\n\r/g,"<br>").replace(/\n/g,"<br>");for(r=r.replace(/\|+/g,"\\|");r.length<3;)r+=" ";return t&&(r=$g(r,t," ")),i+r+" |"}function Vp(n){if(!n.childNodes)return!1;for(let t=0;t<n.childNodes.length;t++){let e=n.childNodes[t];if(e.nodeName==="TABLE"||Vp(e))return!0}return!1}function Da(n){return!!(!n||!n.rows||n.rows.length===1&&n.rows[0].childNodes.length<=1||Vp(n))}function Kp(n){let t=n.parentNode;for(;t.nodeName!=="TABLE";)if(t=t.parentNode,!t)return null;return t}function $g(n,t,e){let i=t.getAttribute("colspan")||1;for(let r=1;r<i;r++)n+=" | "+e.repeat(3);return n}function Xp(n){let t=0;for(let e=0;e<n.rows.length;e++){let r=n.rows[e].childNodes.length;r>t&&(t=r)}return t}function Bg(n){n.keep(function(e){return e.nodeName==="TABLE"});for(var t in Nn)n.addRule(t,Nn[t])}function Ug(n){n.addRule("taskListItems",{filter:function(t){return t.type==="checkbox"&&t.parentNode.nodeName==="LI"},replacement:function(t,e){return(e.checked?"[x]":"[ ]")+" "}})}function Jp(n){n.use([Cg,Ig,Bg,Ug])}var ue=n=>{let t={get(e,i){return e[i]}};return new Proxy(n.attributes,t)};var Qp="\n```\n",Ca=n=>{let t=ue(n),e="-en-codeblock:true";return t.style&&t.style.value.indexOf(e)>=0},jg=n=>{let t=ue(n),e="padding-left:",i=0;return t.style&&t.style.value.indexOf(e)>=0&&(i=Math.floor(t.style.value.split(e)[1].split("px")[0]/20)),i},Ia=n=>n.replace(/\\(.)/g,"$1"),eu=(n,t)=>{let e=jg(t);return n=`${"	".repeat(e)}${n}`,Ca(t)?(n=Ia(n),`${Qp}${n}${Qp}`):t.parentElement&&Ca(t.parentElement)&&t.parentElement.firstElementChild===t?`${n}`:t.parentElement&&Ca(t.parentElement)?`
${n}`:t.isBlock?`
${n}
`:n};var wr="\n```\n",Hg="-en-codeblock:true",qg=/\b(Courier|Mono|Consolas|Console|Inconsolata|Pitch|Monaco|monospace)\b/,tu=n=>{var o,a;if(n.nodeType!==1)return null;let t=n.childNodes,e=t.length;if(e>1)return"mixed";if(e===1){let s=tu(t[0]);if(s)return s}let i=ue(n);if(n.tagName==="FONT")return(o=i.face)==null?void 0:o.value;let r=(a=i.style)==null?void 0:a.value;if(r){let s=r.match(/font-family:([^;]+)/);if(s)return s[1]}return null},mi=n=>{var r;let e=(r=ue(n).style)==null?void 0:r.value;if(e&&e.includes(Hg))return!0;let i=tu(n);return!!i&&qg.test(i)},nu=(n,t)=>{if(mi(t)){let e=t.previousSibling,i=e&&e.tagName===t.tagName&&mi(e),r=t.nextSibling,o=r&&r.tagName===t.tagName&&mi(r);return i||o?(n=i?`
${n}`:`${wr}${n}`,n=o?`${n}
`:`${n}${wr}`,n):(n=Ia(n),n.trim()?`${wr}${n}${wr}`:n)}return t.parentElement&&mi(t.parentElement)&&t.parentElement.firstElementChild===t?n:t.parentElement&&mi(t.parentElement)?`
${n}`:t.isBlock?`
${n}
`:n};var qe=n=>t=>t.nodeName===n||t.nodeName.toLowerCase()===n;var iu={filter:qe("IMG"),replacement:(n,t)=>{let e=ue(t);if(!e.src)return"";let i=e.src.value,r=t.width||"",o=t.height||"",a=i;B.sanitizeResourceNameSpaces?a=a.replace(/ /g,B.replacementChar):B.urlEncodeFileNamesAndLinks&&(a=encodeURI(a));let s=r||o?` =${r}x${o}`:"";if(B.keepImageSize)return s=r||o?`|${r||0}x${o||0}`:"",a.startsWith("./")?`![[${a}${s}]]`:`![${s}](${a})`;if(!i.match(/^[a-z]+:/))return`![[${a}]]`;let l=e.src.value.split("/");return`![${l[l.length-1]}](${a})`}};var fe=class{constructor(){this.noteIdNameMap={},this.noteIdNameTOCMap={}}static getInstance(){return fe.instance||(fe.instance=new fe),fe.instance}addItemToMap(t){this.noteIdNameMap[t.url]={...this.noteIdNameMap[t.url],title:t.title,noteName:this.currentNoteName,notebookName:this.currentNotebookName,uniqueEnd:t.uniqueEnd}}addItemToTOCMap(t){this.noteIdNameTOCMap[t.url]={...this.noteIdNameMap[t.url],title:t.title,noteName:this.currentNoteName,notebookName:this.currentNotebookName,uniqueEnd:t.uniqueEnd}}getNoteIdNameMap(){return this.noteIdNameMap}getNoteIdNameTOCMap(){return this.noteIdNameTOCMap}getAllNoteIdNameMap(){return{...this.noteIdNameMap,...this.noteIdNameTOCMap}}getNoteIdNameMapByNoteTitle(t){return Object.values(this.getAllNoteIdNameMap()).filter(e=>e.title===t)}setCurrentNotebookName(t){this.currentNotebookName=t}getCurrentNotebookName(){return this.currentNotebookName}setCurrentNotebookFullpath(t){this.currentNotebookFullpath=t}setCurrentNoteName(t){this.currentNoteName=t}getCurrentNoteName(){return this.currentNoteName}getCurrentNotePath(){return this.currentNotePath}setCurrentNotePath(t){this.currentNotePath=t}getCurrentNotebookFullpath(){return this.currentNotebookFullpath}};var ru=require("obsidian");var Sn=n=>n.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&");var Wg={"application/andrew-inset":"ez","application/applixware":"aw","application/atom+xml":"atom","application/atomcat+xml":"atomcat","application/atomdeleted+xml":"atomdeleted","application/atomsvc+xml":"atomsvc","application/atsc-dwd+xml":"dwd","application/atsc-held+xml":"held","application/atsc-rsat+xml":"rsat","application/bdoc":"bdoc","application/calendar+xml":"xcs","application/ccxml+xml":"ccxml","application/cdfx+xml":"cdfx","application/cdmi-capability":"cdmia","application/cdmi-container":"cdmic","application/cdmi-domain":"cdmid","application/cdmi-object":"cdmio","application/cdmi-queue":"cdmiq","application/cpl+xml":"cpl","application/cu-seeme":"cu","application/dash+xml":"mpd","application/dash-patch+xml":"mpp","application/davmount+xml":"davmount","application/docbook+xml":"dbk","application/dssc+der":"dssc","application/dssc+xml":"xdssc","application/ecmascript":"es","application/emma+xml":"emma","application/emotionml+xml":"emotionml","application/epub+zip":"epub","application/exi":"exi","application/express":"exp","application/fdt+xml":"fdt","application/font-tdpfr":"pfr","application/geo+json":"geojson","application/gml+xml":"gml","application/gpx+xml":"gpx","application/gxf":"gxf","application/gzip":"gz","application/hjson":"hjson","application/hyperstudio":"stk","application/inkml+xml":"ink","application/ipfix":"ipfix","application/its+xml":"its","application/java-archive":"jar","application/java-serialized-object":"ser","application/java-vm":"class","application/javascript":"js","application/json":"json","application/json5":"json5","application/jsonml+json":"jsonml","application/ld+json":"jsonld","application/lgr+xml":"lgr","application/lost+xml":"lostxml","application/mac-binhex40":"hqx","application/mac-compactpro":"cpt","application/mads+xml":"mads","application/manifest+json":"webmanifest","application/marc":"mrc","application/marcxml+xml":"mrcx","application/mathematica":"ma","application/mathml+xml":"mathml","application/mbox":"mbox","application/media-policy-dataset+xml":"mpf","application/mediaservercontrol+xml":"mscml","application/metalink+xml":"metalink","application/metalink4+xml":"meta4","application/mets+xml":"mets","application/mmt-aei+xml":"maei","application/mmt-usd+xml":"musd","application/mods+xml":"mods","application/mp21":"m21","application/mp4":"mp4s","application/msword":"doc","application/mxf":"mxf","application/n-quads":"nq","application/n-triples":"nt","application/node":"cjs","application/octet-stream":"bin","application/oda":"oda","application/oebps-package+xml":"opf","application/ogg":"ogx","application/omdoc+xml":"omdoc","application/onenote":"onetoc","application/oxps":"oxps","application/p2p-overlay+xml":"relo","application/patch-ops-error+xml":"xer","application/pdf":"pdf","application/pgp-encrypted":"pgp","application/pgp-keys":"asc","application/pgp-signature":"asc","application/pics-rules":"prf","application/pkcs10":"p10","application/pkcs7-mime":"p7m","application/pkcs7-signature":"p7s","application/pkcs8":"p8","application/pkix-attr-cert":"ac","application/pkix-cert":"cer","application/pkix-crl":"crl","application/pkix-pkipath":"pkipath","application/pkixcmp":"pki","application/pls+xml":"pls","application/postscript":"ai","application/provenance+xml":"provx","application/prs.cww":"cww","application/pskc+xml":"pskcxml","application/raml+yaml":"raml","application/rdf+xml":"rdf","application/reginfo+xml":"rif","application/relax-ng-compact-syntax":"rnc","application/resource-lists+xml":"rl","application/resource-lists-diff+xml":"rld","application/rls-services+xml":"rs","application/route-apd+xml":"rapd","application/route-s-tsid+xml":"sls","application/route-usd+xml":"rusd","application/rpki-ghostbusters":"gbr","application/rpki-manifest":"mft","application/rpki-roa":"roa","application/rsd+xml":"rsd","application/rss+xml":"rss","application/rtf":"rtf","application/sbml+xml":"sbml","application/scvp-cv-request":"scq","application/scvp-cv-response":"scs","application/scvp-vp-request":"spq","application/scvp-vp-response":"spp","application/sdp":"sdp","application/senml+xml":"senmlx","application/sensml+xml":"sensmlx","application/set-payment-initiation":"setpay","application/set-registration-initiation":"setreg","application/shf+xml":"shf","application/sieve":"siv","application/smil+xml":"smi","application/sparql-query":"rq","application/sparql-results+xml":"srx","application/srgs":"gram","application/srgs+xml":"grxml","application/sru+xml":"sru","application/ssdl+xml":"ssdl","application/ssml+xml":"ssml","application/swid+xml":"swidtag","application/tei+xml":"tei","application/thraud+xml":"tfi","application/timestamped-data":"tsd","application/toml":"toml","application/trig":"trig","application/ttml+xml":"ttml","application/ubjson":"ubj","application/urc-ressheet+xml":"rsheet","application/urc-targetdesc+xml":"td","application/vnd.1000minds.decision-model+xml":"1km","application/vnd.3gpp.pic-bw-large":"plb","application/vnd.3gpp.pic-bw-small":"psb","application/vnd.3gpp.pic-bw-var":"pvb","application/vnd.3gpp2.tcap":"tcap","application/vnd.3m.post-it-notes":"pwn","application/vnd.accpac.simply.aso":"aso","application/vnd.accpac.simply.imp":"imp","application/vnd.acucobol":"acu","application/vnd.acucorp":"atc","application/vnd.adobe.air-application-installer-package+zip":"air","application/vnd.adobe.formscentral.fcdt":"fcdt","application/vnd.adobe.fxp":"fxp","application/vnd.adobe.xdp+xml":"xdp","application/vnd.adobe.xfdf":"xfdf","application/vnd.age":"age","application/vnd.ahead.space":"ahead","application/vnd.airzip.filesecure.azf":"azf","application/vnd.airzip.filesecure.azs":"azs","application/vnd.amazon.ebook":"azw","application/vnd.americandynamics.acc":"acc","application/vnd.amiga.ami":"ami","application/vnd.android.package-archive":"apk","application/vnd.anser-web-certificate-issue-initiation":"cii","application/vnd.anser-web-funds-transfer-initiation":"fti","application/vnd.antix.game-component":"atx","application/vnd.apple.installer+xml":"mpkg","application/vnd.apple.keynote":"key","application/vnd.apple.mpegurl":"m3u8","application/vnd.apple.numbers":"numbers","application/vnd.apple.pages":"pages","application/vnd.apple.pkpass":"pkpass","application/vnd.aristanetworks.swi":"swi","application/vnd.astraea-software.iota":"iota","application/vnd.audiograph":"aep","application/vnd.balsamiq.bmml+xml":"bmml","application/vnd.blueice.multipass":"mpm","application/vnd.bmi":"bmi","application/vnd.businessobjects":"rep","application/vnd.chemdraw+xml":"cdxml","application/vnd.chipnuts.karaoke-mmd":"mmd","application/vnd.cinderella":"cdy","application/vnd.citationstyles.style+xml":"csl","application/vnd.claymore":"cla","application/vnd.cloanto.rp9":"rp9","application/vnd.clonk.c4group":"c4g","application/vnd.cluetrust.cartomobile-config":"c11amc","application/vnd.cluetrust.cartomobile-config-pkg":"c11amz","application/vnd.commonspace":"csp","application/vnd.contact.cmsg":"cdbcmsg","application/vnd.cosmocaller":"cmc","application/vnd.crick.clicker":"clkx","application/vnd.crick.clicker.keyboard":"clkk","application/vnd.crick.clicker.palette":"clkp","application/vnd.crick.clicker.template":"clkt","application/vnd.crick.clicker.wordbank":"clkw","application/vnd.criticaltools.wbs+xml":"wbs","application/vnd.ctc-posml":"pml","application/vnd.cups-ppd":"ppd","application/vnd.curl.car":"car","application/vnd.curl.pcurl":"pcurl","application/vnd.dart":"dart","application/vnd.data-vision.rdz":"rdz","application/vnd.dbf":"dbf","application/vnd.dece.data":"uvf","application/vnd.dece.ttml+xml":"uvt","application/vnd.dece.unspecified":"uvx","application/vnd.dece.zip":"uvz","application/vnd.denovo.fcselayout-link":"fe_launch","application/vnd.dna":"dna","application/vnd.dolby.mlp":"mlp","application/vnd.dpgraph":"dpg","application/vnd.dreamfactory":"dfac","application/vnd.ds-keypoint":"kpxx","application/vnd.dvb.ait":"ait","application/vnd.dvb.service":"svc","application/vnd.dynageo":"geo","application/vnd.ecowin.chart":"mag","application/vnd.enliven":"nml","application/vnd.epson.esf":"esf","application/vnd.epson.msf":"msf","application/vnd.epson.quickanime":"qam","application/vnd.epson.salt":"slt","application/vnd.epson.ssf":"ssf","application/vnd.eszigno3+xml":"es3","application/vnd.ezpix-album":"ez2","application/vnd.ezpix-package":"ez3","application/vnd.fdf":"fdf","application/vnd.fdsn.mseed":"mseed","application/vnd.fdsn.seed":"seed","application/vnd.flographit":"gph","application/vnd.fluxtime.clip":"ftc","application/vnd.framemaker":"fm","application/vnd.frogans.fnc":"fnc","application/vnd.frogans.ltf":"ltf","application/vnd.fsc.weblaunch":"fsc","application/vnd.fujitsu.oasys":"oas","application/vnd.fujitsu.oasys2":"oa2","application/vnd.fujitsu.oasys3":"oa3","application/vnd.fujitsu.oasysgp":"fg5","application/vnd.fujitsu.oasysprs":"bh2","application/vnd.fujixerox.ddd":"ddd","application/vnd.fujixerox.docuworks":"xdw","application/vnd.fujixerox.docuworks.binder":"xbd","application/vnd.fuzzysheet":"fzs","application/vnd.genomatix.tuxedo":"txd","application/vnd.geogebra.file":"ggb","application/vnd.geogebra.tool":"ggt","application/vnd.geometry-explorer":"gex","application/vnd.geonext":"gxt","application/vnd.geoplan":"g2w","application/vnd.geospace":"g3w","application/vnd.gmx":"gmx","application/vnd.google-apps.document":"gdoc","application/vnd.google-apps.presentation":"gslides","application/vnd.google-apps.spreadsheet":"gsheet","application/vnd.google-earth.kml+xml":"kml","application/vnd.google-earth.kmz":"kmz","application/vnd.grafeq":"gqf","application/vnd.groove-account":"gac","application/vnd.groove-help":"ghf","application/vnd.groove-identity-message":"gim","application/vnd.groove-injector":"grv","application/vnd.groove-tool-message":"gtm","application/vnd.groove-tool-template":"tpl","application/vnd.groove-vcard":"vcg","application/vnd.hal+xml":"hal","application/vnd.handheld-entertainment+xml":"zmm","application/vnd.hbci":"hbci","application/vnd.hhe.lesson-player":"les","application/vnd.hp-hpgl":"hpgl","application/vnd.hp-hpid":"hpid","application/vnd.hp-hps":"hps","application/vnd.hp-jlyt":"jlt","application/vnd.hp-pcl":"pcl","application/vnd.hp-pclxl":"pclxl","application/vnd.hydrostatix.sof-data":"sfd-hdstx","application/vnd.ibm.minipay":"mpy","application/vnd.ibm.modcap":"afp","application/vnd.ibm.rights-management":"irm","application/vnd.ibm.secure-container":"sc","application/vnd.iccprofile":"icc","application/vnd.igloader":"igl","application/vnd.immervision-ivp":"ivp","application/vnd.immervision-ivu":"ivu","application/vnd.insors.igm":"igm","application/vnd.intercon.formnet":"xpw","application/vnd.intergeo":"i2g","application/vnd.intu.qbo":"qbo","application/vnd.intu.qfx":"qfx","application/vnd.ipunplugged.rcprofile":"rcprofile","application/vnd.irepository.package+xml":"irp","application/vnd.is-xpr":"xpr","application/vnd.isac.fcs":"fcs","application/vnd.jam":"jam","application/vnd.jcp.javame.midlet-rms":"rms","application/vnd.jisp":"jisp","application/vnd.joost.joda-archive":"joda","application/vnd.kahootz":"ktz","application/vnd.kde.karbon":"karbon","application/vnd.kde.kchart":"chrt","application/vnd.kde.kformula":"kfo","application/vnd.kde.kivio":"flw","application/vnd.kde.kontour":"kon","application/vnd.kde.kpresenter":"kpr","application/vnd.kde.kspread":"ksp","application/vnd.kde.kword":"kwd","application/vnd.kenameaapp":"htke","application/vnd.kidspiration":"kia","application/vnd.kinar":"kne","application/vnd.koan":"skp","application/vnd.kodak-descriptor":"sse","application/vnd.las.las+xml":"lasxml","application/vnd.llamagraphics.life-balance.desktop":"lbd","application/vnd.llamagraphics.life-balance.exchange+xml":"lbe","application/vnd.lotus-1-2-3":"123","application/vnd.lotus-approach":"apr","application/vnd.lotus-freelance":"pre","application/vnd.lotus-notes":"nsf","application/vnd.lotus-organizer":"org","application/vnd.lotus-screencam":"scm","application/vnd.lotus-wordpro":"lwp","application/vnd.macports.portpkg":"portpkg","application/vnd.mapbox-vector-tile":"mvt","application/vnd.mcd":"mcd","application/vnd.medcalcdata":"mc1","application/vnd.mediastation.cdkey":"cdkey","application/vnd.mfer":"mwf","application/vnd.mfmp":"mfm","application/vnd.micrografx.flo":"flo","application/vnd.micrografx.igx":"igx","application/vnd.mif":"mif","application/vnd.mobius.daf":"daf","application/vnd.mobius.dis":"dis","application/vnd.mobius.mbk":"mbk","application/vnd.mobius.mqy":"mqy","application/vnd.mobius.msl":"msl","application/vnd.mobius.plc":"plc","application/vnd.mobius.txf":"txf","application/vnd.mophun.application":"mpn","application/vnd.mophun.certificate":"mpc","application/vnd.mozilla.xul+xml":"xul","application/vnd.ms-artgalry":"cil","application/vnd.ms-cab-compressed":"cab","application/vnd.ms-excel":"xls","application/vnd.ms-excel.addin.macroenabled.12":"xlam","application/vnd.ms-excel.sheet.binary.macroenabled.12":"xlsb","application/vnd.ms-excel.sheet.macroenabled.12":"xlsm","application/vnd.ms-excel.template.macroenabled.12":"xltm","application/vnd.ms-fontobject":"eot","application/vnd.ms-htmlhelp":"chm","application/vnd.ms-ims":"ims","application/vnd.ms-lrm":"lrm","application/vnd.ms-officetheme":"thmx","application/vnd.ms-outlook":"msg","application/vnd.ms-pki.seccat":"cat","application/vnd.ms-pki.stl":"stl","application/vnd.ms-powerpoint":"ppt","application/vnd.ms-powerpoint.addin.macroenabled.12":"ppam","application/vnd.ms-powerpoint.presentation.macroenabled.12":"pptm","application/vnd.ms-powerpoint.slide.macroenabled.12":"sldm","application/vnd.ms-powerpoint.slideshow.macroenabled.12":"ppsm","application/vnd.ms-powerpoint.template.macroenabled.12":"potm","application/vnd.ms-project":"mpp","application/vnd.ms-word.document.macroenabled.12":"docm","application/vnd.ms-word.template.macroenabled.12":"dotm","application/vnd.ms-works":"wps","application/vnd.ms-wpl":"wpl","application/vnd.ms-xpsdocument":"xps","application/vnd.mseq":"mseq","application/vnd.musician":"mus","application/vnd.muvee.style":"msty","application/vnd.mynfc":"taglet","application/vnd.neurolanguage.nlu":"nlu","application/vnd.nitf":"ntf","application/vnd.noblenet-directory":"nnd","application/vnd.noblenet-sealer":"nns","application/vnd.noblenet-web":"nnw","application/vnd.nokia.n-gage.ac+xml":"ac","application/vnd.nokia.n-gage.data":"ngdat","application/vnd.nokia.n-gage.symbian.install":"n-gage","application/vnd.nokia.radio-preset":"rpst","application/vnd.nokia.radio-presets":"rpss","application/vnd.novadigm.edm":"edm","application/vnd.novadigm.edx":"edx","application/vnd.novadigm.ext":"ext","application/vnd.oasis.opendocument.chart":"odc","application/vnd.oasis.opendocument.chart-template":"otc","application/vnd.oasis.opendocument.database":"odb","application/vnd.oasis.opendocument.formula":"odf","application/vnd.oasis.opendocument.formula-template":"odft","application/vnd.oasis.opendocument.graphics":"odg","application/vnd.oasis.opendocument.graphics-template":"otg","application/vnd.oasis.opendocument.image":"odi","application/vnd.oasis.opendocument.image-template":"oti","application/vnd.oasis.opendocument.presentation":"odp","application/vnd.oasis.opendocument.presentation-template":"otp","application/vnd.oasis.opendocument.spreadsheet":"ods","application/vnd.oasis.opendocument.spreadsheet-template":"ots","application/vnd.oasis.opendocument.text":"odt","application/vnd.oasis.opendocument.text-master":"odm","application/vnd.oasis.opendocument.text-template":"ott","application/vnd.oasis.opendocument.text-web":"oth","application/vnd.olpc-sugar":"xo","application/vnd.oma.dd2+xml":"dd2","application/vnd.openblox.game+xml":"obgx","application/vnd.openofficeorg.extension":"oxt","application/vnd.openstreetmap.data+xml":"osm","application/vnd.openxmlformats-officedocument.presentationml.presentation":"pptx","application/vnd.openxmlformats-officedocument.presentationml.slide":"sldx","application/vnd.openxmlformats-officedocument.presentationml.slideshow":"ppsx","application/vnd.openxmlformats-officedocument.presentationml.template":"potx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":"xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.template":"xltx","application/vnd.openxmlformats-officedocument.wordprocessingml.document":"docx","application/vnd.openxmlformats-officedocument.wordprocessingml.template":"dotx","application/vnd.osgeo.mapguide.package":"mgp","application/vnd.osgi.dp":"dp","application/vnd.osgi.subsystem":"esa","application/vnd.palm":"pdb","application/vnd.pawaafile":"paw","application/vnd.pg.format":"str","application/vnd.pg.osasli":"ei6","application/vnd.picsel":"efif","application/vnd.pmi.widget":"wg","application/vnd.pocketlearn":"plf","application/vnd.powerbuilder6":"pbd","application/vnd.previewsystems.box":"box","application/vnd.proteus.magazine":"mgz","application/vnd.publishare-delta-tree":"qps","application/vnd.pvi.ptid1":"ptid","application/vnd.quark.quarkxpress":"qxd","application/vnd.rar":"rar","application/vnd.realvnc.bed":"bed","application/vnd.recordare.musicxml":"mxl","application/vnd.recordare.musicxml+xml":"musicxml","application/vnd.rig.cryptonote":"cryptonote","application/vnd.rim.cod":"cod","application/vnd.rn-realmedia":"rm","application/vnd.rn-realmedia-vbr":"rmvb","application/vnd.route66.link66+xml":"link66","application/vnd.sailingtracker.track":"st","application/vnd.seemail":"see","application/vnd.sema":"sema","application/vnd.semd":"semd","application/vnd.semf":"semf","application/vnd.shana.informed.formdata":"ifm","application/vnd.shana.informed.formtemplate":"itp","application/vnd.shana.informed.interchange":"iif","application/vnd.shana.informed.package":"ipk","application/vnd.simtech-mindmapper":"twd","application/vnd.smaf":"mmf","application/vnd.smart.teacher":"teacher","application/vnd.software602.filler.form+xml":"fo","application/vnd.solent.sdkm+xml":"sdkm","application/vnd.spotfire.dxp":"dxp","application/vnd.spotfire.sfs":"sfs","application/vnd.stardivision.calc":"sdc","application/vnd.stardivision.draw":"sda","application/vnd.stardivision.impress":"sdd","application/vnd.stardivision.math":"smf","application/vnd.stardivision.writer":"sdw","application/vnd.stardivision.writer-global":"sgl","application/vnd.stepmania.package":"smzip","application/vnd.stepmania.stepchart":"sm","application/vnd.sun.wadl+xml":"wadl","application/vnd.sun.xml.calc":"sxc","application/vnd.sun.xml.calc.template":"stc","application/vnd.sun.xml.draw":"sxd","application/vnd.sun.xml.draw.template":"std","application/vnd.sun.xml.impress":"sxi","application/vnd.sun.xml.impress.template":"sti","application/vnd.sun.xml.math":"sxm","application/vnd.sun.xml.writer":"sxw","application/vnd.sun.xml.writer.global":"sxg","application/vnd.sun.xml.writer.template":"stw","application/vnd.sus-calendar":"sus","application/vnd.svd":"svd","application/vnd.symbian.install":"sis","application/vnd.syncml+xml":"xsm","application/vnd.syncml.dm+wbxml":"bdm","application/vnd.syncml.dm+xml":"xdm","application/vnd.syncml.dmddf+xml":"ddf","application/vnd.tao.intent-module-archive":"tao","application/vnd.tcpdump.pcap":"pcap","application/vnd.tmobile-livetv":"tmo","application/vnd.trid.tpt":"tpt","application/vnd.triscape.mxs":"mxs","application/vnd.trueapp":"tra","application/vnd.ufdl":"ufd","application/vnd.uiq.theme":"utz","application/vnd.umajin":"umj","application/vnd.unity":"unityweb","application/vnd.uoml+xml":"uoml","application/vnd.vcx":"vcx","application/vnd.visio":"vsd","application/vnd.visionary":"vis","application/vnd.vsf":"vsf","application/vnd.wap.wbxml":"wbxml","application/vnd.wap.wmlc":"wmlc","application/vnd.wap.wmlscriptc":"wmlsc","application/vnd.webturbo":"wtb","application/vnd.wolfram.player":"nbp","application/vnd.wordperfect":"wpd","application/vnd.wqd":"wqd","application/vnd.wt.stf":"stf","application/vnd.xara":"xar","application/vnd.xfdl":"xfdl","application/vnd.yamaha.hv-dic":"hvd","application/vnd.yamaha.hv-script":"hvs","application/vnd.yamaha.hv-voice":"hvp","application/vnd.yamaha.openscoreformat":"osf","application/vnd.yamaha.openscoreformat.osfpvg+xml":"osfpvg","application/vnd.yamaha.smaf-audio":"saf","application/vnd.yamaha.smaf-phrase":"spf","application/vnd.yellowriver-custom-menu":"cmp","application/vnd.zul":"zir","application/vnd.zzazz.deck+xml":"zaz","application/voicexml+xml":"vxml","application/wasm":"wasm","application/watcherinfo+xml":"wif","application/widget":"wgt","application/winhlp":"hlp","application/wsdl+xml":"wsdl","application/wspolicy+xml":"wspolicy","application/x-7z-compressed":"7z","application/x-abiword":"abw","application/x-ace-compressed":"ace","application/x-apple-diskimage":"dmg","application/x-arj":"arj","application/x-authorware-bin":"aab","application/x-authorware-map":"aam","application/x-authorware-seg":"aas","application/x-bcpio":"bcpio","application/x-bdoc":"bdoc","application/x-bittorrent":"torrent","application/x-blorb":"blb","application/x-bzip":"bz","application/x-bzip2":"bz2","application/x-cbr":"cbr","application/x-cdlink":"vcd","application/x-cfs-compressed":"cfs","application/x-chat":"chat","application/x-chess-pgn":"pgn","application/x-chrome-extension":"crx","application/x-cocoa":"cco","application/x-conference":"nsc","application/x-cpio":"cpio","application/x-csh":"csh","application/x-debian-package":"deb","application/x-dgc-compressed":"dgc","application/x-director":"dir","application/x-doom":"wad","application/x-dtbncx+xml":"ncx","application/x-dtbook+xml":"dtb","application/x-dtbresource+xml":"res","application/x-dvi":"dvi","application/x-envoy":"evy","application/x-eva":"eva","application/x-font-bdf":"bdf","application/x-font-ghostscript":"gsf","application/x-font-linux-psf":"psf","application/x-font-pcf":"pcf","application/x-font-snf":"snf","application/x-font-type1":"pfa","application/x-freearc":"arc","application/x-futuresplash":"spl","application/x-gca-compressed":"gca","application/x-glulx":"ulx","application/x-gnumeric":"gnumeric","application/x-gramps-xml":"gramps","application/x-gtar":"gtar","application/x-hdf":"hdf","application/x-httpd-php":"php","application/x-install-instructions":"install","application/x-iso9660-image":"iso","application/x-iwork-keynote-sffkey":"key","application/x-iwork-numbers-sffnumbers":"numbers","application/x-iwork-pages-sffpages":"pages","application/x-java-archive-diff":"jardiff","application/x-java-jnlp-file":"jnlp","application/x-keepass2":"kdbx","application/x-latex":"latex","application/x-lua-bytecode":"luac","application/x-lzh-compressed":"lzh","application/x-makeself":"run","application/x-mie":"mie","application/x-mobipocket-ebook":"prc","application/x-ms-application":"application","application/x-ms-shortcut":"lnk","application/x-ms-wmd":"wmd","application/x-ms-wmz":"wmz","application/x-ms-xbap":"xbap","application/x-msaccess":"mdb","application/x-msbinder":"obd","application/x-mscardfile":"crd","application/x-msclip":"clp","application/x-msdos-program":"exe","application/x-msdownload":"exe","application/x-msmediaview":"mvb","application/x-msmetafile":"wmf","application/x-msmoney":"mny","application/x-mspublisher":"pub","application/x-msschedule":"scd","application/x-msterminal":"trm","application/x-mswrite":"wri","application/x-netcdf":"nc","application/x-ns-proxy-autoconfig":"pac","application/x-nzb":"nzb","application/x-perl":"pl","application/x-pilot":"prc","application/x-pkcs12":"p12","application/x-pkcs7-certificates":"p7b","application/x-pkcs7-certreqresp":"p7r","application/x-rar-compressed":"rar","application/x-redhat-package-manager":"rpm","application/x-research-info-systems":"ris","application/x-sea":"sea","application/x-sh":"sh","application/x-shar":"shar","application/x-shockwave-flash":"swf","application/x-silverlight-app":"xap","application/x-sql":"sql","application/x-stuffit":"sit","application/x-stuffitx":"sitx","application/x-subrip":"srt","application/x-sv4cpio":"sv4cpio","application/x-sv4crc":"sv4crc","application/x-t3vm-image":"t3","application/x-tads":"gam","application/x-tar":"tar","application/x-tcl":"tcl","application/x-tex":"tex","application/x-tex-tfm":"tfm","application/x-texinfo":"texinfo","application/x-tgif":"obj","application/x-ustar":"ustar","application/x-virtualbox-hdd":"hdd","application/x-virtualbox-ova":"ova","application/x-virtualbox-ovf":"ovf","application/x-virtualbox-vbox":"vbox","application/x-virtualbox-vbox-extpack":"vbox-extpack","application/x-virtualbox-vdi":"vdi","application/x-virtualbox-vhd":"vhd","application/x-virtualbox-vmdk":"vmdk","application/x-wais-source":"src","application/x-web-app-manifest+json":"webapp","application/x-x509-ca-cert":"der","application/x-xfig":"fig","application/x-xliff+xml":"xlf","application/x-xpinstall":"xpi","application/x-xz":"xz","application/x-zmachine":"z1","application/xaml+xml":"xaml","application/xcap-att+xml":"xav","application/xcap-caps+xml":"xca","application/xcap-diff+xml":"xdf","application/xcap-el+xml":"xel","application/xcap-ns+xml":"xns","application/xenc+xml":"xenc","application/xhtml+xml":"xhtml","application/xliff+xml":"xlf","application/xml":"xml","application/xml-dtd":"dtd","application/xop+xml":"xop","application/xproc+xml":"xpl","application/xslt+xml":"xsl","application/xspf+xml":"xspf","application/xv+xml":"mxml","application/yang":"yang","application/yin+xml":"yin","application/zip":"zip","audio/3gpp":"3gpp","audio/adpcm":"adp","audio/amr":"amr","audio/basic":"au","audio/midi":"mid","audio/mobile-xmf":"mxmf","audio/mp3":"mp3","audio/mp4":"m4a","audio/mpeg":"mpga","audio/ogg":"oga","audio/s3m":"s3m","audio/silk":"sil","audio/vnd.dece.audio":"uva","audio/vnd.digital-winds":"eol","audio/vnd.dra":"dra","audio/vnd.dts":"dts","audio/vnd.dts.hd":"dtshd","audio/vnd.lucent.voice":"lvp","audio/vnd.ms-playready.media.pya":"pya","audio/vnd.nuera.ecelp4800":"ecelp4800","audio/vnd.nuera.ecelp7470":"ecelp7470","audio/vnd.nuera.ecelp9600":"ecelp9600","audio/vnd.rip":"rip","audio/wav":"wav","audio/wave":"wav","audio/webm":"weba","audio/x-aac":"aac","audio/x-aiff":"aif","audio/x-caf":"caf","audio/x-flac":"flac","audio/x-m4a":"m4a","audio/x-matroska":"mka","audio/x-mpegurl":"m3u","audio/x-ms-wax":"wax","audio/x-ms-wma":"wma","audio/x-pn-realaudio":"ram","audio/x-pn-realaudio-plugin":"rmp","audio/x-realaudio":"ra","audio/x-wav":"wav","audio/xm":"xm","chemical/x-cdx":"cdx","chemical/x-cif":"cif","chemical/x-cmdf":"cmdf","chemical/x-cml":"cml","chemical/x-csml":"csml","chemical/x-xyz":"xyz","font/collection":"ttc","font/otf":"otf","font/ttf":"ttf","font/woff":"woff","font/woff2":"woff2","image/aces":"exr","image/apng":"apng","image/avci":"avci","image/avcs":"avcs","image/avif":"avif","image/bmp":"bmp","image/cgm":"cgm","image/dicom-rle":"drle","image/emf":"emf","image/fits":"fits","image/g3fax":"g3","image/gif":"gif","image/heic":"heic","image/heic-sequence":"heics","image/heif":"heif","image/heif-sequence":"heifs","image/hej2k":"hej2","image/hsj2":"hsj2","image/ief":"ief","image/jls":"jls","image/jp2":"jp2","image/jpeg":"jpeg","image/jph":"jph","image/jphc":"jhc","image/jpm":"jpm","image/jpx":"jpx","image/jxr":"jxr","image/jxra":"jxra","image/jxrs":"jxrs","image/jxs":"jxs","image/jxsc":"jxsc","image/jxsi":"jxsi","image/jxss":"jxss","image/ktx":"ktx","image/ktx2":"ktx2","image/png":"png","image/prs.btif":"btif","image/prs.pti":"pti","image/sgi":"sgi","image/svg+xml":"svg","image/t38":"t38","image/tiff":"tif","image/tiff-fx":"tfx","image/vnd.adobe.photoshop":"psd","image/vnd.airzip.accelerator.azv":"azv","image/vnd.dece.graphic":"uvi","image/vnd.djvu":"djvu","image/vnd.dvb.subtitle":"sub","image/vnd.dwg":"dwg","image/vnd.dxf":"dxf","image/vnd.fastbidsheet":"fbs","image/vnd.fpx":"fpx","image/vnd.fst":"fst","image/vnd.fujixerox.edmics-mmr":"mmr","image/vnd.fujixerox.edmics-rlc":"rlc","image/vnd.microsoft.icon":"ico","image/vnd.ms-dds":"dds","image/vnd.ms-modi":"mdi","image/vnd.ms-photo":"wdp","image/vnd.net-fpx":"npx","image/vnd.pco.b16":"b16","image/vnd.tencent.tap":"tap","image/vnd.valve.source.texture":"vtf","image/vnd.wap.wbmp":"wbmp","image/vnd.xiff":"xif","image/vnd.zbrush.pcx":"pcx","image/webp":"webp","image/wmf":"wmf","image/x-3ds":"3ds","image/x-cmu-raster":"ras","image/x-cmx":"cmx","image/x-freehand":"fh","image/x-icon":"ico","image/x-jng":"jng","image/x-mrsid-image":"sid","image/x-ms-bmp":"bmp","image/x-pcx":"pcx","image/x-pict":"pic","image/x-portable-anymap":"pnm","image/x-portable-bitmap":"pbm","image/x-portable-graymap":"pgm","image/x-portable-pixmap":"ppm","image/x-rgb":"rgb","image/x-tga":"tga","image/x-xbitmap":"xbm","image/x-xpixmap":"xpm","image/x-xwindowdump":"xwd","message/disposition-notification":"disposition-notification","message/global":"u8msg","message/global-delivery-status":"u8dsn","message/global-disposition-notification":"u8mdn","message/global-headers":"u8hdr","message/rfc822":"eml","message/vnd.wfa.wsc":"wsc","model/3mf":"3mf","model/gltf+json":"gltf","model/gltf-binary":"glb","model/iges":"igs","model/mesh":"msh","model/mtl":"mtl","model/obj":"obj","model/step+xml":"stpx","model/step+zip":"stpz","model/step-xml+zip":"stpxz","model/stl":"stl","model/vnd.collada+xml":"dae","model/vnd.dwf":"dwf","model/vnd.gdl":"gdl","model/vnd.gtw":"gtw","model/vnd.mts":"mts","model/vnd.opengex":"ogex","model/vnd.parasolid.transmit.binary":"x_b","model/vnd.parasolid.transmit.text":"x_t","model/vnd.sap.vds":"vds","model/vnd.usdz+zip":"usdz","model/vnd.valve.source.compiled-map":"bsp","model/vnd.vtu":"vtu","model/vrml":"wrl","model/x3d+binary":"x3db","model/x3d+fastinfoset":"x3db","model/x3d+vrml":"x3dv","model/x3d+xml":"x3d","model/x3d-vrml":"x3dv","text/cache-manifest":"appcache","text/calendar":"ics","text/coffeescript":"coffee","text/css":"css","text/csv":"csv","text/html":"html","text/jade":"jade","text/jsx":"jsx","text/less":"less","text/markdown":"markdown","text/mathml":"mml","text/mdx":"mdx","text/n3":"n3","text/plain":"txt","text/prs.lines.tag":"dsc","text/richtext":"rtx","text/rtf":"rtf","text/sgml":"sgml","text/shex":"shex","text/slim":"slim","text/spdx":"spdx","text/stylus":"stylus","text/tab-separated-values":"tsv","text/troff":"t","text/turtle":"ttl","text/uri-list":"uri","text/vcard":"vcard","text/vnd.curl":"curl","text/vnd.curl.dcurl":"dcurl","text/vnd.curl.mcurl":"mcurl","text/vnd.curl.scurl":"scurl","text/vnd.dvb.subtitle":"sub","text/vnd.familysearch.gedcom":"ged","text/vnd.fly":"fly","text/vnd.fmi.flexstor":"flx","text/vnd.graphviz":"gv","text/vnd.in3d.3dml":"3dml","text/vnd.in3d.spot":"spot","text/vnd.sun.j2me.app-descriptor":"jad","text/vnd.wap.wml":"wml","text/vnd.wap.wmlscript":"wmls","text/vtt":"vtt","text/x-asm":"s","text/x-c":"c","text/x-component":"htc","text/x-fortran":"f","text/x-handlebars-template":"hbs","text/x-java-source":"java","text/x-lua":"lua","text/x-markdown":"mkd","text/x-nfo":"nfo","text/x-opml":"opml","text/x-org":"org","text/x-pascal":"p","text/x-processing":"pde","text/x-sass":"sass","text/x-scss":"scss","text/x-setext":"etx","text/x-sfv":"sfv","text/x-suse-ymp":"ymp","text/x-uuencode":"uu","text/x-vcalendar":"vcs","text/x-vcard":"vcf","text/xml":"xml","text/yaml":"yaml","video/3gpp":"3gp","video/3gpp2":"3g2","video/h261":"h261","video/h263":"h263","video/h264":"h264","video/iso.segment":"m4s","video/jpeg":"jpgv","video/jpm":"jpm","video/mj2":"mj2","video/mp2t":"ts","video/mp4":"mp4","video/mpeg":"mpeg","video/ogg":"ogv","video/quicktime":"qt","video/vnd.dece.hd":"uvh","video/vnd.dece.mobile":"uvm","video/vnd.dece.pd":"uvp","video/vnd.dece.sd":"uvs","video/vnd.dece.video":"uvv","video/vnd.dvb.file":"dvb","video/vnd.fvt":"fvt","video/vnd.mpegurl":"mxu","video/vnd.ms-playready.media.pyv":"pyv","video/vnd.uvvu.mp4":"uvu","video/vnd.vivo":"viv","video/webm":"webm","video/x-f4v":"f4v","video/x-fli":"fli","video/x-flv":"flv","video/x-m4v":"m4v","video/x-matroska":"mkv","video/x-mng":"mng","video/x-ms-asf":"asf","video/x-ms-vob":"vob","video/x-ms-wm":"wm","video/x-ms-wmv":"wmv","video/x-ms-wmx":"wmx","video/x-ms-wvx":"wvx","video/x-msvideo":"avi","video/x-sgi-movie":"movie","video/x-smv":"smv","x-conference/x-cooltalk":"ice"},Zg=/^\s*([^;\s]*)(?:;|\s|$)/;function vr(n){if(!n||typeof n!="string")return"";let t=Zg.exec(n);return t&&Wg[t[1].toLowerCase()]||""}var Rn=n=>Ze(n).replace(/[\[\]\#\^]/g,""),hi=(n,t)=>K.readdirSync(n).filter(i=>{let r=i.split(".").slice(0,-1).join("."),o=Sn(t),a=r.match(new RegExp(`${o}\\.\\d+`));return r===t||a}).length,ou=(n,t)=>{let e=B.useUniqueUnknownFileNames?"unknown_filename"+(Math.random().toString(16)+"0000000").slice(2,10):"unknown_filename",i=Gg(t),r=e;if(t["resource-attributes"]&&t["resource-attributes"]["file-name"]){let s=t["resource-attributes"]["file-name"].substr(0,50);r=re(s).basename}r=r.replace(/[/\\?%*:|"<>\[\]\+]/g,"-"),B.sanitizeResourceNameSpaces&&(r=r.replace(/ /g,B.replacementChar));let o=hi(n,r);return{fileName:`${o>0?`${r}.${o}`:r}.${i}`,extension:i,index:o}},Fa=n=>Rn(n.title?`${n.title.toString()}`:"Untitled"),au=(n,t,e="md")=>`${La(n,t)}.${e}`,Yg=n=>{if(!(n["resource-attributes"]&&n["resource-attributes"]["file-name"]))return"";let t=n["resource-attributes"]["file-name"].split(".");return t.length>1?t[t.length-1]:void 0},zg=n=>{let t=n.mime;return t&&vr(t)||""},Gg=n=>{let t="dat";return Yg(n)||zg(n)||t},Vg=(n,t)=>(0,ru.moment)(n.created).format("YYYYMMDDHHmm"),La=(n,t)=>{let e;if(B.isZettelkastenNeeded||B.useZettelIdAsFilename){let i=Vg(t,n),r=hi(n,i),o=" ";e=r!==0?`${i}.${r}`:i,B.useZettelIdAsFilename||(e+=Fa(t)!=="Untitled"?`${o}${Fa(t)}`:"")}else{let i=Fa(t),r=hi(n,i);e=r===0?i:`${i}.${r}`}return e};var su=n=>n==="Table of Contents";var Kg=n=>n.replace(/\[|\]/g,""),Xg=n=>n.replace(/\\/g,""),cu={filter:qe("A"),replacement:(n,t)=>{let e=ue(t);if(!e.href)return"";let i=Er(B).turndown(Kg(t.innerHTML));i=Xg(i);let r="",o=i.match(/^(#{1,6} )(.*)/);o&&(r=o[1],i=o[2]);let a=e.href.value,s=e.type?e.type.value:void 0,l=B.urlEncodeFileNamesAndLinks?encodeURI(a):a;if(s==="file")return`![[${l}]]`;if(a.match(/^(https?:|www\.|file:|ftp:|mailto:)/))return r+e0(i,a);if(a.startsWith("evernote://")){let c=Rn(i),p=fe.getInstance(),m=xn(6);return su(p.getCurrentNoteName())?p.addItemToTOCMap({url:a,title:c,uniqueEnd:m}):p.addItemToMap({url:a,title:c,uniqueEnd:m}),r+`[[${a}]]`}return r+`[[${l}${i===l?"":`|${i}`}]]`}},Jg={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},pu=/&(?:amp|lt|gt|quot|#39);/g,Qg=RegExp(pu.source);function lu(n){return n&&Qg.test(n)?n.replace(pu,t=>Jg[t]):n}var e0=(n,t)=>!n||lu(n)===lu(t)?B.generateNakedUrls?t:`<${t}>`:`[${n}](${t})`;var we={bold:"**",italic:"_",highlight:"==",strikethrough:"~~"};var t0="-evernote-highlight:true;",n0="--en-highlight",i0="bold",r0="italic",uu={filter:qe("SPAN"),replacement:(n,t)=>{let e=ue(t);if(e.style&&n.trim()!==""){let i=e.style.value;if(n!=="<YARLE_NEWLINE_PLACEHOLDER>"){let r=i.includes(i0),o=i.includes(r0);if(r&&!o)return`${we.bold}${n}${we.bold}`;if(!r&&o)return`${we.italic}${n}${we.italic}`;if(r&&o)return`${we.italic}${we.bold}${n}${we.bold}${we.italic}`}return i.includes(t0)||i.includes(n0)?`${we.highlight}${n}${we.highlight}`:n}return n}};var fu={filter:["del","s","strike"],replacement:n=>`${we.strikethrough}${n}${we.strikethrough}`};var du={filter:qe("EN-TODO"),replacement:(n,t)=>{var r,o;let e=ue(t);return`${((o=(r=t.parentElement)==null?void 0:r.nodeName)==null?void 0:o.toUpperCase())==="LI"?"":"- "}${e.checked&&e.checked.value==="true"?"[x]":"[ ]"} ${n}`}};var mu={filter:qe("BR"),replacement:(n,t)=>"<YARLE_NEWLINE_PLACEHOLDER>"};var o0=n=>{let t=ue(n),e="--en-task-group:true";return t.style&&t.style.value.indexOf(e)>=0},a0=n=>{let t=ue(n),e="--en-id:";return t.style.value.split(e)[1].split(";")[0]},hu={filter:qe("DIV"),replacement:(n,t)=>o0(t)?`<YARLE-EN-V10-TASK>${a0(t)}</YARLE-EN-V10-TASK>`:B.monospaceIsCodeBlock?nu(n,t):eu(n,t)};var gu={filter:["i"],replacement:n=>n.trim()!==""?`${we.italic}${n}${we.italic}`:n};var _r="- [ ]",Tr="- [x]";var yu="	",bu={filter:"li",replacement:(n,t,e)=>{let i=m=>{let y=ue(m),w="--en-checked:true;";return y.style&&y.style.value.indexOf(w)>=0},r=m=>{let y=ue(m),w="--en-checked:false;";return y.style&&y.style.value.indexOf(w)>=0},o=n.match(/^\n*/)[0].length||0,a=yu.repeat(o),s=n.replace(/^\n+/,"").replace(/\n+$/,`
`).replace(/\n/gm,`
${yu}`),l=o>0?a:i(t)?`${Tr} `:r(t)?`${_r} `:"* ",c=t.parentNode;if(c.nodeName==="OL"){let m=c.getAttribute("start"),y=Array.prototype.indexOf.call(c.children,t);l=`${m?Number(m)+y:y+1}. `}let p;return p=l+s+(t.nextSibling&&!/\n$/.test(s)?`
`:""),p}};var Er=n=>{let t=new window.TurndownService({br:"",...n.turndownOptions,blankReplacement:(e,i)=>i.isBlock?`

`:"",keepReplacement:(e,i)=>i.isBlock?`
${i.outerHTML}
`:i.outerHTML,defaultReplacement:(e,i)=>i.isBlock?`
${e}
`:e});return t.use(Jp),t.addRule("span",uu),t.addRule("strikethrough",fu),t.addRule("evernote task items",du),t.addRule("wikistyle links",cu),t.addRule("images",iu),t.addRule("list",bu),t.addRule("italic",gu),n.keepMDCharactersOfENNotes&&(t.escape=e=>e),t.addRule("divBlock",hu),n.keepOriginalAmountOfNewlines&&t.addRule("newline",mu),t};var xu=n=>{n.replaceWith(...Array.from(n.children))},wu=n=>{let t=n.parentElement;t.replaceWith(...Array.from(t.childNodes)),t.append(...Array.from(n.childNodes)),n.appendChild(t)},s0=n=>(Array.from(n.querySelectorAll("span>en-todo")).forEach(wu),Array.from(n.querySelectorAll("a>en-todo")).forEach(wu),n),l0=n=>{let t=n.replace(/<li>/g,"<li><div>");return t=t.replace(/<\/li>/g,"</div></li>"),t=t.replace(/<li><div>(\s)*<div>/g,"<li><div>"),t=t.replace(/<\/div>(\s)*<\/div><\/li>/g,"</div></li>"),t},c0=n=>{var o;let t=Array.from(n.getElementsByTagName("ul")),e=Array.from(n.getElementsByTagName("ol")),i=t.concat(e);i.forEach(a=>{a.parentElement.tagName==="LI"&&a.parentElement.replaceWith(a),a.previousElementSibling&&a.previousElementSibling.tagName==="LI"&&a.previousElementSibling.appendChild(a)});for(let a of i){let s=a.parentElement;(s==null?void 0:s.tagName)==="DIV"&&((o=s==null?void 0:s.parentElement)==null?void 0:o.tagName)==="UL"&&xu(s),((s==null?void 0:s.tagName)==="UL"||(s==null?void 0:s.tagName)==="OL")&&(s==null?void 0:s.childNodes.length)===1&&xu(s)}let r=Array.from(n.getElementsByTagName("li"));for(let a of r){let s=a.firstElementChild;if(s&&s.tagName==="DIV"){let l=Array.from(s.childNodes);s.replaceWith(...l)}}return n},vu=(n,{htmlContent:t})=>{let e=t.replace(/<!DOCTYPE en-note [^>]*>/,"<!DOCTYPE html>").replace(/(<a [^>]*)\/>/,"$1></a>").replace(/<div[^\/\<]*\/>/g,""),i=new DOMParser().parseFromString(l0(e),"text/html").getElementsByTagName("en-note").item(0),r=Er(n).turndown(s0(c0(i))),o=new RegExp("<YARLE_NEWLINE_PLACEHOLDER>","g");return r=r.replace(o,""),r&&r!=="undefined"?{content:r}:{content:""}};var df=rs(require("crypto"));var yt=require("obsidian");var Eu=(n,t)=>({createdAt:p0(n),updatedAt:u0(n),sourceUrl:f0(n),location:d0(n),reminderTime:m0(n),reminderOrder:h0(n),reminderDoneTime:g0(n),notebookName:t});var p0=n=>!B.skipCreationTime&&n.created?(0,yt.moment)(n.created).format(B.dateFormat):"",u0=n=>!B.skipUpdateTime&&n.updated?(0,yt.moment)(n.updated).format(B.dateFormat):"",f0=n=>!B.skipSourceUrl&&n["note-attributes"]?n["note-attributes"]["source-url"]:"",d0=n=>!B.skipLocation&&n["note-attributes"]&&n["note-attributes"].longitude?`${n["note-attributes"].latitude},${n["note-attributes"].longitude}`:"",m0=n=>!B.skipReminderTime&&n["note-attributes"]&&n["note-attributes"]["reminder-time"]?(0,yt.moment)(n["note-attributes"]["reminder-time"]).format(B.dateFormat):"",h0=n=>!B.skipReminderOrder&&n["note-attributes"]&&n["note-attributes"]["reminder-order"]?n["note-attributes"]["reminder-order"]:"",g0=n=>!B.skipReminderDoneTime&&n["note-attributes"]&&n["note-attributes"]["reminder-done-time"]?(0,yt.moment)(n["note-attributes"]["reminder-done-time"]).format(B.dateFormat):"",_u=n=>({tags:y0(n)}),y0=n=>{if(!B.skipTags&&n.tag){let t=Array.isArray(n.tag)?n.tag:[n.tag],e=B.nestedTags;return t.map(r=>{let o=r.toString().replace(/^#/,"");e&&(o=o.replace(new RegExp(Sn(e.separatorInEN),"g"),e.replaceSeparatorWith));let a=e&&e.replaceSpaceWith||"-";return o=o.replace(/ /g,a),`${B.useHashTags?"#":""}${o}`}).join(" ")}return""},Pa;try{Pa=window.require("btime")}catch(n){}var Ar=(n,t)=>{let e=(0,yt.moment)(t.created).valueOf();e>0&&Pa&&Pa.btime(n,e);let r=(0,yt.moment)(t.updated).valueOf()/1e3;try{K.utimesSync(n,r,r)}catch(o){}},Tu=n=>n["resource-attributes"]&&n["resource-attributes"].timestamp?(0,yt.moment)(n["resource-attributes"].timestamp):(0,yt.moment)();var Au=(n,t,e)=>{let i=n.lastIndexOf(t);return i<0?n:n.substring(0,i)+e+n.substring(i+t.length)};var Ae={mdPath:"",resourcePath:""},Nr=249,ku=(n,t)=>La(n,t).replace(/\s/g,"_"),Nu=(n,t)=>{if(n.length<=11)throw Error("FATAL: note folder directory path exceeds the OS limitation. Please pick a destination closer to the root folder.");return`${$a()}${q.sep}${n}`.length<Nr?n:`${n.slice(0,Nr-11)}_${t}.md`},b0=(n,t,e)=>{let r=fe.getInstance().getNoteIdNameMapByNoteTitle(Rn(n.title))[0]||{uniqueEnd:xn(6)};if(t.length<=11)throw Error("FATAL: note folder directory path exceeds the OS limitation. Please pick a destination closer to the root folder.");return`${e.slice(0,Nr-11)}_${r.uniqueEnd}.md`},x0=(n,t,e)=>{let i=au(n,t,e),r=`${n}${q.sep}${Rn(i)}`;return r.length<Nr?r:b0(t,i,r)},Su=n=>x0(Ae.mdPath,n,"md");var w0=n=>{K.existsSync(n)&&(K.rmSync?K.rmSync(n,{recursive:!0,force:!0}):K.rmdirSync(n,{recursive:!0})),K.mkdirSync(n)},Ru=n=>{let t=`${q.sep}${B.resourcesDir}`;return B.haveGlobalResources?`..${t}`:B.haveEnexLevelResources?`.${t}`:`.${t}${q.sep}${ku(Ae.mdPath,n)}.resources`},Ma=n=>B.haveGlobalResources?q.resolve(Ae.resourcePath,"..","..",B.resourcesDir):B.haveEnexLevelResources?Ae.resourcePath:`${Ae.resourcePath}${q.sep}${ku(Ae.mdPath,n)}.resources`,kr=new Map,Ou=n=>{let t=Ma(n);kr.has(t)||kr.set(t,0);let e=kr.get(t)||0;(B.haveEnexLevelResources||B.haveGlobalResources)&&e>=1||(w0(t),kr.set(t,e+1))},Du=n=>{let t=n.split("@@@"),e=t.pop();return e||(e=n),{notebookName:e,notebookFolderNames:t}},Cu=n=>{if(!(n instanceof Me))throw new Error("Evernote import currently only works on desktop");let{notebookName:t}=Du(n.basename);return{fullpath:Au(n.fullpath,n.basename,t||n.basename),basename:t}},Iu=(n,t)=>{let{notebookFolderNames:e}=Du(n.basename);return K.mkdirSync(q.join(t.outputDir,...e),{recursive:!0}),[t.outputDir,...e].join(t.pathSeparator)},Fu=(n,t)=>{let e=n.basename;Pu(e,t)},Lu=(n,t)=>{let e=n.basename;Pu(e,t)},Pu=(n,t)=>{let e=q.isAbsolute(t.outputDir)?t.outputDir:`${process.cwd()}${q.sep}${t.outputDir}`;Ae.mdPath=`${e}${q.sep}`,Ae.resourcePath=`${e}${q.sep}${t.resourcesDir}`,t.skipEnexFileNameFromOutputPath||(Ae.mdPath=`${Ae.mdPath}${n}`,Ae.resourcePath=`${e}${q.sep}${n}${q.sep}${t.resourcesDir}`),K.mkdirSync(Ae.mdPath,{recursive:!0}),!t.haveEnexLevelResources&&!t.haveGlobalResources&&K.mkdirSync(Ae.resourcePath,{recursive:!0}),console.log(`path ${Ae.mdPath} created`)},$a=()=>Ae.mdPath;var Mu=n=>!!n.resource,$u=n=>n["note-attributes"]&&(n["note-attributes"]["source-application"]==="webclipper.evernote"||n["note-attributes"].source==="web.clip7");var Ba={};Se(Ba,{CONTENT_PLACEHOLDER:()=>v0,END_BLOCK:()=>_0,START_BLOCK:()=>E0});var v0="{content}",E0="{content-block}",_0="{end-content-block}";var Pt=({template:n,check:t,startBlockPlaceholder:e,endBlockPlaceholder:i,valuePlaceholder:r,value:o})=>{if(o&&t())return n.replace(new RegExp(`${e}`,"g"),"").replace(new RegExp(`${i}`,"g"),"").replace(new RegExp(`${r}`,"g"),o);let a=`${e}([\\d\\D])(?:.|(\r
|\r|
))*?(?=${i})${i}`;return n.replace(new RegExp(a,"g"),"")};var Mt=(n,t,e,i)=>({template:n,check:t,startBlockPlaceholder:e.START_BLOCK,endBlockPlaceholder:e.END_BLOCK,valuePlaceholder:e.CONTENT_PLACEHOLDER,value:i});var Bu=(n,t,e)=>{let i=Mt(t,e,Ba,n.content);return Pt(i)};var gi={};Se(gi,{CONTENT_PLACEHOLDER:()=>T0,END_BLOCK:()=>k0,START_BLOCK:()=>A0});var T0="{tags}",A0="{tags-block}",k0="{end-tags-block}";var Uu=(n,t,e)=>{let i=Mt(t,e,gi,n.tags);return Pt(i)};var Ua={};Se(Ua,{CONTENT_PLACEHOLDER:()=>N0,END_BLOCK:()=>R0,START_BLOCK:()=>S0});var N0="{title}",S0="{title-block}",R0="{end-title-block}";var ju=(n,t,e)=>{let i=Mt(t,e,Ua,n.title);return Pt(i)};var rn={};Se(rn,{CONTENT_PLACEHOLDER:()=>O0,END_BLOCK:()=>C0,START_BLOCK:()=>D0});var O0="{created-at}",D0="{created-at-block}",C0="{end-created-at-block}";var Ee=(n,t,e)=>n.replace(new RegExp(`${t.CONTENT_PLACEHOLDER}`,"g"),e||"").replace(new RegExp(`${t.START_BLOCK}`,"g"),"").replace(new RegExp(`${t.END_BLOCK}`,"g"),"");var Hu=(n,t)=>Ee(t,rn,n.createdAt);var on={};Se(on,{CONTENT_PLACEHOLDER:()=>I0,END_BLOCK:()=>L0,START_BLOCK:()=>F0});var I0="{updated-at}",F0="{updated-at-block}",L0="{end-updated-at-block}";var qu=(n,t)=>Ee(t,on,n.updatedAt);var an={};Se(an,{CONTENT_PLACEHOLDER:()=>P0,END_BLOCK:()=>$0,START_BLOCK:()=>M0});var P0="{source-url}",M0="{source-url-block}",$0="{end-source-url-block}";var Wu=(n,t)=>Ee(t,an,n.sourceUrl);var sn={};Se(sn,{CONTENT_PLACEHOLDER:()=>B0,END_BLOCK:()=>j0,START_BLOCK:()=>U0});var B0="{location}",U0="{location-block}",j0="{end-location-block}";var Zu=(n,t)=>Ee(t,sn,n.location);var ln={};Se(ln,{CONTENT_PLACEHOLDER:()=>H0,END_BLOCK:()=>W0,START_BLOCK:()=>q0});var H0="{notebook}",q0="{notebook-block}",W0="{end-notebook-block}";var Yu=(n,t)=>Ee(t,ln,n.notebookName);var On={};Se(On,{CONTENT_PLACEHOLDER:()=>Z0,END_BLOCK:()=>z0,START_BLOCK:()=>Y0});var Z0="{reminder-time}",Y0="{reminder-time-block}",z0="{end-reminder-time-block}";var zu=(n,t)=>Ee(t,On,n.reminderTime);var Dn={};Se(Dn,{CONTENT_PLACEHOLDER:()=>G0,END_BLOCK:()=>K0,START_BLOCK:()=>V0});var G0="{reminder-order}",V0="{reminder-order-block}",K0="{end-reminder-order-block}";var Gu=(n,t)=>Ee(t,Dn,n.reminderOrder);var Cn={};Se(Cn,{CONTENT_PLACEHOLDER:()=>X0,END_BLOCK:()=>Q0,START_BLOCK:()=>J0});var X0="{reminder-done-time}",J0="{reminder-done-time-block}",Q0="{end-reminder-done-time-block}";var Vu=(n,t)=>Ee(t,Cn,n.reminderDoneTime);var yi={};Se(yi,{CONTENT_PLACEHOLDER:()=>ey,END_BLOCK:()=>ny,START_BLOCK:()=>ty});var ey="{tags-yaml-list}",ty="{tags-yaml-list-block}",ny="{end-tags-yaml-list-block}";var Ku=(n,t,e)=>{let i;n.tags&&(i=`
`+n.tags.split(" ").map(o=>`  - ${o.replace(/^#/,"")}`).join(`
`));let r=Mt(t,e,yi,i);return Pt(r)};var ja="{metadata-block}",Ha="{end-metadata-block}";var Xu=`\r?
?`;var de=(n,t)=>n.replace(new RegExp(`${t.START_BLOCK}(?<=${t.START_BLOCK})(.*)(?=${t.END_BLOCK})${t.END_BLOCK}${Xu}`,"g"),"");var Ju=n=>de(n,rn);var Qu=n=>de(n,on);var ef=n=>de(n,an);var tf=n=>de(n,ln);var nf=n=>de(n,sn);var Sr={};Se(Sr,{CONTENT_PLACEHOLDER:()=>ry,END_BLOCK:()=>ay,START_BLOCK:()=>oy});var ry="{link-to-original}",oy="{link-to-original-block}",ay="{end-link-to-original-block}";var rf=n=>de(n,Sr);var of=n=>de(n,On);var af=n=>de(n,Cn);var sf=n=>de(n,Dn);var lf=(n,t)=>{let e=t.currentTemplate;return e=ju(n,e,()=>n.title),e=Uu(n,e,()=>!t.skipTags),e=Ku(n,e,()=>!t.skipTags),e=Bu(n,e,()=>n.content),e=rf(e),e=!t.skipCreationTime&&n.createdAt?Hu(n,e):Ju(e),e=!t.skipUpdateTime&&n.updatedAt?qu(n,e):Qu(e),e=!t.skipSourceUrl&&n.sourceUrl?Wu(n,e):ef(e),e=!t.skipLocation&&n.location?Zu(n,e):nf(e),e=t.isNotebookNameNeeded&&n.notebookName?Yu(n,e):tf(e),e=!t.skipReminderTime&&n.reminderTime?zu(n,e):of(e),e=!t.skipReminderOrder&&n.reminderOrder?Gu(n,e):sf(e),e=!t.skipReminderDoneTime&&n.reminderDoneTime?Vu(n,e):af(e),e=e.replace(ja,"").replace(Ha,""),e};var cf=(n,t,e)=>{try{K.writeFileSync(n,t),Ar(n,e)}catch(i){throw console.error("Cannot write file ",i),i}};var pf=(n,t)=>{let e=Su(t);fe.getInstance().setCurrentNotePath(e),cf(e,n,t),console.log(`Note saved to ${e}`)};var mf=n=>{let t=new RegExp(`\\${q.sep}`,"g"),e=Ru(n).replace(t,B.pathSeparator||"/");return{absoluteResourceWorkDir:Ma(n),relativeResourceWorkDir:e}},hf=n=>{let t={},e=n.content,{absoluteResourceWorkDir:i,relativeResourceWorkDir:r}=mf(n);if(console.log(`relative resource work dir: ${r}`),console.log(`absolute resource work dir: ${i}`),Ou(n),Array.isArray(n.resource))for(let o of n.resource)t={...t,...ff(i,o)};else t={...t,...ff(i,n.resource)};for(let o of Object.keys(t))e=sy(e,t,o,r);return e},sy=(n,t,e,i)=>{let r=`${i}${B.pathSeparator}${t[e].fileName.replace(/ /g," ")}`;console.log(`mediaReference src ${r} added`);let o,a=`<en-media ([^>]*)hash="${e}".([^>]*)>`,s=new RegExp(a,"g"),l=n.match(s),c=l&&l.length>0&&l[0].split("type=");if(c&&c.length>1&&c[1].startsWith('"image')){let p=l[0].match(/width="(\w+)"/),m=p?` width="${p[1]}"`:"",y=l[0].match(/height="(\w+)"/),w=y?` height="${y[1]}"`:"";o=n.replace(s,`<img src="${r}"${m}${w} alt="${t[e].fileName}">`)}else o=n.replace(s,`<a href="${r}" type="file">${t[e].fileName}</a>`);return o},ff=(n,t)=>{let e={},i=t.data.$text,r=Tu(t),a=ou(n,t).fileName,s=`${n}${q.sep}${a}`;console.log(t),console.log(i);let l=Buffer.from(i,"base64");K.writeFileSync(s,l);let c=r.valueOf()/1e3;try{K.utimesSync(s,c,c)}catch(p){}if(t.recognition&&a){let p=t.recognition.match(/[a-f0-9]{32}/);console.log(`resource ${a} added with hash ${p}`),e[p]={fileName:a,alreadyUsed:!1}}else{let p=df.default.createHash("md5");p.update(l);let m=p.digest("hex");e[m]={fileName:a,alreadyUsed:!1}}return e},gf=(n,t)=>{if(t.indexOf('src="data:')<0)return t;let{absoluteResourceWorkDir:e,relativeResourceWorkDir:i}=mf(n);return K.mkdirSync(e,{recursive:!0}),t.replace(/src="data:([^;,]*)(;base64)?,([^"]*)"/g,(r,o,a,s)=>{let l=ly(o,a===";base64",s,e,n);return`src="${`${i}${B.pathSeparator}${l}`}"`})},ly=(n,t,e,i,r)=>{let o="embedded",a=cy(n)||".dat",s=hi(i,o),l=s<1?`${o}.${a}`:`${o}.${s}.${a}`,c=`${i}${q.sep}${l}`;return t||(e=decodeURIComponent(e)),K.writeFileSync(c,e,t?"base64":void 0),Ar(c,r),console.log(`data url resource ${l} added`),l},cy=n=>n.split("/").pop().split("+")[0];var yf=(n,t)=>{let e=new Date;fe.getInstance().setCurrentNoteName(n.title),Array.isArray(n.content)&&(n.content=n.content.join(""));let r={title:n.title,content:n.content,htmlContent:n.content,originalContent:n.content};console.log(`Converting for note "${r.title}" started at ${e}...`);try{Mu(n)&&(r.htmlContent=hf(n)),r.htmlContent=gf(n,r.htmlContent),r={...r,...vu(B,r)},r={...r,...Eu(n,t)},r={...r,..._u(n)};let o=lf(r,B);pf(o,n)}catch(o){throw console.error(`Failed to convert note: ${r.title}`,o),o}finally{let o=new Date,a=(o.getTime()-e.getTime())/1e3;console.log(`Conversion for note "${r.title}" finished at ${o}. Took ${a} seconds`)}};var wf=require("obsidian");var bf=n=>n.taskstatus==="open"?_r:Tr;var py="\u{1F53C}",uy="\u{1F53D}",fy="\u{1F4C5}",dy="\u23F3",vf=(n,t)=>{let e=bf(n),i=n.title?` ${n.title}`:"",r=B.obsidianTaskTag!==""?` ${B.obsidianTaskTag}`:"",o=n.duedate&&!isNaN(n.duedate.getTime())?` ${fy} ${xf(n.duedate)}`:"",a=n.reminderdate?` ${dy} ${xf(n.reminderdate)}`:"",s=n.taskflag?` ${py}`:` ${uy}`;return`${e}${r}${i}${o}${a}${s}`},xf=n=>(0,wf.moment)(n).format("YYYY-MM-DD").toString();var Wa=(n,t,e)=>{let i=K.readdirSync(n);return t=t||[],i.forEach(r=>{K.statSync(`${n}${q.sep}${r}`).isDirectory()?t=Wa(`${n}${q.sep}${r}`,t,e):(e&&q.extname(r)==`.${e}`||!e)&&t.push(q.join(n,"/",r))}),t};var Ef=(n,t)=>{let i=fe.getInstance().getAllNoteIdNameMap(),r=Object.entries(i);if(r.length===0)return;console.log("About to update links...");let o=[];for(let a of t)Wa(a,o,"");for(let a of t){console.log(`Notebook: ${a}`);let l=K.readdirSync(a).filter(c=>q.extname(c).toLowerCase()===".md");console.log(`${l.length} files to check for links`);for(let c of l){let p=q.join(a,c),m=K.readFileSync(p,"utf8"),y=m;for(let[w,E]of r){let d=E.uniqueEnd,f=E.title;o.find(D=>D.includes(d))&&(f=Nu(f,d));let g=E.notebookName,v=n.urlEncodeFileNamesAndLinks?encodeURI(f):f,_=v;g&&!a.endsWith(g)&&(_=`${g}/${v}`);let A=new RegExp(Sn(w),"g");y=y.replace(A,_)}m!==y&&(console.log(`File written: ${p}`),K.writeFileSync(p,y))}}console.log("Link update complete.")};var _f=n=>cn(rn,n);var Tf=n=>cn(sn,n),Af=n=>cn(ln,n);var kf=n=>cn(an,n),Nf=n=>cn(gi,n)||cn(yi,n);var Sf=n=>cn(on,n),cn=(n,t)=>t.includes(n.START_BLOCK)&&t.includes(n.CONTENT_PLACEHOLDER)&&t.includes(n.END_BLOCK);var Rf=`---
`,my=`{source-url-block}source: {source-url}{end-source-url-block}
`,hy=`{tags-yaml-list-block}
tags: {tags-yaml-list}

{end-tags-yaml-list-block}`,gy=`{content-block}{content}{end-content-block}
`,Of=Rf+hy+my+Rf+gy;var Cy=Bf.Platform.isDesktopApp?$f():null,Dr={enexSources:[],currentTemplate:"",outputDir:"./mdNotes",isMetadataNeeded:!1,isNotebookNameNeeded:!1,isZettelkastenNeeded:!1,useZettelIdAsFilename:!1,plainTextNotesOnly:!1,skipWebClips:!1,useHashTags:!0,nestedTags:{separatorInEN:"_",replaceSeparatorWith:"/",replaceSpaceWith:"-"},obsidianTaskTag:"",urlEncodeFileNamesAndLinks:!1,sanitizeResourceNameSpaces:!1,replacementChar:"_",pathSeparator:"/",resourcesDir:"_resources",turndownOptions:{headingStyle:"atx"}},Iy="@@@",B={...Dr};function Fy(n){return n==null?n:JSON.parse(JSON.stringify(n))}function Uf(n,...t){for(let e of t)for(let i of Object.keys(e)){let r=e[i],o=n[i];!Array.isArray(r)&&typeof r=="object"&&!Array.isArray(o)&&typeof o=="object"?n[i]=Uf({},o,r):n[i]=Fy(r)}return n}var Ly=n=>{B=Uf({},Dr,n);let t=B.templateFile?K.readFileSync(B.templateFile,"utf-8"):Of;t=B.currentTemplate?B.currentTemplate:t,B.skipCreationTime=!_f(t),B.skipLocation=!Tf(t),B.skipSourceUrl=!kf(t),B.skipTags=!Nf(t),B.skipUpdateTime=!Sf(t),B.isNotebookNameNeeded=Af(t),B.currentTemplate=t,console.log(`Current config is: ${JSON.stringify(B,null,4)}`),console.log(`Path separator:${q.sep}`)},Py=async(n,t,e)=>{if(!(t instanceof Me))throw new Error("Evernote import currently only works on desktop");let i=fe.getInstance();e.status("Processing "+t.name),console.log(`Getting stream from ${t}`);let r=t.createReadStream(),o={},a=i.getCurrentNotebookName();return new Promise((s,l)=>{let c=y=>(e.reportFailed(i.getCurrentNotebookFullpath(),y),l(y)),p=Cy(r),m=null;p.on("tag:note-attributes",y=>{m=y}),p.on("tag:note",y=>{if(e.isCancelled()){r.close();return}if(n.skipWebClips&&$u(y))e.reportSkipped(y.title);else{e.status("Importing note "+y.title),m&&(y["note-attributes"]=m);try{yf(y,a),e.reportNoteSuccess(a+"/"+y.title)}catch(E){return e.reportFailed(y.title||t,E),s()}}m=null;let w=i.getCurrentNotePath();if(w)for(let E of Object.keys(o)){let d=`<YARLE-EN-V10-TASK>${E}</YARLE-EN-V10-TASK>`,f=K.readFileSync(w,"utf8"),g=new Map([...o[E]].sort()),v=f.replace(d,[...g.values()].join(`
`));K.writeFileSync(w,v)}}),p.on("tag:task",y=>{let w=zp(y);o[w.taskgroupnotelevelid]||(o[w.taskgroupnotelevelid]=new Map),o[w.taskgroupnotelevelid].set(w.sortweight,vf(w,a))}),p.on("end",s),p.on("error",c),r.on("error",c)})};async function jf(n,t){Ly(n);let e=[],i=n.outputDir;for(let r of n.enexSources){if(t.isCancelled())return;let o,a=fe.getInstance();r.basename.includes(Iy)?(n.outputDir=Iu(r,n),o=Cu(r),Lu(o,n),a.setCurrentNotebookName(o.basename),a.setCurrentNotebookFullpath(o.fullpath)):(Fu(r,n),a.setCurrentNotebookName(r.basename),a.setCurrentNotebookFullpath(r.fullpath)),await Py(n,r,t),e.push($a()),n.outputDir=i}t.isCancelled()||await Ef(n,e)}var Cr=class extends ce{init(){this.addFileChooserSetting("Evernote",["enex"],!0),this.addOutputLocationSetting("Evernote")}async import(t){let{files:e}=this;if(e.length===0){new xi.Notice("Please pick at least one file to import.");return}let i=await this.getOutputFolder();if(!i){new xi.Notice("Please select a location to export to.");return}let{app:r}=this,o=r.vault.adapter;if(!(o instanceof xi.FileSystemAdapter))return;let a={...Dr,enexSources:e,outputDir:q.join(o.getBasePath(),i.path)};await jf(a,t)}};var ke=require("obsidian");var Ir=class extends ce{init(){this.addFileChooserSetting("HTML",["htm","html"],!0),this.addAttachmentSizeLimit(0),this.addMinimumImageSize(65),this.addOutputLocationSetting("HTML import")}addAttachmentSizeLimit(e){this.attachmentSizeLimit=e*10**6,new ke.Setting(this.modal.contentEl).setName("Attachment size limit (MB)").setDesc("Set 0 to disable.").addText(i=>i.then(({inputEl:r})=>{r.type="number",r.step="0.1"}).setValue(e.toString()).onChange(r=>{let o=["+","-"].includes(r)?0:Number(r);if(Number.isNaN(o)||o<0){i.setValue((this.attachmentSizeLimit/10**6).toString());return}this.attachmentSizeLimit=o*10**6}))}addMinimumImageSize(e){this.minimumImageSize=e,new ke.Setting(this.modal.contentEl).setName("Minimum image size (px)").setDesc("Set 0 to disable.").addText(i=>i.then(({inputEl:r})=>r.type="number").setValue(e.toString()).onChange(r=>{let o=["+","-"].includes(r)?0:Number(r);if(!Number.isInteger(o)||o<0){i.setValue(this.minimumImageSize.toString());return}this.minimumImageSize=o}))}async import(e){let{files:i}=this;if(i.length===0){new ke.Notice("Please pick at least one file to import.");return}let r=await this.getOutputFolder();if(!r){new ke.Notice("Please select a location to export to.");return}let o=new Map;e.reportProgress(0,i.length);for(let c=0;c<i.length;c++){if(e.isCancelled())return;let p=i[c],m=await this.processFile(e,r,p);m&&o.set(p instanceof Me?Jn.pathToFileURL(p.filepath).href:p.name,{file:p,tFile:m}),e.reportProgress(c+1,i.length)}let{metadataCache:a}=this.app,s,l=new Promise(c=>{s=c});a.onCleanCache(async()=>{for(let[c,{file:p,tFile:m}]of o){if(e.isCancelled())break;try{let y=await this.app.vault.cachedRead(m),w=a.computeMetadataAsync?await a.computeMetadataAsync(Oo(y)):a.getFileCache(m);if(!w)continue;let E=[];if(w.links)for(let{link:d,position:f,displayText:g}of w.links){let{path:v,subpath:_}=(0,ke.parseLinktext)(d),A;if(Jn){let T=new URL(encodeURI(v),c);T.hash="",T.search="",A=decodeURIComponent(T.href)}else A=re(v.replace(/#/gu,"%23")).name;let D=o.get(A);if(D){let T=this.app.fileManager.generateMarkdownLink(D.tFile,m.path,_,g);E.push({from:f.start.offset,to:f.end.offset,text:T})}}E.sort((d,f)=>f.from-d.from);for(let d of E)y=y.substring(0,d.from)+d.text+y.substring(d.to);await this.vault.modify(m,y)}catch(y){e.reportFailed(p.fullpath,y)}}s()}),await l}async processFile(e,i,r){e.status("Processing "+r.name);try{let o=await r.readText(),a=ft(o);My(a);let s=r instanceof Me?Jn.pathToFileURL(r.filepath):void 0,l=new Map,c=new Map;for(let y of a.findAll("img, audio, video")){if(e.isCancelled())return;let w=y.getAttribute("src");if(w)try{let E=new URL(w.startsWith("//")?`https:${w}`:w,s),d=E.href,f=l.get(d);l.has(d)||(e.status("Downloading attachment for "+r.name),f=await this.downloadAttachment(i,y,E),l.set(d,f),f?(c.set(f.path,f),e.reportAttachmentSuccess(f.name)):e.reportSkipped(w)),f&&(y.setAttribute("src",f.path.replace(/ /g,"%20")),y instanceof HTMLImageElement||y.replaceWith(createEl("img",{attr:{src:f.path.replace(/ /g,"%20"),alt:y.getAttr("alt")}})))}catch(E){e.reportFailed(w,E)}}let p=(0,ke.htmlToMarkdown)(a),m=await this.saveAsMarkdownFile(i,r.basename,p);if(!Object.isEmpty(l)){let{metadataCache:y}=this.app,w;y.computeMetadataAsync?w=await y.computeMetadataAsync(Oo(p)):w=await new Promise(d=>{let f=y.getFileCache(m);if(f)return d(f);let g=y.on("changed",(v,_,A)=>{v===m&&(y.offref(g),d(A))})});let E=[];if(w.embeds){for(let{link:d,position:f}of w.embeds)if(c.has(d)){let g=this.app.fileManager.generateMarkdownLink(c.get(d),m.path);E.push({from:f.start.offset,to:f.end.offset,text:g})}}E.sort((d,f)=>f.from-d.from);for(let d of E)p=p.substring(0,d.from)+d.text+p.substring(d.to);await this.vault.modify(m,p)}return e.reportNoteSuccess(r.fullpath),m}catch(o){e.reportFailed(r.fullpath,o)}return null}async downloadAttachment(e,i,r){let o="",a="",s;switch(r.protocol){case"file:":let p=Jn.fileURLToPath(r.href);({basename:o,extension:a}=re(p)),s=Zi(await Pe.readFile(p));break;case"https:":case"http:":let m=await By(r),y=$y(r);o=y.basename,s=m.data,a=vr(m.mime)||y.extension;break;default:throw new Error(r.href)}if(!this.filterAttachmentSize(s)||i instanceof HTMLImageElement&&!await this.filterImageSize(s))return null;if(!a)if(i instanceof HTMLImageElement)a="png";else if(i instanceof HTMLAudioElement)a="mp3";else if(i instanceof HTMLVideoElement)a="mp4";else return null;let l=await this.createFolders((0,ke.normalizePath)(e.path+"/Attachments")),c=await this.vault.getAvailablePath(l.getParentPrefix()+o,a);return await this.vault.createBinary(c,s)}filterAttachmentSize(e){let{byteLength:i}=e;return!this.attachmentSizeLimit||i<=this.attachmentSizeLimit}async filterImageSize(e){if(!this.minimumImageSize)return!0;let i;try{i=await Uy(e)}catch(a){return!0}let{height:r,width:o}=i;return o>=this.minimumImageSize&&r>=this.minimumImageSize}};function Hf(n,t){let e=n.getAttribute(t);e!==null&&n.setAttribute(t,e.replace(/ /gu,"%20"))}function My(n){n.findAll("a").forEach(t=>Hf(t,"href")),n.findAll("audio, img, video").forEach(t=>Hf(t,"src"))}function $y(n){return re((0,ke.normalizePath)(decodeURIComponent(n.pathname)))}async function By(n){var e,i;try{let r=await fetch(n,{mode:"cors",referrerPolicy:"no-referrer"});if(r.ok)return{data:await r.arrayBuffer(),mime:(e=r.headers.get("Content-Type"))!=null?e:""}}catch(r){}let t=await(0,ke.requestUrl)(n.href);return{data:t.arrayBuffer,mime:(i=t.headers["Content-Type"])!=null?i:""}}async function Uy(n){let t=new Image,e=URL.createObjectURL(new Blob([n]));try{return await new Promise((i,r)=>{t.addEventListener("error",({error:o})=>r(o),{once:!0,passive:!0}),t.addEventListener("load",()=>i({height:t.naturalHeight,width:t.naturalWidth}),{once:!0,passive:!0}),t.src=e})}finally{URL.revokeObjectURL(e)}}var Fn=require("obsidian");var jy=/(#[^ ^#]*)/g,Hy=/[\\:*?<>\"|!@#$%^&()+=\`\'~;,.]/g;function za(n){let t=n.replace(Hy,"");return t=t.split(" ").join("-"),isNaN(t[0])||(t="_"+t),t}function Ga(n){return n.replace(jy,t=>"#"+za(t))}function qf(n){return n.charAt(0).toUpperCase()+n.slice(1).toLowerCase()}var qy=["zip"],Wy=["json"],Zy=["html","txt"],Fr=class extends ce{constructor(){super(...arguments);this.importArchived=!1;this.importTrashed=!1}init(){this.addFileChooserSetting("Notes & attachments",[...qy,...Wy,...Ln],!0),this.importArchivedSetting=new Fn.Setting(this.modal.contentEl).setName("Import archived notes").setDesc("If imported, files archived in Google Keep will be tagged as archived.").addToggle(e=>{e.setValue(this.importArchived),e.onChange(async i=>{this.importArchived=i})}),this.importTrashedSetting=new Fn.Setting(this.modal.contentEl).setName("Import deleted notes").setDesc("If imported, files deleted in Google Keep will be tagged as deleted. Deleted notes will only exist in your Google export if deleted recently.").addToggle(e=>{e.setValue(this.importTrashed),e.onChange(async i=>{this.importTrashed=i})}),this.addOutputLocationSetting("Google Keep")}async import(e){let{files:i}=this;if(i.length===0){new Fn.Notice("Please pick at least one file to import.");return}let r=await this.getOutputFolder();if(!r){new Fn.Notice("Please select a location to import your files to.");return}let o=`${r.path}/Assets`;for(let a of i){if(e.isCancelled())return;await this.handleFile(a,r,o,e)}}async handleFile(e,i,r,o){let{fullpath:a,name:s,extension:l}=e;o.status("Processing "+s);try{l==="zip"?await this.readZipEntries(e,i,r,o):l==="json"?await this.importKeepNote(e,i,o):Ln.contains(l)?(o.status("Importing attachment "+s),await this.copyFile(e,r),o.reportAttachmentSuccess(a)):!(e instanceof fi)&&!Zy.contains(l)&&o.reportSkipped(a)}catch(c){o.reportFailed(a,c)}}async readZipEntries(e,i,r,o){await gt(e,async(a,s)=>{for(let l of s){if(o.isCancelled())return;await this.handleFile(l,i,r,o)}})}async importKeepNote(e,i,r){let{fullpath:o,basename:a}=e;r.status("Importing note "+a);let s=await e.readText(),l=JSON.parse(s);if(!l||!l.userEditedTimestampUsec||!l.createdTimestampUsec){r.reportFailed(o,"Invalid Google Keep JSON");return}if(l.isArchived&&!this.importArchived){r.reportSkipped(o,"Archived note");return}if(l.isTrashed&&!this.importTrashed){r.reportSkipped(o,"Deleted note");return}await this.convertKeepJson(l,i,a),r.reportNoteSuccess(o)}async copyFile(e,i){let r=await this.createFolders(i),o=await e.read();await this.vault.createBinary(`${r.path}/${e.name}`,o)}async convertKeepJson(e,i,r){let o=[],a={};if(e.title){let c=e.title.split(`
`).filter(p=>p!==r);c.length>0&&(a.aliases=c)}let s=[];if(e.color&&e.color!=="DEFAULT"){let c=e.color.toLowerCase();c=qf(c),s.push(`Keep/Color/${c}`)}if(e.isPinned&&s.push("Keep/Pinned"),e.attachments&&s.push("Keep/Attachment"),e.isArchived&&s.push("Keep/Archived"),e.isTrashed&&s.push("Keep/Deleted"),e.labels)for(let c of e.labels)s.push(`Keep/Label/${c.name}`);if(s.length>0&&(a.tags=s.map(c=>za(c))),o.push(Yi(a)),e.textContent&&(o.push(`
`),o.push(Ga(e.textContent))),e.listContent){let c=[];for(let p of e.listContent){if(!p.text)continue;let m=`- [${p.isChecked?"X":" "}] ${p.text}`;c.push(Ga(m))}o.push(`

`),o.push(c.join(`
`))}if(e.attachments){o.push(`

`);for(let c of e.attachments)o.push(`![[${c.filePath}]]`)}let l=await this.saveAsMarkdownFile(i,r,o.join(""));await this.vault.append(l,"",{ctime:e.createdTimestampUsec/1e3,mtime:e.userEditedTimestampUsec/1e3})}};var xt=require("obsidian");var Va=require("obsidian");function Wf({info:n,vault:t,targetFolderPath:e,parentsInSubfolders:i}){let r=t.getAllLoadedFiles(),o=new Set,a=new Set(r.map(s=>s.name));i&&zy(n),Yy({info:n,pathDuplicateChecks:o,titleDuplicateChecks:a}),Gy({info:n,loadedFiles:r,titleDuplicateChecks:a,targetFolderPath:e})}function Yy({info:n,pathDuplicateChecks:t,titleDuplicateChecks:e}){for(let i of Object.values(n.idsToFileInfo)){let r=n.getPathForFile(i);if(t.has(`${r}${i.title}`)){let o=2;for(i.title=i.title+" "+o;t.has(`${r}${i.title}`);)o++,i.title=`${i.title.replace(/ \d+$/,"")} ${o}`}e.has(i.title+".md")&&(i.fullLinkPathNeeded=!0),t.add(`${r}${i.title}`),e.add(i.title+".md")}}function zy(n){let t=new Set(Object.values(n.idsToFileInfo).map(e=>e.parentIds).concat(Object.values(n.pathsToAttachmentInfo).map(e=>e.parentIds)).map(e=>e.length>0?e[e.length-1]:""));for(let e of Object.keys(n.idsToFileInfo))t.has(e)&&n.idsToFileInfo[e].parentIds.push(e)}function Gy({info:n,loadedFiles:t,titleDuplicateChecks:e,targetFolderPath:i}){var l;let r=new Set(t.filter(c=>!c.path.endsWith(".md")).map(c=>c.path)),o=n.attachmentPath,a=/^\.\//.test(o),s=(l=o.match(/\.\/(.*)/))==null?void 0:l[1];for(let c of Object.values(n.pathsToAttachmentInfo)){e.has(c.nameWithExtension)&&(c.fullLinkPathNeeded=!0);let p="";if(a?p=(0,Va.normalizePath)(`${i}${n.getPathForFile(c)}${s!=null?s:""}`):p=(0,Va.normalizePath)(o+"/"),p.endsWith("/")||(p+="/"),r.has(p+c.nameWithExtension)){let m=2,{basename:y,extension:w}=re(c.path);for(;r.has(`${p}${y} ${m}.${w}`);)m++;c.nameWithExtension=`${y} ${m}.${w}`}c.targetParentFolder=p,r.add(p+c.nameWithExtension),e.add(c.nameWithExtension)}}var Pn=require("obsidian");var Zf=n=>n.replace(/-/g,"").replace(/[ -]?[a-z0-9]{32}(\.|$)/,"$1"),bt=n=>{var t;return(t=n.replace(/-/g,"").match(/([a-z0-9]{32})(\?|\.|$)/))==null?void 0:t[1]},Ka=n=>{let{parent:t}=re(n);return t.split("/").map(e=>bt(e)).filter(e=>e)};function Xa(n){return n.hour()===0&&n.minute()===0?n.format("YYYY-MM-DD"):n.format("YYYY-MM-DDTHH:mm")}function Yf(n){return n.replace(/^(\.\.\/)+/,"")}function zf(n){let t=/#\d*?(?:[-_/a-z]|[^\x00-\x7F])/gi;if(!t.test(n))return n;let e=n.split(`
`);for(let i=0;i<e.length;i++){let r=e[i].match(t);if(!r)continue;let o=e[i];for(let a of r)new RegExp(`\\[\\[[^\\]]*${a}(?:.*[^\\]])?\\]\\]|\\[[^\\]]*${a}[^\\]]*\\]\\([^\\)]*\\)|\\[[^\\]]*\\]\\([^\\)]*${a}[^\\)]*\\)|\\\\${a}|\`[^\`]*${a}[^\`]*\``).test(o)||(o=o.replace(a,"\\"+a));e[i]=o}return n=e.join(`
`),n}function Ja(n){n.replaceWith(...Array.from(n.childNodes))}async function id(n,t){var p;let e=await t.readText(),i=ft(e),r=i.find("div[class=page-body]");if(r===null)throw new Error("page body was not found");let o=Vf(n,r);nd(n,o,!0);let a={},s=i.find("table[class=properties] > tbody");if(s){let m=Vf(n,s);nd(n,m,!1),m1(s);for(let y of Array.from(s.rows)){let w=Vy(y);w&&(w.title=="Tags"&&(w.title="tags",typeof w.content=="string"?w.content=w.content.replace(/ /g,"-"):w.content instanceof Array&&(w.content=w.content.map(E=>E.replace(/ /g,"-")))),a[w.title]=w.content)}}o1(r,["strong","em","mark","del"]),n1(r),Xy(r),u1(r),e1(r),p1(r),ad(r),ed(r,"div.indented"),ed(r,"details"),f1(r),td(r,"ul"),td(r,"ol"),d1(r),c1(r),i1(r);let l=(0,Pn.htmlToMarkdown)(r.innerHTML);n.singleLineBreaks&&(l=l.replace(/\n\n(?!>)/g,`
`)),l=zf(l),l=Ky(l);let c=(p=i.find("p[class*=page-description]"))==null?void 0:p.textContent;return c&&(l=c+`

`+l),Yi(a)+l}var Gf={checkbox:["checkbox"],date:["created_time","last_edited_time","date"],list:["file","multi_select","relation"],number:["number","auto_increment_id"],text:["email","person","phone_number","text","url","status","select","formula","rollup","last_edited_by","created_by"]};function Vy(n){var a,s,l,c,p,m;let t=(a=n.className.match(/property-row-(.*)/))==null?void 0:a[1];if(!t)throw new Error("property type not found for: "+n);let e=(0,Pn.htmlToMarkdown)((s=n.cells[0].textContent)!=null?s:""),i=n.cells[1],r=Object.keys(Gf).find(y=>Gf[y].includes(t));if(!r)throw new Error("type not found for: "+i);let o="";switch(r){case"checkbox":o=i.innerHTML.includes("checkbox-on");break;case"number":if(o=Number(i.textContent),isNaN(o))return;break;case"date":ad(i);let y=i.getElementsByTagName("time");if(y.length===0)o="";else if(y.length===1)o=Xa((0,Pn.moment)((l=y.item(0))==null?void 0:l.textContent));else{let d=[];for(let f=0;f<y.length;f++)d.push(Xa((0,Pn.moment)((c=y.item(f))==null?void 0:c.textContent)));o=d.join(" - ")}if(o.length===0)return;break;case"list":let w=i.children,E=[];for(let d=0;d<w.length;d++){let f=(p=w.item(d))==null?void 0:p.textContent;f&&E.push(f)}if(o=E,o.length===0)return;break;case"text":if(o=(m=i.textContent)!=null?m:"",o.length===0)return;break}return{title:e,content:o}}function Vf(n,t){var i,r;let e=[];for(let o of t.findAll("a")){let a=Yf(decodeURI((i=o.getAttribute("href"))!=null?i:"")),s=bt(a),l=Object.keys(n.pathsToAttachmentInfo).find(c=>c.includes(a));s&&a.endsWith(".html")?e.push({type:"relation",a:o,id:s}):l?e.push({type:"attachment",a:o,path:l}):s&&a.startsWith("#")&&((r=o.parentElement)!=null&&r.classList.contains("table_of_contents-item"))&&e.push({type:"toc-item",a:o,id:s})}return e}function Ky(n){let t=/\[\[[^\]]*(\\\\)\|[^\]]*\]\]/,e=n.match(new RegExp(t,"g"));return e==null||e.forEach(i=>{n=n.replace(i,i.replace(/\\\\\|/g,"\\|"))}),n}function Xy(n){r1(n,"style"),rd(n,"span.notion-text-equation-token");let t=n.ownerDocument,e=n.findAll("figure.equation");for(let r of e){let o=r.find("annotation");if(!o)continue;let a=t.createElement("div");a.className="annotation",a.appendText(`$$${Kf(o.textContent)}$$`),r.replaceWith(a)}let i=n.findAll("span.notion-text-equation-token");for(let r of i){let o=r.find("annotation");o&&r.replaceWith(`$${Kf(o.textContent,!0)}$`)}}function Kf(n,t=!1){var i;let e=new RegExp(/^(?:\s|\\\\|\\\s)*(.*?)[\s\\]*$/,"s");return(i=n==null?void 0:n.replace(e,"$1").replace(/[\r\n]+/g,t?" ":`
`))!=null?i:""}function Jy(n){var e;let t=(e=n.match(/^[^\.\?\!\n]*[\.\?\!]?/))==null?void 0:e[0];return t!=null?t:""}function Qy(n){var t;return!!/callout|bookmark/.test((t=n.getAttribute("class"))!=null?t:"")}function e1(n){var e;let t=n.ownerDocument;for(let i of n.findAll("figure.callout")){let r=(e=i.lastElementChild)==null?void 0:e.childNodes;if(!r)continue;let o=t.createElement("blockquote");o.append(...Array.from(r)),t1(o),i.replaceWith(o)}}function t1(n){var o,a,s;let t=n.firstChild,e=(o=t==null?void 0:t.nodeName)!=null?o:"",i=n.ownerDocument.createElement("p"),r="";e=="#text"?r=(a=t==null?void 0:t.textContent)!=null?a:"":e=="P"?r=t.innerHTML:["EM","STRONG","DEL","MARK"].includes(e)?r=t.outerHTML:n.prepend(i),r=r.replace(/<br>/g,"&lt;br&gt;"),i.innerHTML=`[!important] ${r}`,(s=n.firstChild)==null||s.replaceWith(i)}function n1(n){var t,e,i;for(let r of n.findAll("a.bookmark.source")){let o=r.getAttribute("href"),a=(t=r.find("div.bookmark-title"))==null?void 0:t.textContent,s=Jy((i=(e=r.find("div.bookmark-description"))==null?void 0:e.textContent)!=null?i:""),l=`> [!info] ${a}
> ${s}
> [${o}](${o})
`;r.nextElementSibling&&Qy(r.nextElementSibling)&&(l+=`
`),r.replaceWith(l)}}function i1(n){var t,e,i;for(let r of n.findAll("span[class=user]"))r.innerText=(t=r.textContent)!=null?t:"";for(let r of n.findAll("td div[class*=checkbox]")){let o=createSpan();o.setText(r.hasClass("checkbox-on")?"X":""),r.replaceWith(o)}for(let r of n.findAll("table span[class*=selected-value]"))((e=r.parentElement)==null?void 0:e.lastElementChild)!==r&&r.setText(r.textContent+", ");for(let r of n.findAll("a[href]"))if(!/^(https?:\/\/|www\.)/.test(r.href)){let o=createSpan();o.setText((i=r.textContent)!=null?i:""),r.replaceWith(o)}}function r1(n,t){for(let e of n.findAll(t))e.remove()}function o1(n,t){for(let e of t)a1(n,e);for(let e of t)s1(n,e);for(let e of t)rd(n,e);for(let e of t)l1(n,e)}function a1(n,t){for(let e of n.findAll(t)){if(!e.parentElement||e.parentElement.tagName===t.toUpperCase())continue;let i=e.find(t);for(;i;)Ja(i),i=e.find(t)}}function s1(n,t){let e=n.findAll(t);if(!e)return;let i=new RegExp(`</${t}>( *)<${t}>`,"g");for(let r of e){if(!r||!r.parentElement)continue;let o=r.parentElement,a=o==null?void 0:o.innerHTML;o.innerHTML=a==null?void 0:a.replace(i,"$1")}}function rd(n,t){let e=n.findAll(t);if(e)for(let i of e){let r=i.previousSibling;(r==null?void 0:r.nodeName)=="BR"&&(r==null||r.remove())}}function l1(n,t){let e=n.innerHTML,i=e.match(new RegExp(`<${t}>.*?</${t}>`,"sg"));if(i){for(let r of i.filter(o=>o.includes("<br>")))e=e.replace(r,r.split("<br>").join(`</${t}><br><${t}>`));n.innerHTML=e}}function Xf(n){var t;return Number((t=n==null?void 0:n.classList[1].slice(-1))!=null?t:-1)}function od(n,t){let e=Xf(n),i=Xf(t);if(e>i&&t.childElementCount==1){let r=createEl("ul");r.append(n),t.append(r)}else if(e>i&&t.childElementCount==2){let r=t.lastElementChild;r==null||r.append(n)}else if(e==i){let r=t.parentElement;r==null||r.append(n)}else e<i&&(t=t.parentElement.parentElement,od(n,t))}function Jf(n){var e;let t=createEl("li");return t.className=n.className,t.append((e=n.firstElementChild)!=null?e:""),t}function c1(n){let t=n.find(".table_of_contents"),e=t==null?void 0:t.children;if(!t||e.length==0)return;let i=createEl("ul"),r=Jf(e[0]);i.append(r);let o=r;for(let a=1;a<e.length;a++)r=Jf(e[a]),od(r,o),o=r;t.replaceWith(i)}function p1(n){n.innerHTML=n.innerHTML.replace(/(?:\n|<br ?\/>)/g,"<br>");for(let t of n.findAll("code"))for(let e of t.findAll("br"))e.replaceWith(`
`)}function u1(n){var t;for(let e of n.findAll("link"))e.innerText=(t=e.textContent)!=null?t:""}function ad(n){var t,e;for(let i of n.findAll("time"))i.textContent=(e=(t=i.textContent)==null?void 0:t.replace(/@/g,""))!=null?e:""}var Qf={"1.875em":"h1","1.5em":"h2","1.25em":"h3"};function f1(n){var e;let t=n.findAll("summary");for(let i of t){let r=i.getAttribute("style");if(r){for(let o of Object.keys(Qf))if(r.includes(o)){i.replaceWith(createEl(Qf[o],{text:(e=i.textContent)!=null?e:""}));break}}}}function ed(n,t){let e=n.findAll(t);for(let i of e)Ja(i)}function td(n,t){for(let e of n.findAll(t)){let i=[],r=[],o=e;for(;o.tagName===t.toUpperCase();){i.push(o);for(let s=0;s<o.children.length;s++)r.push(o.children[s]);if(!o.nextElementSibling||o.getAttribute("class")!==o.nextElementSibling.getAttribute("class"))break;o=o.nextElementSibling}let a=n.createEl(t);for(let s of r)a.appendChild(s);i[0].replaceWith(a),i.slice(1).forEach(s=>s.remove())}}function d1(n){for(let t of n.findAll(".checkbox.checkbox-on"))t.replaceWith("[x] ");for(let t of n.findAll(".checkbox.checkbox-off"))t.replaceWith("[ ] ")}function m1(n){var e;let t=n.findAll("a");if(t.length===0)return n;for(let i of t){let r=createSpan();r.setText((e=i.getAttribute("href"))!=null?e:""),i.replaceWith(r)}}function nd(n,t,e){var i,r,o;for(let a of t){let s=createSpan(),l="";switch(a.type){case"relation":let c=n.idsToFileInfo[a.id];if(c){let y=a.a.closest("table");l=`[[${c.fullLinkPathNeeded?`${n.getPathForFile(c)}${c.title}${y?"\\":""}|${c.title}`:c.title}]]`}else{console.warn("missing relation data for id: "+a.id);let{basename:y}=re(decodeURI((i=a.a.getAttribute("href"))!=null?i:""));l=`[[${Zf(y)}]]`}break;case"attachment":let p=n.pathsToAttachmentInfo[a.path];if(!p){console.warn("missing attachment data for: "+a.path);continue}l=`${e?"!":""}[[${p.fullLinkPathNeeded?p.targetParentFolder+p.nameWithExtension+"|"+p.nameWithExtension:p.nameWithExtension}]]`;break;case"toc-item":l=(r=a.a.textContent)!=null?r:"";let m=(o=l.endsWith("]"))!=null?o:!1;l=`[[#${l+(m?" ":"")}]]`}s.setText(l),a.a.replaceWith(s)}}var Lr=class{constructor(t,e){this.idsToFileInfo={};this.pathsToAttachmentInfo={};this.attachmentPath=t,this.singleLineBreaks=e}getPathForFile(t){let{idsToFileInfo:e}=this,i=t.path.split("/");return t.parentIds.map(r=>{var o,a,s;return(s=(o=e[r])==null?void 0:o.title)!=null?s:(a=i.find(l=>l.contains(r)))==null?void 0:a.replace(` ${r}`,"")}).filter(r=>r).map(r=>r.replace(/[\. ]+$/,"")).join("/")+"/"}};async function ld(n,t){var i,r;let{filepath:e}=t;if(t.extension==="html"){let o=await t.readText(),a=ft(o),l=a.find("body").children,c;for(let E=0;E<l.length&&(c=bt((i=l[E].getAttr("id"))!=null?i:""),!c);E++);if(!c)throw new Error("no id found for: "+e);let p=sd(a,"property-row-created_time"),m=sd(a,"property-row-last_edited_time"),y=((r=a.find("title"))==null?void 0:r.textContent)||"Untitled",w=h1(Ze(y.replace(/\n/g," ").replace(/[:\/]/g,"-").replace(/#/g,"").trim()));n.idsToFileInfo[c]={path:e,parentIds:Ka(e),ctime:p,mtime:m,title:w,fullLinkPathNeeded:!1}}else n.pathsToAttachmentInfo[e]={path:e,parentIds:Ka(e),nameWithExtension:Ze(decodeURIComponent(t.name)),targetParentFolder:"",fullLinkPathNeeded:!1}}function h1(n){if(n.length<200)return n;let t=n.split(" "),e=[],i=0,r=0,o=!1;for(;i<200;){if(!t[r]){o=!0;break}e.push(t[r]),i+=t[r].length+1}let a=e.join(" ");return o||(a+="..."),a}function g1(n){let t=n.startsWith("@")?n.substr(1).trim():n.trim(),e=new Date(t);return isNaN(e.getTime())?null:e}function sd(n,t){let e=n.querySelector(`tr.${t}`);if(e){let i=e.querySelector("time");return i&&i.textContent?g1(i.textContent):null}return null}var Pr=class extends ce{init(){this.parentsInSubfolders=!0,this.addFileChooserSetting("Exported Notion",["zip"]),this.addOutputLocationSetting("Notion"),new xt.Setting(this.modal.contentEl).setName("Save parent pages in subfolders").setDesc("Places the parent database pages in the same folder as the nested content.").addToggle(e=>e.setValue(this.parentsInSubfolders).onChange(i=>this.parentsInSubfolders=i)),new xt.Setting(this.modal.contentEl).setName("Single line breaks").setDesc("Separate Notion blocks with only one line break (default is 2).").addToggle(e=>e.setValue(this.singleLineBreaks).onChange(i=>{this.singleLineBreaks=i}))}async import(e){var w;let{vault:i,parentsInSubfolders:r,files:o}=this;if(o.length===0){new xt.Notice("Please pick at least one file to import.");return}let a=await this.getOutputFolder();if(!a){new xt.Notice("Please select a location to export to.");return}let s=a.path;s=(0,xt.normalizePath)(s),s!=null&&s.endsWith("/")||(s+="/");let l=new Lr((w=i.getConfig("attachmentFolderPath"))!=null?w:"",this.singleLineBreaks);e.status("Looking for files to import");let c=0;if(await Qa(e,o,async E=>{try{await ld(l,E),c=Object.keys(l.idsToFileInfo).length+Object.keys(l.pathsToAttachmentInfo).length,e.reportProgress(0,c)}catch(d){e.reportSkipped(E.fullpath)}}),e.isCancelled())return;e.status("Resolving links and de-duplicating files"),Wf({vault:i,info:l,targetFolderPath:s,parentsInSubfolders:r});let p=new Set([s]),m=Object.values(l.idsToFileInfo).map(E=>s+l.getPathForFile(E)).concat(Object.values(l.pathsToAttachmentInfo).map(E=>E.targetParentFolder));for(let E of m)p.add(E);for(let E of p){if(e.isCancelled())return;await this.createFolders(E)}let y=0;e.status("Starting import"),await Qa(e,o,async E=>{y++,e.reportProgress(y,c);try{if(E.extension==="html"){let d=bt(E.name);if(!d)throw new Error("ids not found for "+E.filepath);let f=l.idsToFileInfo[d];if(!f)throw new Error("file info not found for "+E.filepath);e.status(`Importing note ${f.title}`);let g=await id(l,E),v={};f.ctime&&(v.ctime=f.ctime.getTime(),v.mtime=f.ctime.getTime()),f.mtime&&(v.mtime=f.mtime.getTime());let _=`${s}${l.getPathForFile(f)}${f.title}.md`;await i.create(_,g,v),e.reportNoteSuccess(E.fullpath)}else{let d=l.pathsToAttachmentInfo[E.filepath];if(!d)throw new Error("attachment info not found for "+E.filepath);e.status(`Importing attachment ${E.name}`);let f=await E.read();await i.createBinary(`${d.targetParentFolder}${d.nameWithExtension}`,f),e.reportAttachmentSuccess(E.fullpath)}}catch(d){if(d.message==="page body was not found"){e.reportSkipped(E.fullpath,"page body was not found");return}e.reportFailed(E.fullpath,d)}})}};async function Qa(n,t,e){for(let i of t){if(n.isCancelled())return;try{await gt(i,async(r,o)=>{for(let a of o){if(n.isCancelled())return;if(a.extension==="md"&&bt(a.name))throw new xt.Notice("Notion Markdown export detected. Please export Notion data to HTML instead."),n.cancel(),new Error("Notion importer uses only HTML exports. Please use the correct format.");if(!(a.extension==="csv"&&bt(a.name))&&a.name!=="index.html")if(a.extension==="zip"&&a.parent==="")try{await Qa(n,[a],e)}catch(s){n.reportFailed(a.fullpath)}else await e(a)}})}catch(r){n.reportFailed(i.fullpath)}}}var be=require("obsidian");var es="onenote-importer-refresh-token",cd="66553851-08fa-44f2-8bb1-1436f121a73d",pd=["user.read","notes.read"],y1=/<(object|iframe)([^>]*)\/>/g,ud=/(<\/p>)\s*(<p[^>]*>)|\n  \n/g,ts=5,b1=new RegExp(/^data:[\w\d]+\/[\w\d]+;base64,/),Mr=class extends ce{constructor(){super(...arguments);this.importPreviouslyImported=!1;this.importIncompatibleAttachments=!1;this.selectedIds=[];this.notebooks=[];this.graphData={state:xn(32),accessToken:""};this.attachmentDownloadPauseCounter=0;this.rememberMe=!1}async init(){this.addOutputLocationSetting("OneNote"),new be.Setting(this.modal.contentEl).setName("Import incompatible attachments").setDesc("Imports incompatible attachments which cannot be embedded in Obsidian, such as .exe files.").addToggle(r=>r.setValue(!1).onChange(o=>this.importIncompatibleAttachments=o)),new be.Setting(this.modal.contentEl).setName("Skip previously imported").setDesc("If enabled, notes imported previously by this plugin will be skipped.").addToggle(r=>r.setValue(!0).onChange(o=>this.importPreviouslyImported=!o));let e=!1;if(this.retrieveRefreshToken())try{await this.updateAccessToken(),e=!0}catch(r){}this.microsoftAccountSetting=new be.Setting(this.modal.contentEl).setName("Sign in with your Microsoft account").setDesc("You need to sign in to import your OneNote data.").addButton(r=>r.setCta().setButtonText("Sign in").onClick(()=>{this.registerAuthCallback(this.authenticateUser.bind(this));let o=new URLSearchParams({client_id:cd,scope:"offline_access "+pd.join(" "),response_type:"code",redirect_uri:$r,response_mode:"query",state:this.graphData.state});window.open(`https://login.microsoftonline.com/common/oauth2/v2.0/authorize?${o.toString()}`)})),this.microsoftAccountSetting.settingEl.toggle(!e);let i=new be.Setting(this.modal.contentEl).setName("Remember me").setDesc("If checked, you will be automatically logged in for subsequent imports.").addToggle(r=>{r.onChange(o=>{this.rememberMe=o,o&&this.refreshToken?this.storeRefreshToken(this.refreshToken):this.clearStoredRefreshToken()})});i.settingEl.toggle(!e),this.switchUserSetting=new be.Setting(this.modal.contentEl).addButton(r=>r.setCta().setButtonText("Switch user").onClick(()=>{this.microsoftAccountSetting.settingEl.show(),i.settingEl.show(),this.clearStoredRefreshToken(),this.switchUserSetting.settingEl.hide(),this.contentArea.empty()})),this.loadingArea=this.modal.contentEl.createDiv({text:"Loading notebooks..."}),this.loadingArea.hide(),this.contentArea=this.modal.contentEl.createDiv(),this.contentArea.hide(),e?(await this.setSwitchUser(),await this.showSectionPickerUI()):this.switchUserSetting.settingEl.hide()}async authenticateUser(e){try{if(e.state!==this.graphData.state)throw new Error(`An incorrect state was returned.
Expected state: ${this.graphData.state}
Returned state: ${e.state}`);await this.updateAccessToken(e.code),await this.setSwitchUser(),await this.showSectionPickerUI()}catch(i){console.error("An error occurred while we were trying to sign you in. Error details: ",i),this.modal.contentEl.createEl("div",{text:"An error occurred while trying to sign you in."}).createEl("details",{text:i}).createEl("summary",{text:"Click here to show error details"})}}async setSwitchUser(){let e=await this.fetchResource("https://graph.microsoft.com/v1.0/me","json");this.switchUserSetting.setDesc(`Signed in as ${e.displayName} (${e.mail}). If that's not the correct account, sign in again.`),this.switchUserSetting.settingEl.show(),this.microsoftAccountSetting.settingEl.hide()}async updateAccessToken(e){let i=new URLSearchParams({client_id:cd,scope:"offline_access "+pd.join(" "),redirect_uri:$r});if(e)i.set("code",e),i.set("grant_type","authorization_code");else{let o=this.retrieveRefreshToken();if(!o)throw new Error("Missing token required for authentication. Please try logging in again.");i.set("refresh_token",o),i.set("grant_type","refresh_token")}let r=await(0,be.requestUrl)({method:"POST",url:"https://login.microsoftonline.com/common/oauth2/v2.0/token",contentType:"application/x-www-form-urlencoded",body:i.toString()}).json;if(!r.access_token)throw new Error(`Unexpected data was returned instead of an access token. Error details: ${r}`);r.refresh_token&&this.storeRefreshToken(r.refresh_token),this.graphData.accessToken=r.access_token}storeRefreshToken(e){this.refreshToken=e,this.rememberMe&&localStorage.setItem(es,e)}retrieveRefreshToken(){return this.refreshToken?this.refreshToken:localStorage.getItem(es)}clearStoredRefreshToken(){localStorage.removeItem(es)}async showSectionPickerUI(){var o,a;this.loadingArea.show(),this.selectedIds=[];let e="https://graph.microsoft.com/v1.0/me/onenote/notebooks",i=new URLSearchParams({$expand:"sections($select=id,displayName),sectionGroups($expand=sections,sectionGroups)",$select:"id,displayName,lastModifiedDateTime",$orderby:"lastModifiedDateTime DESC"}),r=`${e}?${i.toString()}`;try{this.notebooks=(await this.fetchResource(r,"json")).value,this.contentArea.empty(),this.contentArea.createEl("h4",{text:"Choose data to import"});for(let s of this.notebooks){if(((o=s.sectionGroups)==null?void 0:o.length)!==0)for(let c of s.sectionGroups)await this.fetchNestedSectionGroups(c);let l=this.contentArea.createDiv();new be.Setting(l).setName(s.displayName).setDesc(`Last edited on: ${be.moment.utc(s.lastModifiedDateTime).format("Do MMMM YYYY")}. Contains ${(a=s.sections)==null?void 0:a.length} sections.`).addButton(c=>c.setCta().setButtonText("Select all").onClick(()=>{l.querySelectorAll('input[type="checkbox"]:not(:checked)').forEach(p=>p.click())})),this.renderHierarchy(s,l)}}catch(s){this.showContentAreaErrorMessage()}this.loadingArea.hide(),this.contentArea.show()}async fetchNestedSectionGroups(e){if(e.sectionGroups=(await this.fetchResource(e.sectionGroupsUrl+"?$expand=sectionGroups($expand=sections),sections","json")).value,e.sectionGroups)for(let i=0;i<e.sectionGroups.length;i++)await this.fetchNestedSectionGroups(e.sectionGroups[i])}renderHierarchy(e,i){if(e.sectionGroups)for(let r of e.sectionGroups){let o=i.createDiv({attr:{style:"padding-inline-start: 1em; padding-top: 8px"}});o.createEl("strong",{text:r.displayName}),this.renderHierarchy(r,o)}if(e.sections){let r=i.createEl("ul",{attr:{style:"padding-inline-start: 1em;"}});for(let o of e.sections){let s=r.createEl("li",{cls:"task-list-item"}).createEl("label"),l=s.createEl("input");l.type="checkbox",s.appendChild(document.createTextNode(o.displayName)),s.createEl("br"),l.addEventListener("change",()=>{if(l.checked)this.selectedIds.push(o.id);else{let c=this.selectedIds.findIndex(p=>p===o.id);c!==-1&&this.selectedIds.splice(c,1)}})}}}showContentAreaErrorMessage(){this.contentArea.empty(),this.contentArea.createEl("p",{text:"Microsoft OneNote has limited how fast notes can be imported. Please try again in 30 minutes to continue importing."}),this.contentArea.show(),this.loadingArea.hide()}async import(e){let i=new Set,r=await this.modal.plugin.loadData();r.importers.onenote||(r.importers.onenote={previouslyImportedIDs:[]});for(let c of r.importers.onenote.previouslyImportedIDs)i.add(c);if(!await this.getOutputFolder()){new be.Notice("Please select a location to export to.");return}if(!this.graphData.accessToken){new be.Notice("Please sign in to your Microsoft Account.");return}e.status("Starting OneNote import");let a=0,s=0,l=0;for(let c of this.selectedIds){e.reportProgress(s,a);let p=`https://graph.microsoft.com/v1.0/me/onenote/sections/${c}/pages`,m=new URLSearchParams({$select:"id,title,createdDateTime,lastModifiedDateTime,level,order,contentUrl",$orderby:"order",pagelevel:"true"}),y=`${p}?${m.toString()}`,w=null;try{w=(await this.fetchResource(y,"json")).value}catch(E){e.status("Microsoft OneNote has limited how fast notes can be imported. Please try again in 30 minutes to continue importing.");return}if(w){a+=w.length,this.insertPagesToSection(w,c),e.reportProgress(s,a);for(let E=0;E<w.length;E++){if(e.isCancelled())return;let d=w[E];if(d.title||(d.title=`Untitled-${(0,be.moment)().format("YYYYMMDDHHmmss")}`),!this.importPreviouslyImported&&d.id&&i.has(d.id)){e.reportSkipped(d.title,"it was previously imported");continue}try{e.status(`Importing note ${d.title}`),await this.processFile(e,await this.fetchResource(`https://graph.microsoft.com/v1.0/me/onenote/pages/${d.id}/content?includeInkML=true`,"text"),d),d.id&&(i.add(d.id),r.importers.onenote.previouslyImportedIDs=Array.from(i),await this.modal.plugin.saveData(r)),s++,l=0}catch(f){if(l++,e.reportFailed(d.title,f.toString()),l>5){e.status("Microsoft OneNote has limited how fast notes can be imported. Please try again in 30 minutes to continue importing.");return}}e.reportProgress(s,a)}}}}insertPagesToSection(e,i,r){if(!r){for(let o of this.notebooks)this.insertPagesToSection(e,i,o);return}if(r.sectionGroups){let o=r.sectionGroups;for(let a of o)this.insertPagesToSection(e,i,a)}if(r.sections){let o=r;for(let a of o.sections)a.id===i&&(a.pages=e)}}async processFile(e,i,r){var o,a;try{let s=this.convertFormat(i),l=await this.getOutputFolder(),c=this.getEntityPathNoParent(r.id,l.name),p;await this.vault.adapter.exists(c)?p=this.vault.getAbstractFileByPath(c):p=await this.vault.createFolder(c);let m=this.convertTags(ft(s.html)),y=await this.getAllAttachments(e,m.replace(ud,"<br />")),w=this.styledElementToHTML(y);w=this.convertInternalLinks(w),w=this.convertDrawings(w);let E=(0,be.htmlToMarkdown)(w).trim().replace(ud," "),d=await this.saveAsMarkdownFile(p,r.title,E),f=r!=null&&r.lastModifiedDateTime?Date.parse(r.lastModifiedDateTime):null,g=r!=null&&r.createdDateTime?Date.parse(r.createdDateTime):null,v={ctime:(o=g!=null?g:f)!=null?o:Date.now(),mtime:(a=f!=null?f:g)!=null?a:Date.now()};await this.vault.append(d,"",v),e.reportNoteSuccess(r.title)}catch(s){e.reportFailed(r.title,s)}}convertFormat(e){let i={html:"",inkml:""},r=e.split(`
`,1)[0];e.slice(0,-2);let o=e.split(r);if(o.shift(),o.length===2)for(let a of o){let l=a.split(`
`).find(p=>p.includes("Content-Type")).split(";")[0].split(":")[1].trim(),c=a.split(`
`).slice(2).join(`
`).trim();l==="text/html"?i.html=c:l==="application/inkml+xml"&&(i.inkml=c)}else throw new Error("The input string is incorrect and may be missing data. Inputted string: "+e);return i}convertTags(e){var r,o;let i=Array.from(e.querySelectorAll("[data-tag]"));for(let a of i)if((r=a.getAttribute("data-tag"))!=null&&r.contains("to-do")){let l=a.getAttribute("data-tag")==="to-do:completed"?"[x]":"[ ]";a.innerHTML=`- ${l} ${a.innerHTML}`}else{let s=(o=a.getAttribute("data-tag"))==null?void 0:o.split(",");s==null||s.forEach(l=>{a.innerHTML=a.innerHTML+` #${l.replace(":","-")} `})}return e.outerHTML}convertInternalLinks(e){let i=e.findAll("a");for(let r of i)if(r.href.startsWith("onenote:")){let o=r.href.indexOf("#")+1,a=r.href.indexOf("&",o);r.href=r.href.slice(o,a)}return e}getEntityPathNoParent(e,i){for(let r of this.notebooks){let o=this.getEntityPath(e,`${i}/${r.displayName}`,r);if(o)return o}return null}getEntityPath(e,i,r){let o=null;if("sectionGroups"in r&&r.sectionGroups){let a=this.searchSectionGroups(e,i,r.sectionGroups);a!==null&&(o=a)}if("sections"in r&&r.sections){let a=this.searchSectionGroups(e,i,r.sections);a!==null&&(o=a)}if("pages"in r&&r.pages){let a=this.searchPages(e,i,r);a!==null&&(o=a)}return o}searchPages(e,i,r){var a;let o=null;for(let s=0;s<r.pages.length;s++){let l=r.pages[s],c=(a=l.contentUrl.split("page-id=")[1])==null?void 0:a.split("}")[0];if(l.id===e||c===e){if(l.level===0)r.pages[s+1]&&r.pages[s+1].level!==0?o=`${i}/${l.title}`:o=i;else{o=i;for(let p=r.pages.indexOf(l)-1;p>=0;p--)if(r.pages[p].level===l.level-1){o+="/"+r.pages[p].title;break}}break}}return o}searchSectionGroups(e,i,r){let o=null;for(let a of r)if(a.id===e)o=`${i}/${a.displayName}`;else{let s=this.getEntityPath(e,`${i}/${a.displayName}`,a);if(s){o=s;break}}return o}async getAllAttachments(e,i){var l,c,p,m;let r=ft(i.replace(y1,"<$1$2></$1>")),o=r.findAll("object"),a=r.findAll("img"),s=r.findAll("iframe");for(let y of o){for(;y.firstChild;)(l=y.parentNode)==null||l.insertBefore(y.firstChild,y.nextSibling);let w=y.getAttribute("data-attachment").split("."),E=w[w.length-1];if(!(!Ln.contains(E)&&!this.importIncompatibleAttachments)){let d=y.getAttribute("data-attachment"),f=y.getAttribute("data"),g=await this.fetchAttachment(e,d,f),v=document.createElement("p");v.innerText=`![[${g}]]`,(c=y.parentNode)==null||c.replaceChild(v,y)}}for(let y=0;y<a.length;y++){let w=a[y],d=w.getAttribute("data-fullres-src-type").split("/")[1],g=`Exported image ${(0,be.moment)().format("YYYYMMDDHHmmss")}-${y}.${d}`,v=w.getAttribute("data-fullres-src"),_=await this.fetchAttachment(e,g,v);_&&(w.src=encodeURI(_),!w.alt||b1.test(w.alt)?w.alt="Exported image":w.alt=w.alt.replace(/[\r\n]+/gm,""))}for(let y of s)if(y.src.contains("youtube.com")||y.src.contains("youtu.be")){let w=document.createTextNode(`![Embedded YouTube video](${y.src})`);(p=y.parentNode)==null||p.replaceChild(w,y)}else{let w=document.createElement("a");w.href=y.src,(m=y.parentNode)==null||m.replaceChild(w,y)}return r}async fetchAttachment(e,i,r){this.attachmentDownloadPauseCounter===7&&await new Promise(o=>{e.status("Pausing attachment download to avoid rate limiting."),this.attachmentDownloadPauseCounter=0,setTimeout(o,3e3)}),this.attachmentDownloadPauseCounter++,e.status("Downloading attachment "+i);try{let o=await this.getAvailablePathForAttachment(i,[]),a=await this.fetchResource(r,"file");return await this.app.vault.createBinary(o,a),e.reportAttachmentSuccess(i),o}catch(o){e.reportFailed(i),console.error(o)}}styledElementToHTML(e){let i={"font-weight:bold":"b","font-style:italic":"i","text-decoration:underline":"u","text-decoration:line-through":"s","background-color":"mark"};e.findAll("cite").forEach(l=>l.innerHTML="> "+l.innerHTML+"<br>");let o=!1,a=document.createElement("pre");return e.querySelectorAll("*").forEach(l=>{let c=l.getAttribute("style")||"",p=Object.keys(i).find(m=>c.includes(m));if(c!=null&&c.contains("font-family:Consolas"))o?a.innerHTML=a.innerHTML.slice(0,-3)+l.innerHTML+"\n```":(o=!0,l.replaceWith(a),a.innerHTML="```\n"+l.innerHTML+"\n```");else if(l.nodeName==="BR"&&o)a.innerHTML=a.innerHTML.slice(0,-3)+"\n```";else if(p){let m=i[p],y=document.createElement(m);y.innerHTML=l.innerHTML,l.replaceWith(y)}}),e}convertDrawings(e){var o;let i=document.createTreeWalker(e,NodeFilter.SHOW_COMMENT),r=!1;for(;i.nextNode();)((o=i.currentNode.nodeValue)==null?void 0:o.trim())==="InkNode is not supported"&&(r=!0);if(r){let a=document.createTextNode("> [!caution] This page contained a drawing which was not converted.");e.insertBefore(a,e.firstChild)}else for(let a=0;a<e.children.length;a++){let s=e.children[a];s instanceof HTMLElement&&this.convertDrawings(s)}return e}async fetchResource(e,i="json",r=0){try{let o=await fetch(e,{headers:{Authorization:`Bearer ${this.graphData.accessToken}`}}),a;if(o.ok)switch(i){case"text":a=await o.text();break;case"file":a=await o.arrayBuffer();break;default:a=await o.json(),"@odata.nextLink"in a&&a.value.push(...(await this.fetchResource(a["@odata.nextLink"],"json")).value);break}else{let s=null,l=await o.json();if(l.hasOwnProperty("error")&&(s=l.error),!s){if(console.log("An error has occurred while fetching an resource:",l),r<ts)return this.fetchResource(e,i,r+1);throw new Error("Unexpected error retrieving resource")}if(console.log("An error has occurred while fetching an resource:",s),s.code==="40001"&&r<ts)return await this.updateAccessToken(),this.fetchResource(e,i,r+1);if(s.code==="20166"){let c=+!o.headers.get("Retry-After")*1e3||15e3;if(console.log(`Rate limit exceeded, waiting for: ${c} ms`),r<ts)return await new Promise(p=>setTimeout(p,c)),this.fetchResource(e,i,r+1);throw new Error("Exceeded maximum retry attempts")}}return a}catch(o){throw console.error(`An internal error occurred while trying to fetch '${e}'. Error details: `,o),o}}};var Ut=require("obsidian");var fd=require("obsidian"),x1=/[\?<>\\:\*\|"]/g,w1=/[\x00-\x1f\x80-\x9f]/g,v1=/^\.+$/,E1=/^(con|prn|aux|nul|com[0-9]|lpt[0-9])(\..*)?$/i,_1=/[\. ]+$/,T1=/^\./,A1=/\[/g,k1=/\]/g;function Br(n){return n.replace(x1,"").replace(w1,"").replace(v1,"").replace(E1,"").replace(_1,"").replace(A1,"").replace(k1,"").replace(T1,"")}function Ur(n,t){let e="MMMM Do, YYYY",i=(0,fd.moment)(n,e);return i.format(e)!==n?n:i.isValid()?i.format(t):n}var jr=require("obsidian"),N1=["POMO","word-count","date","slider","encrypt","TaoOfRoam","orphans","count","character-count","comment-button","query","streak","attr-table","mentions","search","roam/render","calc"],S1=new RegExp(`\\{\\{(\\[\\[)?(${N1.join("|")})(\\]\\])?.*?\\}\\}(\\})?`,"g"),R1=/{{pdf:|{{\[\[pdf|{{\[\[audio|{{audio:|{{video:|{{\[\[video/,dd=/https:\/\/firebasestorage(.*?)\?alt(.*?)\)/,O1=/https:\/\/firebasestorage(.*?)\?alt(.*?)/,D1=/(?<=\(\()\b(.*?)\b(?=\)\))/g,Hr=class extends ce{constructor(){super(...arguments);this.downloadAttachments=!1;this.fileDateYAML=!1;this.titleYAML=!1;this.newestTimestamp=0;this.oldestTimestamp=0}init(){this.addFileChooserSetting("Roam (.json)",["json"]),this.addOutputLocationSetting("Roam"),this.userDNPFormat=this.getUserDNPFormat(),new Ut.Setting(this.modal.contentEl).setName("Import settings").setHeading(),new Ut.Setting(this.modal.contentEl).setName("Download all attachments").setDesc("If enabled, all attachments uploaded to Roam will be downloaded to your attachments folder.").addToggle(e=>{e.setValue(this.downloadAttachments),e.onChange(async i=>{this.downloadAttachments=i})}),new Ut.Setting(this.modal.contentEl).setName("Add YAML created/update date").setDesc("If enabled, notes will have the create-time and edit-time from Roam added as properties.").addToggle(e=>{e.setValue(this.fileDateYAML),e.onChange(async i=>{this.fileDateYAML=i})}),new Ut.Setting(this.modal.contentEl).setName("Add YAML title").setDesc("If enabled, notes will have the full title added as a property (regardless of illegal file name characters).").addToggle(e=>{e.setValue(this.titleYAML),e.onChange(async i=>{this.titleYAML=i})})}async import(e){this.progress=e;let{files:i}=this;if(i.length===0){new Ut.Notice("Please pick at least one file to import.");return}let r=await this.getOutputFolder();if(!r){new Ut.Notice("Please select a location to export to.");return}for(let o of i){if(e.isCancelled())return;let a=Ze(o.basename),s=`${r.path}/${a}`,l=`${r.path}/${a}/Attachments`;await this.createFolders(s),await this.createFolders(l);let c=await o.readText(),p=JSON.parse(c),[m,y]=this.preprocess(p),w=new Map;for(let g in p){let v=p[g],_=Ur(Br(v.title),this.userDNPFormat).trim();if(_===""){e.reportFailed(v.uid,"Title is empty"),console.error("Cannot import data with an empty title",v);continue}let A=`${s}/${_}.md`,D=this.titleYAML?v.title:"",T=0,k=0;if(this.fileDateYAML){let C=v["create-time"],O=v["edit-time"];typeof C=="number"&&(T=C),typeof O=="number"&&(k=O)}let b=await this.jsonToMarkdown(s,l,v,"",!1,D,T,k);w.set(A,b)}for(let g of y.values()){let v=await this.roamMarkupScrubber(s,l,g.blockString,!0),_=await this.extractAndProcessBlockReferences(w,m,s,v),A=`${s}/${g.pageName}.md`,D=w.get(A);if(D){let T=D.split(`
`),k=T.findIndex(b=>b.contains("* "+v));k!==-1&&(T[k]=T[k].replace(v,_)),w.set(A,T.join(`
`))}}let{vault:E}=this,d=w.size,f=1;for(let[g,v]of w.entries()){if(e.isCancelled())return;try{let{parent:_}=re(g);await this.createFolders(_);let A=E.getAbstractFileByPath(g);A?await E.modify(A,v):await E.create(g,v),e.reportNoteSuccess(g),e.reportProgress(f,d)}catch(_){console.error("Error saving Markdown to file:",g,_),e.reportFailed(g)}f++}}}getUserDNPFormat(){let e=this.app.internalPlugins.getPluginById("daily-notes").instance;return e?e.options.format||"YYYY-MM-DD":(console.log('Daily note plugin is not enabled. Roam import defaulting to "YYYY-MM-DD" format.'),"YYYY-MM-DD")}preprocess(e){let i=new Map,r=new Map,o=this.userDNPFormat;function a(s,l){if(l.uid){let c=new Date(s.uid);if(!isNaN(c.getTime())){let y=Ur(s.title,o);s.title=y}let p={pageName:Br(s.title),blockString:l.string};/.*?(\(\(.*?\)\)).*?/g.test(l.string)&&r.set(l.uid,p),i.set(l.uid,p)}if(l.children)for(let c of l.children)a(s,c)}for(let s of e)if(s.children)for(let l of s.children)a(s,l);return[i,r]}async roamMarkupScrubber(e,i,r,o=!1){return r=r.replace(S1,""),r.substring(0,8)==":hiccup "&&r.includes(":hr")?"---":(r=r.replace(/\[\[(.*?)\]\]/g,(a,s)=>`[[${Ur(Br(s),this.userDNPFormat)}]]`),r=r.replace(/\[\[(.*\/.*)\]\]/g,(a,s)=>`[[${e}/${s}|${s}]]`),r=r.replace(/\[.+?\]\((\(.+?\)\))\)/g,"$1"),r=r.replace(/\[(.+?)\]\(\[\[(.+?)\]\]\)/g,"[[$2|$1]]"),r=r.replace(/\[\[>\]\]/g,">"),r=r.replace(/{{TODO}}|{{\[\[TODO\]\]}}/g,"[ ]"),r=r.replace(/{{DONE}}|{{\[\[DONE\]\]}}/g,"[x]"),r=r.replace("::",":"),r=r.replace(/{{.*?\bvideo\b.*?(\bhttp.*?\byoutu.*?)}}/g,"![]($1)"),r=r.replace(/(https?:\/\/twitter\.com\/(?:#!\/)?\w+\/status\/\d+(?:\?[\w=&-]+)?)/g,"![]($1)"),r=r.replace(/\_\_(.+?)\_\_/g,"*$1*"),r=r.replace(/\^\^(.+?)\^\^/g,"==$1=="),r=r.replace(/{{\[{0,2}embed.*?(\(\(.*?\)\)).*?}}/g,"$1"),r=r.replace(/{{\[{0,2}embed.*?(\[\[.*?\]\]).*?}}/g,"$1"),this.downloadAttachments&&!o&&r.includes("firebasestorage")&&(r=await this.downloadFirebaseFile(r,i)),r)}async jsonToMarkdown(e,i,r,o="",a=!1,s,l,c){let p=[],m=[],y=r["edit-time"],w=r["create-time"];if(this.newestTimestamp<this.oldestTimestamp&&(this.oldestTimestamp=this.newestTimestamp),this.newestTimestamp=!y||c>y?c:y,w!==void 0?l>10?this.oldestTimestamp=Math.min(l,w):this.oldestTimestamp=w:this.oldestTimestamp=l,"string"in r&&r.string){let E=r.heading?"#".repeat(r.heading)+" ":"",d=await this.roamMarkupScrubber(e,i,r.string);p.push(`${a?o+"* ":o}${E}${d}`)}if(r.children)for(let E of r.children)p.push(await this.jsonToMarkdown(e,i,E,o+"  ",!0,"",this.oldestTimestamp,this.newestTimestamp));if((this.fileDateYAML||this.titleYAML)&&!a){let E=this.oldestTimestamp;if(m.push("---"),this.titleYAML&&m.push(`title: "${s}"`),this.fileDateYAML){let d="YYYY-MM-DD HH:mm:ss",f=this.newestTimestamp?(0,jr.moment)(this.newestTimestamp).format(d):(0,jr.moment)(new Date).format(d),g=E?(0,jr.moment)(E).format(d):f;m.push("created: "+g),m.push("updated: "+f)}m.push("---"),p.unshift(m.join(`
`))}return p.join(`
`)}async modifySourceBlockString(e,i,r,o){if(!i.blockString.endsWith("^"+o)){let a=`${r}/${i.pageName}.md`,s=e.get(a);if(s){let l=s.split(`
`),c=l.findIndex(p=>p.contains("* "+i.blockString));if(c!==-1){let p=i.blockString+" ^"+o;l[c]=l[c].replace(i.blockString,p),i.blockString=i.blockString+" ^"+o}e.set(a,l.join(`
`))}}}async extractAndProcessBlockReferences(e,i,r,o){let a=o.match(D1);if(!a)return o;let s=[];for(let p of a)try{let m=i.get(p);if(!m){s.push(p);continue}let y=m.blockString.replace(/\[\[|\]\]/g,""),w=`[[${r}/${m.pageName}#^${p}|${y}]]`;await this.modifySourceBlockString(e,m,r,p),s.push(w)}catch(m){s.push(p)}let l=0;return o.replace(/\(\(\b.*?\b\)\)/g,()=>s[l++])}async downloadFirebaseFile(e,i){let{progress:r,vault:o}=this,a="";try{let s,l;if(R1.test(e)?(s=e.match(/https:\/\/firebasestorage(.*?)\?alt(.*?)\}/),l=e.match(/{{.*https:\/\/firebasestorage.*?alt=media&.*?(?=\s|$)/)):dd.test(e)?(s=e.match(dd),l=e.match(/!\[.*https:\/\/firebasestorage.*?alt=media&.*?(?=\s|$)/)):(s=e.match(O1),l=e.match(/https:\/\/firebasestorage.*?alt=media&.*?(?=\s|$)/)),s&&l){let c="https://firebasestorage"+s[1],p=decodeURIComponent(c.split("/").last()||"");if(p){let d=p.split("/");d.length>1&&(d.splice(-1,1),this.createFolders(`${i}/${d.join("/")}`))}else{let d=Math.floor(Date.now()/1e3),f=c.slice(-5).match(/(.*?)\.(.+)/);if(!f)return r.reportSkipped(s[1],"Unexpected file extension"),e;p=`${d}.${f[2]}`}let m=`${i}/${p}`;if(o.getAbstractFileByPath(m))return r.reportSkipped(s[1],"File already exists"),e;a=s[0].slice(0,-1);let E=await(await fetch(a,{})).arrayBuffer();return await o.createBinary(m,E),r.reportAttachmentSuccess(a),e.replace(l[0],`![[${m}]]`)}}catch(s){console.error(s),r.reportFailed(a,s)}return e}};var jt=require("obsidian");var md=/!\[\]\(assets\/([^)]*)\)/g,qr=class extends ce{init(){jt.Platform.isMacOS||this.modal.contentEl.createEl("p",{text:"Due to platform limitations, only textpack and zip files can be imported from this device. Open your vault on a Mac to import textbundle files."});let e=jt.Platform.isMacOS?["textbundle","textpack","zip"]:["textpack","zip"];this.addFileChooserSetting("Textbundle",e,!0),this.addOutputLocationSetting("Textbundle")}async import(e){let{files:i}=this;if(i.length===0){new jt.Notice("Please pick at least one file to import.");return}let r=await this.getOutputFolder();if(!r){new jt.Notice("Please select a location to export to.");return}this.attachmentsFolderPath=await this.createFolders(`${r.path}/assets`);for(let o of i)if(o.extension==="textpack")await gt(o,async(a,s)=>{await this.process(e,o.name,s)});else if(o.extension==="zip")await gt(o,async(a,s)=>{let l=this.groupFilesByTextbundle(o.name,s);for(let c of l)await this.process(e,o.name,c)});else{let s=await new ut(`${o.toString()}/`).list();await this.process(e,o.name,s)}}groupFilesByTextbundle(e,i){let r={},o=e+"/",a=".textbundle";for(let s of i){if(!s.fullpath.startsWith(o)){console.log("Skipping",s.fullpath);continue}let l=s.fullpath.slice(o.length);if(l.startsWith("._")||l.startsWith("__MACOSX")){console.log("Skipping",s.fullpath);continue}let c=l.indexOf(a);if(c===-1){console.log("Skipping",s.fullpath);continue}let p=l.slice(0,c)+".textbundle";if(l.slice(c+a.length+1).startsWith("._")){console.log("Skipping",s.fullpath);continue}p in r?r[p].push(s):r[p]=[s]}return Object.values(r)}async process(e,i,r){let o=r.find(a=>a.name==="info.json");if(o){let a=await o.readText(),s=JSON.parse(a);if(s.hasOwnProperty("type")&&s.type!=="net.daringfireball.markdown"){e.reportSkipped(i,"The textbundle does not contain markdown");return}}for(let a of r)if(!a.name.startsWith("._"))try{if(a.type==="file"&&(a.extension==="md"||a.extension==="markdown")){let s="parent"in a?a.parent:i;s=s.replace(/.textbundle$/,"");let l=await a.readText();l.match(md)&&(l=l.replace(md,`![[${this.attachmentsFolderPath.path}/$1]]`));let c=(0,jt.normalizePath)(s),p=await this.getOutputFolder();await this.saveAsMarkdownFile(p,c,l),e.reportNoteSuccess(s)}else if(a.type==="file"&&a.fullpath.contains("assets/"))await this.importAsset(e,a);else if(a.type==="folder"){let{basename:s}=re(a.toString());if(s!=="assets")continue;let c=await new ut(`${a.toString()}/`).list();for(let p of c)await this.importAsset(e,p)}else a.name!=="info.json"&&e.reportSkipped(a.name,"the file is not a media or markdown file.")}catch(s){e.reportFailed(a.name,s)}}async importAsset(e,i){if(i.type==="folder"){e.reportSkipped(i.name);return}let r=`${this.attachmentsFolderPath.path}/${i.name}`;this.vault.getAbstractFileByPath(r)&&e.reportSkipped(i.name,"the file already exists.");let a=await i.read();await this.vault.createBinary(r,a),e.reportAttachmentSuccess(i.name)}};var $r="obsidian://importer-auth/",Ln=["png","webp","jpg","jpeg","gif","bmp","svg","mpg","m4a","webm","wav","ogv","3gp","mov","mp4","mkv","pdf"],Wr=class{constructor(t){this.notes=0;this.attachments=0;this.skipped=[];this.failed=[];this.maxFileNameLength=100;this.cancelled=!1;this.el=t,t.empty(),this.statusEl=t.createDiv("importer-status"),this.progressBarEl=t.createDiv("importer-progress-bar",e=>{this.progressBarInnerEl=e.createDiv("importer-progress-bar-inner")}),t.createDiv("importer-stats-container",e=>{e.createDiv("importer-stat mod-imported",i=>{this.importedCountEl=i.createDiv({cls:"importer-stat-count",text:"0"}),i.createDiv({cls:"importer-stat-name",text:"imported"})}),e.createDiv("importer-stat mod-attachments",i=>{this.attachmentCountEl=i.createDiv({cls:"importer-stat-count",text:"0"}),i.createDiv({cls:"importer-stat-name",text:"attachments"})}),e.createDiv("importer-stat mod-remaining",i=>{this.remainingCountEl=i.createDiv({cls:"importer-stat-count",text:"0"}),i.createDiv({cls:"importer-stat-name",text:"remaining"})}),e.createDiv("importer-stat mod-skipped",i=>{this.skippedCountEl=i.createDiv({cls:"importer-stat-count",text:"0"}),i.createDiv({cls:"importer-stat-name",text:"skipped"})}),e.createDiv("importer-stat mod-failed",i=>{this.failedCountEl=i.createDiv({cls:"importer-stat-count",text:"0"}),i.createDiv({cls:"importer-stat-name",text:"failed"})})}),this.importLogEl=t.createDiv("importer-log"),this.importLogEl.hide()}status(t){this.statusEl.setText(t.trim()+"...")}reportNoteSuccess(t){this.notes++,this.importedCountEl.setText(this.notes.toString())}reportAttachmentSuccess(t){this.attachments++,this.attachmentCountEl.setText(this.attachments.toString())}reportSkipped(t,e){let{importLogEl:i}=this;this.skipped.push(t),this.skippedCountEl.setText(this.skipped.length.toString()),console.log("Import skipped",t,e),this.importLogEl.createDiv("list-item",r=>{r.createSpan({cls:"importer-error",text:"Skipped: "}),r.createSpan({text:`"${Qn(t,this.maxFileNameLength)}"`+(e?` because ${Qn(String(e),this.maxFileNameLength)}`:"")})}),i.scrollTop=i.scrollHeight,i.show()}reportFailed(t,e){let{importLogEl:i}=this;this.failed.push(t),this.failedCountEl.setText(this.failed.length.toString()),console.log("Import failed",t,e),this.importLogEl.createDiv("list-item",r=>{r.createSpan({cls:"importer-error",text:"Failed: "}),r.createSpan({text:`"${Qn(t,this.maxFileNameLength)}"`+(e?` because ${Qn(String(e),this.maxFileNameLength)}`:"")})}),i.scrollTop=i.scrollHeight,i.show()}reportProgress(t,e){e<=0||(console.log("Current progress:",(100*t/e).toFixed(1)+"%"),this.remainingCountEl.setText((e-t).toString()),this.importedCountEl.setText(t.toString()),this.progressBarInnerEl.style.width=(100*t/e).toFixed(1)+"%")}cancel(){this.cancelled=!0,this.progressBarEl.hide(),this.statusEl.hide()}hideStatus(){this.progressBarEl.hide(),this.statusEl.hide()}isCancelled(){return this.cancelled}},C1={importers:{onenote:{previouslyImportedIDs:[]}}},Zr=class extends Ht.Plugin{async onload(){this.importers={"apple-notes":{name:"Apple Notes",optionText:"Apple Notes",importer:br,helpPermalink:"import/apple-notes"},bear:{name:"Bear",optionText:"Bear (.bear2bk)",importer:xr,helpPermalink:"import/bear"},evernote:{name:"Evernote",optionText:"Evernote (.enex)",importer:Cr,helpPermalink:"import/evernote"},keep:{name:"Google Keep",optionText:"Google Keep (.zip/.json)",importer:Fr,helpPermalink:"import/google-keep"},html:{name:"HTML files",optionText:"HTML (.html)",importer:Ir,helpPermalink:"import/html"},onenote:{name:"Microsoft OneNote",optionText:"Microsoft OneNote",importer:Mr,helpPermalink:"import/onenote"},notion:{name:"Notion",optionText:"Notion (.zip)",importer:Pr,helpPermalink:"import/notion",formatDescription:"Export your Notion workspace to HTML format."},"roam-json":{name:"Roam Research",optionText:"Roam Research (.json)",importer:Hr,helpPermalink:"import/roam",formatDescription:"Export your Roam Research workspace to JSON format."},textbundle:{name:"Textbundle files",optionText:"Textbundle (.textbundle, .textpack)",importer:qr,helpPermalink:"import/textbundle"}},this.addRibbonIcon("lucide-import","Open Importer",()=>{new wi(this.app,this).open()}),this.addCommand({id:"open-modal",name:"Open importer",callback:()=>{new wi(this.app,this).open()}}),this.registerObsidianProtocolHandler("importer-auth",e=>{if(this.authCallback){this.authCallback(e),this.authCallback=void 0;return}new Ht.Notice("Unexpected auth event. Please restart the auth process.")})}onunload(){}async loadData(){return Object.assign({},C1,await super.loadData())}async saveData(e){await super.saveData(e)}registerAuthCallback(e){this.authCallback=e}},wi=class extends Ht.Modal{constructor(e,i){super(e);this.current=null;this.plugin=i,this.titleEl.setText("Import data into Obsidian"),this.modalEl.addClass("mod-importer");let r=Object.keys(i.importers);r.length>0&&(this.selectedId=r[0],this.updateContent())}updateContent(){let{contentEl:e,selectedId:i}=this,r=this.plugin.importers,o=r[i];e.empty();let a=new DocumentFragment;if(a.createSpan({text:"The format to be imported."}),o.formatDescription&&(a.createEl("br"),a.createSpan({text:o.formatDescription})),a.createEl("br"),a.createEl("a",{text:`Learn more about importing from ${o.name}.`,href:`https://help.obsidian.md/${o.helpPermalink}`}),new Ht.Setting(e).setName("File format").setDesc(a).addDropdown(s=>{for(let l in r)r.hasOwnProperty(l)&&s.addOption(l,r[l].optionText);s.onChange(l=>{r.hasOwnProperty(l)&&(this.selectedId=l,this.updateContent())}),s.setValue(this.selectedId)}),i&&r.hasOwnProperty(i)){let s=this.importer=new o.importer(this.app,this);if(s.notAvailable)return;e.createDiv("modal-button-container",l=>{l.createEl("button",{cls:"mod-cta",text:"Import"},c=>{c.addEventListener("click",async()=>{this.current&&this.current.cancel(),e.empty();let p=e.createDiv(),m=this.current=new Wr(p),y=e.createDiv("modal-button-container"),w=y.createEl("button",{cls:"mod-danger",text:"Stop"},E=>{E.addEventListener("click",()=>{m.cancel(),w.detach()})});try{await s.import(m)}finally{this.current===m&&(this.current=null),y.createEl("button",{text:"Upload more"},E=>{E.addEventListener("click",()=>this.updateContent())}),w.detach(),y.createEl("button",{cls:"mod-cta",text:"Done"},E=>{E.addEventListener("click",()=>this.close())}),m.hideStatus()}})})})}}onClose(){let{contentEl:e,current:i}=this;e.empty(),i&&i.cancel()}};
/*! Bundled license information:

sax/lib/sax.js:
  (*! http://mths.be/fromcodepoint v0.1.0 by @mathias *)
*/

/* nosourcemap */