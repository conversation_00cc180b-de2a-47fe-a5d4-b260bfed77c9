{"openViewOnStartup": true, "filterEmpty": true, "excludePaths": ["_Templates/"], "includePaths": [], "useIncludeTags": false, "taskIncludeTags": [], "fileIncludeTags": [], "useExcludeTags": false, "taskExcludeTags": [], "fileExcludeTags": [], "styles": ["style1"], "dailyNoteFolder": "", "dailyNoteFormat": "YYYY, MMMM DD - dddd", "sectionForNewTasks": "## Tasks", "hideTags": [], "forward": true, "sort": "(t1, t2) => t1.order <= t2.order ? -1 : 1", "taskStatusOrder": ["overdue", "due", "scheduled", "start", "process", "unplanned", "done", "cancelled"], "dateFormat": "dddd, MMM, D", "inbox": "Inbox.md", "taskFiles": [], "tagColorPalette": {"#TODO": "#339988", "#TEST": "#998877"}, "useCounters": true, "counterBehavior": "Filter", "useQuickEntry": true, "entryPosition": "today", "useYearHeader": true, "useRelative": true, "useRecurrence": true, "usePriority": true, "useTags": true, "useFileBadge": true, "useSection": true, "hideStatusTasks": ["x", "-"], "defaultTodayFocus": false, "defaultFilters": "", "useBuiltinStyle": true, "convert24HourTimePrefix": false}