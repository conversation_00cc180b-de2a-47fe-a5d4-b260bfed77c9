Stack trace:
Frame         Function      Args
0007FFFF9F80  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8E80) msys-2.0.dll+0x1FE8E
0007FFFF9F80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA258) msys-2.0.dll+0x67F9
0007FFFF9F80  000210046832 (000210286019, 0007FFFF9E38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9F80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9F80  000210068E24 (0007FFFF9F90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA260  00021006A225 (0007FFFF9F90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFFE4210000 ntdll.dll
7FFFE3140000 KERNEL32.DLL
7FFFE1960000 KERNELBASE.dll
7FFFE1F10000 USER32.dll
7FFFE1320000 win32u.dll
7FFFE2DA0000 GDI32.dll
7FFFE14A0000 gdi32full.dll
7FFFE1700000 msvcp_win.dll
7FFFE1350000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFFE2BB0000 advapi32.dll
7FFFE3090000 msvcrt.dll
7FFFE2CF0000 sechost.dll
7FFFE1470000 bcrypt.dll
7FFFE2EE0000 RPCRT4.dll
7FFFE0920000 CRYPTBASE.DLL
7FFFE1860000 bcryptPrimitives.dll
7FFFE3E60000 IMM32.DLL
