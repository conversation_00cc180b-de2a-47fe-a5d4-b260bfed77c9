在 Unity 中实现地面反射效果有几种方法，最常见的是使用反射探针 (Reflection Probe)、平面反射 (Planar Reflection) 或者着色器 (Shader) 来实现。下面我将介绍几种实现方法：

## 1. 使用反射探针 (Reflection Probe)

反射探针是一种简单的方法，但对于地面反射可能不是最理想的：

```csharp:c:\Users\<USER>\source\Wind-tunnel-demonstration-system\Assets\Scripts\ReflectionManager.cs
using UnityEngine;

public class ReflectionManager : MonoBehaviour
{
    public ReflectionProbe reflectionProbe;
    
    void Start()
    {
        // 确保反射探针已分配
        if (reflectionProbe == null)
        {
            reflectionProbe = GetComponent<ReflectionProbe>();
            if (reflectionProbe == null)
            {
                reflectionProbe = gameObject.AddComponent<ReflectionProbe>();
            }
        }
        
        // 设置反射探针参数
        reflectionProbe.size = new Vector3(20, 10, 20); // 根据场景大小调整
        reflectionProbe.resolution = 256;
        reflectionProbe.refreshMode = ReflectionProbeRefreshMode.EveryFrame; // 实时更新反射
    }
}
```

## 2. 使用平面反射 (推荐)

平面反射是实现地面反射的更好方法，它通过渲染一个镜像摄像机来创建反射效果：

```csharp:c:\Users\<USER>\source\Wind-tunnel-demonstration-system\Assets\Scripts\PlanarReflection.cs
using UnityEngine;

public class PlanarReflection : MonoBehaviour
{
    public Camera reflectionCamera;
    public LayerMask reflectionLayers = -1;
    public float clipPlaneOffset = 0.07f;
    
    private RenderTexture reflectionTexture;
    private Material reflectionMaterial;
    private static bool isReflecting = false;
    
    void Start()
    {
        // 创建反射相机
        if (reflectionCamera == null)
        {
            GameObject reflCamObj = new GameObject("ReflectionCamera");
            reflCamObj.transform.parent = transform;
            reflectionCamera = reflCamObj.AddComponent<Camera>();
            reflectionCamera.enabled = false;
        }
        
        // 创建反射材质
        Renderer rend = GetComponent<Renderer>();
        if (rend)
        {
            reflectionMaterial = rend.material;
        }
    }
    
    void OnWillRenderObject()
    {
        if (!enabled || !reflectionMaterial || isReflecting)
            return;
        
        isReflecting = true;
        
        Camera cam = Camera.current;
        if (!cam)
            return;
        
        // 创建反射纹理
        if (reflectionTexture == null || reflectionTexture.width != Screen.width || reflectionTexture.height != Screen.height)
        {
            if (reflectionTexture)
                DestroyImmediate(reflectionTexture);
            
            reflectionTexture = new RenderTexture(Screen.width, Screen.height, 16);
            reflectionTexture.name = "ReflectionTexture";
            reflectionTexture.isPowerOfTwo = true;
            reflectionTexture.hideFlags = HideFlags.DontSave;
            
            // 设置材质的反射纹理
            reflectionMaterial.SetTexture("_ReflectionTex", reflectionTexture);
        }
        
        // 设置反射相机
        reflectionCamera.CopyFrom(cam);
        reflectionCamera.cullingMask = reflectionLayers;
        
        // 反射矩阵
        Vector3 pos = transform.position;
        Vector3 normal = transform.up;
        float d = -Vector3.Dot(normal, pos) - clipPlaneOffset;
        Vector4 reflectionPlane = new Vector4(normal.x, normal.y, normal.z, d);
        
        Matrix4x4 reflection = Matrix4x4.zero;
        CalculateReflectionMatrix(ref reflection, reflectionPlane);
        
        Vector3 oldPos = cam.transform.position;
        Vector3 newPos = reflection.MultiplyPoint(oldPos);
        reflectionCamera.worldToCameraMatrix = cam.worldToCameraMatrix * reflection;
        
        // 设置投影矩阵以处理裁剪平面
        Vector4 clipPlane = CameraSpacePlane(reflectionCamera, pos, normal, 1.0f, clipPlaneOffset);
        Matrix4x4 projection = cam.projectionMatrix;
        CalculateObliqueMatrix(ref projection, clipPlane);
        reflectionCamera.projectionMatrix = projection;
        
        reflectionCamera.transform.position = newPos;
        Vector3 euler = cam.transform.eulerAngles;
        reflectionCamera.transform.eulerAngles = new Vector3(-euler.x, euler.y, euler.z);
        
        // 渲染到纹理
        reflectionCamera.targetTexture = reflectionTexture;
        GL.invertCulling = true;
        reflectionCamera.Render();
        GL.invertCulling = false;
        
        isReflecting = false;
    }
    
    // 计算反射矩阵
    private void CalculateReflectionMatrix(ref Matrix4x4 reflectionMat, Vector4 plane)
    {
        reflectionMat.m00 = (1F - 2F * plane[0] * plane[0]);
        reflectionMat.m01 = (-2F * plane[0] * plane[1]);
        reflectionMat.m02 = (-2F * plane[0] * plane[2]);
        reflectionMat.m03 = (-2F * plane[3] * plane[0]);

        reflectionMat.m10 = (-2F * plane[1] * plane[0]);
        reflectionMat.m11 = (1F - 2F * plane[1] * plane[1]);
        reflectionMat.m12 = (-2F * plane[1] * plane[2]);
        reflectionMat.m13 = (-2F * plane[3] * plane[1]);

        reflectionMat.m20 = (-2F * plane[2] * plane[0]);
        reflectionMat.m21 = (-2F * plane[2] * plane[1]);
        reflectionMat.m22 = (1F - 2F * plane[2] * plane[2]);
        reflectionMat.m23 = (-2F * plane[3] * plane[2]);

        reflectionMat.m30 = 0F;
        reflectionMat.m31 = 0F;
        reflectionMat.m32 = 0F;
        reflectionMat.m33 = 1F;
    }
    
    // 计算相机空间中的平面
    private Vector4 CameraSpacePlane(Camera cam, Vector3 pos, Vector3 normal, float sideSign, float clipPlaneOffset)
    {
        Vector3 offsetPos = pos + normal * clipPlaneOffset;
        Matrix4x4 m = cam.worldToCameraMatrix;
        Vector3 cpos = m.MultiplyPoint(offsetPos);
        Vector3 cnormal = m.MultiplyVector(normal).normalized * sideSign;
        return new Vector4(cnormal.x, cnormal.y, cnormal.z, -Vector3.Dot(cpos, cnormal));
    }
    
    // 计算斜投影矩阵
    private void CalculateObliqueMatrix(ref Matrix4x4 projection, Vector4 clipPlane)
    {
        Vector4 q = projection.inverse * new Vector4(
            Mathf.Sign(clipPlane.x),
            Mathf.Sign(clipPlane.y),
            1.0f,
            1.0f
        );
        Vector4 c = clipPlane * (2.0F / Vector4.Dot(clipPlane, q));
        projection[2] = c.x - projection[3];
        projection[6] = c.y - projection[7];
        projection[10] = c.z - projection[11];
        projection[14] = c.w - projection[15];
    }
}
```

## 3. 反射着色器

为了使用上面的平面反射脚本，你需要一个支持反射的着色器：

```shader:c:\Users\<USER>\source\Wind-tunnel-demonstration-system\Assets\Shaders\ReflectiveGround.shader
Shader "Custom/ReflectiveGround"
{
    Properties
    {
        _Color ("Color", Color) = (1,1,1,1)
        _MainTex ("Albedo (RGB)", 2D) = "white" {}
        _Glossiness ("Smoothness", Range(0,1)) = 0.5
        _Metallic ("Metallic", Range(0,1)) = 0.0
        _ReflectionTex ("Reflection", 2D) = "white" {}
        _ReflectionStrength ("Reflection Strength", Range(0,1)) = 0.5
    }
    SubShader
    {
        Tags { "RenderType"="Opaque" }
        LOD 200

        CGPROGRAM
        #pragma surface surf Standard fullforwardshadows
        #pragma target 3.0

        sampler2D _MainTex;
        sampler2D _ReflectionTex;

        struct Input
        {
            float2 uv_MainTex;
            float4 screenPos;
        };

        half _Glossiness;
        half _Metallic;
        fixed4 _Color;
        half _ReflectionStrength;

        void surf (Input IN, inout SurfaceOutputStandard o)
        {
            // 基础纹理
            fixed4 c = tex2D (_MainTex, IN.uv_MainTex) * _Color;
            
            // 反射纹理
            float2 screenUV = IN.screenPos.xy / IN.screenPos.w;
            fixed4 refl = tex2D(_ReflectionTex, screenUV);
            
            // 混合基础颜色和反射
            o.Albedo = lerp(c.rgb, refl.rgb, _ReflectionStrength);
            o.Metallic = _Metallic;
            o.Smoothness = _Glossiness;
            o.Alpha = c.a;
        }
        ENDCG
    }
    FallBack "Diffuse"
}
```

## 使用方法

1. 创建上述脚本和着色器
2. 创建一个平面作为地面
3. 将 PlanarReflection 脚本附加到地面对象上
4. 将 ReflectiveGround 着色器应用到地面材质
5. 调整反射强度和其他参数以获得最佳效果

## 4. 使用 URP/HDRP 内置反射功能

如果你使用的是 Universal Render Pipeline (URP) 或 High Definition Render Pipeline (HDRP)，它们提供了更简单的方法来实现反射：

- 在 URP 中，你可以使用 Screen Space Reflection 功能
- 在 HDRP 中，你可以使用 Planar Reflection Probe 或 Screen Space Reflection

这些方法通常只需要在渲染设置中启用相应功能，并调整材质的反射属性即可。

希望这些方法能帮助你在 Unity 中实现地面反射效果！