---
tags:
  - project
Status: 完成
goal: 顺利完成项目
Deadline: 2025-04-30
created: 2025-01-06 17:15:36
---

- **项目目标/描述:** 这个项目具体要做什么。
- **任务列表 (Tasks):** 这是核心部分。使用 Obsidian 的任务语法 `- [ ] 任务描述` 列出所有需要完成的具体步骤。可以添加 `@due(YYYY-MM-DD)` 或 `#priority/high` 等标签（配合 Tasks 插件更佳）。
- **所需资源 (Resources):** 链接到需要的笔记、文件或网页。
- **相关笔记 (Related Notes):**
	- [[Unity]]
	- [[Blender]]



# 待办事项

- [x] 修改 4 k 上字体显示异常的问题（把目标尺寸改为 4 k），调整由此带来的异常。 🛫 2025-04-08 📅 2025-04-08 ✅ 2025-04-08
- [x] 调研 unity 按钮点击事件如何触发外部html里面图表的变化 ✅ 2025-04-08 ——有方案，但是用不上了，李扬觉得 unity内部能画出好看的图表
- [x] 完成实验模块的页面初步的布局 🛫 2025-04-09

2025-04-11：客户反馈
- [x] 1.漫游速度减小，到部件停顿时间加长，过程中加上各结构说明，结束时回到总览页面 ✅ 2025-04-14
- [x] 2.潜艇模型更换成有颜色版本，大小按照实际图纸调整 ✅ 2025-04-16
- [x] 3.潜艇增加烟流效果 ✅ 2025-04-15
- [x] 4.潜艇模型不关联舵机数据 
- [x] 5.三个试验名称和界面优化 
- [x] 6.倒影效果弱化 ✅ 2025-04-17
- [x] 7.增加一个全屏按钮 ✅ 2025-04-18
- [x] 8.标题修改成“消声风洞综合示教系统” ✅ 2025-04-14
- [x] 9.倾角关联 ✅ 2025-04-18
- [x] 10. 调整模型在unity和blender之间的效果差异，显示潜艇上的白边 🛫 2025-04-16 ✅ 2025-04-16
- [x] 11. 调整模型的锚点，解决旋转中心点不对的问题——李扬找朋友公司的美工解决的 ✅ 2025-04-17
- [x] 12. 提供[[消声风洞综合示教系统·使用手册]] ✅ 2025-04-18


## 周报
- [[风洞项目周报2025-04-18]]


# 总结

## 时间轴
- 2025年1月2号建项目群。
- 第一阶段（1月8号~1月17号）：跟客户沟通需求，完成《[风洞演示系统大屏需求文档](https://doc.weixin.qq.com/doc/w3_AVoAygYkAGM1t7DGLhgRAyW0rUhdG?scode=ADUA_AdLAEMT4QqqRS)》
- 第二阶段（1月17号~2月23号）：尝试用外包，，其中1月28号~2月8号春节假期，节后，吴京京陆续给了给了4个版本，效果不佳。2月23号跟他讨论签合同的事情，他也不能保证按时提供源码，感觉风险太大。启动 Plan B：在网上直接买一套源码，然后内部边学习边开发
- **2025-02-24：在 B 站买了一套代码——《数字孪生机房源码》，花费450。**
- 第三阶段（2月25号~3月28号）：边学边干，完成主体功能
	- ~2月28号：花了一周时间：带李扬一起预研关键技术点
		1. 关键剖面展示  
		2. 漫游  
		3. 粒子风  
		4. 绑定数据，显示图表  
		5. 根据数据控制潜艇
	- ~3月7号：
		- 现状：风洞展示功能完成。  
		- 问题：模型显示效果要调。（ @陆克军下周花1小时试试，不行的话就找冰姐一起看看）  
		- 计划：下周开始做风洞实验功能，@李扬做数据接入和展示，Panda 做潜艇模型粒子风效果，预期1~2周做完。
	- ~3月13号
		- 实现潜艇运动效果，跟客户开会，客户反馈——潜艇的颜色要调整；UI界面优化（界面偏暗了，能不能亮一点；总览页面单调了，参考刘总提供的展板图，多放置一些内容）
	- ~3月21号：
		- 现状：图表组件和粒子风效果已经完成。 （图表比预期的难） 
		- 问题：UI效果难定稿，估计跟客户展示后会有返工。  
		- 计划：下周 @李扬完成数据接入和展示，Panda 把剖面整合到总览里面，让总览页面丰富起来。
	- ~3月28号：
		- 现状：本周完成  
			  1. 数据接入和展示；  
			  2. 总览采用设计稿；剖面增加描述，显得丰富一些；  
			  3. 客户再次反馈界面偏暗，把提亮工作外包给了原作者。  
		  - 问题：暂无。  
		  - 下周计划：  
			  1. 美工外包下周一、周二提供界面提亮优化；  
			  2. 完善实验功能模块；  
			  3. 给客户展示，看看接下来还要做哪些改动。  
		  - 后续事宜：  
			  1. camera摄像头集成尝试，太难可以不做。  
			  2. 现场部署，估计在4月份
- 第四阶段（3月25号~4月21号）：给客户演示，处理客户的反馈意见
	 * ~4月2号： **外部调优效果不佳，耗费500元，没有效果**
		 * 内部会议纪要：接下来沿着原始场景继续往下做：  
			1. 去掉现有总览，把现有的剖面当做总览 @Panda  
			2. 提取海报相关信息丰富剖面 @Panda  
			3. 漫游速度调慢2~3倍 @Panda  
			4. 实验模型视角改一下 @Panda  
			5. 实验页面控件调优 @李扬   
			5. 说服客户接受风格 @陆克军
	- **~4月10号：Look 带着大家一起优化**
	- **~4月11号（周五）：给客户第一次远程部署，客户反馈9条意见**
		1. 漫游速度减小，到部件停顿时间加长，过程中加上各结构说明，结束时回到总览页面 
		2. 潜艇模型更换成有颜色版本，大小按照实际图纸调整  
		3. 潜艇增加烟流效果  
		4. 潜艇模型不关联舵机数据  
		5. 三个试验名称和界面优化  
		6. 倒影效果弱化  
		7. 增加一个全屏按钮  
		8. 标题修改成“消声风动综合示教系统”  
		9. 倾角关联
	- **~4月18号：修改好已有问题，给客户第二次部署。**
- 第五阶段（4月22号~4月25号）：李扬去客户现场支持、实施
- 第六阶段（4月28号~5月06号）：李扬根据客户要求修改《用户手册》

## 经验总结：
1. **外包管理**：踩了2次外包的坑，一次是外包 unity 开发给京京；一次是外包 UI 优化给阿吉。整体都不达预期。**一个东西如果内部不能表述清楚，纯粹靠外部人员发挥，效果很难保证，如果内部能清晰地讲清楚了，大概率靠 AI 辅助也基本上能做出来了**。一方便受限于费用，对方不会为了几百几千块钱单独费心做；另一方面很多 UI 上效果，主观性太大，无从量化。所以更好的实现途径，就是问外包要他以前做过的效果，好的话就买下来，不好就换人问。定制化的需求很难满足——费用低了效果大概率差，费用多了我们没有预算，也不能保证效果。
2. **客户管理**：“我要酷炫效果”VS“不用担心，拿着 PPT 都能交付”，都是客户的话，本质上客户也不会很清楚自己要什么，但他肯定也是有保底方案，对我们而言，就是尽量贴合客户需求改，至于最终效果满不满意这个强求不了，通过限制服务时间、服务次数，防止项目做亏本即可。
3. **流程管理**：为了避免最终效果和客户预期差别太大，最好是前期能出效果图，不过这样做也有利有弊，有利的一面是客户提前反馈预期差，避免研发后期返工；不利的一面是客户可能觉得都不太满意，改找其他人来做了，项目丢失。


## 20250422李扬出差问题总结
1.  页面优化 (页面元素的位置、大小、颜色的修改)
2.  图表位置和大小的修改，现场9块拼接屏，客户希望在大屏上的图表位置避开拼接处
3.  把12路通道修改为6个操作台，客户的本意是振动噪声和激振力总共12路数据，即6路振动噪声和6路激振力，共12路，现在修改为6个操作台，每个操作台1路振动噪声和1个激振力
4.  6个操作台的复选框对应折线颜色
5.  3个实验中，振动噪声和激振力实验虽然不展示潜艇模型，但是需要展示风场特效
6.  模型联动效果不正确，客户说可以不做
7.  客户希望用户手册内容更丰富点——4.28已经发一版给客户
目前来看，这个项目没有后续的研发动作了。