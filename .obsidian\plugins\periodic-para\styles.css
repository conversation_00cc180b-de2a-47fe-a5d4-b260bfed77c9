/* src/component/CreateNote/index.less */
.periodic-para-create-note {
  overflow: hidden !important;
}
.periodic-para-create-note button:not(.ant-btn) {
  background-color: unset !important;
  box-shadow: unset !important;
}
.periodic-para-create-note .periodic-form {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  align-content: flex-start;
}
.periodic-para-create-note .ant-picker-cell-selected .ant-picker-cell-inner .cell-container .chinese-cal {
  color: var(--text-on-accent);
}
.periodic-para-create-note .ant-picker-cell-inner {
  display: flex !important;
  width: 100% !important;
  height: 100% !important;
  align-items: center;
  justify-content: center;
}
.periodic-para-create-note .ant-picker-cell-inner .cell-container {
  position: relative;
  border-radius: var(--radius-s);
}
.periodic-para-create-note .ant-picker-cell-inner .cell-container .chinese-cal {
  display: block;
  line-height: initial;
  font-size: var(--font-smallest);
  color: var(--text-muted);
}
.periodic-para-create-note .ant-picker-cell-inner .cell-container .dot {
  position: relative;
  height: 2px;
  color: var(--interactive-accent);
  display: block;
  margin: auto;
  top: -15px;
}
.periodic-para-create-note .ant-picker-cell-inner .cell-container .week-dot {
  position: fixed;
  left: calc(var(--size-4-1) * 2);
  color: var(--interactive-accent);
}
.periodic-para-create-note .ant-picker-cell-inner .cell-container .label {
  position: absolute;
  top: calc(var(--size-4-1) * -1.5);
  right: calc(var(--size-4-1) * -2.5);
  color: var(--text-on-accent);
  width: calc(var(--size-4-1) * 3);
  line-height: calc(var(--size-4-1) * 3);
  border-radius: calc(var(--size-4-1) * 3);
  font-size: var(--font-smallest);
}
.periodic-para-create-note .ant-picker-cell-inner .cell-container .label.workday {
  background-color: var(--text-muted);
}
.periodic-para-create-note .ant-picker-cell-inner .cell-container .label.holiday {
  background-color: var(--interactive-accent);
}
.periodic-para-create-note .ant-btn-primary {
  color: var(--text-on-accent);
  background-color: var(--interactive-accent);
  box-shadow: 0 2px 0 var(--background-modifier-box-shadow);
}
.periodic-para-create-note .ant-tabs-nav {
  margin: var(--size-4-1) 0;
}
.periodic-para-create-note .ant-tabs-nav::before {
  border: 0;
}
.periodic-para-create-note .ant-form-item .ant-picker {
  visibility: hidden;
}
.periodic-para-create-note .ant-form-item .ant-picker-dropdown {
  display: flex;
  justify-content: center;
  position: relative;
  inset: -40px auto auto 0px !important;
}
.periodic-para-create-note .ant-form-item .ant-picker-dropdown .ant-picker-panel-container {
  border-radius: 0;
  box-shadow: unset;
  transition: unset;
  background-color: transparent !important;
}
.periodic-para-create-note .ant-form-item .ant-picker-dropdown .ant-picker-header {
  border: 0;
}
.periodic-para-create-note .ant-form-item .ant-picker-dropdown .ant-picker-body {
  padding: 0 var(--size-4-3);
}
.periodic-para-create-note .ant-form-item .ant-picker-dropdown .ant-picker-header-view {
  line-height: var(--line-height-normal);
}
.periodic-para-create-note .ant-form-item .ant-input-affix-wrapper {
  padding: 0 var(--size-4-2);
}
.is-phone .periodic-para button {
  width: unset;
}
.is-phone .periodic-para .ant-tabs-tab + .ant-tabs-tab {
  margin: 0 0 0 var(--size-4-6);
}

/* src/component/SettingTab/index.less */
.periodic-para-setting-tab .ant-tabs-nav-list {
  display: flex;
  justify-content: space-around;
  width: 100%;
}
.periodic-para-setting-tab .ant-tabs-nav-wrap {
  justify-content: center;
}
.periodic-para-setting-tab .ant-tabs-tab {
  flex: 1;
  display: flex;
  justify-content: center;
}
.periodic-para-setting-tab .ant-tabs-tab + .ant-tabs-tab {
  margin-left: 0;
}
.periodic-para-setting-tab .ant-form-item {
  margin-left: auto;
  margin-right: auto;
}
.periodic-para-setting-tab .ant-form {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

/* src/component/TopBanner/index.less */
.m-top-banner .pro-link {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px 0;
}
.m-top-banner .pro-link a {
  display: flex;
  align-items: center;
  text-decoration: none;
  height: 40px;
}
.m-top-banner .icon-links {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 5px 0;
}
.m-top-banner .icon-links a {
  display: flex;
  align-items: center;
  text-decoration: none;
  height: 40px;
}

/* src/index.less */
.lifeos * {
  font-family: var(--font-interface);
}
.lifeos button[class*=ant-] {
  padding: unset !important;
}
